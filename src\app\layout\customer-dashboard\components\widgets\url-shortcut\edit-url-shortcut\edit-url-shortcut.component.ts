import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { DefaultWidgetEditorComponent } from '../../default-widget/default-widget-editor.component';
import { UrlShortcutWidget } from '../../../../models/url-shortcut-widget';
import { DashboardUtilsService } from 'app/layout/customer-dashboard/services/dashboard-utils.service';
import { DashboardActionType } from 'app/layout/customer-dashboard/enums/dashboard-action-type.enum';
import { DomainValidator } from 'app/shared/validators/domain-validators';
@Component({
  selector: 'app-edit-url-shortcut',
  templateUrl: './edit-url-shortcut.component.html',
  styleUrls: ['./edit-url-shortcut.component.scss']
})
export class EditUrlShortcutComponent extends DefaultWidgetEditorComponent implements OnInit {
  public urlShortcutForm: FormGroup = null;
  data: UrlShortcutWidget;
  localDomain: string = window.location.origin
  constructor(
    private formBuilder: FormBuilder,
    private dashboardUtilsService: DashboardUtilsService) {
    super();
  }

  ngOnInit(): void {
    this.data = new UrlShortcutWidget(this.data);
    this.generateForm(this.data);
  }

  generateForm(data: UrlShortcutWidget): void {
    this.urlShortcutForm = this.formBuilder.group({
      url: new FormControl(data.url ? data.url : '', Validators.compose([Validators.required, DomainValidator.haveDomainValidator(window.location.origin)])),
    });
    this.onFieldChanges();
    this.dashboardUtilsService.setSidebarActionState({actionType: DashboardActionType.save, disabled: true});
  }

  onFieldChanges(): void {
    this.urlShortcutForm.get('url').valueChanges.subscribe(val => {
      this.data.url = val;
      this.onFormStatusChanges();
    });
  }

  onFormStatusChanges(): void {
    this.urlShortcutForm.statusChanges.subscribe((result) => {
      switch (result) {
        case "VALID":
          this.dashboardUtilsService.setSidebarActionState({actionType: DashboardActionType.save, disabled: false});
          this.dashboardUtilsService.setWidgetDataChange(this.data);
          break;
        case "INVALID":
          this.dashboardUtilsService.setSidebarActionState({actionType: DashboardActionType.save, disabled: true});
          break;
        default:
        break;
      }
    });
  }

}
