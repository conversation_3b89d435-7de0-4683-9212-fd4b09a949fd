
import { HttpClient } from '@angular/common/http';
import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, QueryList, SimpleChanges, ViewChild, ViewChildren } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { FileUploaderComponent } from 'app/shared/components/file-uploader/file-uploader.component';
import { ToastTypes } from 'app/shared/enum/toast-types';
import { URI } from 'app/shared/enum/uri';
import { DeviceInfo } from 'app/shared/models/device-info';
import { DeviceType } from 'app/shared/models/device-type';
import { ServerTypes } from 'app/shared/modules/data-layer/enum/resource/resource-server-types.enum';
import { apiMap } from 'app/shared/services/api.map';
import { StringService } from 'app/shared/services/string.service';
import { environment } from 'environments/environment';
import * as _ from 'lodash';
import { MessageService } from 'primeng/api';
import { Subscription, forkJoin } from 'rxjs';
import { take } from 'rxjs/operators';
import { EntityModel } from '../../../shared/models/entity.model';

import { HeaderStoreService } from 'app/services/store_servcies/header-store.service';
import * as fromAppReducers from '../../../store/app.reducers';
import { EntitiesActionType } from '../enums/entitiesActionType.enum';
import { Guid } from './../../../shared/enum/guid';
import {ApiCommands} from "app/shared/enum/enum";
import {TableData} from "app/shared/components/ng-turbo-table/models/table-data";

@Component({
    selector: 'app-entity-form',
    templateUrl: './entity-form.component.html',
    styleUrls: ['./entity-form.component.scss']
})
export class EntityFormComponent implements OnInit, OnChanges,OnDestroy {

    @Input('newEntity') entity:EntityModel;
    @Input('newEntityDevice') defaultDevice: {
        deviceId,
        deviceValue
    };
    @Output('displayForm') displayForm = new EventEmitter<any>();
    @Output('saveEntity') saveEntity = new EventEmitter<any>();
    @Output('deleteEntity') deleteEntity = new EventEmitter<any>();
    @ViewChild('uploader', {static: false}) fileUpload: FileUploaderComponent;
    @ViewChildren('photoUploaders') photoUploaders: QueryList<FileUploaderComponent>;

    entityForm: FormGroup;
    checkboxes = {};
    serverDevices: URI[] = [];
    serverResources = [];
    associatedDevices = [];
    resourceGroups = [];
    startDate;
    expirationDate;
    maxDate;
    invalidDates = false;
    imgSrc;
    newImage;
    newImgFromIdTag;
    isRtl = false;
    deviceInfos:DeviceInfo[];
    subscriptions: Subscription[] = [];

    constructor(
        private fb: FormBuilder,

        private sanitizer: DomSanitizer,
        private messageService: MessageService,
        private i18n: TranslateService,
        private store: Store<fromAppReducers.AppState>,
        private stringService: StringService,
        private httpClient: HttpClient,
        private headerStoreService: HeaderStoreService) {
            let userSettingsSubscription=this.headerStoreService.getState().pipe(take(1)).subscribe(settings => {
                this.isRtl = settings.direction === 'rtl' ? true : false;
            })

        this.subscriptions.push(userSettingsSubscription);
    }
    ngOnDestroy(): void {
        this.subscriptions.forEach(subscription => subscription.unsubscribe());
    }

    ngOnInit() {

        this.loadDevices();
       let gethResourceGroupsSubscription= this.httpClient.get(environment.apiUrl + apiMap.hResourceGroups.url+ServerTypes.Core_RES_Entity).subscribe(
            (res:any) => this.serverResources = res,
            err => {
                this.messageService.add({severity: 'error', summary: this.i18n.instant(ToastTypes.error), detail: this.i18n.instant('unableToLoadData')});
                this.displayForm.emit();
            });
        this.initFormData(this.entity);
        this.subscriptions.push(gethResourceGroupsSubscription);
    }

    loadDevices():void
    {
      let loadDevicesSubscription = forkJoin(
            this.httpClient.get(environment.apiUrl + apiMap.entityDevices.url),
            this.httpClient.get(environment.apiUrl + apiMap.getDeviceInfo.url)

        ).subscribe((results:any) =>{
                if (!results[0] || results[0].length == 0)
                    return;

                this.serverDevices = results[0].map(device => new URI(device));
                this.deviceInfos = results[1].map(deviceInfo => {
                    var result : DeviceInfo = new DeviceInfo();//TODO remove when we have a strongly typed api
                    result.id = Guid.parse( deviceInfo.id);
                    result.Name = deviceInfo.name;
                    result.spec = deviceInfo.spec;

                    return result;
                });
            },
            err => {
                this.messageService.add({severity: 'error', summary: this.i18n.instant(ToastTypes.error), detail: this.i18n.instant('unableToLoadData')});
                this.displayForm.emit();
            });
            this.subscriptions.push(loadDevicesSubscription);
    }

    isCorticaDevice(device):boolean
    {
        if (!device.value.id || device.value.id.length === 0)
        {
            return false;
        }

        var deviceID = Guid.parse(device.value.id);

        return this.isCorticaDeviceID(deviceID);
    }

    isCorticaDeviceID(id:Guid):boolean
    {
        let deviceInfo = this.deviceInfos.find(info => info.id.equals(id));
        return deviceInfo.spec.TypeId.toString() === DeviceType.Cortica.toString();
    }

    ngOnChanges(changes: SimpleChanges) {
        if(!changes) {
            return;
        }
        if (changes.entity && changes.entity.currentValue) {
            if (_.isEmpty(changes.entity.currentValue)) {
                this.entity = this.initEntity();
            }
            let newEntity =this.mapToModel(this.entity);
            this.initFormData(newEntity);
        } else {
            this.entity = this.initEntity();
            let newEntity =this.mapToModel(this.entity);
            this.initFormData(newEntity);
        }


    }

    setDefaultAssociatedDevice() {
        if (!this.defaultDevice)//only used from entities tab probably
        {
            return;
        }

        for (var i = 0; i < this.serverDevices.length; i++) {
            if (this.serverDevices[i].endId === this.defaultDevice.deviceId) {
                let deviceID : Guid = Guid.parse(this.defaultDevice.deviceId);
                this.associatedDevices.push({
                    DeviceName: this.serverDevices[i].endName,
                    Value: this.defaultDevice.deviceValue,
                    DeviceId: this.defaultDevice.deviceId,
                    IsCorticaDevice: this.isCorticaDeviceID(deviceID)
                });
            }
        }
    }

    buildAssociatedResourceGroups() {
        const control = <FormArray>this.entityForm.get('associatedResourceGroups.groups');
        this.serverResources.forEach(sResource => {
            this.resourceGroups.findIndex(eResource => eResource.Identity === sResource.Identity) > -1 ?
                control.push(this.patchValues(sResource.Name, true, sResource.Identity)) :
                control.push(this.patchValues(sResource.Name, false, sResource.Identity))
        })
    }

    buildAssociatedDevices() {

        const control = <FormArray>this.entityForm.get('associatedDevices.devices');
        this.associatedDevices.forEach(x => {
            control.push(this.patchValues(x.DeviceName, x.Value, x.DeviceId))
        })
    }

    get resources() { return <FormArray>this.entityForm.get('associatedResourceGroups.groups'); }

    get devices() { return <FormArray>this.entityForm.get('associatedDevices.devices'); }

    patchValues(name, value, id) {
        if (value === "unknown") {
            value = "";
        }

        return this.fb.group({
            'name': [name],
            'value': new FormControl({value:value, disabled:true}, [Validators.required]),
            'photoValue': ['', Validators.required],//hack to force validation
            'id': [id],
            'changed': [false]
        });
    }

    initEntity(): any {
        this.newImage = null;
        return {
            AssociatedDevices: [],
            AssociatedRG: [],
            Email: "",
            Enabled: true,
            EndDate: "",
            FirstName: "",
            Identity: "",
            IdentityStr: "",
            Img: "",
            LastName: "",
            MobilePhone: "",
            StartDate: "",
            UniqueID: "",
            Username: ""
        }
    }
    //TODO: remove when we have a strongly typed api
    mapToModel(data:any):EntityModel {
        //TODO: remove OPTIONAL when we have a strongly typed api
        return {
            AssociatedDevices: [],
            AssociatedRG: [],
            Email: data.Email || "",
            Enabled: data.Enabled || true,
            EndDate: data.EndDate || "",
            FirstName: data.entityInfo?.FirstName,

            Identity: data.entityValue?.trim(),
            IdentityStr: data.entityValue?.trim(),
            Img: data.images,
            LastName: data.entityInfo?.LastName,
            MobilePhone: data.MobilePhone || "",
            StartDate: data.StartDate || "",
            Username: data.Username || "",
            DynamicAttributesJsonString: data && data.description ? data.description.extra_data : null,
        };
    }
    initFormData(entity) {
        this.resourceGroups = entity.AssociatedRG ? entity.AssociatedRG : [];
        if (!entity.AssociatedDevices || _.isEmpty(entity.AssociatedDevices)) {
            this.setDefaultAssociatedDevice();
        } else {
            this.associatedDevices = entity.AssociatedDevices;
        }
        let entityImage=entity.Img ? entity.Img : "/assets/images/unknown_user.png"
        this.imgSrc = this.sanitizer.bypassSecurityTrustResourceUrl(entityImage);



        if (entity.Img && !entity.Identity) {
            this.newImgFromIdTag = entity.Img;
        }


        this.entityForm = this.fb.group({
            'image': [entity.Img],
            'firstName': [entity.FirstName, Validators.required],
            'lastName': [entity.LastName, Validators.required],
            'username': [entity.Username],
            'id': [entity.Identity ? entity.Identity.trim() : ''],
            'phone': [entity.MobilePhone],
            'email': [entity.Email],
            'associatedDevices': this.fb.group({
                'devices': this.fb.array([])
            }),
            'associatedResourceGroups': this.fb.group({
                'groups': this.fb.array([])
            })
        });

        if (entity.StartDate) {
            this.startDate = new Date(entity.StartDate);
            this.startDate.setHours(0, 0, 0, 0);
        } else {
            this.startDate = new Date();
            this.startDate.setHours(0, 0, 0, 0);
        }

        if (entity.EndDate) {
            this.expirationDate = new Date(entity.EndDate);
        } else {
            this.expirationDate = new Date();
            this.expirationDate.setDate(this.expirationDate.getDate() + 1);
        }

        this.buildAssociatedResourceGroups();
        this.buildAssociatedDevices();
        if (this.entityForm) {

            let formArray = <FormArray>this.entityForm.get('associatedDevices.devices');
            if(formArray.controls.length > 0){
                let formGroup = <FormGroup>formArray.controls[0];
                let formControl = <FormControl>formGroup.controls.photoValue;
                formControl.setValue(entity.Img);
            }


        }
    }

    onChange(event, index) {
        this.serverDevices.filter(device => device.endName === event)
            .forEach(device => {
                let associatedDevices = <FormArray>this.entityForm.get('associatedDevices.devices');
                let formDevice = <FormGroup> (associatedDevices).at(index);
                var isCorticaDevice = this.isCorticaDeviceID(device.endId);
                if (isCorticaDevice)
                {
                    formDevice.controls.value.disable();
                    formDevice.controls.photoValue.enable();
                }
                else
                {
                    formDevice.controls.value.enable();
                    formDevice.controls.photoValue.disable();
                }
                formDevice.patchValue({ id: device.endId});
            });
    }

    onAddAssociatedDevice() {
        (<FormArray>this.entityForm.get('associatedDevices.devices')).push(this.patchValues("", "", ""));
    }

    onRemoveDevice(i) {
        (<FormArray>this.entityForm.get('associatedDevices.devices')).removeAt(i);
    }

    onSatrtDateChange(event) {
        this.startDate = event;
        this.validateDates();
    }

    onExpirationDateChange(event) {
        this.expirationDate = event;
        this.validateDates();
    }

    validateDates() {
        return this.invalidDates = (this.startDate > this.expirationDate) ? true : false;
    }

    onToggleCheckbox(checked, i) {
        (<FormArray>this.entityForm.get('associatedResourceGroups.groups')).controls[i].value.value = checked;
        (<FormArray>this.entityForm.get('associatedResourceGroups.groups')).controls[i].value.changed = !(<FormArray>this.entityForm.get('associatedResourceGroups.groups')).controls[i].value.changed;
    }

    onUpload(event) {
        this.fileUpload.clear();
    }

    onSelectFile(event) {
      let readFileSubscription=  this.fileUpload.readFileAsDataURL(event.files[0])
            .subscribe(res => {
                this.imgSrc = res;
                this.newImage = res;
            }, err => {
                this.messageService.add({severity: 'error', summary: this.i18n.instant(ToastTypes.error), detail: this.i18n.instant('operationFailed')});
            });
        this.subscriptions.push(readFileSubscription);
    }

    onSelectPhoto(event, index: number) {
        let photoUploader = this.photoUploaders.find((uploaderComponent:FileUploaderComponent, i: number) => i == index);

       let photoUploaderSubscription= photoUploader.readFileAsDataURL(event.files[0])
            .subscribe(result => {

                let formArray = <FormArray>this.entityForm.get('associatedDevices.devices');//hack to force validation
                let formGroup = <FormGroup>formArray.controls[index];
                let formControl = <FormControl>formGroup.controls.photoValue;
                formControl.setValue(result);
            },
            err =>  {
                this.messageService.add({severity: 'error', summary: this.i18n.instant(ToastTypes.error), detail: this.i18n.instant('operationFailed')});
            }
            ,
            () =>{
                photoUploader.clear();
            }
            );
        this.subscriptions.push(photoUploaderSubscription);
    }

    onCancel() {
        this.entity = this.initEntity();
        this.associatedDevices = [];
        this.displayForm.emit();
    }

    onDelete() {
      let deleteSubscription=  this.httpClient.get(environment.apiUrl + apiMap.deleteEntity.url+this.entity.identity)
            .subscribe(res => {
                this.messageService.add({severity: 'success', summary: this.i18n.instant(ToastTypes.success), detail: this.i18n.instant('entityDeleted')});
                this.entity = this.initEntity();
                this.associatedDevices = [];
                this.displayForm.emit();
            }, err => {
                this.messageService.add({severity: 'error', summary: this.i18n.instant(ToastTypes.error), detail: this.i18n.instant('operationFailed')});
            });

        this.subscriptions.push(deleteSubscription);

    }

    setEntityBeforeSend(entity): any {

        entity.Email = this.entityForm.value.email ? this.entityForm.value.email : "";
        entity.FirstName = this.entityForm.value.firstName ? this.entityForm.value.firstName : "";
        entity.LastName = this.entityForm.value.lastName ? this.entityForm.value.lastName : "";
        entity.Username = this.entityForm.value.username ? this.entityForm.value.username : "";
        entity.UniqueID = this.entityForm.value.id ? this.entityForm.value.id : "";
        entity.EndDate = this.expirationDate.toISOString();
        entity.StartDate = this.startDate.toISOString();
        entity.MobilePhone = this.entityForm.value.phone ? this.entityForm.value.phone : "";
        entity.DynamicAttributesJsonString=this.entity.description ? JSON.parse(this.entity.description).description.extra_data : null;
        if (this.newImage) {
            entity.Img = this.newImage;
        } else if (this.newImgFromIdTag) {
            entity.Img = this.newImgFromIdTag;
        }else if(!this.newImage && !this.newImgFromIdTag)
        {

            entity.Img=this.imgSrc.changingThisBreaksApplicationSecurity[0];
        }
        entity.AssociatedDevices = [];
        this.entityForm.value.associatedDevices.devices.forEach(device => {
            let value = this.isCorticaDeviceID(device.id) ? this.stringService.base64FromDataUri(device.photoValue[0]) : device.value;
            entity.AssociatedDevices.push({
                DeviceId: device.id,
                DeviceName: device.name,
                Value: value
            });
        });
        entity.AssociatedRG = [];
        this.entityForm.value.associatedResourceGroups.groups.forEach(group => {
            if (group.value) {
                entity.AssociatedRG.push(group)
            }
        });
        return entity;
    }

    onSubmit() {
        if (this.entity.Identity) {
            this.entity = this.setEntityBeforeSend(this.entity);

        } else {
                let newEntity = {
                Identity: Guid.create().toString(),
                AssociatedDevices: [],
                AssociatedRG: [],
                Email: "",
                Enabled: true,
                EndDate: "",
                FirstName: "",
                Img: "",
                LastName: "",
                MobilePhone: "",
                StartDate: "",
                UniqueID: "",
                Username: "",
            }
            this.entity = this.setEntityBeforeSend(newEntity);
            this.entity.isNew = true;
        }

        this.messageService.add({severity: 'info', key: ToastTypes.requestPending, summary: this.i18n.instant(ToastTypes.info), detail: this.i18n.instant('uploadingEntities')});
       let addEntitySubscription= this.httpClient.post(environment.apiUrl + apiMap.addEntity.url, this.entity)
           .pipe(take(1)).subscribe(
            () => {
                this.messageService.clear(ToastTypes.requestPending);
                this.messageService.add({severity: 'success', summary: this.i18n.instant(ToastTypes.success),
                    detail: this.i18n.instant('entitySavedSuccessfully')});
                this.displayForm.emit();
                this.entity.isNew = false;

                let action = this.entity.isNew ? EntitiesActionType.addEntity : EntitiesActionType.editEntity;
                this.saveEntity.emit({ entity: this.entity, action: action });

                this.entityForm.value.associatedResourceGroups.groups.forEach(resource => {
                    if (resource.changed) {
                        let actionType = resource.value ? ApiCommands.AddResourceToGroup : ApiCommands.RemoveResourceFromGroup;
                        let  data= {
                            ResourceGroupId: resource.id,
                            ResourceId: this.entity.Identity,
                            Type: ServerTypes.Core_RES_Entity
                        }
                        this.httpClient.post(environment.apiUrl + apiMap[actionType].url, data)
                            .subscribe(res => {
                                this.messageService.clear(ToastTypes.requestPending);
                                this.messageService.add({severity: 'success', summary: this.i18n.instant(ToastTypes.success), detail: this.i18n.instant('resourceGroupAddedSuccessfully')});
                            }, err => {
                                this.messageService.clear(ToastTypes.requestPending);
                                this.messageService.add({severity: 'error', summary: this.i18n.instant(ToastTypes.error), detail: this.i18n.instant('operationFailed')});
                            });

                    }
                });
                this.entityForm.reset();
            },
            () => {
                this.messageService.clear(ToastTypes.requestPending);
                this.messageService.add({severity: 'error', summary: this.i18n.instant(ToastTypes.error), detail: this.i18n.instant('operationFailed')});
                this.entity = this.initEntity();
                this.associatedDevices = [];
            });
        this.subscriptions.push(addEntitySubscription);
    }
}
