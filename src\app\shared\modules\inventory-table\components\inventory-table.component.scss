$red: #FF0000;
$green: #00D600;
$textColor: #808381;

:host {
    display: block;
    width: 100%;
    max-width: 100%;
}
:host.p-column-filter-overlay .p-column-filter-row-items .p-column-filter-row-item.p-highlight {
    color: #ffffff;
    background: #00D600 !important;
}

.table-container {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1rem;
    width: 100%;
}

.table-title {
    font-weight: 500;
    color: #667085;
}

.column-selector {
    .p-multiselect-label {
        padding: 0.5rem;
    }
}

.entity-id {
    border: 1px solid #667085;
    padding: 5px 10px;
    border-radius: 8px;
    max-width: 180px;
    width: 100%;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    font-size: 0.8rem;
}

.action-buttons {
    display: flex;
    gap: 0.25rem;
    justify-content: flex-end;
    margin-left: auto;
}

:host ::ng-deep {
    .p-datatable {
        width: 100%;
        background: #ffffff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.12);
        border-radius: 8px;
        position: relative;
        z-index: 1;
        
        .p-datatable-wrapper {
            width: 100%;
        }

        .p-datatable-table {
            width: 100%;
            table-layout: auto;
        }

        .p-datatable-header {
            border: none;
            padding: 1rem;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        .p-datatable-thead > tr > th {
            border: none;
            border-bottom: 1px solid #e0e0e0;
            color: #667085;
            font-weight: 500;
            padding: 1rem;
            font-size: 0.875rem;

            &:first-child {
                border-top-left-radius: 8px;
            }

            &:last-child {
                border-top-right-radius: 8px;
            }

            .p-checkbox {
                width: 1.25rem;
                height: 1.25rem;

                .p-checkbox-box {
                    border-radius: 4px;
                    border: 2px solid #6c757d;

                    &.p-highlight {
                        background: $green;
                        border-color: $green;
                    }
                }
            }
        }
        
        .p-datatable-tbody > tr {          
            border: 1px solid #EAECF0;
  
            &:nth-child(even) {
                background: #F9FAFB;
            }

            &.p-highlight {
                background: #e8f5e9 !important;
                outline: none;
            }

            > td {
                padding: 0.875rem 1rem;
                font-size: 0.875rem;
                color: #667085;
                text-align: left;
                .p-checkbox {
                    width: 1.25rem;
                    height: 1.25rem;

                    .p-checkbox-box {
                        border-radius: 4px;
                        border: 2px solid #6c757d;

                        &.p-highlight {
                            background: $green;
                            border-color: $green;
                        }
                    }
                }
            }

            &:hover {
                background: #f0f7ff !important;
            }
        }
        
        .p-paginator {
            background: #ffffff;
            border: none;
            padding: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-top: 1px solid #e0e0e0;
            position: relative;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;

            .p-paginator-left-content {
                position: absolute;
                left: 1rem;
                font-size: 0.875rem;
                color: #6c757d;
            }

            .p-paginator-current {
                display: none;
            }

            .p-paginator-pages {
                display: flex;
                gap: 0.25rem;

                .p-paginator-page {
                    min-width: 2rem;
                    height: 2rem;
                    margin: 0;
                    border-radius: 4px;
                    color: #6c757d;
                    font-weight: 500;
                    border: none;
                    background: #E4ECE6;
                    font-size: 0.875rem;

                    &.p-highlight {
                        background: $green;
                        color: #ffffff;
                    }

                    &:hover:not(.p-highlight) {
                        background: $green;
                        color: #ffffff;
                    }
                }
            }

            .p-paginator-first,
            .p-paginator-prev,
            .p-paginator-next,
            .p-paginator-last {
                width: 2rem;
                height: 2rem;
                margin: 0 0.25rem;
                border-radius: 4px;
                color: $green;
                border: none;
                background: #E4ECE6;
                display: flex;
                align-items: center;
                justify-content: center;

                &:hover {
                    background: $green;
                    color: #ffffff;
                }

                &.p-disabled {
                    opacity: 0.5;
                    background: #E4ECE6;
                    color: #6c757d;
                }
            }
        }
    }

    // Style the buttons in action columns
    app-default-actions,
    app-light-actions,
    app-channel-actions,
    app-map-actions,
    app-reset-resource,
    app-iocommands,
    app-ioligntinfo,
    app-light-power-action,
    app-check-weather,
    app-check-traffic,
    app-power-meter-actions {
        display: inline-block;
        margin-right: 0.25rem;

        .p-button.p-button-icon-only, 
        button.p-button.p-button-text,
        .p-button {
            width: 2rem;
            height: 2rem;
            border-radius: 4px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #E4ECE6;
            border: none;
            margin: 0;
            color: $green;
            border: 1px solid #D9D9D9;

            &:hover {
                background: $green;
                color: #ffffff;
            }

            &:focus {
                box-shadow: none;
            }

            .fa, .pi, i {
                font-size: 1rem;
            }
        }
    }

    .buttonGroupLightAction {
        display: inline-flex;
        gap: 0.25rem;
    }

    // Fix column multiselect
    .p-multiselect.column-selector {
        min-width: 12rem;
        
        .p-multiselect-trigger {
            color: $green;
        }

        &.p-multiselect-open {
            border-color: $green;
        }
    }

    p-columnfilter {
        input {
            width: 100%;
            padding: 0.5rem;
            border-radius: 4px;
            border: 1px solid #ced4da;
            font-size: 0.875rem;
            
            &:focus {
                outline: none;
                border-color: $green;
                box-shadow: 0 0 0 0.2rem rgba(0, 214, 0, 0.25);
            }
        }
    }
}

.inventory-container {
    display: grid;
    transition: all 0.3s ease;
    height: 100%;
    width: 100%;
    
    &.full {
        grid-template-columns: 1fr;
    }
    
    &.divided {
        grid-template-columns: 7fr 3fr;
        column-gap: 20px;
    }
    
    app-inventory-details {
        height: 100%;
        overflow-y: auto;
        background-color: #ffffff;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.12);
        animation: slideIn 0.3s ease;
    }
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
