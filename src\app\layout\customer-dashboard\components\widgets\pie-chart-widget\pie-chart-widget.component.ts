import {<PERSON>mpo<PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,ViewChild} from '@angular/core';
import {LangChangeEvent,TranslateService} from '@ngx-translate/core';
import {PieChartData} from 'app/layout/customer-dashboard/enums/pie-chart-data.enum';
import {WidgetSize} from 'app/layout/customer-dashboard/enums/widget-size.enum';
import {PieChartSiteReadinessData} from 'app/layout/customer-dashboard/models/pie-chart-site-readiness-data';
import {PieChartWidget} from 'app/layout/customer-dashboard/models/pie-chart-widget';
import {ReportsQueryParams} from 'app/layout/customer-dashboard/models/reports-query-params.interface';
import {DashboardUtilsService} from 'app/layout/customer-dashboard/services/dashboard-utils.service';
import {Guid} from 'app/shared/enum/guid';
import {ReportCollectOption} from 'app/shared/modules/data-layer/enum/reports/report-collect-options.enum';
import {DeviceStatus} from 'app/shared/modules/data-layer/models/device-status';
import {Procedure} from 'app/shared/modules/data-layer/models/procedure';
import {Resource} from 'app/shared/modules/data-layer/models/resource';
import {ResourceGroup} from 'app/shared/modules/data-layer/models/resource-group';
import {ResourceTriggerGroup} from 'app/shared/modules/data-layer/models/resource-trigger-group';
import {TriggerGroup} from 'app/shared/modules/data-layer/models/trigger-group';
import {DeviceStatusService} from "app/shared/modules/data-layer/services/device-status/device-status.service";
import {ProcedureServicesNew} from "app/shared/modules/data-layer/services/procedure/procedure-service-new";
import {ResourceGroupService} from 'app/shared/modules/data-layer/services/resource-group/resource-group.service';
import {
    ResourceTriggersService
} from 'app/shared/modules/data-layer/services/resource-triggers/resource-triggers.service';
import {ResourceCacheService} from "app/shared/modules/data-layer/services/resource/resource.cache.service";
import {TriggerService} from "app/shared/modules/data-layer/services/trigger-group/TriggerService";
import {Chart} from 'chart.js';
import * as moment from 'moment';
import {forkJoin,Subscription} from 'rxjs';
import {take} from 'rxjs/operators';
import {ResourceService} from "app/services/resource/resource.service";
import {ResourceState} from "app/shared/modules/data-layer/enum/resource/resource-state.enum";
import {DateRangeSearchType,GuidUtils} from "app/shared/enum/enum";

@Component({
  selector: 'app-pie-chart-widget',
  templateUrl: './pie-chart-widget.component.html',
  styleUrls: ['./pie-chart-widget.component.scss']
})
export class PieChartWidgetComponent implements OnInit, OnDestroy {

  public data: {index: number, widgetData: PieChartWidget};
  private subscriptions: Subscription[] = [];
  public pieChartLabels = [];
  public pieChartData: number[] = [];
  public pieChartType = 'pie';
  public pieChartLegend: boolean = true;
  public pieChartPlugins = [];
  private chartElement: any;
  private aspectRatio: number;

  public pieChartOptions= {
    responsive: true,
    legend: {
      position: 'top',
      labels: {
        boxWidth: 10,
        usePointStyle: true,
        fontSize: 9
      }
    },
    aspectRatio: 0.48,
    layout: {
          padding: {
              left: 0,
              right: 0,
              top: 0,
              bottom: 0
          }
      },
    plugins: {
      datalabels: {
        formatter: (value, ctx) => {
          const label = ctx.chart.data.labels[ctx.dataIndex];
          return label;
        },
      },
    },
    onResize: (chartElement:Chart, size) => {
      this.chartElement = chartElement;
      this.aspectRatio = parseFloat(((this.widgetWrapper.nativeElement.offsetWidth + 30) / this.widgetWrapper.nativeElement.offsetHeight).toFixed(2));
      this.chartElement.aspectRatio = this.aspectRatio;
    }
  };

  //colors taken from design templates
  public pieChartColors = [{backgroundColor: ['#54D8FF', '#5EE2A0', '#FF8474', '#FF7CC3', '#A3A1FB', '#FF5174']}];

  private resourcesModelStore: [{ [id: string] : ResourceGroup }, {[id: string] : DeviceStatus}];
  private procedureModelStore: { [id: string] : Procedure };
  private triggerModelStore: { [id: string] : TriggerGroup };
  private userResourceTriggerModelStore: {[id: string] : ResourceTriggerGroup};
  private userResourceTrigger: ResourceTriggerGroup;

  public resourceGroupArray: ResourceGroup[];
  public resourceArray: Resource[];
  public procedureArray: Procedure[];
  public triggerGroup: TriggerGroup;

  public dataNotSet: boolean = true;
  @ViewChild('widgetWrapper', {static: false}) widgetWrapper: ElementRef;

  constructor(
    private resourceService: ResourceService,
    private dashboardUtilsService: DashboardUtilsService,
    private resourceGroupService: ResourceGroupService,
    private translateService: TranslateService,
    private resourceTriggersService: ResourceTriggersService,
    private triggerService: TriggerService,
    private procedureServiceNew: ProcedureServicesNew,
    private deviceStatusService:DeviceStatusService,
    private resourceCacheService: ResourceCacheService
  ) { }

  ngOnInit() {

    this.data.widgetData = new PieChartWidget(this.data.widgetData);

    let langChangeSubscription = this.translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.getData(true);
    })
    this.subscriptions.push(langChangeSubscription);


    this.getData();
    this.drawPieChart(this.data.widgetData.size);

  }

  ngOnDestroy() {
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
  }

  drawPieChart(widgetSize: WidgetSize){
    switch(widgetSize){
      case WidgetSize.medium:
      case WidgetSize.big:
        this.setPieChartOptions(2.47, 'left');
        break;
      case WidgetSize.tall: {
        this.setPieChartOptions(0.95, 'top');
        break;
      }
    }
  }

  setPieChartOptions(aspectRation: number, legendPosition: string){
    //TODO
    //BUG unable to resize & change position of chart element through change of options. updating the module is not an option (angular 7 dependency)

    this.pieChartOptions.aspectRatio = aspectRation;
    this.pieChartOptions.legend.position = legendPosition;

    if(this.chartElement){
      this.chartElement.aspectRatio = aspectRation;
      this.chartElement.options.legend.position = legendPosition;
    }
  }

  getData(onlyRedrawPieChart?: boolean): void{
    switch(this.data.widgetData.pieChartDataType){
      case PieChartData.siteReadiness: {
        this.dataNotSet = true;
        !onlyRedrawPieChart ? this.getResourceGroupsWithStatus() : this.buildSiteReadinessData(this.resourceArray);
        break;
      }
      case PieChartData.tasks:
      case PieChartData.tasksUrgency:
        this.dataNotSet = true;
        !onlyRedrawPieChart ? this.getProcedures() : this.buildProcedureData(this.procedureArray);
        break;
      case PieChartData.triggers:
        this.dataNotSet = true;
        !onlyRedrawPieChart ? this.getTriggers() : this.buildTriggerData(this.triggerGroup, this.userResourceTrigger);
        break;
      default : {
        this.dataNotSet = true;
        break;
      }
    }
  }

  getProcedures(){

   let getProceduresSubscription= this.procedureServiceNew.getAll().subscribe(res => {
       this.procedureModelStore = res;
      this.procedureArray = this.buildProcedureDataArray(res);
      this.buildProcedureData(this.procedureArray);
    })
    this.subscriptions.push(getProceduresSubscription);
  }

  getResourceGroupsWithStatus(){
    let resourceGroupSubscription = this.resourceGroupService.getAll().pipe(take(1))
    let deviceStatusSubscription = this.deviceStatusService.getAll().pipe(take(1));

      let resourcesSubscription = this.resourceCacheService.storageCompleted.subscribe(() =>{
          const resourcesArray: Resource[] = this.resourceCacheService.getAll();
          let forkJoinSubscription = forkJoin([
              resourceGroupSubscription,
              deviceStatusSubscription
          ]).subscribe(([resourceGroups,deviceStatuses])=>{
              let deviceStatusMap = deviceStatuses.reduce((accumulator,currentDeviceStatus)=>{
                  accumulator[currentDeviceStatus.identity] = currentDeviceStatus;
                  return accumulator;
              },{});

              this.resourcesModelStore = [resourceGroups,deviceStatusMap];

              this.resourceGroupArray = this.buildDataArray(this.resourcesModelStore,resourcesArray);
              this.resourceArray = this.filterResources(this.resourceGroupArray);
              this.buildSiteReadinessData(this.resourceArray);
          });

          this.subscriptions.push(forkJoinSubscription);
      });
      this.subscriptions.push(resourcesSubscription);
  }

  getTriggers(){
    let currentDate = moment(this.data.widgetData.currentDate);
    let fromDate = moment(this.data.widgetData.fromDate);
    let toDate = moment(this.data.widgetData.toDate);

    switch(this.data.widgetData.selectedDateRangeType){
      case DateRangeSearchType.Date:
        fromDate = moment(currentDate).startOf('day');
        toDate = moment(currentDate).endOf('day');
        break;
      case DateRangeSearchType.Last:
        fromDate = moment();
        toDate = moment();
        break;
      case DateRangeSearchType.Range:
        //TODO
        //check date validation
        break;
    }

    let queryparams: ReportsQueryParams = {
      DateLastUnit: this.data.widgetData.selectedDateUnitLast,
      DateRangeType: this.data.widgetData.selectedDateRangeType.toString(),
      From: fromDate.toISOString(),
      To: toDate.toISOString(),
      Identity: GuidUtils.newGuid(),
      Name: "",
      Resource: Guid.EMPTY,
      ResourceGroup: this.data.widgetData.selectedGroupId,
      TextFilter: "",
      Transformation: "CORTICA_COUNT",
      TriggerTypes: this.data.widgetData.selectedTriggers,
      Type: "1", //only triggers
      Value: this.data.widgetData.unitLastValue,
      selectedReport: "addNew",
      CollectOption: ReportCollectOption.Collect
    };




   let getTriggersSubscription= forkJoin([
      this.triggerService.getAll(queryparams).pipe(take(1)),
      this.resourceTriggersService.getAll().pipe(take(1))
    ]).subscribe(res => {
      this.triggerModelStore = res[0];
      this.triggerGroup = res[0][Guid.EMPTY];
      this.userResourceTriggerModelStore = res[1];
      this.userResourceTrigger = res[1][Guid.EMPTY];
      this.buildTriggerData(this.triggerGroup, this.userResourceTrigger);
    })
    this.subscriptions.push(getTriggersSubscription);
  }

  buildProcedureDataArray(data: {[id: string] : Procedure}): Procedure[]{
    let dataArray: Procedure[] = [];
    for(let i in data){
      dataArray.push(data[i])
    }
    return dataArray
  }

  buildDataArray(data: [{[id: string] : ResourceGroup}, {[id: string] : DeviceStatus}], allResources: Resource[]): ResourceGroup[]{
    let dataArray: ResourceGroup[] = [];

    for(let i in data[0]){
      if(this.data.widgetData.selectedGroupId !== Guid.EMPTY){
        if(this.data.widgetData.selectedGroupId === data[0][i].identity){
          dataArray.push(data[0][i]);
        }
      }
      else {
        dataArray.push(data[0][i]);
      }
    }

    let resourcesMap :  Map<string, Resource>= new  Map<string, Resource>();
    allResources.forEach((resource) => resourcesMap.set(resource.identity, resource));

    //set device status
    dataArray.map(element => {
      element.resources.map(resource => {

        let resourceInMap = resourcesMap.get(resource.identity);
        resource.status = resourceInMap.status;

      })
    });

    return dataArray;
  }

  filterResources(data: ResourceGroup[]): Resource[] {
    let filteredData: Resource[] = [];
    data.forEach(group => {

      group.resources.forEach(resource => {

        resource.URI.forEach(triplet => {

          let index = filteredData.findIndex(element => element.identity === resource.identity);
          if(index === -1 && this.dashboardUtilsService.includesSiteReadinessTypes(triplet.Type)){
            filteredData.push(resource)
          }
        })
      })
    });
    return filteredData;
  }

  buildSiteReadinessData(resources: Resource[]): void {
    let data: PieChartSiteReadinessData = {
      online: 0,
      offline: 0,
      on: 0,
      off: 0,
      unknown: 0,
      free: 0,
      occupied: 0,
      alarmed: 0,
      error: 0,
      saved: 0,
      offByRadio: 0,
      dim: 0,
      offByPower: 0,
      tempError: 0,
      notSpecified: 0,
      withoutCommunication: 0
    };
    resources.forEach(resource => {

      if(resource){
        switch(resource.status){
          case 'online':
            data.online++;
            break;
          case 'offline':
            data.offline++;
            break;
          case 'off':
            data.off++;
            break;
          case 'on':
            data.on++;
            break;
          case 'unknown':
            data.offline++;
            break;
          case 'free':
            data.free++;
            break;
          case 'occupied':
            data.occupied++;
            break;
          case 'alarmed':
            data.alarmed++
            break;
          case 'error':
            data.error++
            break;
          case 'saved':
            data.saved++
            break;
          case 'offByRadio':
            data.offByRadio++
            break;
          case 'dim':
            data.dim++
            break;
          case 'offByPower':
            data.offByPower++
            break;
          case 'tempError':
            data.tempError++
            break;
          case 'notSpecified':
            data.notSpecified++
            break;
          case 'withoutCommunication':
            data.withoutCommunication++
            break;
          }
      }
    })
    this.pieChartLabels.length = 0;
    this.pieChartData.length = 0;
    for(let i in data){
      if(data[i] !== undefined && data[i] > 0) {
        if(this.data.widgetData.resourceStates && this.data.widgetData.resourceStates.length === 0){
          this.pieChartLabels.push(this.translateService.instant('customerDashboard.pieChartLabels.'+i.toLowerCase()));
          this.pieChartData.push(data[i]);
        }
        else {
          let index = this.data.widgetData.resourceStates.findIndex(resource => resource === i);
          if(index !== -1){
            this.pieChartLabels.push(this.translateService.instant('customerDashboard.pieChartLabels.'+i.toLowerCase()));
            this.pieChartData.push(data[i]);
          }
        }
      }
    }

    this.checkPieChartData();
  }

  buildProcedureData(procedures: Procedure[]){
    let procedure: Procedure;
    switch(this.data.widgetData.pieChartDataType){
      case PieChartData.tasks:
        procedure = procedures[0];
        break;
      case PieChartData.tasksUrgency:
        procedure = procedures[1];
        break;
    }

    this.pieChartLabels.length = 0;
    this.pieChartData.length = 0;

    for(let i in procedure){
      if(procedure[i] !== undefined && procedure[i] > 0){
        this.pieChartLabels.push(this.translateService.instant('customerDashboard.pieChartLabels.'+i.toLowerCase()));
        this.pieChartData.push(procedure[i])
      }
    }

    this.checkPieChartData();

  }

  buildTriggerData(triggerGroup: TriggerGroup, userTriggers: ResourceTriggerGroup): void{
    let triggerCount: {triggerType : number, triggerCount: number, triggerName: string }[] = [];

    this.pieChartLabels.length = 0;
    this.pieChartData.length = 0;

    triggerGroup.Triggers.forEach(element => {
      let index = triggerCount.findIndex(el => el.triggerType === element.trigger_type);
      if(index === -1){
        let index = userTriggers.Triggers.findIndex(el => el.SurpriseCode === element.trigger_type)
        let triggerName = index > -1 ? userTriggers.Triggers[index].Name : 'unknownTrigger';
        triggerCount.push({triggerType: element.trigger_type, triggerCount: 1, triggerName: triggerName})
      }
      else{
        triggerCount[index].triggerCount++
      }
    })

    triggerCount.forEach(element => {
      this.pieChartLabels.push(this.translateService.instant(element.triggerName));
      this.pieChartData.push(element.triggerCount);
    })

    this.checkPieChartData();
  }

  checkPieChartData(): void{
    if(this.pieChartLabels.length === 0 || this.pieChartData.length == 0){
      this.pieChartLabels.push(this.translateService.instant('customerDashboard.dataIsZero'));
      this.pieChartData.push(1);
    }
    setTimeout(_ => { this.dataNotSet = false}, 50);
  }

}
