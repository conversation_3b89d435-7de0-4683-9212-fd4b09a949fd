<div class="layout-container"
    pDroppable="dashboardWidgetDrag"
    (onDrop)="onDropAddWidget()"
    #layoutContainer>
  <h1 *ngIf="widgets?.length === 0">{{ 'customerDashboard.noWidgetsAvailable' | translate }}</h1>
  <div class="widgetWrapper" #widgetWrapper>
    <div 
      *ngFor="let widget of widgets; let i = index"
      class="widget {{widget.type}}" 
      [ngClass]="[widget.gridColumnSize ? 'grid-column-'+widget.gridColumnSize : '', 
      selectedWidget && widget.id === selectedWidget.id || editMode && widget.type === 'emptySpace' ? 'edit' : '']"
      id="{{'widget' + i}}"
      (dragstart)="onDragStart($event, i)"
      (dragover)="onDragOver($event, i)" 
      (dragend)="onDragEnd($event)" 
      #widgetElement>
          
      <app-generic-widget [widgetData]="widget" [dashboardEditMode]="editMode" [index]="i"></app-generic-widget>
          
    </div>
  </div>          
</div>  