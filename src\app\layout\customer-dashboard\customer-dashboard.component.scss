@import '~styles/app';

:host {
    ::ng-deep {
        #results-div {
            margin-top: 0;
        }
        .input-element {
            width: 100%;
        }
        .sidebar-div {
            padding: 0;
        }
        .layout-container{
            overflow-y: scroll !important;
        }
        h1, h2 {
            font-size: 1.375rem;
        }
        .form-item {
            position: relative;
            &.multiple {
                display: flex;
                justify-content: space-between;
                flex-flow: wrap;

                h2 {
                    width: 100%;
                }
            }
            margin: 20px 0;
        }

        .sidebar-actions {
            margin-top: auto;
            margin-right: 25px;
            display: flex;
            justify-content: flex-end;
            button {
                margin-left: 10px;
            }
        }
        .dynamic-edit-component {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .widget-icon {
            font-family: 'FontAwesome';
            font-size: 1rem;
            &.dashbox, &.player, &.map, &.gauge, &.notification,  &.pieChart, &.lineChart, &.chart, &.sensorStatus, &.emptySpace, &.embeddedFile , &.urlShortcut {
                &::before {
                    display: block;
                }
            }
            &.dashbox {
                &::before {
                    content: "\f096";
                }
            }
            &.player {
                &::before {
                    content: "\f03d";
                }
            }
            &.map {
                &::before {
                    content: "\f279";
                }
            }
            &.gauge {
                &::before {
                    content: "\f0e4";
                }
            }
            &.notification {
                &::before {
                    content: "\f0f3";
                }
            }
            &.pieChart {
                &::before {
                    content: "\f200";
                }
            }
            &.lineChart, &.chart {
                &::before {
                    content: "\f201";
                }
            }
            &.sensorStatus {
                font-family: 'icomoon';
                &::before {
                    content: "\e912";
                }
            }
            &.emptySpace {
                 &::before {
                    content: "\f00a";
                }
            }
            &.embeddedFile {
                &::before {
                   content: "\f016";
               }
           }
           &.urlShortcut {
            &::before {
               content: "\f13d";
           }
       }
        }


        .form-item {
            .btn, .btn-secondary {
                color: var(--primary-1);
                line-height: 1.75rem;
                &:focus {
                    box-shadow: none;
                }
            }
            .groups-active {
                background: var(--secondary-1);
                border-bottom: none !important;
                border-bottom-left-radius: 0;
                border-bottom-right-radius: 0;
            }
        }
        .ui-calendar {
            &.full-width {
                min-width: 100%;
            }
            > .ui-inputtext {
                padding: 12px 20px;
                width: 100%;
            }
        }

        .input-error {
            position: absolute;
            bottom: -18px;
            left: 0;
            color: $widgetRed;
            font-size: 0.8rem;
        }

        input::placeholder {
            opacity: 0.5;
        }
    }

    .dashboard-priority {
        position: absolute;
        z-index: 0;
        top: 0;
        left: 0;
        height: 100px;
        width: 100%;

        &.low {
            background: var(--secondary-highlight-8);
            background: linear-gradient(180deg, rgba(var(--secondary-highlight-8), 0.33) 0%, rgba(255,255,255,0) 100%);
        }
        &.normal {
            background: var(--secondary-highlight-9);
            background: linear-gradient(180deg, rgba(var(--secondary-highlight-9), 0.28) 0%, rgba(255,255,255,0) 100%);
        }
        &.important {
            background: var(--secondary-highlight-10);
            background: linear-gradient(180deg, rgba(var(--secondary-highlight-10), 0.65) 0%, rgba(255,255,255,0) 100%);
        }
        &.urgent {
            background: var(--secondary-highlight-11);
            background: linear-gradient(180deg, rgba(var(--secondary-highlight-11), 0.59) 0%, rgba(255,255,255,0) 100%);
        }
    }
}

.dashboard-content {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 40px;
    @include get-theme-content-component();

    .dashboard-actions-wrapper {
        position: relative;
        display: flex;
        justify-content: space-between;
        flex-direction: row;
        width: 100%;
        align-items: center;
        border-bottom: 1px solid var(--secondary-highlight-5);
        margin-bottom: 30px;
        flex-wrap: wrap;
        z-index: 1;
    }
}

.side-container {
    ::ng-deep {
        h2 {
            font-size: 1.375rem;
            color: var(--secondary-3);
            margin-bottom: 10px;
        }
        .dynamic-edit-component {
            overflow-y: scroll;
            padding: 25px;
            height: calc(100vh - 70px);
        }
    }
}

