<div class="dashboard-rectangle-16">
    <p-tabView class="notification-tab">
      <p-tabPanel header="{{ 'warnings' | translate }}">
              <div class="list-header" *ngIf="notifications && notifications.length > 0">
                <div class="header-item">{{ 'type' | translate }}</div>
                <div class="header-item">{{ 'eventName' | translate }}</div>
                <div class="header-item">{{ 'TimeStamp' | translate }}</div>
              </div>
              
              <div class="scrollable-container" (scroll)="onScroll($event)">
                <ul class="notification-list" *ngIf="notifications && notifications.length > 0">
                  <li *ngFor="let notification of notifications" class="notification-item" (click)="onRowClick(notification)">
                    <div class="item-content">
                      <div class="item-cell">
                        <div class="image-inline">
                          <img [src]="notification | triggerIcon" alt="{{ notification.Types[0] }}" title="{{ notification.Types[0] }}"/>
                          <p class="entity-id">{{ notification.Types[0]  }}</p>
                        </div>
                      </div>
                      <div class="item-cell">{{ notification.Name | ellipsis: 13 }}</div>
                      <div class="item-cell">{{ notification.TimeStamp | iotTimestamp | iotTimestamp }}</div>
                    </div>
                  </li>
                </ul>
              
                <div *ngIf="!notifications || notifications.length === 0" class="no-data">
                  {{ 'alertsAlarms.noData' | translate }}
                </div>
              
                <div *ngIf="notificationLoading && notifications.length === 0" class="loading-indicator">
                  <div class="spinner"></div>
                </div>
              
                <div *ngIf="notificationLoading && notifications.length > 0" class="bottom-loading-indicator">
                  <div class="spinner"></div>
                </div>
              </div>
      </p-tabPanel>
    </p-tabView>
  </div>