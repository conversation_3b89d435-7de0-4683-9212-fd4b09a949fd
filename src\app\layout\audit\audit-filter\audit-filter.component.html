<div class="filters-container">
    <div class="filters-wrapper">
        <div class="title">
            {{ 'audit.title' | translate }}
        </div>
        <div class="filters-content">
            <!-- Message search -->
            <div class="filter-element-container">
                <div class="filter-element">
                    <i class="fa fa-search green"></i>
                    <input type="text" pInputText placeholder="{{ 'auditFilter.searchMessage' | translate }}"
                        [(ngModel)]="searchQuery" (input)="onSearch()" />
                </div>
            </div>

            <!-- User search -->
            <div class="filter-element-container">
                <div class="filter-element">
                    <i class="fa fa-user green"></i>
                    <input type="text" pInputText placeholder="{{ 'auditFilter.searchUser' | translate }}"
                        [(ngModel)]="userQuery" (input)="onSearch()" />
                </div>
            </div>

            <!-- IP search -->
            <div class="filter-element-container">
                <div class="filter-element">
                    <i class="fa fa-desktop green"></i>
                    <input type="text" pInputText placeholder="{{ 'auditFilter.searchIP' | translate }}"
                        [(ngModel)]="ipQuery" (input)="onSearch()" />
                </div>
            </div>

            <!-- Date range -->
            <div class="filter-element-container date-range">
                <div class="filter-element">
                    <i class="fa fa-calendar green"></i>
                    <p-calendar [(ngModel)]="startDate" [showTime]="true" [showSeconds]="true"
                        dateFormat="dd/mm/yy" placeholder="{{ 'auditFilter.startDate' | translate }}"
                        (onSelect)="onDateChange()"></p-calendar>
                </div>
            </div>

            <div class="filter-element-container date-range">
                <div class="filter-element">
                    <i class="fa fa-calendar green"></i>
                    <p-calendar [(ngModel)]="endDate" [showTime]="true" [showSeconds]="true"
                        dateFormat="dd/mm/yy" placeholder="{{ 'auditFilter.endDate' | translate }}"
                        (onSelect)="onDateChange()" ></p-calendar>
                </div>
            </div>

            <!-- Reset button -->
            <div class="filter-element reset-button">
                <button pButton class="p-button-rounded p-button-text"
                        [pTooltip]="'auditFilter.reset' | translate" [tooltipOptions]="tooltipOptions"
                        [disabled]="isResetDisabled" (click)="resetFilterTable()">
                    <i class="fa fa-refresh red"></i>
                </button>
            </div>

            <div class="filter-element export-buttons">
                <p-splitButton #exportBtn 
                    [label]="exportLoading ?  ( 'exporting' | translate) : ('export' | translate)" [disabled]="exportLoading"
                    [icon]="exportLoading ? 'pi pi-spin pi-spinner' : 'fa fa-download'" (onClick)="onDefaultClick($event)" [model]="items"
                    appendTo="body" >
                </p-splitButton>
            </div>
        </div>
    </div>
</div>
