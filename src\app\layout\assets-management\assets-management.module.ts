import { AssetReservationFormComponent } from './asset-reservation-form/asset-reservation-form.component';
import { AssestManagementComponent } from './assets-management.component';
import { NgModule } from '@angular/core';
import { SharedModule } from 'app/shared/modules/shared.module';
import { AssestManagementRoutingModule } from 'app/layout/assets-management/assest-management-routing.module';
import { AssetsUploader } from 'app/layout/assets-management/assets-uploader/assetsUploader.component';
import { EntityFormComponent } from '../entities/entity-form/entity-form.component';

@NgModule({
    imports: [
        SharedModule,
        AssestManagementRoutingModule
    ],
    declarations: [
        AssestManagementComponent,
        AssetsUploader,
        AssetReservationFormComponent,
       
    ],
    entryComponents: [AssestManagementComponent]
})
export class AssetsManagementModule { }