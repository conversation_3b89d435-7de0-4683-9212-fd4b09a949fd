<app-cym-sidebar [opened]="false" [dockedSize]="'0px'"  [modeNum]="'push'" inputClass="second-sidebar">

    <ng-container side>
        <app-add-device #addDevice *ngIf="showAddResource" class="second-sidebar-format"></app-add-device>
       <app-add-inventory #addInventory [resourceId]="resourceId" class="second-sidebar-format" *ngIf="!resourceManagerIsActive && !showAddResource"></app-add-inventory> 
        <app-add-resource-group #addResources class="second-sidebar-format" *ngIf="resourceManagerIsActive !showAddResource"></app-add-resource-group>
    </ng-container>

    <div class="page-format dashboard-content" content>
        <div class="container-fluid">
            <div class="row border-bottom">
                <div class="col inline-headers align-self-center">
                    <ul class="list-inline heading-list list-format">
                        <li class="list-inline-item li-heading">
                            <a class="pointer" href="javascript:void(0)" [ngClass]="!resourceManagerIsActive ? 'active' : ''  ">
                                {{'inventory.inventory' | translate}}
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="col structure-floating align-self-center">
                    <ul class="list-inline icon-list list-format">
                        <li class="list-inline-item">
                            <button class="btn btn-link" [disabled]="!isFilterActive"
                            [ngClass]="isFilterActive ? 'active' : 'disabled' " 
                             (click)="resetTable()" pTooltip="{{ 'resetTable' | translate }}"
                                tooltipPosition="bottom">
                                <i class="fa fa-repeat"></i>
                            </button>
                       </li> 
                       <li class="list-inline-item">
                            <button class="btn btn-link" (click)="addDevice()" pTooltip="{{ 'inventory.addDevice' | translate }}"
                                tooltipPosition="bottom">
                                <i class="fa fa-plus"></i>
                            </button>
                        </li> 
                        <li class="list-inline-item">
                            <button class="btn btn-link" (click)="uploadCSV()" pTooltip="{{ 'importCSV' | translate }}"
                                tooltipPosition="bottom">
                                <i class="fa fa-upload"></i>
                            </button>
                        </li>
                        <li class="list-inline-item">
                            <button class="btn btn-link" (click)="exportToCSV()"
                                pTooltip="{{ 'exportToCSV' | translate }}" tooltipPosition="bottom">
                                <i class="fa fa-download"></i>
                            </button>
                        </li>
                        <li class="list-inline-item" *ngIf="selectedItems && selectedItems.length"
                            pTooltip="{{ 'delete' | translate }}" tooltipPosition="bottom" (click)="delete()">
                            <button class="btn btn-link">
                                <i class="fa fa-trash"></i>
                            </button>
                        </li>
                        <li class="list-inline-item">
                            <button class="btn btn-link" (click)="overlayActions.toggle($event)">
                                <i class="fa fa-ellipsis-v"></i>
                            </button>
                        </li>
                       
                        <p-overlayPanel appendTo="body" #overlayActions>
                            <ul>
                                <li *ngFor="let action of dropDownActions">
                                    <a (click)="downloadFileLayout(action.type)"> {{ action.type | translate}} </a>
                                </li>
                                <li >
                                    <a (click)="openLumenFactorModal()">
                                        {{( 'editFactors' | translate )}}
                                    </a>
                                </li>
                                <li >
                                    <a (click)="openResourceGroupSelector()">
                                        {{ ('enableDisableGroups' | translate)}} 
                                    </a>
                                </li>
                            </ul>
                        </p-overlayPanel>
                    </ul>
                </div>
            </div>

            <div class="row content-router">
                <inventory-table #inventory *ngIf="!resourceManagerIsActive" [resourceModelStore]="resourceModelStore" [filterProperties]="filterProperties" 
                    (filterEvent)="onFilterEvent($event)" (selectedItem)="onSelectedItem($event)" (selectedElements)="onSelectedElements($event)">
                </inventory-table>
            </div>
        </div>
    </div>
</app-cym-sidebar>

<app-entity-uploader #entityUploader></app-entity-uploader>
<app-lights-uploader #lightsUploader></app-lights-uploader>

<p-confirmDialog appendTo="body" acceptButtonStyleClass="btn-primary" rejectButtonStyleClass="btn-secondary"></p-confirmDialog>
<app-cym-uploader #cymUploader></app-cym-uploader>




  