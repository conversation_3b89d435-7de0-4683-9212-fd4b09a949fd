import { Widget } from "./widget";
import { DashboardWidget } from "./dashboard-widget.interface";
import { WidgetSize } from "../enums/widget-size.enum";
import { PieChartData } from "../enums/pie-chart-data.enum";
import { Guid } from "app/shared/enum/guid";
import { ResourceState } from 'app/shared/modules/data-layer/enum/resource/resource-state.enum';

export class PieChartWidget extends Widget {
    selectedGroupId: string;
    pieChartDataType: PieChartData;
    resourceStates: ResourceState[];
    selectedTriggers: number[];
    selectedDateRangeType: number;
    selectedDateUnitLast: number;
    fromDate: Date;
    toDate: Date;
    currentDate: Date;
    unitLastValue: number;
    
    
    constructor(obj?: DashboardWidget){
        super(obj);
        if(!this.selectedGroupId){
            this.selectedGroupId = Guid.EMPTY;
        }
        if(!this.resourceStates){
            this.resourceStates = [];
        }
        if(!this.selectedTriggers){
            this.selectedTriggers = [];
        }
        if(!this.unitLastValue){
            this.unitLastValue = 10;
        }
        if(!this.selectedDateRangeType && this.selectedDateRangeType !== 0){
            this.selectedDateRangeType = 2;
        }
        
    }

    getWidgetSize(): WidgetSize[]{
        return [WidgetSize.medium, WidgetSize.big, WidgetSize.tall]
    }
}