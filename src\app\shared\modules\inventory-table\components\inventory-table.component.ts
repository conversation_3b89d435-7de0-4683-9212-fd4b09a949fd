import {
    ChangeDetector<PERSON><PERSON>,
    Component,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    Output,
    SimpleChanges,
    TemplateRef,
    ViewChild
} from "@angular/core";
import { TranslateService } from "@ngx-translate/core";
import { CymsidebarService } from "app/shared/components/cym-sidebar/cym-sidebar.service";
import { StatusTableCell } from "app/shared/components/ng-turbo-table/models/status-table-element";
import { TableData } from "app/shared/components/ng-turbo-table/models/table-data";
import {
    ElementTableCell,
    TableActionsComponent,
    TableCell,
    TableColumnProperties,
    TableFilterMatchMode
} from "app/shared/components/ng-turbo-table/models/table.models";
import { ResourceStatusComponent } from "app/shared/components/resource-status/resource-status.component";
import { Guid } from "app/shared/enum/guid";
import { ToastTypes } from "app/shared/enum/toast-types";
import { StatusChange } from "app/shared/models/StatusChange.model";
import { ServerTypes } from "app/shared/modules/data-layer/enum/resource/resource-server-types.enum";
import { ResourceState } from "app/shared/modules/data-layer/enum/resource/resource-state.enum";
import { Resource } from "app/shared/modules/data-layer/models/resource";
import { ExtraData } from "app/shared/modules/data-layer/models/resource-properties.interface";
import * as _ from "lodash";
import { MessageService, SelectItem } from "primeng/api";
import { Subscription } from "rxjs";
import { DataChangeType } from "../../data-layer/models/data-change-type.enum";
import { DataChangeIOType } from "../../data-layer/models/dataChangeIOType.enum";
import { ChannelActionsComponent } from "./actions/channel-actions/channel-actions.component";
import { DefaultActionsComponent } from "./actions/default-actions/default-actions.component";
import { LightActionsComponent } from "./actions/light-actions/light-actions.component";
import { MapActionsComponent } from "./actions/map-actions/map-actions.component";
import { PowerMeterActionsComponent } from "./actions/power-meter-actions/power-meter-actions.component";
import { ResetResourceComponent } from "./actions/reset-resource/reset-resource.component";
import { inventoryActiveColumns } from "./active-columns";

import { ResourceService } from "app/services/resource/resource.service";
import { AppModal } from "app/shared/components/app-modal/app-modal.component";
import { IocommandsComponent } from "app/shared/components/iocommands/iocommands.component";
import { FactorCompensationService } from "app/shared/services/factor-compensation.service";
import { LumenFactorService } from "app/shared/services/lumen-factor.service";
import { DataPropertiesComponent } from "../../cymbiot-map/components/data-properties/data-properties.component";

import { Triplet } from "app/shared/modules/data-layer/models/triplet";
import { ResourcePort } from "app/shared/modules/data-layer/services/resource/resource-port";
import { ResourceCacheService } from "app/shared/modules/data-layer/services/resource/resource.cache.service";
import { ResourceGroupService } from "app/shared/services/resourceGroup.service";
import { IOMetadata } from "../../data-layer/models/ioMetadata.model";
import { ResourceApiService } from "../../data-layer/services/resource/resource-api.service";
import { Table } from "primeng/table";
import { TablePaginatorEvent } from "../../../components/ng-turbo-table/models/table-paginator-event.interface";


@Component({
    selector: 'inventory-table',
    templateUrl: './inventory-table.component.html',
    styleUrls: ['./inventory-table.component.scss'],
    providers: [CymsidebarService],


})
export class InventoryTableComponent implements OnInit, OnDestroy, OnChanges {
    @ViewChild('actionsLight', {static: true}) actionsLight: TemplateRef<LightActionsComponent>;
    @ViewChild('actionsChannel', {static: true}) actionsChannel: TemplateRef<ChannelActionsComponent>;
    @ViewChild('actionsDefault', {static: true}) actionsDefault: TemplateRef<DefaultActionsComponent>;
    @ViewChild('actionsPowerMeter', {static: true}) actionsPowerMeter: TemplateRef<PowerMeterActionsComponent>;
    @ViewChild('actionsIOcommands', {static: true}) actionsIOcommands: TemplateRef<IocommandsComponent>;
    @ViewChild('ioLightInfo', {static: true}) ioLightInfo: TemplateRef<DataPropertiesComponent>;

    @ViewChild('resetResourceActions', {static: true}) resetResourceActions: TemplateRef<ResetResourceComponent>;
    @ViewChild('actionsMap', {static: true}) actionsMap: TemplateRef<MapActionsComponent>;
    @ViewChild('statusCell', {static: true}) statusCell: TemplateRef<ResourceStatusComponent>;
    @ViewChild('IostatusCell', {static: true}) IostatusCell: TemplateRef<ResourceStatusComponent>;
    @ViewChild('turboTable', {static: false}) turboTable: Table;
    @ViewChild('lumenFactorModal', {static: false}) lumenFactorModal: AppModal;
    @ViewChild('resourceGroupSelector', {static: false}) resourceGroupSelector: AppModal;
    @Output('selectedElements') selectedElements: EventEmitter<{ [columnField: string]: TableCell | string }[]> = new EventEmitter();
    @Output('filterEvent') filterEvent: EventEmitter<boolean> = new EventEmitter();
    @Output('selectedItem') selectedItem: EventEmitter<Guid> = new EventEmitter();
    @Input('resourceModelStore') resourceModelStore: { [id: string]: Resource } = null;
    @Input('filterProperties') filterProperties: { [key: string] : string } = null;
    @Input('showTableCaption') showTableCaption: boolean = true;
    fields: { [field: string] : boolean } = {};
    temp: { [columnField: string]: TableCell | string } = {};
    columns: TableColumnProperties[] = [];
    availableDevices: Resource[] = [];
    device: Resource = new Resource();
    data: { [columnField: string]: TableCell | string }[] = [];
    private subscriptions: Subscription[] = [];
    filteredGroups: SelectItem[] = [];
    resource:any;
    weatherFactorStatusResponse;
    dict:any;
    selectedFactorItems:any[]=[];


    loading: boolean = false;
    selectedRows: any[] = [];
    pageIndex: number = 0;


    columnSelectionOptions: any[] = [];
    selectedColumns: any[] = [];
    visibleColumns: TableColumnProperties[] = [];
    defaultVisibleColumns: string[] = ['name', 'resourceType', 'groups', 'status'];

    selectedResourceId: Guid = null;
    selectedResourceDetails: Resource = null;
    detailsLoading: boolean = false;
    editingResourceId: Guid = null;

    constructor(
        private i18n: TranslateService,
        private resourceService: ResourceService,
        private changeDetector: ChangeDetectorRef,
        private resourceGroupService: ResourceGroupService,
        private resourceApiService: ResourceApiService,
        private resourcePort: ResourcePort,
        private resourceCacheService: ResourceCacheService,
        private messageService: MessageService,private factorCompensationService:FactorCompensationService,private lumenFactorService:LumenFactorService) {


        let resourceServiceSubscription = this.resourceService.resourceChanged.subscribe((res:any) => {
            if(this.data !== null){

                this.notifyOperation(res);

            }
        })

        let ioNotificationSubscription = this.resourceApiService.resourceIOChangedSubject.subscribe((res) => {
            this.ioNotificationOperation(res);
        });
        this.subscriptions.push(resourceServiceSubscription);

        let modalSubjectSubscription= this.lumenFactorService.modalSubject.subscribe(res=>{
            if(res){
                this.lumenFactorModal.openModal();
            }else{
                this.lumenFactorModal.closeModal();
            }

        });

        this.subscriptions.push(modalSubjectSubscription);
        let lumenFactorServiceSubscriptions=  this.lumenFactorService.resourceGroupModalSubject.subscribe(res=>{
            this.resourceGroupSelector.openModal();
        });
        this.subscriptions.push(lumenFactorServiceSubscriptions);
    }


    onRowSelect(event: any): void {
        if (event && event.identity) {
            this.selectedResourceId = event.identity;
            this.editingResourceId = null;
            this.getResourceDetails(event.identity);
            this.selectedItem.next(event.identity);
        }
    }


    onSelectionChange(event: any[]): void {
        this.selectedElements.next(event);
    }


    onPageChange(event: TablePaginatorEvent): void {
        this.pageIndex = event.first / event.rows;
    }


    onColumnToggle(event: any): void {
        this.updateVisibleColumns();
    }


    updateVisibleColumns(): void {
        if (this.selectedColumns && this.selectedColumns.length) {
            this.visibleColumns = this.columns.filter(col =>
                this.selectedColumns.some(selCol => selCol.field === col.field)
            );
        } else {

            this.visibleColumns = this.columns.filter(col =>
                this.defaultVisibleColumns.includes(col.field)
            );
        }


        this.changeDetector.detectChanges();
    }


    filter(value: any, field: string, matchMode: string): void {
        if (this.turboTable) {
            this.turboTable.filter(value, field, matchMode);
        }
    }

    resetSelectedTableElements(): void {
        this.selectedRows = [];
        this.selectedElements.next([]);
    }

    resetTable(): void {
        if (this.turboTable) {
            this.turboTable.reset();
        }
    }

    ngOnInit(): void {
        this.getResourceGroups();


        this.fields = {};


        this.selectedItem.subscribe((resourceId: Guid) => {

            this.onEditResource(resourceId);
        });
    }

    checkIfRender(row?){

        if(row && row.resourceType != 'Device' && row.resourceType != 'ALPRLane'){
            return true
        }else{
            return false;
        }

    }

    ngOnChanges(changes: SimpleChanges): void{

        if(changes && changes.resourceModelStore && changes.resourceModelStore.currentValue){

            this.columns = [];
            this.fields = {};

            this.availableDevices = _.values(changes.resourceModelStore.currentValue);

            const result = this.getFlattenObject(this.availableDevices);

            this.generateColumnsTable(result.fields);
            this.data = result.items;


            this.columnSelectionOptions = this.columns
                .filter(col => col.field !== 'actions')
                .map(col => ({
                    field: col.field,
                    header: col.header
                }));


            this.selectedColumns = this.columnSelectionOptions.filter(col =>
                this.defaultVisibleColumns.includes(col.field)
            );


            this.updateVisibleColumns();


            this.changeDetector.detectChanges();

            let WeatherFactorStatusesSubscription=this.factorCompensationService.getWeatherFactorStatuses().subscribe(response=>{
                this.weatherFactorStatusResponse=response;
            });
            this.subscriptions.push(WeatherFactorStatusesSubscription);
            let trafficFactorStatusesSubscription= this.factorCompensationService.getTrafficFactorStatuses().subscribe(response=>{

                this.availableDevices = _.values(changes.resourceModelStore.currentValue);
                const result = this.getFlattenObject(this.availableDevices);


                this.data=result.items;
                response.map(item=>{
                    let index= this.data.findIndex((el => el.identity === item.id));
                    if(this.data[index]){
                        this.data[index].trafficStatus=item.enabled;
                    }
                });
                if(this.weatherFactorStatusResponse){
                    this.weatherFactorStatusResponse.map(item=>{
                        let index= this.data.findIndex((el => el.identity === item.id));
                        if(this.data[index]){
                            this.data[index].weatherStatus=item.enabled;
                        }
                    });
                }


                this.changeDetector.detectChanges();
            });
            this.subscriptions.push(trafficFactorStatusesSubscription);
        }

    }

    getResourceGroups(): void {
        let resourceGroupGetAllSubscription = this.resourceGroupService.getAll().subscribe((response) => {
            let resourceGroups = Object['values'](response);
            resourceGroups.forEach((item) => {
                this.filteredGroups.push({ label: item.name, value: item.name });
            });
        });
        this.subscriptions.push(resourceGroupGetAllSubscription);
    }

    generateColumnsTable(fields: {[field: string] : boolean}): void {
        const keys = Object.keys(fields);
        keys.map((key: string) => {
            this.columns.push(new TableColumnProperties({
                field: key,
                header: key,
                visibile: inventoryActiveColumns[key] ? inventoryActiveColumns[key].visibile : false,
                frozen: inventoryActiveColumns[key] ? inventoryActiveColumns[key].frozen : false,
                isFilterable: inventoryActiveColumns[key] ? inventoryActiveColumns[key].isFilterable : true,
                sortable: inventoryActiveColumns[key] ? inventoryActiveColumns[key].sortable : true,
                filterOptions: this.returnFilterOptions(key),
                filterMatchMode: inventoryActiveColumns[key] ? inventoryActiveColumns[key].filterMatchMode : TableFilterMatchMode.contains,
            }));
        });

    }


    returnFilterOptions(key: string): SelectItem[] {
        let filterOptions: SelectItem[] = [];
        switch (key) {
            case 'groups':
                filterOptions = this.filteredGroups;
                break;
            case 'status':
                for (let state in ResourceState) {
                    filterOptions.push({ label: state, value: state });
                }
                break;
            case 'resourceType':
                for (let item in ServerTypes) {
                    if (!isNaN(Number(item))) {
                        filterOptions.push({ label: ServerTypes[item], value: this.i18n.instant(ServerTypes[item].toString()) });
                    }
                }
                break;
            default:
                break;
        }
        return filterOptions;
    }

    getFlattenObject(data: Resource[]): TableData  {
        const results: { [columnField: string]: TableCell | string }[] = [];
        data.map((item: Resource) => {
            this.temp = {};
            this.temp.identity = item.identity;
            this.fields['identity'] = false;
            this.temp.name = item.name;
            this.fields['name'] = true;
            let groups: string[] = item.groups ? item.groups.map(group => {
                return group.name;
            }) : [];
            this.fields['groups'] = true;

            this.temp.resourceType = item.resourceType != null ? this.i18n.instant(ServerTypes[item.resourceType].toString()) : '';
            this.fields['resourceType'] = true;

            this.temp.groups = groups.join(', ');
            for (let key in item.location) {
                this.fields[key] = true;
                this.temp[key] = item.location[key] ?  item.location[key] : '';
            }
            if(item.extraData && item.extraData.length > 0){
                item.extraData.forEach((property: ExtraData) => {
                    this.temp[property.name] = property.value ? property.value : '';
                    this.fields[property.name] = true;
                });
            }
            let actionCell: TableCell;
            let actionsTemplate: TemplateRef<TableActionsComponent> = null;
            switch (item.resourceType) {
                case ServerTypes.Core_RES_Light:
                    actionsTemplate = this.actionsLight;
                    break;
                case ServerTypes.Core_RES_InputChannel:
                    actionsTemplate = this.actionsChannel;
                    break;
                case ServerTypes.Core_RES_Map:
                    actionsTemplate = this.actionsMap;
                    break;
                case ServerTypes.Core_RES_Device:
                    actionsTemplate = this.resetResourceActions;
                    break;
                case ServerTypes.Core_RES_PowerMeter:
                    actionsTemplate = this.actionsPowerMeter;
                    break;
                case ServerTypes.Core_RES_Output:
                    actionsTemplate = this.actionsIOcommands;
                    break;
                default:
                    actionsTemplate = this.actionsDefault;
                    break;
            }
            if (actionsTemplate === null) {
                actionCell = null;
            } else {
                actionCell = new ElementTableCell(actionsTemplate);
            }

            this.temp.extraData = item.extraData && (item.extraData.length > 0)  ? JSON.stringify(item.extraData) : item.metadata ? JSON.stringify(item.metadata):'';
            this.temp['actions'] = actionCell;
            this.fields['actions'] = true;
            this.temp.status = new StatusTableCell(this.statusCell, item.status);
            this.fields['status'] = true;
            if(item.metadata){
                if(item.resourceType == ServerTypes.Core_RES_Output){
                    this.setOutputFields(item);
                }else{
                    this.setInputFields(item);
                }
            }

            results.push(this.temp);
        });
        return {
            items: results,
            fields: this.fields
        };
    }

    ngOnDestroy(): void {
        this.subscriptions.forEach(subscription => { subscription.unsubscribe();});
    }
    setOutputFields(item:Resource): void{

        this.temp.ConcentratorId = item.metadata.ConcentratorId.toString();
        this.fields['ConcentratorId'] = true;

        this.fields['IO_Status'] = true;
        this.temp.IO_Status = new StatusTableCell(this.IostatusCell,item.metadata.Status  == 0  ? ResourceState.off : ResourceState.on);
        this.temp.IO_Latitude = item.metadata.Latitude.toString();

        this.fields['IO_Latitude'] = true;

        this.temp.IO_Longitude = item.metadata.Longitude.toString();
        this.fields['IO_Longitude'] = true;
        this.temp.metaData=JSON.stringify(item.metadata);

    }

    setInputFields(item:Resource):void{
        this.temp.ConcentratorId = item.metadata.ConcentratorId.toString();
        this.fields['ConcentratorId'] = true;

        this.fields['IO_Status'] = true;

        this.temp.IO_Status = new StatusTableCell(this.IostatusCell,item.metadata.Status  == 0  ? ResourceState.off : ResourceState.on);
        this.temp.IO_Latitude = item.metadata.Latitude.toString();
        this.fields['IO_Latitude'] = true;

        this.temp.IO_Longitude = item.metadata.Longitude.toString();
        this.fields['IO_Longitude'] = true;

        this.temp.Io_Analog_Value = item.metadata.IoAnalogValue;
        this.fields['Io_Analog_Value'] = true;

        this.temp.Io_Value_0_Volt = item.metadata.IoValue0Volt > 0 && item.metadata.IoValue0Volt != null ? item.metadata.IoValue0Volt.toString() : '0';
        this.fields['Io_Value_0_Volt'] = true;


        this.temp.Io_Value_10_Volt = item.metadata.IoValue10Volt > 0 && item.metadata.IoValue10Volt != null ? item.metadata.IoValue10Volt.toString() : '0';
        this.fields['Io_Value_10_Volt'] = true;
        this.temp.metaData=JSON.stringify(item.metadata);
    }
    changeStatus(res:{type: number, models: Resource[]}): void{
        res.models.forEach((resource) => {
            this.data.map(item=>{
                if(item.identity ==resource.identity){
                    if(resource.strength){
                        resource.status = ResourceState.on;
                    }

                    item.status=new StatusTableCell(this.statusCell, resource.status);
                    if (resource.name != null){
                        item.name=resource.name;
                    }
                    this.resourceService.update(resource);
                }
            })
        });
    }


    notifyOperation(operation:any): void{
        switch(operation.type){
            case DataChangeType.Delete:
                this.checkandRemoveInventoryElement(operation);
                break;
            case DataChangeType.Create:
                let flattenObject=this.getFlattenObject(operation.models);
                if(this.columns.length <= 0){
                    this.generateColumnsTable(flattenObject.fields);
                }
                let x = this.data.concat(flattenObject.items);
                this.data=x;
                this.messageService.add({ severity: 'success', summary: this.i18n.instant(ToastTypes.success), detail: this.i18n.instant('createResourceGroup') });
                break;
            case DataChangeType.Update:
                this.changeStatus(operation);
                break;
            default:
                console.error("operation type is not supported: ", operation.type);
                break;
        }
    }

    ioNotificationOperation(operation: {type: number, models: any}): void {
        switch(operation.type){
            case DataChangeIOType.RemoveInputs:
            case DataChangeIOType.RemoveOutput:
                this.checkandRemoveInventoryElement(operation);
                break;
            case DataChangeIOType.UpdateInput:
            case DataChangeIOType.UpdateOutput:
                let itemOutput ={identity:operation.models.Guid, item:JSON.parse(operation.models.Metadata)}
                this.updateIoItem(itemOutput);
                break;
            default:
                console.error("operation type is not supported: ", operation.type);
                break;
        }
    }

    updateIoItem(operation: {identity:string ,item:IOMetadata}):void {

        let tempItem;
        this.data.map((item,index)=>{
            if(item.identity == operation.identity || item.Guid==operation.identity){
                tempItem=item;
                this.data.splice(index, 1)
                this.availableDevices.splice(index,1);

            }
        })
        tempItem.IO_Latitude=operation.item.Latitude;
        tempItem.IO_Longitude=operation.item.Longitude;

        tempItem.IO_Status=new StatusTableCell(this.IostatusCell,operation.item.Status.toString());
        tempItem.ConcentratorId=operation.item.ConcentratorId;





        this.data.push(tempItem);

    }

    checkandRemoveInventoryElement(operation: {type: number, models: StatusChange[] | Resource[]}):void
    {

        if(operation.models.length > 0){
            operation.models.forEach((resource) => {
                if(resource.resourceType == ServerTypes.Core_RES_Node){
                    this.removeAllInventoryElementsForNode(resource.Guid);
                }
                if(resource !== null){
                    let resourceInCache = this.resourceCacheService.get(resource.Guid);
                    this.removeInventoryElement(resourceInCache);
                }
            });
        }

    }

    removeAllInventoryElementsForNode(nodeGuid:string):void{
        let resources=this.resourceCacheService.getAll()
        resources.map((resource:Resource)=>{
            resource.URI.map((triplet:Triplet)=>{
                if(triplet.Type == ServerTypes.Core_RES_Node && triplet.Id.toString() == nodeGuid.toString()){
                    this.removeInventoryElement(resource);
                }
            })
        })


    }
    removeInventoryElement(resourceElement:Resource): void
    {
        resourceElement.identity = resourceElement.Guid != null ? resourceElement.Guid : resourceElement.identity;
        this.data.map((item,index)=>{
            if(item.identity == resourceElement.identity){
                this.data.splice(index, 1)
                this.availableDevices.splice(index,1);
            }
        })
        this.resourceService.delete(resourceElement);
        const result = this.getFlattenObject(this.availableDevices);
        this.data=result.items;

    }
    ngAfterContentChecked(): void {
        this.changeDetector.detectChanges();
    }

    getResourceDetails(resourceId: Guid): void {
        this.detailsLoading = true;


        if (this.availableDevices && this.availableDevices.length) {
            const resource = this.availableDevices.find(r => r.identity && r.identity.toString() === resourceId.toString());
            if (resource) {
                this.selectedResourceDetails = resource;
                this.detailsLoading = false;
            }
        }
    }

    onCloseDetails(): void {
        this.selectedResourceId = null;
        this.selectedResourceDetails = null;
        this.editingResourceId = null;
    }

    onSaveResource(updatedResource: Resource): void {

        if (updatedResource === null) {
            this.editingResourceId = null;
            return;
        }

        if (updatedResource) {
            this.detailsLoading = true;
            this.resourceService.update(updatedResource).subscribe(
                () => {

                    if (this.resourceModelStore && updatedResource.identity) {
                        this.resourceModelStore[updatedResource.identity] = updatedResource;
                    }


                    this.selectedResourceDetails = updatedResource;


                    this.messageService.add({
                        severity: 'success',
                        summary: this.i18n.instant('success'),
                        detail: this.i18n.instant('resourceUpdated')
                    });


                    this.ngOnChanges({
                        resourceModelStore: {
                            currentValue: this.resourceModelStore,
                            previousValue: null,
                            firstChange: false,
                            isFirstChange: () => false
                        }
                    });


                    this.editingResourceId = null;
                    this.detailsLoading = false;
                },
                (error) => {
                    console.error('Error updating resource:', error);
                    this.messageService.add({
                        severity: 'error',
                        summary: this.i18n.instant('error'),
                        detail: this.i18n.instant('errorUpdatingResource')
                    });
                    this.detailsLoading = false;
                }
            );
        }
    }


    onEditResource(resourceId: Guid): void {
        if (resourceId) {
            this.selectedResourceId = resourceId;
            this.editingResourceId = resourceId;
            this.getResourceDetails(resourceId);
        }
    }

}
