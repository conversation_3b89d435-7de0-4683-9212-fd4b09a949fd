export interface ProceduresPageRequest {
    PageIndex: number;
    PageSize: number;
    StartTimeStamp?: string; // ISO format timestamp (optional)
    EndTimeStamp?: string;   // ISO format timestamp (optional)
    NameFilter?: string;
    StatusFilter?: number;   // If greater than 0 server uses for filtering
    EventIdFilter?: string;
    ProcedureTemplateFilter?: string;
    StepDescriptionFilter?:string;
}

export interface ProceduresPageExportRequest {
    StartTimestamp?: string; 
    EndTimestamp?: string;
    FileType?: string;
    Name?: string;
    EventId?: string;
    Status?: number;
    ProcedureTemplate?: string;
    StepDescription?:string;
}
