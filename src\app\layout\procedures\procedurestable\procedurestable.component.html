<p-panel [toggleable]="false" [collapsed]="false">
  <ng-template pTemplate="header">
    <div class="table-header">
      <div class="header-left">
        <div class="header-item">
          <span>{{'status' | translate}}</span>
          <p-dropdown [options]="statuses" [(ngModel)]="status" styleClass="status-dropdown" [placeholder]="'None'" (onChange)="onStatusChange($event.value)">
            <ng-template let-item pTemplate="selectedItem">
              <span>{{item.label| translate}}</span>
            </ng-template>
            <ng-template let-item pTemplate="item">
              <div>{{item.label | translate}}</div>
            </ng-template>
          </p-dropdown>
        </div>

          <div class="header-item">
              <span>{{'Core_RES_ProcedureTemplate' | translate}}</span>
              <p-dropdown [options]="procedureTemplates"
                         [(ngModel)]="selectedTemplateDropdown"
                         styleClass="status-dropdown"
                         [placeholder]="'None'"
                         (onChange)="onProcedureTemplateChange($event.value)"
                         optionLabel="Name">
                  <ng-template let-procedure pTemplate="selectedItem">
                      <span>{{procedure.Name}}</span>
                  </ng-template>
                  <ng-template let-procedure pTemplate="item">
                      <span>{{procedure.Name}}</span>
                  </ng-template>
              </p-dropdown>
          </div>

          <div class="header-item">
              <span>{{'procedureStep' | translate}}</span>
              <p-dropdown [options]="stepDescriptionDropDown"
                         [(ngModel)]="selectedStep"
                         styleClass="status-dropdown"
                         [placeholder]="'None'"
                         (onChange)="onProcedureStepChange($event.value)"
                         optionLabel="Question">
                  <ng-template let-procedureSteps pTemplate="selectedItem">
                      <span>{{procedureSteps.Question}}</span>
                  </ng-template>
                  <ng-template let-procedureSteps pTemplate="item">
                      <span>{{procedureSteps.Question}}</span>
                  </ng-template>
              </p-dropdown>
          </div>

          <div class="status-dropdown">

          </div>
        <div class="header-item search-item">
          <span>{{'eventId' |  translate}}</span>
          <input type="text" [(ngModel)]="eventId" (ngModelChange)="onEventIdChange($event)" placeholder="{{'eventId' | translate}}" class="search-input" />
        </div>

        <div class="header-item search-item">
          <span>{{'description' |  translate}}</span>
          <input type="text" [(ngModel)]="name" (ngModelChange)="onNameChange($event)" placeholder="{{'search' | translate}}" class="search-input" />
        </div>

        <div class="header-item">
          <span>{{'created' | translate}}</span>
        </div>

        <div class="header-item">
          <span>{{'updated' | translate}}</span>
        </div>


      </div>

      <div class="header-right">
        <input type="text" [(ngModel)]="name" (ngModelChange)="loadProceduresPage()" placeholder="{{'search' | translate}}" class="search-input" />

        <div class="export-buttons">
          <button pButton class="p-button-text export-btn"
                  [pTooltip]="'exportToCSV' | translate"
                  [disabled]="exportLoading" (click)="export(exportTypes.csv)">
              <i class="fa fa-file-excel-o" [class.fa-spin]="exportLoading && exportType === exportTypes.csv"></i>
          </button>
          <button pButton class="p-button-text export-btn"
                  [pTooltip]="'exportToPDF' | translate"
                  [disabled]="exportLoading" (click)="export(exportTypes.pdf)">
              <i class="fa fa-file-pdf-o" [class.fa-spin]="exportLoading && exportType === exportTypes.pdf"></i>
          </button>
          <button pButton class="p-button-text export-btn"
                  [pTooltip]="'exportToHTML' | translate"
                  [disabled]="exportLoading" (click)="export(exportTypes.html)">
              <i class="fa fa-html5" [class.fa-spin]="exportLoading && exportType === exportTypes.html"></i>
          </button>
        </div>
      </div>
    </div>
  </ng-template>

  <p-panel [toggleable]="true" [collapsed]="true" *ngFor="let procedure of pagedProcedures; let index = index"
    styleClass="{{getStatusClass(procedure.Status)}}" (onBeforeToggle)="onPanelBeforeToggle($event, procedure)" (onAfterToggle)="onPanelAfterToggle($event, procedure)">
    <p-header class="procedure-header">
      <div class="procedure-row">
        <div class="procedure-status">
          <!-- Status Icons -->
          <span class="status-icon">
            <img [src]="getStatusIcon(procedure.Status)" [alt]="getStatusLabel(procedure.Status)">
          </span>
          <span class="status-text">{{getStatusLabel(procedure.Status)}}</span>
          <div class="step-progress-indicator">
            <ng-container *ngIf="procedure?.StepsList && procedure?.ProcTemplate?.Steps">
              <ng-container *ngIf="procedure.StepsList.length > 0">
                <ng-container *ngFor="let step of getCompletedSteps(procedure); let i = index; let last = last">
                  <div class="step-circle"
                       [style.background-color]="getColorFromNumber(step.color)"
                       [title]="'Step ' + step.stepIndex + (step.nextIndex !== -1 ? ' → ' + step.nextIndex : ' (End)')">
                    {{step.stepIndex}}
                  </div>
                  <div *ngIf="!last"
                       class="step-connector"
                       [title]="'Step ' + step.stepIndex + ' → ' + step.nextIndex">
                    →
                  </div>
                </ng-container>
              </ng-container>
            </ng-container>
          </div>
          <span class="procedure-name">{{procedure.Name}}</span>
        </div>

        <div class="procedure-date"><b>{{'eventId' | translate}}</b>: {{procedure.Source}}</div>
        <div class="procedure-date">{{procedure.Description}}</div>

        <div class="procedure-date">{{procedure.CreatedDateTime | iotTimestamp }}</div>
        <div class="procedure-date">{{procedure.LastUpdateDateTime | iotTimestamp }}</div>
          <div class="procedure-button" (click)="navigateToGeneralDetection(procedure)">
              <i class="fa fa-bell generalDetectionButton" aria-hidden="true"></i>

<!--              {{'generalDetections.generalDetection' | translate}}-->
          </div>
      </div>
    </p-header>

    <table class="table">
      <thead>
        <tr>
          <th>{{'id'| translate}}</th>
          <th>{{'description' | translate}}</th>
          <th>{{'options' | translate}}</th>
          <th>{{'comments' | translate}}</th>
          <th class="headAllignCenter">{{'actions' | translate}}</th>
          <th>{{'commpleteStep' | translate}}</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngIf="procedure.StepsList && procedure.StepsList.length > 0">
          <tr *ngFor="let step of procedure.StepsList">
            <td>{{step.StepIndex}}</td>
            <td>{{step.Description}}</td>
            <td><input type="text" value="{{step.SelectedOptionVal}}" disabled></td>
            <td><input type="text" value="{{step.Comment}}"></td>
            <td>
              <ul class="actionList">
                <li *ngFor="let stepAction of getStepActions(procedure, step.StepIndex)"
                  [ngSwitch]='procedureActionTypes[stepAction.Type.replace(" ", "_")]'>
                  <i *ngSwitchCase=procedureActionTypes.Activate_Output
                    (click)="makeAction(stepAction,procedure.Identity,procedure.Name)" class='fa fa-bolt'></i>
                  <i *ngSwitchCase=procedureActionTypes.Send_Email
                    (click)="makeAction(stepAction,procedure.Identity,procedure.Name)" class='fa fa-envelope'></i>
                  <i *ngSwitchCase=procedureActionTypes.Open_Channel
                    (click)="makeAction(stepAction,procedure.Identity,procedure.Name)" class='fa fa-television'></i>
                  <i *ngSwitchCase=procedureActionTypes.Send_SMS
                    (click)="makeAction(stepAction,procedure.Identity,procedure.Name)" class='fa fa-commenting'></i>
                  <i *ngSwitchCase=procedureActionTypes.Open_Map
                    (click)="makeAction(stepAction,procedure.Identity,procedure.Name)" class='fa fa-map'></i>
                </li>
              </ul>
            </td>
            <td>
              <i class="fa fa-check"></i>
            </td>
          </tr>
        </ng-container>
        <tr *ngIf="getNextStepsForProcedure(procedure.Identity)">
          <td>{{getNextStepsForProcedure(procedure.Identity)?.StepIndex}}</td>
          <td>{{getNextStepsForProcedure(procedure.Identity)?.Question}}</td>
          <td>
            <select class="form-control"
                    [ngModel]="getSelectedValue(procedure.Identity)"
                    (ngModelChange)="updateSelectedValue(procedure.Identity, $event)">
              <option [ngValue]="undefined" disabled selected>{{'selectStep' | translate}}</option>
              <option *ngFor="let option of getNextStepsForProcedure(procedure.Identity)?.Options || []"
                [ngValue]="option.ProcOptionId">{{option.Value}}</option>
            </select>
          </td>
          <td>
            <input type="text"
                   [ngModel]="getStepComment(procedure.Identity)"
                   (ngModelChange)="setStepComment(procedure.Identity, $event)">
          </td>
          <td>
            <ul class="actionList">
              <li *ngFor="let stepAction of getStepActions(procedure, getNextStepsForProcedure(procedure.Identity)?.StepIndex)"
                [ngSwitch]='procedureActionTypes[stepAction.Type.replace(" ", "_")]'>
                <i *ngSwitchCase=procedureActionTypes.Activate_Output
                  (click)="makeAction(stepAction,procedure.Identity,procedure.Name)" class='fa fa-bolt'></i>
                <i *ngSwitchCase=procedureActionTypes.Send_Email
                  (click)="makeAction(stepAction,procedure.Identity,procedure.Name)" class='fa fa-envelope'></i>
                <i *ngSwitchCase=procedureActionTypes.Open_Channel
                  (click)="makeAction(stepAction,procedure.Identity,procedure.Name)" class='fa fa-television'></i>
                <i *ngSwitchCase=procedureActionTypes.Send_SMS
                  (click)="makeAction(stepAction,procedure.Identity,procedure.Name)" class='fa fa-commenting'></i>
                <i *ngSwitchCase=procedureActionTypes.Open_Map
                  (click)="makeAction(stepAction,procedure.Identity,procedure.Name)" class='fa fa-map'></i>
              </li>
            </ul>
          </td>
          <td>
            <button class="btn btn-primary"
                    [disabled]="!isValidStepOptionSelected(procedure.Identity)"
                    (click)="addStep(getNextStepsForProcedure(procedure.Identity),procedure.Identity)">
              {{'apply'| translate}}
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </p-panel>

  <div class="row mainPaginationContainer">
    <p-paginator [rows]="rowsOnPage" [totalRecords]="totalProcedures" (onPageChange)="paginate($event)" [rowsPerPageOptions]="[5,10,20,50]"></p-paginator>
  </div>
</p-panel>
