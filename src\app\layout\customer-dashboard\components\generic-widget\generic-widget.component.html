<div class="widget-wrapper" [ngClass]="{'edit-mode': dashboardEditMode}">
  <div class="widget-actions" *ngIf="!widgetData?.hideActionsMenu || dashboardEditMode">
    <button class="btn btn-link" (click)="overlayActions.toggle($event)"><i class="fa fa-ellipsis-v"></i></button>
    <p-overlayPanel appendTo="body" #overlayActions>
      <ul class="actions-menu">
        <li *ngFor="let action of dropDownActions">
          <a href="javascript:void(0)" *ngIf="action.isVisible" (click)="emitWidgetAction(action.type)">{{action.name | translate}}</a>
        </li>
      </ul>
    </p-overlayPanel>
  </div>
  <div class="info-wrapper" *ngIf="widgetData.hasInfoWrapper">
    <span class="widget-icon {{widgetData.type}}"></span>
    <h3>{{ widgetData?.title }}</h3>
  </div>  
    
  <ng-container class="widget-factory-wrapper" #widgetFactory></ng-container>
</div>
