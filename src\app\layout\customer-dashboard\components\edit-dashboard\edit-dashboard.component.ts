import { Component, OnInit, Output, EventEmitter, OnDestroy } from '@angular/core';
import { DashboardState } from '../../models/dashboard-state.interface';
import { DashboardObject } from '../../models/dashboard-object.interface';
import { Dashboard } from 'app/shared/modules/data-layer/models/dashboard';
import { DashboardWidget } from '../../models/dashboard-widget.interface';
import { availableWidgetTypesData } from '../../mock-data';
import { DashboardUtilsService } from '../../services/dashboard-utils.service';
import { Subscription, Subject, EMPTY } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';
import { SelectItem } from 'primeng/api';
import { DashboardPriority } from 'app/shared/modules/data-layer/enum/dashboard/dashboard-priority.enum';
import { Guid } from 'app/shared/enum/guid';


@Component({
  selector: 'app-edit-dashboard',
  templateUrl: './edit-dashboard.component.html',
  styleUrls: ['./edit-dashboard.component.scss']
})
export class EditDashboardComponent implements OnInit, OnDestroy {

  data : {
    state: DashboardState,
    selectedDashboard: DashboardObject,
    dashboardsTree: Dashboard[],
    parentDashboardTree: Dashboard,
    dashboardsObject: { [id: string] : Dashboard }
  }

  @Output() onAddWidget: EventEmitter<DashboardWidget> = new EventEmitter();
  @Output() dashboardAction:EventEmitter<string> = new EventEmitter();

  public availableWidgetTypes = availableWidgetTypesData;
  private subscriptions: Subscription[] = [];
  public showDashboardTree: boolean = false;

  name$: Subject<string> = new Subject<string>();
  dashboardPriorityOptions: SelectItem [] = Object.keys(DashboardPriority).map(key => {
    return {
      label: DashboardPriority[key],
      value: DashboardPriority[key]
    };
  })

  constructor(
    private dashboardUtilsService: DashboardUtilsService
  ) { }

  ngOnInit(): void {
    let selectParentTreeItemSubscription = this.dashboardUtilsService.getSelectParentDashboardTreeitem().subscribe(res => {
        this.data.parentDashboardTree = res;
        this.data.selectedDashboard.item.parent.identity = res.identity;
        this.toggleDashboardTree();
    });
    this.subscriptions.push(selectParentTreeItemSubscription);

    let dahsboardNameSubscription = this.name$.pipe(
      debounceTime(500),
      distinctUntilChanged(),
      switchMap(name => {
        this.data.selectedDashboard.item.name = this.returnValidDashboardName(name.trim());
        return EMPTY;
      })
    ).subscribe();
    this.subscriptions.push(dahsboardNameSubscription);
  }

  addWidget(widget: DashboardWidget): void {
    this.onAddWidget.emit(widget);
  }

  onDragStartWidget(event:MouseEvent, widget: DashboardWidget): void {
    this.dashboardUtilsService.setDragAndDropWidget(widget);
  }

  ngOnDestroy(): void{
    this.subscriptions.forEach(item => {return item.unsubscribe();});
  }

  toggleDashboardTree():void {
    this.showDashboardTree = !this.showDashboardTree;
  }

  filterPossibleParents(): Dashboard[]
  {
    let results = this.data.dashboardsTree.filter((dashboard) => {
      let currentId = Guid.parse( dashboard.identity);
      let selectedDashboardId = Guid.parse( this.data.selectedDashboard.id);
      return !currentId.equals( selectedDashboardId);
    });

    return results;
  }

  private isFilenameIncremented(name:string): string {
    const m = /\s\((\d+)\)$/.exec(name);
    return m && m[1];
  }


  private incrementFilename(name: string): string {
    const isInc = this.isFilenameIncremented(name);
    return isInc ? name.replace(/\d+(?=\)$)/, m => {return ((+m)+1).toString();}) : `${name} (1)`;
  }

  private returnValidDashboardName(name:string): string {
    for(let i in this.data.dashboardsObject){
      if(this.data.state.edit && i === this.data.selectedDashboard.id){
        continue;
      }
      if(this.data.dashboardsObject[i].name === name){
        name = this.returnValidDashboardName(this.incrementFilename(name));
      }
    }
    return name;
  }

}
