<div class="notificationSettings">

    <app-edit-widget [selectedWidget]="data"></app-edit-widget>

    <div class="form-item">
        <h2>{{ 'customerDashboard.selectNotificationNumber' | translate }}</h2>
        <p-dropdown [options]="notificationNumberList" [(ngModel)]="data.selectedNotificationNumber"
            [styleClass]="'input-element'" (onChange)="selectNumber($event)">
            <ng-template let-item pTemplate="selectedItem">
                <span>{{item.label| translate}}</span>
            </ng-template>
            <ng-template let-item pTemplate="item">
                <div class="ui-helper-clearfix">
                    <div>{{item.label | translate}}</div>
                </div>
            </ng-template>
        </p-dropdown>
    </div>

</div>