<a href="javascript:void(0)" title="{{ breadcrumbTree.name }}" (click)="startNavigationPanel($event, breadcrumbTreePath)">
  <i class="fa double-arrow" aria-hidden="true" *ngIf="hasParent"></i>
  <span [ngClass]="{'selected': selectedDashboardId === breadcrumbTree.identity}">{{ breadcrumbTree.name }}</span>
  <span class="fa fa-chevron-down" app-padding="5px" direction="left" *ngIf="breadcrumbTreePath?.length > 1"></span>
</a>
<p-overlayPanel appendTo="body" #siblingsOp>
  <ul class="siblings-dropdown">
    <ng-container *ngFor="let sibling of breadcrumbTreePath">
      <li *ngIf="showSibling(sibling)">
        <a href="javascript:void(0)" title="{{ sibling.name }}" (click)="selectItem(sibling)">
          <span>{{ sibling.name }}</span>
        </a>  
      </li>
    </ng-container>
  </ul>
</p-overlayPanel>
<ul *ngIf="displayedChildTree">
	<li>
		<app-breadcrumb-nested-tree [breadcrumbTree]="displayedChildTree" [breadcrumbTreePath]="breadcrumbTree.children" 
    [selectedDashboardId]="selectedDashboardId"  [hasParent]="true"></app-breadcrumb-nested-tree>
	</li>
</ul>
