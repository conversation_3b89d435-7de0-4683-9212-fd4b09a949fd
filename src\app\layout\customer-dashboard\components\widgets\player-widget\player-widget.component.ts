import { Component, OnInit, ViewChild, <PERSON><PERSON><PERSON>roy, ElementRef } from '@angular/core';
import { PlayersService } from 'app/shared/services/players.service';
import { PlayerWidget } from 'app/layout/customer-dashboard/models/player-widget';
import { CymsidebarService } from 'app/shared/components/cym-sidebar/cym-sidebar.service';
import { Subscription } from 'rxjs';
import { DashboardUtilsService } from 'app/layout/customer-dashboard/services/dashboard-utils.service';
import { DashboardActionType } from 'app/layout/customer-dashboard/enums/dashboard-action-type.enum';
import { PlayerComponent } from 'app/shared/components/player/player.component';
import { PlayerConfig } from 'app/shared/components/player/player-config.interface';
import { LocalStorageService } from 'app/shared/services/local-storage.service';

@Component({
  selector: 'app-player-widget',
  templateUrl: './player-widget.component.html',
  styleUrls: ['./player-widget.component.scss']
})
export class PlayerWidgetComponent implements OnInit, OnDestroy {
  data: {index: number, widgetData: PlayerWidget};
  playerId: string;
  subscriptions: Subscription[] = [];

  @ViewChild('player', {static: true}) playerCRef: PlayerComponent;
  @ViewChild('playerWrapper', {static: false}) playerWrapper: ElementRef;
  @ViewChild('aspectRatio', {static: false}) aspectRatio: ElementRef;
  playerConfig: PlayerConfig = null;
  constructor(
    private playerService: PlayersService,
    private cymsidebarService: CymsidebarService,
    private dashboardUtilsService: DashboardUtilsService,
    private localStorageService: LocalStorageService,
  ) { }

  ngOnInit(): void{

    this.data.widgetData = new PlayerWidget(this.data.widgetData);
    this.data.widgetData.config.playerId = this.playerCRef.playerId;
    let openVideoSubscription = this.playerService.openVideoChannelDlg.subscribe(playerId => {
     
      if(this.data.widgetData.config.playerId === playerId){
        this.openEditSettings();
      }
    });
    this.subscriptions.push(openVideoSubscription);

    let channelClosedSubscription = this.playerService.ChannelClosed.subscribe((res: { playerId: string, channelId: string }) => {
    
      if(this.data.widgetData.config.playerId === res.playerId){
        this.data.widgetData.config.selectedChannelId = null;
        this.dashboardUtilsService.setWidgetDataChange(this.data.widgetData);
        this.dashboardUtilsService.setWidgetAction(this.data.widgetData, DashboardActionType.save);
      }
    });
    this.subscriptions.push(channelClosedSubscription);

    this.setPlayerConfig(this.data.widgetData);
    this.playerService.setPlayersRef([this.playerCRef]);
  }

  ngOnDestroy(): void{
    this.subscriptions.forEach(element => {element.unsubscribe();});
  }

  openEditSettings(): void{
    this.dashboardUtilsService.setWidgetDataChange(this.data.widgetData);
    this.dashboardUtilsService.setWidgetAction(this.data.widgetData, DashboardActionType.editWidget);
    this.cymsidebarService.openSidebar();
  }

  setPlayerConfig(data: PlayerWidget): void{
    if(data.config.selectedChannelId){
      this.localStorageService.set("widgetPlayerChannel", data.config.selectedChannelId);
    }
    this.playerConfig = {
      selectedChannelId: this.localStorageService.get("widgetPlayerChannel") ? this.localStorageService.get("widgetPlayerChannel") :   data.config.selectedChannelId,
      playerId: data.config.playerId,
      vcaConfig: data.config.vcaConfig ? data.config.vcaConfig : false,
      vcaObject: data.config.vcaObject ? data.config.vcaObject : false,
      ptzControls: data.config.ptzControls ? data.config.ptzControls : false
    };
  }
 }
