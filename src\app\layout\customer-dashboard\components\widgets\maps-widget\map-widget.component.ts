import { Component, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { MapWidget } from 'app/layout/customer-dashboard/models/map-widget';

import { Subscription } from 'rxjs';
import { MapFilters } from 'app/layout/app-map/models/map-filters.interface';
import { MapUtilsService } from 'app/layout/app-map/services/map-utils.service';
import { CymbiotMapComponent } from 'app/shared/modules/cymbiot-map/components/cymbiot-map/cymbiot-map.component';
import { MapLayerService } from 'app/shared/modules/data-layer/services/map-layer/map-layer.service';
import * as _ from 'lodash';
import {MapService} from "app/services/map/map.service";

@Component({
  selector: 'app-map-widget',
  templateUrl: './map-widget.component.html',
  styleUrls: ['./map-widget.component.scss']
})
export class MapWidgetComponent implements OnInit, OnD<PERSON>roy {
  data: {index: number, widgetData: MapWidget};
  subscriptions: Subscription[] = [];
  mapFilterOptions: MapFilters;
  @ViewChild('cymbiotMap', {static: false}) cymbiotMap: CymbiotMapComponent;
  constructor(
    private mapService: MapService,
    private mapUtilsService: MapUtilsService,
    private mapLayerService: MapLayerService
  ) { }

  ngOnInit(): void {
    this.data.widgetData = new MapWidget(this.data.widgetData);

    let dataChangeSubscription = this.mapService.resourceChanged.subscribe();
    this.subscriptions.push(dataChangeSubscription);

    let mapFilterChangeSubscription = this.mapUtilsService.getFilterDataChange().subscribe((response) => {
      if(response[this.cymbiotMap.instanceId]){
        this.mapFilterOptions = {
          selectedResourceGroups: response[this.cymbiotMap.instanceId]
        };
      }
    });
    this.subscriptions.push(mapFilterChangeSubscription);

    if(!this.data.widgetData.mapLayers){
      this.data.widgetData.mapLayers = [];
      let mapLayersSubscription = this.mapLayerService.getAll().subscribe(res => {
        for(const key in res){
          this.data.widgetData.mapLayers.push(res[key]);
        }
      });
      this.subscriptions.push(mapLayersSubscription);
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(subscription => {return subscription.unsubscribe();});
  }

}
