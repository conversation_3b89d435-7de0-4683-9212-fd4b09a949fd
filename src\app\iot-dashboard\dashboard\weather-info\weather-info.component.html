<div *ngIf="weatherInfo; else loading" class="dashboard-rectangle-16">
  <div class="weather-info-card">
    <h3 class="weather-info-title">{{ 'weatherInfo.title' | translate }}</h3>

    <div class="weather-info-box">
      <div class="weather-info-row">
        <label>{{ 'Temperature' | translate }}:</label>
        <span>{{ weatherInfo.temperature }} °C</span>
      </div>
      <div class="weather-info-row">
        <label>{{ 'weatherInfo.humidity' | translate }}:</label>
        <span>{{ weatherInfo.humidity }} %</span>
      </div>
      <div class="weather-info-row">
        <label>{{ 'weatherInfo.pressure' | translate }}:</label>
        <span>{{ weatherInfo.pressure }} mb</span>
      </div>
    </div>
  </div>
</div>

<ng-template #loading>
  <div class="dashboard-rectangle-16">
    <div class="weather-info-card loading">
<span>
  {{ (hasError 
       ? 'weatherInfo.loadingWeatherDataError' 
       : 'weatherInfo.loadingWeatherData') 
     | translate }}
</span>
    </div>
  </div>
</ng-template>
