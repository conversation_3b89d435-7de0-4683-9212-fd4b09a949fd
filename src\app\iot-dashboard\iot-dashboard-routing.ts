import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DashboardComponent } from "./dashboard/dashboard.component";
import { MainLayoutComponent } from "./main-layout/main-layout.component";
import { NotificationsPageComponent } from "../pages/notifications-page/notifications-page.component";


const routes: Routes = [
    {
        path: '',
        component: MainLayoutComponent,
        children: [
            {
                path: '',
                component: DashboardComponent
            },
            {
                path: 'generalDetections',
                loadChildren: () => import("../layout/general-detections/general-detections.module")
                    .then((m) => m.GeneralDetectionsModule)
            },
            {
                path: 'reports',
                loadChildren: () => import("../layout/reports/reports.module")
                    .then((m) => m.ReportsModule)
            },
            {
                path: 'notifications',
                component: NotificationsPageComponent
            },
            {
                path: 'procedures',
                loadChildren: () => import('../layout/procedures/procedures.module')
                    .then(m => m.ProceduresModule)

            },
            {
                path: "vms",
                loadChildren: () => import("../layout/vms/vms.module").then((m) => m.VMSModule)
            },
            {
                path: "dossier",
                loadChildren: () => import("../layout/dossier/dossier.module")
                    .then(m => m.DossierModule)
            },
            {   path: "inventory",
                 loadChildren: () => import("../layout/inventory-new/inventory-new.module").then((m) => m.InventoryNewModule)
            },
            {
                path: "assetsManagement",
                loadChildren: () => import("../layout/assets-management/assets-management.module").then((m) => m.AssetsManagementModule),
            },
            {   path: "swarmLPR",
                loadChildren: () => import("../layout/swarm-lpr/swarm-lpr.module").then((m) => m.SwarmLPRModule)
            },
            { path: "entities",
                loadChildren: () => import("../layout/entities/entities.module").then((m) => m.EntitiesModule)
            },
            { path: "map-new",
                loadChildren: () => import("../layout/app-map/app-map.module").then((m) => m.AppMapModule) },
            { path: "audit",
                loadChildren: () => import("../layout/audit/audit.module").then((m) => m.AuditModule) },
            { path: "vehicleTraffic",
                loadChildren: () => import("../layout/vehicle-traffic/vehicle-traffic.module").then((m) => m.VehicleTrafficModule)
            },
            { path: "info-page",
                loadChildren: () => import("../pages/info-page/info-page.module").then((m) => m.InfoPageModule)
            },
            { path: "ai",
                loadChildren: () => import("../layout/ai/ai.module").then((m) => m.AiModule)
            }
        ]
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class IotDashboardRouting { }
