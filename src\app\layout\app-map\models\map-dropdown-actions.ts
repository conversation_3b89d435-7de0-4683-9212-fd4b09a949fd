
import { GlobalAction } from "../../../shared/models/global-action.interface";
import { MapActionType } from "../enums/map-action-type.enum";

export const mapDropDownActions: GlobalAction[] = [
    // {name: 'appMap.addMap', type: MapActionType.addNew},
    // {name: 'appMap.editMap', type: MapActionType.edit},
    // {name: 'appMap.deleteMap', type: MapActionType.delete},
    {name: 'appMap.saveAsDefault', type: MapActionType.saveAsDefault}
]