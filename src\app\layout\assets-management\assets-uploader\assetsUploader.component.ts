
import {finalize} from 'rxjs/operators';
import { HelperService } from 'app/shared/services/helper.service';
import { Component, ViewChild, Output, EventEmitter, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { DataService } from 'app/shared/services/data.service';
import { ConfigUploaderComponent } from 'app/shared/components/config-uploader/config-uploader.component';
import { Export2CSV } from 'app/shared/services/exportData.service';
import { EdFieldsService } from 'app/shared/services/edFields.service';
import * as moment from 'moment';
import { AppModal } from 'app/shared/components/app-modal/app-modal.component';
import { MessageService } from 'primeng/api';
import { ToastTypes } from 'app/shared/enum/toast-types';
import { ServerTypes } from 'app/shared/modules/data-layer/enum/resource/resource-server-types.enum';
import {ApiCommands} from "app/shared/enum/enum";

@Component({
    selector: 'app-assets-uploader',
    templateUrl: './assetsUploader.component.html',
    styleUrls: ['./assetsUploader.component.scss']
})
export class AssetsUploader implements OnInit {
    @ViewChild('configUploader', {static: true}) configUploader: ConfigUploaderComponent;
    @ViewChild('summaryModal', {static: false}) summaryModal: AppModal;
    @Output('AssetsUploaded') assetsUploaded = new EventEmitter<any>();


    totalAssets = 0;
    rowsMissingMandatoryFields = 0;
    rowsWithWrongFieldType = 0;
    assetsAlreadyExist = 0;
    assetsFailed = 0;
    assetsAdded = 0;
    assetsNotAdded = [];

    serverKeys = [];

    constructor(private dataService: DataService,
        private messageService: MessageService,
        private export2CSV: Export2CSV,
        private helperService: HelperService,
        private i18n: TranslateService,
        private edFieldsService: EdFieldsService) {
    }

    ngOnInit(): void {
        this.serverKeys = [
            {
                groupName: "keys",
                groupKeys: [
                    { id: '0', text: 'name', data: 'name', required: true },
                    { id: '1', text: 'address', data: 'address', required: true },
                    { id: '2', text: 'type', data: 'type', required: true },
                    { id: '3', text: 'status', data: 'status', required: true },
                    { id: '4', text: 'symbol', data: 'symbol', required: true },
                    { id: '5', text: 'startTime', data: 'startTime', required: false },
                    { id: '6', text: 'endTime', data: 'endTime', required: false }
                ]
            },
            {
                groupName: "EdFields",
                groupKeys: [{ id: 'addNewEdFields', text: 'addNewEdFields', data: 'addNewEdFields', required: false, type: this.configUploader.Types.Array }]
            }];

        this.edFieldsService.GetEdFieldsByType(ServerTypes.Core_RES_Asset).subscribe(res => {
            if (res) {
                res.forEach(element => {
                    this.serverKeys[1].groupKeys.push({ id: element, text: element, data: element, required: false })
                });
            }
        });
    }

    OpenDialog() {
        this.configUploader.OpenDialog();
    }

    onDataUploaded(evt) {
        this.rowsMissingMandatoryFields = 0;
        this.rowsWithWrongFieldType = 0;
        this.assetsNotAdded = [];
        this.assetsAlreadyExist = 0;
        this.assetsFailed = 0;
        this.assetsAdded = 0;

        let dataToUpload = [];

        evt.data.forEach(element => {
            let newObj = {};

            let elementKeys = Object.keys(element);
            let missingMandatoryField = false;
            let wrongFieldType = false;
            for (let i = 0; i < elementKeys.length; i++) {
                let key = elementKeys[i];

                let assetKey = this.findGroupKey(key);
                if (assetKey) {
                    if (!element[key] || element[key] == "") {
                        if (assetKey.key.required) {
                            missingMandatoryField = true;
                        }
                        else {
                            continue;
                        }
                    }
                    else if ((assetKey.key.data == 'type' || assetKey.key.data == 'status') && !this.isInt(element[key])) {
                        wrongFieldType = true;
                    }

                    if (assetKey.groupName == 'EdFields') {
                        if (!newObj['edFields']) {
                            newObj['edFields'] = [];
                        }

                        if (assetKey.key.id == 'addNewEdFields') {
                            element[key].forEach(el => {
                                Object.keys(el).forEach(key => {
                                    if (this.serverKeys[1].groupKeys.findIndex(q => q.id == key) == -1) {
                                        this.serverKeys[1].groupKeys.push({ id: key, text: key, data: key, required: false })
                                    }

                                    newObj['edFields'].push({ field: key, value: el[key] });
                                });
                            });
                        }
                        else {
                            newObj['edFields'].push({ field: key, value: element[key] });
                        }
                    } else {
                        if ((assetKey.key.data == 'startTime' || assetKey.key.data == 'endTime')) {
                            let time = moment(element[key], "HH:mm", true);

                            if (time && time.isValid()) {
                                newObj[assetKey.key.data] = time.toISOString();
                            }
                            else {
                                wrongFieldType = true;
                                newObj[assetKey.key.data] = element[key];
                            }
                        } else {
                            newObj[assetKey.key.data] = element[key];
                        }
                    }
                }
            }

            if (missingMandatoryField) {
                this.rowsMissingMandatoryFields++;
                this.addAssetsFailedToHandle(newObj, 'missingMandatoryFields');
            }
            else if (wrongFieldType) {
                this.rowsWithWrongFieldType++;
                this.addAssetsFailedToHandle(newObj, 'wrongFieldType');
            }
            else {
                dataToUpload.push(newObj);
            }
        });

        this.createAssetsEdFields();
        this.uploadAssets(dataToUpload);
    }

    createAssetsEdFields() {
        let edFields = [];
        this.serverKeys[1].groupKeys.forEach(element => {
            if (element.id != 'addNewEdFields') {
                edFields.push(element.id);
            }
        });
        this.edFieldsService.SetResourceTypesEDField(ServerTypes.Core_RES_Asset, edFields).subscribe();
    }

    findGroupKey(key) {
        for (let i = 0; i < this.serverKeys.length; i++) {
            let el = this.serverKeys[i];

            for (let j = 0; j < el.groupKeys.length; j++) {
                if (el.groupKeys[j].id == key) {
                    return { groupName: el.groupName, key: el.groupKeys[j] };
                }
            }
        }

        return null;
    }

    uploadAssets(data) {
        this.dataService.api({
            type: ApiCommands.SetAssets,
            data: data,
            disableBI: true,
            disableErrorHandler: true
        }).pipe(
            finalize(() => {
                this.assetsUploaded.emit();
            }))
            .subscribe(
                res => {
                    if (res) {
                        res.forEach((element, i) => {
                            switch (element.status) {
                                case 'alreadyExist': {
                                    this.assetsAlreadyExist++;
                                    let asset = this.createAssetFromServerAsset(element.asset, data[i].edFields);
                                    this.addAssetsFailedToHandle(asset, 'alreadyExist');
                                    this.edFieldsService.SetResourceExtraData(element.asset.Identity, data[i].edFields).subscribe();
                                    break;
                                }
                                case 'fail': {
                                    this.assetsFailed++;
                                    let asset = this.createAssetFromServerAsset(element.asset, data[i].edFields);
                                    this.addAssetsFailedToHandle(asset, 'fail');
                                    break;
                                }
                                case 'add': {
                                    this.assetsAdded++;
                                    this.edFieldsService.SetResourceExtraData(element.asset.Identity, data[i].edFields).subscribe();
                                    break;
                                }
                            }
                        });

                        this.totalAssets = this.assetsNotAdded.length + this.assetsAdded;
                        this.summaryModal.openModal();
                    }
                },
                err => {
                    this.messageService.add({severity: 'error', summary: this.i18n.instant(ToastTypes.error), detail: this.i18n.instant('fileUploadFailed')});
                });
    }

    downloadErrorsFile() {
        this.export2CSV.downloadFromJson(this.assetsNotAdded, 'assets');
    }

    createAssetFromServerAsset(asset, edFields) {
        let retVal = {};

        retVal['name'] = asset.Name;
        retVal['address'] = asset.Address;
        retVal['type'] = asset.Type;
        retVal['status'] = asset.Status;
        retVal['symbol'] = asset.Symbol;
        retVal['edFields'] = edFields;

        return retVal;
    }

    addAssetsFailedToHandle(asset, failureReason) {
        asset['failureReason'] = this.i18n.instant(failureReason);
        let ast = this.helperService.TranslateObjectKeys(asset);
        this.assetsNotAdded.push(ast);
    }

    isInt(value) {
        return !isNaN(value) && parseInt(value) == value && !isNaN(parseInt(value, 10));
    }

    isDateValid(value) {
        return moment(value, "hh:mm").isValid();
    }

    onModalConfirm(){
        this.summaryModal.closeModal()
    }
}
