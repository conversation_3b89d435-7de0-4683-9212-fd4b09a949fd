<div class="map-filter-wrapper">
  <div class="title-wrapper">
    <h2>{{'appMap.filter' | translate}}</h2>
  </div>

  <form [formGroup]="filterMapForm">
    <div class="form-item">
      <p-multiSelect formControlName="selectedResourceGroups" [options]="resourceItems" [filter]="true" filterBy="label"
        [styleClass]="'input-element'" selectedItemsLabel="{{ 'nrItemsSelected' | translate }}"
        defaultLabel="{{ 'appMap.selectResourceGroup' | translate }}" [maxSelectedLabels]="0" >
        <ng-template let-item pTemplate="item">
          {{item.label | translate}}
        </ng-template>
      </p-multiSelect>
    </div>

    <div class="sidebar-actions">
      <button class="btn btn-secondary" type="submit" (click)="onCancel()">{{ 'cancel' | translate}}</button>
      <button class="btn btn-primary" type="submit" (click)="onSubmit()"
        [disabled]="!filterMapForm.valid">{{ 'execute' | translate}}</button>
    </div>
  </form>
</div>