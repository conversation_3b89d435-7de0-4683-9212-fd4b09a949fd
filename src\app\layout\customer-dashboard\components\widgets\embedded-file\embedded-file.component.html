<div class="embedded-file-wrapper"  >
  <div class="no-data-set" *ngIf="!resourceLink">{{'customerDashboard.dataNotSet' | translate}}</div>
  <ng-container *ngIf="resourceLink">
    <span class="document-icon"><i class="fa fa-share" aria-hidden="true"></i></span>
    <a class="document-link pointer" (click)="openDocument()" pTooltip="{{ 'openDocument' | translate }}"
      tooltipPosition="top">{{ data.widgetData.title | ellipsis:30 }}</a>
  </ng-container>
</div>

<app-modal #document [title]="'document'" [styleClass]="'embedded-file'">
  <ng-container ngProjectAs="contentModal">
    <div class="embedded-content">
          <iframe  *ngIf="resourceLink" [src]="resourceLink" width="100%" height="100%"></iframe> 
      </div>
  </ng-container>
  <ng-container ngProjectAs="footerModal">
      <button type="button" class="btn btn-secondary" (click)="close()">{{ "close" | translate }}</button>
  </ng-container>
</app-modal>