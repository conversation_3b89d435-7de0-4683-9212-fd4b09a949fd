import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { WeatherInfo } from '../../shared/modules/data-layer/models/weather-info';
import { apiMap } from '../../shared/services/api.map';

@Injectable({
  providedIn: 'root'
})
export class WeatherInfoService {

  constructor(private http: HttpClient) {}

  getWeatherInfo(): Observable<WeatherInfo> {
    return this.http.get<WeatherInfo>(environment.apiUrl+ apiMap.getWeatherInfo.url);
  }
}
