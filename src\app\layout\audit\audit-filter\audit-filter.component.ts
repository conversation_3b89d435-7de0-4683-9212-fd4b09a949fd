import { Compo<PERSON>, On<PERSON><PERSON>roy, OnInit, Output, EventEmitter, ViewChild } from '@angular/core';
import { Subject, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { HttpResponse } from '@angular/common/http';
import { MenuItem, MessageService } from 'primeng/api';
import { TranslateService } from '@ngx-translate/core';
import { ToastTypes } from '../../../shared/enum/toast-types';
import { environment } from '../../../../environments/environment';
import { ExportTypes } from '../../../shared/enum/export-types.enum';
import { AuditPageRequest, AuditService } from '../../../services/audit/audit.service';
import { SplitButton } from 'primeng/splitbutton';

@Component({
  selector: 'app-audit-filter',
  templateUrl: './audit-filter.component.html',
  styleUrls: ['./audit-filter.component.scss']
})
export class AuditFilterComponent implements OnInit, OnDestroy {
  @Output() filterChange = new EventEmitter<any>();
  @ViewChild('exportBtn') exportBtn!: SplitButton;

  searchQuery: string = '';
  userQuery: string = '';
  ipQuery: string = '';
  startDate: Date;
  endDate: Date;

  // Export state
  exportLoading = false;
  exportType: ExportTypes | null = null;
  exportTypes = ExportTypes;

  searchSubject: Subject<any> = new Subject<any>();
  subscriptions: Subscription[] = [];
  items: MenuItem[];

  tooltipOptions = {
    showDelay: 150,
    autoHide: false,
    tooltipEvent: 'hover',
    tooltipPosition: 'top'
  };

  constructor(
    private messageService: MessageService,
    private translateService: TranslateService,
    private auditService: AuditService
  ) {
    // Set default date range to last 24 hours
    this.endDate = new Date();
    this.startDate = new Date();
    this.startDate.setDate(this.startDate.getDate() - 1);
    this.items = [
      {
        label: this.translateService.instant('exportToCSV'),
        icon: 'fa fa-file-excel-o',
        command: () => this.export(ExportTypes.csv)
      },
      {
        label: this.translateService.instant('exportToPDF'),
        icon: 'fa fa-file-pdf-o',
        command: () => this.export(ExportTypes.pdf)
      },
      {
        label: this.translateService.instant('exportToHTML'),
        icon: 'fa fa-html5',
        command: () => this.export(ExportTypes.html)
      }
    ];
  }

  get isResetDisabled(): boolean {
    return !this.searchQuery && !this.userQuery && !this.ipQuery;
  }

  onDefaultClick(event: MouseEvent): void {
    this.exportBtn.menu.toggle(event);
  }

  ngOnInit(): void {
    this.subscriptions.push(
      this.searchSubject.pipe(
        debounceTime(700),
        distinctUntilChanged()
      ).subscribe(() => {
        this.emitFilterChange();
      })
    );

    // Initial filter with default values
    this.emitFilterChange();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  onSearch(): void {
    this.searchSubject.next({
      searchQuery: this.searchQuery,
      user: this.userQuery,
      ip: this.ipQuery,
      startDate: this.startDate?.toISOString(),
      endDate: this.endDate?.toISOString()
    });
  }

  onDateChange(): void {
    this.onSearch();
  }

  resetData(): void {
    this.searchQuery = '';
    this.userQuery = '';
    this.ipQuery = '';

    // Reset to last 24 hours
    this.endDate = new Date();
    this.startDate = new Date();
    this.startDate.setDate(this.startDate.getDate() - 1);
  }

  resetFilterTable(): void {
    this.resetData();
    this.emitFilterChange();
  }

  private emitFilterChange(): void {
    this.filterChange.emit({
      searchQuery: this.searchQuery,
      user: this.userQuery,
      ip: this.ipQuery,
      startDate: this.startDate?.toISOString(),
      endDate: this.endDate?.toISOString()
    });
  }


  export(type: ExportTypes): void {
    if (this.exportLoading){
      return;
    } 
    this.exportLoading = true;
    this.exportType = type;
    const exportRequest = this.buildExportRequest(type);
    this.callExportEndpoint(exportRequest);
  }

  private buildExportRequest(fileType: ExportTypes): AuditPageRequest {
    const request: AuditPageRequest = {} as AuditPageRequest;

    if (fileType) {
      request.FileType = fileType.toUpperCase();
    }
    if (this.searchQuery && this.searchQuery.trim()) {
      request.Message = this.searchQuery.trim();
    }
    if (this.userQuery && this.userQuery.trim()) {
      request.User = this.userQuery.trim();
    }
    if (this.ipQuery && this.ipQuery.trim()) {
      request.IP = this.ipQuery.trim();
    }
    if (this.startDate) {
      request.StartTimestamp = this.startDate.toISOString();
    }
    if (this.endDate) {
      request.EndTimestamp = this.endDate.toISOString();
    }

    return request;
  }

  private callExportEndpoint(request: AuditPageRequest): void {
    this.subscriptions.push(
      this.auditService.exportAudit(request).subscribe({
        next: (response) => {
          this.handleExportSuccess(response)
        },
        error: () => {
          this.handleExportError();
        }
      })
    );
  }

  private handleExportSuccess(response: HttpResponse<Blob>): void {
     if (!response.body) {
        this.messageService.add({
          severity: ToastTypes.error,
          detail: this.translateService.instant('noDataToExport')
        });
        this.resetExportState();
        return;
      }

    const blob = response.body;

    const mimeType = blob.type;
    const extension = mimeType ? `.${mimeType.split('/')[1]}` : '';

    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const fileName = `Audit-export-${timestamp}${extension}`;

    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    this.messageService.add({
      severity: ToastTypes.success,
      detail: this.translateService.instant('exportSuccess')
    });
    this.resetExportState();
  }

  private handleExportError(): void {
    let errorMessage = this.translateService.instant('exportFailed');
    this.messageService.add({
      severity: ToastTypes.error,
      detail: errorMessage
    });

    this.resetExportState();
  }

  private resetExportState(): void {
    this.exportLoading = false;
    this.exportType = null;
  }

}
