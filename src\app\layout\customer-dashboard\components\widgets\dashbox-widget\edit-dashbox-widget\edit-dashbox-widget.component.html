<div class="dashboardSettings">
    <app-edit-widget [selectedWidget]="data"></app-edit-widget>
    <form role="form" class="form-format" *ngIf="dashboxForm" [formGroup]="dashboxForm">
        <div class="form-item ">
            <h2>{{ 'customerDashboard.selectBakgroundColorClass' | translate }}</h2>
            <div class="flexbox">
                <p-colorPicker formControlName="selectedBackgroundColourCode" (onChange)="onSelectColourCode(dashboxColorType.backgroundColor, $event.value)" appendTo="body">
                </p-colorPicker>
                <div class="validator-input">
                    <input type="text" #backgroundColor name="selectedBackgroundColourCode" (change)="onSelectColourCode(dashboxColorType.backgroundColor, $event.target.value)" 
                    formControlName="selectedBackgroundColourCode" placeholder="#{{'code' | translate}}"/>
                </div>        
            </div>
            <div class="all-pattern-error"
            *ngIf="!dashboxForm.controls.selectedBackgroundColourCode.valid && dashboxForm.controls.selectedBackgroundColourCode.dirty">       
                <div class="input-error">
                    <small *ngIf="dashboxForm.controls['selectedBackgroundColourCode'].hasError('pattern')" class="text-danger">
                    {{'formErrors.pattern' | translate}}
                    </small>
                </div>
             </div>
        </div>

        <div class="form-item">
            <h2>{{ 'customerDashboard.selectResourceType' | translate }}</h2>
            <p-dropdown [options]="resourceTypeList" formControlName="selectedResourceType" placeholder="{{ 'choose' | translate }}"
                (onChange)="onResourceTypeChanged('selectedResourceType');">
                <ng-template let-item pTemplate="selectedItem">
                    <span>{{item.label| translate}}</span>
                </ng-template>
                <ng-template let-item pTemplate="item">
                    <div class="ui-helper-clearfix">
                        <div>{{item.label | translate}}</div>
                    </div>
                </ng-template>
            </p-dropdown>
            <div class="all-pattern-error"
            *ngIf="!dashboxForm.controls.selectedResourceType.valid && dashboxForm.controls.selectedResourceType.dirty">       
                <div class="input-error">
                    <small *ngIf="dashboxForm.controls['selectedResourceType'].hasError('required')" class="text-danger">
                    {{'formValidation.field' | translate}}
                    </small>
                </div>
             </div>
        </div>
        <div class="form-item">
            <h2>{{ 'customerDashboard.selectGroup' | translate }}</h2>
            <p-dropdown [options]="groupList" [filter]="true" filterBy="label" formControlName="selectedGroupId"
                placeholder="{{ 'choose' | translate }}"
                (onChange)="onWidgetDataChange('selectedGroupId'); onGroupChange($event.value)">
                <ng-template let-item pTemplate="selectedItem">
                    <span>{{item.label| translate}}</span>
                </ng-template>
                <ng-template let-item pTemplate="item">
                    <div class="ui-helper-clearfix">
                        <div>{{item.label | translate}}</div>
                    </div>
                </ng-template>
            </p-dropdown>
            <div class="all-pattern-error"
            *ngIf="!dashboxForm.controls.selectedGroupId.valid && dashboxForm.controls.selectedGroupId.dirty">       
                <div class="input-error">
                    <small *ngIf="dashboxForm.controls['selectedGroupId'].hasError('required')" class="text-danger">
                    {{'formValidation.field' | translate}}
                    </small>
                </div>
             </div>
        </div>
        <div class="form-item">
            <h2>{{ 'customerDashboard.selectResource' | translate }}</h2>
            <p-multiSelect [options]="resourceList" [filter]="true" filterBy="label"
                formControlName="selectedResourceIds" (onChange)="onWidgetDataChange('selectedResourceIds')"
                selectedItemsLabel="{{ 'nrItemsSelected' | translate }}"
                defaultLabel="{{ 'choose' | translate }}" [maxSelectedLabels]="0"
                [virtualScroll]="true" itemSize="30">
                <ng-template let-item pTemplate="item">
                    {{item.label | translate}}
                </ng-template>
            </p-multiSelect>
        </div>
        <div class="form-item">
            <h2>{{ 'customerDashboard.selectResourceState' | translate }}</h2>
            <p-multiSelect [options]="statusList" formControlName="selectedResourceStates"
                (onChange)="onWidgetDataChange('selectedResourceStates')" [selectionLimit]="6"
                selectedItemsLabel="{{ 'nrItemsSelected' | translate }}"
                defaultLabel="{{ 'choose' | translate }}" [maxSelectedLabels]="0">
                <ng-template let-item pTemplate="item">
                    {{item.label | translate}}
                </ng-template>
            </p-multiSelect>
            <div class="all-pattern-error"
            *ngIf="!dashboxForm.controls.selectedResourceStates.valid && dashboxForm.controls.selectedResourceStates.dirty">       
                <div class="input-error">
                    <small *ngIf="dashboxForm.controls['selectedResourceStates'].hasError('required')" class="text-danger">
                    {{'formValidation.field' | translate}}
                    </small>
                </div>
             </div>
        </div>
    </form>
</div>