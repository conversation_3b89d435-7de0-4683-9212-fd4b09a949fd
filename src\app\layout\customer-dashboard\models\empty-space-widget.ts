import { Widget } from "./widget";
import { DashboardWidget } from "./dashboard-widget.interface";
import { WidgetSize } from "../enums/widget-size.enum";

export class EmptySpaceWidget extends Widget {

    gridColumnSize: number;

    constructor(obj?: DashboardWidget){
        super(obj);
        if(!this.gridColumnSize) {
            this.gridColumnSize = 2;
        }
    }

    getWidgetSize(): WidgetSize[]{
        return [
            WidgetSize.xXSmall,
            WidgetSize.small,
            WidgetSize.xSmall,
            WidgetSize.xMedium,
            WidgetSize.medium,
            WidgetSize.big,
            WidgetSize.xBig,
            WidgetSize.xXBig,
            WidgetSize.xTall,
            WidgetSize.tall,
            WidgetSize.wide,
            WidgetSize.xWide
        ];
    }

}