<div class="map-edit-layers-wrapper">
  
  <div class="title-wrapper">
    <h2>{{'appMap.layers' | translate}}</h2>
    <!-- <button class="btn btn-link" (click)="toggleManage()">{{'appMap.manage' | translate}}</button> -->
  </div>
  
  <ul class="elements">
    <li *ngIf="manageLayers">
      <button  (click)="addLayer()" class="btn btn-link add-layer">
        <i class="menu-icon icon-layer-group"></i>
        {{'appMap.addNewLayer' | translate}}
      </button>
    </li>
    <li *ngFor="let element of mapLayers; let index = index" [ngClass]="selectedMapLayer && element.identity === selectedMapLayer.identity ? 'edited' : null">
      <span class="element-title">{{element?.name}}</span>
      <span class="actions">
        
        <button class="btn visibility" (click)="toggleVisibility(index)" *ngIf="!manageLayers">
          <i class="fa {{element.hidden ? 'fa-eye-slash': 'fa-eye'}}" aria-hidden="true"></i>
        </button>
        
        <button class="btn edit" (click)="editLayer(element, index)" *ngIf="manageLayers">
          <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
        </button>

        <button class="btn delete" (click)="deleteLayer(element, index)" *ngIf="manageLayers">
          <i class="fa fa-trash-o" aria-hidden="true"></i>
        </button>

      </span>
      <div class="dynamic-wrapper" #dynamicForm></div>  
    </li>
  </ul>

  

  <div #newDynamicForm></div>  

</div>