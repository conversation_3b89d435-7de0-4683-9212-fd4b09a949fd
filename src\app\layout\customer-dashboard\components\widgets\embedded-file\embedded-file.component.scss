.no-data-set {
    padding: 15px;
}
.embedded-file-wrapper {
    height: 100%;
    position: relative;
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0px 20px 0px;
    .document-icon {
        i {
            transform: rotate(-50deg);
            font-size: 2.3rem;
            color: #c4c4c4;
        }
     }
    .document-link {
        font-size: 1.18rem;
        color: var(--primary-1);
        padding-left: 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

::ng-deep {   
    .embedded-file {
        width: 55rem !important;
        .embedded-content {
            position: relative;
            height: 50rem !important;
            min-height: 50rem !important;
        }
        body .ui-dialog {
            min-width: 40rem;
        } 
    } 
}