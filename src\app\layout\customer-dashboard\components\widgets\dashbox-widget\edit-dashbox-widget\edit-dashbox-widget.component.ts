import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, ViewChild } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { DashboardActionType } from 'app/layout/customer-dashboard/enums/dashboard-action-type.enum';
import { DashboxColorType } from 'app/layout/customer-dashboard/enums/dashbox.enum';
import { DashboxWidget } from 'app/layout/customer-dashboard/models/dashbox-widget';
import { DashboardUtilsService } from 'app/layout/customer-dashboard/services/dashboard-utils.service';
import { Guid } from 'app/shared/enum/guid';
import { ServerTypes } from 'app/shared/modules/data-layer/enum/resource/resource-server-types.enum';
import { ResourceState } from 'app/shared/modules/data-layer/enum/resource/resource-state.enum';
import { ModelStore } from 'app/shared/modules/data-layer/models/model-store';
import { ResourceGroup } from 'app/shared/modules/data-layer/models/resource-group';
import {
    DeviceStatusService
} from "app/shared/modules/data-layer/services/device-status/device-status.service";
import { ResourceGroupService } from 'app/shared/modules/data-layer/services/resource-group/resource-group.service';
import { SelectItem } from 'primeng/api';
import { Subscription } from 'rxjs/internal/Subscription';
import { DefaultWidgetEditorComponent } from '../../default-widget/default-widget-editor.component';

@Component({
  selector: 'app-edit-dashbox-widget',
  templateUrl: './edit-dashbox-widget.component.html',
  styleUrls: ['./edit-dashbox-widget.component.scss']
})
export class EditDashboxWidgetComponent extends DefaultWidgetEditorComponent implements OnInit,OnDestroy {
  @ViewChild('backgroundColor', {static: false}) backgroundColor: ElementRef;
  data: DashboxWidget;
  groupList: SelectItem[] = [{label: "customerDashboard.showAllGroups", value: Guid.EMPTY}];
  resourceTypeList: SelectItem[] = [];
  statusList: SelectItem[] = [];
  resourceList: SelectItem[] = [];
  dashboxColorType = DashboxColorType;
  dashboxForm: FormGroup = null;
  resourceGroups: ResourceGroup[] = [];
  subscriptions: Subscription[] = [];
  constructor(
    private resourceGroupService: ResourceGroupService,
    private dashboardUtilsService: DashboardUtilsService,
    private formBuilder: FormBuilder,
    private deviceStatusService: DeviceStatusService
  ) {
    super();
  }
  ngOnDestroy(): void {
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
  }

  ngOnInit(): void {
    this.data = new DashboxWidget(this.data);
    this.getDeviceStatus();
    this.getResourceGroups();
  }

  getResourceGroups(): void {

    let resourceGroupServiceSubscription =this.resourceGroupService.getAll().subscribe(res => {

      let resourceGroupArray = this.returnResourceGroupArray(res);
      this.buildResourceList(resourceGroupArray);
      for (const key in res) {
        if(res[key].isHomogeneous){
          //Resource Group type can be different from it's homogenous Resources

          res[key].resources.forEach(resource => {

              resource.URI.map(mapItem=>{

                if(this.resourceTypeList.findIndex(el => {return el.value === mapItem.Type;}) === -1){
                  this.resourceTypeList.push({label: ServerTypes[mapItem.Type], value: mapItem.Type});
                }
              })

          });
        }
      }
      this.generateDashboxForm(this.data);
      this.formStatusChanges();
      this.dashboardUtilsService.setSidebarActionState({actionType: DashboardActionType.save, disabled: !this.dashboxForm.valid});
      this.buildGroupList(res);
    });
    this.subscriptions.push(resourceGroupServiceSubscription);
  }

  getDeviceStatus(): void{
   let desiceStatusSubscription= this.deviceStatusService.getAll().subscribe(res => {
       for(const key in res) {
        if(this.statusList.findIndex(el => {return el.value === res[key].status;}) === -1){
          this.statusList.push({label: res[key].status, value: res[key].status});
        }
      }
      for(let state in ResourceState){
        if(this.statusList.findIndex(el => {return el.value === state;}) === -1){
          this.statusList.push({label: state, value: state});
        }
      }
    });
    this.subscriptions.push(desiceStatusSubscription);
  }

  generateDashboxForm(data: DashboxWidget): void {
    this.dashboxForm = this.formBuilder.group({
      selectedBackgroundColourCode: new FormControl(data.selectedBackgroundColourCode ? data.selectedBackgroundColourCode : null, Validators.compose([Validators.pattern('^#([a-fA-F0-9]){3}$|[a-fA-F0-9]{6}$')])),
        selectedResourceType: new FormControl(data.selectedResourceType ? data.selectedResourceType : null, Validators.compose([Validators.required])),
        selectedGroupId: new FormControl(data.selectedGroupId ? data.selectedGroupId : [], Validators.compose([Validators.required])),
        selectedResourceIds: new FormControl(data.selectedResourceIds ? data.selectedResourceIds : []),
        selectedResourceStates: new FormControl(data.selectedResourceStates ? data.selectedResourceStates : []),
      });
  }

  onSelectColourCode(type: DashboxColorType, color: string): void {
    switch (type) {
      case DashboxColorType.backgroundColor:
        this.data.selectedBackgroundColourCode = color;
        this.backgroundColor.nativeElement.value = color;
      break;
      default:
        console.error('Cannot find dashbox color type', type);
      break;
    }
    this.dashboardUtilsService.setWidgetDataChange(this.data);
  }

  onWidgetDataChange(type: string): void {
    this.data[type] = this.dashboxForm.get(type).value;
    this.dashboardUtilsService.setWidgetDataChange(this.data);
  }

  onResourceTypeChanged(selectedResourceType: string):void
  {
    this.data.selectedResourceType = this.dashboxForm.get(selectedResourceType).value;
    this.dashboardUtilsService.setWidgetDataChange(this.data);

    this.buildResourceList(this.resourceGroups);
    this.filterGroupList();
  }

  onGroupChange(value: string): void {

    let groups : ResourceGroup[] = [];
    if (value !== Guid.EMPTY)
    {
      let group = this.resourceGroups.find((item) => {return item.identity === value;});
      groups.push(group);
    }
    else
    {
      this.resourceGroups.forEach(group => groups.push(group));
    }
    this.buildResourceList(groups);
  }

  formStatusChanges(): void {
   let dashBoxFormSubscription= this.dashboxForm.statusChanges.subscribe((result) => {
      switch (result) {
        case "VALID":
          this.dashboardUtilsService.setSidebarActionState({actionType: DashboardActionType.save, disabled: false});
          break;
        case "INVALID":
          this.dashboardUtilsService.setSidebarActionState({actionType: DashboardActionType.save, disabled: true});
          break;
        default:
        break;
      }
    });
    this.subscriptions.push(dashBoxFormSubscription);
  }

  private buildGroupList(data: ModelStore<ResourceGroup>): void {
    this.groupList = this.groupList.slice(0, 1);
    for (const key in data) {
      if(data[key].isHomogeneous ){
        this.groupList.push({label: data[key].name, value: data[key].identity});
      }
    }

  }

  private returnResourceGroupArray(data: ModelStore<ResourceGroup>): ResourceGroup[] {
    for (const key in data) {
      this.resourceGroups.push(data[key]);
    }
    return this.resourceGroups;
  }

  private buildResourceList(data: ResourceGroup[]): void {

    this.resourceList = [];
    data.forEach(resourceGroup => {
      if(this.data.selectedGroupId !== Guid.EMPTY && resourceGroup.identity !== this.data.selectedGroupId){
        return;
      }
      resourceGroup.resources.forEach(resource => {

        if (this.data.selectedResourceType !== resource.resourceType)
        {
          return;
        }

        if(this.resourceList.findIndex(el => {return el.value === resource.identity;}) === -1){
          this.resourceList.push({label: resource.name, value: resource.identity});
        }
      });
    });
  }

    private filterGroupList():void{
      this.groupList=[];
        let resourceGroupsSelected= this.resourceGroups.filter(group => group.allResourcesType === this.data.selectedResourceType);
        resourceGroupsSelected.forEach(resourceGroup=>{
            if(resourceGroup.isHomogeneous ){
                this.groupList.push({label: resourceGroup.name, value: resourceGroup.identity});
            }
        })
    }
}
