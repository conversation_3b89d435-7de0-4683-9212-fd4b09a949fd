import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { TableColumnProperties } from '../components/ng-turbo-table/models/table.models';

import { TriggerTypes } from '../enum/trigger-types';
import { TimeService } from './time.service';
import {NativeType} from "app/shared/enum/enum";
import * as _ from 'lodash';
@Injectable({
  providedIn: 'root'
})
export class ReportsTurboTableService {

  constructor(
    private timeService: TimeService,
    private translateService: TranslateService
  ) { }

  private essentialColumns: string[] = [
    'time_stamp_formatted',
    'resource_name',
    'resource_state',
    'trigger_type',
    'event_name',
    'description'
  ];

  buildTableColumns(data: {contentType: number, fieldName: string, title: string}[],filterValues?): TableColumnProperties[] {
    let tableColumns: TableColumnProperties[] = [];

    if(data.length > 0) {
      const essentialColumnIndices = data
        .map((element, index) => ({ index, isEssential: this.essentialColumns.includes(element.fieldName) }))
        .filter(item => item.isEssential)
        .map(item => item.index);

      const visibleColumnIndices = new Set(essentialColumnIndices);
      if (essentialColumnIndices.length < 6) {
        for (let i = 0; i < data.length && visibleColumnIndices.size < 6; i++) {
          if (!visibleColumnIndices.has(i)) {
            visibleColumnIndices.add(i);
          }
        }
      }

      data.forEach((element, index) => {
        tableColumns.push({
          field: element.fieldName,
          header: element.title.toString(),
          isFilterable: true, // Make all columns filterable
          filterOptions: element.fieldName == 'field_value' ? (filterValues ? filterValues : []) : [],
          frozen: false,
          visibile: visibleColumnIndices.has(index),
          sortable: true
        });
      });
      return tableColumns;
    }
  }



  exportToPDF(tableData: {[key: string] : string}[]):void{
    var doc = new jsPDF('l','pt', 'A3');
    let header=[];
    tableData.forEach((element) => {
      let keys =  Object.keys(element);
      keys.map(item=>{
        if(!header.includes(item)){
        header.push(item);
        }
      }) ;
    });
    var outputData = [];
        for(var i = 0; i < tableData.length; i++) {
          let obj = {};
          for (let key of header) {
            obj[key] = "na";
          }
            var input = tableData[i];

            let keys =  Object.keys(input);
            keys.map((key,keyIndex)=>{
                obj[key]=input[key];
            });
            outputData.push(obj);
        }
        let bodyItems=[];
        outputData.forEach((element,index) => {
          var values = Object.keys(element).map(function(e) {
            return element[e]
          })
          bodyItems.push(values);
        })
          autoTable(doc, {
            styles: { fontSize:7 },
            head: [header],
            body:bodyItems,
            didDrawCell: (data) => { },
         });
         let timestamp=this.getTimeStampInMilliseconds(new Date());
          doc.save(`PDFexport${timestamp}.pdf`);
    }

    // now in milliseconds
    getTimeStampInMilliseconds(date: Date): number {
      return date.getTime();
    }

    exportToCSV(tableData: {[key: string] : string}[], fileName: string = 'CSVexport'): void {
      if (!tableData || tableData.length === 0) {
        console.warn('No data to export to CSV');
        return;
      }

      // Get all unique headers from the data
      const headers: string[] = [];
      tableData.forEach(row => {
        Object.keys(row).forEach(key => {
          if (!headers.includes(key)) {
            headers.push(key);
          }
        });
      });

      // Format headers for CSV (convert to title case)
      const formattedHeaders = headers.map(header => _.startCase(header));

      // Create CSV content
      let csvContent = formattedHeaders.join(',') + '\r\n';

      // Add data rows
      tableData.forEach(row => {
        const rowValues = headers.map(header => {
          const value = row[header];
          // Handle null, undefined, or empty values
          if (value === null || value === undefined || value === '') {
            return '';
          }
          // Escape commas and quotes in the value
          const formattedValue = String(value).replace(/"/g, '""');
          // Wrap in quotes if the value contains commas, quotes, or newlines
          return formattedValue.includes(',') || formattedValue.includes('"') || formattedValue.includes('\n')
            ? `"${formattedValue}"`
            : formattedValue;
        });
        csvContent += rowValues.join(',') + '\r\n';
      });

      // Create a Blob and download the file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const timestamp = this.getTimeStampInMilliseconds(new Date());
      const finalFileName = `${fileName}${timestamp}.csv`;

      // Create a download link and trigger the download
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', finalFileName);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }

  buildTableData(data:{[propertyName: string]: string}[]):{ [columnField: string]:string } []{
    let newData: {[key: string] : string}[] = [];
    data.forEach((element) => {
      Object.keys(element).forEach(key => {
        element[key] = this.translateValueByKeyAndReturnString(key, element[key]);
      });

      newData.push(element);
    });
    return newData;
  }

  translateValueByKeyAndReturnString(key: string, value: string): string {
    let returnedValue: string;
    switch (key) {
      case 'time_stamp_formatted': {
        let date = new Date(value);
        returnedValue = this.timeService.ConvertStringByTimeType(date, NativeType.DateTime);
        break;
      }
      case 'trigger_type': {
        //display of actual value instead of a number or changes heading to int value, instead of a float
        returnedValue = TriggerTypes[value] ? this.translateService.instant(TriggerTypes[value]) : value;
        break;
      }
      case 'cost': {
        returnedValue = parseFloat(value).toFixed(2)
        break;
      }
      case 'costPerUnit': {
        returnedValue = parseFloat(value).toFixed(4)
        break;
      }
      case 'distance': {
        returnedValue = value.substring(0, value.indexOf(".") + 3);
        break;
      }
      default: {
        returnedValue = value;
        break;
      }
    }
    return returnedValue === null || returnedValue === undefined ? this.translateService.instant('undefined').toString() : returnedValue.toString();
  }
}
