<div class="filter-bar">
    <div class="filter-group">
        <div class="select-wrapper">
            <select id="report-select" [(ngModel)]="selectedReport" (ngModelChange)="selectReportOnChange($event)">
                <option *ngFor="let option of reportsName" [value]="option.value">{{ option.label | translate }}</option>
            </select>
        </div>
        <input type="text" placeholder="{{ 'enterNewTemplateName' | translate }}" class="text-input"
            [(ngModel)]="newTemplateName" *ngIf="selectedReport === 'addNew'"
            (keypress)="validateNewTemplateInput($event)">
    </div>

    <div class="filter-group">
        <i class="pi pi-calendar filter-icon"></i>
        <label for="date-filter" class="group-label">{{ 'dateFilter' | translate }}</label>
        <div class="select-wrapper">
            <button class="report-type-button" (click)="toggleDropdown($event)">
                <span *ngIf="!startDate || !endDate">{{ 'dateFilter' | translate }}</span>
                <span *ngIf="startDate && endDate">{{ startDate | date:'dd/MM/yyyy' }} - {{ endDate | date:'dd/MM/yyyy' }}</span>
                <span *ngIf="startDate && !endDate">{{ startDate | date:'dd/MM/yyyy' }}</span>
                <i class="pi" [ngClass]="isDropdownOpen ? 'pi-chevron-up' : 'pi-chevron-down'"></i>
            </button>

            <!-- Date Filter Dropdown -->
            <div class="dropdown-content" *ngIf="isDropdownOpen" (click)="$event.stopPropagation()" data-dropdown-id="date-filter-dropdown">
                <div class="dropdown-header">
                    <span>{{ 'dateFilter' | translate }}</span>
                    <i class="pi pi-chevron-up" (click)="toggleDropdown($event)"></i>
                </div>
                <div class="dropdown-inner">
                <button class="option-btn" (click)="useLatestAlerts()">
                    {{ 'latestAlerts' | translate }}
                </button>

                <div class="period-section">
                    <span>{{ 'period' | translate }}</span>
                    <div class="period-controls">
                        <div class="number-controls">
                            <button class="btn-minus" (click)="decrementPeriod(); $event.stopPropagation()">-</button>
                            <input type="text" class="number-input" [(ngModel)]="periodValue" (click)="$event.stopPropagation()">
                            <button class="btn-plus" (click)="incrementPeriod(); $event.stopPropagation()">+</button>
                        </div>
                        <!-- Period select wrapper -->
                        <div class="period-select-wrapper">
                            <div class="period-select-button" (click)="togglePeriodDropdown($event)">
                                <span>{{ periodUnit | translate }}</span>
                                <i class="pi pi-chevron-down"></i>
                            </div>
                            <div class="period-dropdown" *ngIf="isPeriodDropdownOpen">
                                <div class="period-option" (click)="selectPeriodUnit('days', $event)">{{ 'days' | translate }}</div>
                                <div class="period-option" (click)="selectPeriodUnit('weeks', $event)">{{ 'weeks' | translate }}</div>
                                <div class="period-option" (click)="selectPeriodUnit('months', $event)">{{ 'months' | translate }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="date-section">
                    <div class="date-label">{{ 'startDate' | translate }}</div>
                    <div class="date-display">
                        {{ startDate ? (startDate | date:'dd/MM/yyyy') : '--/--/----' }}
                    </div>

                    <div class="calendar-container">
                        <div class="month-header">
                            <button class="month-nav prev" (click)="previousMonth(); $event.stopPropagation()">&lt;</button>
                            <span>{{ formattedCurrentMonth | translate }}</span>
                            <button class="month-nav next" (click)="nextMonth(); $event.stopPropagation()">&gt;</button>
                        </div>
                        <div class="weekdays">
                            <span *ngFor="let day of weekDays">{{ day | translate }}</span>
                        </div>
                        <div class="calendar-days">
                            <!-- Calendar days with range selection support -->
                            <span *ngFor="let day of calendarDays"
                                  (click)="selectDate(day); $event.stopPropagation()"
                                  [class.empty]="!day.day"
                                  [class.selected]="isDateSelected(day)"
                                  [class.in-range]="isDateInRange(day)">
                                {{ day.day || '' }}
                            </span>
                        </div>
                    </div>

                    <div class="time-section">
                        <div class="time-label">{{ 'time' | translate }}</div>
                        <div class="time-input-wrapper">
                            <input type="text" class="time-input" [(ngModel)]="timeValue" (click)="$event.stopPropagation()">
                        </div>
                        <div class="am-pm-toggle">
                            <button class="btn-am"
                                    [class.active]="isAm"
                                    (click)="setAmPm(true); $event.stopPropagation()">{{ 'AM' | translate }}</button>
                            <button class="btn-pm"
                                    [class.active]="!isAm"
                                    (click)="setAmPm(false); $event.stopPropagation()">{{ 'PM' | translate }}</button>
                        </div>
                    </div>
                </div>

                <div class="date-section">
                    <div class="date-label">{{ 'endDate' | translate }}</div>
                    <div class="date-display">
                        {{ endDate ? (endDate | date:'dd/MM/yyyy') : '--/--/----' }}
                    </div>
                </div>

                <div class="selection-status" *ngIf="selectionMode === 1">
                    <span>{{ 'selectEndDate' | translate }}</span>
                </div>
                </div>
            </div>
        </div>

        <!-- Custom Report Type Dropdown -->
        <div class="select-wrapper report-type-wrapper">
            <button class="report-type-button" (click)="toggleReportTypeDropdown($event)">
                <span>{{ selectedReportType || ('reportType' | translate) }}</span>
                <i class="pi" [ngClass]="isReportTypeDropdownOpen ? 'pi-chevron-up' : 'pi-chevron-down'"></i>
            </button>
            <div class="report-type-dropdown dropdown-content" *ngIf="isReportTypeDropdownOpen" (click)="$event.stopPropagation()">
                <div class="dropdown-header">
                    <span>{{ 'reportType' | translate }}</span>
                    <i class="pi pi-chevron-up" (click)="toggleReportTypeDropdown($event)"></i>
                </div>
                <div class="report-option-section">
                    <span>{{ 'alert' | translate }}</span>
                    <p-multiSelect [options]="alertOptions" [(ngModel)]="selectedAlerts" placeholder="{{ 'selectAlerts' | translate }}"
                                styleClass="dark-dropdown" [showClear]="true" [filter]="true"
                                (ngModelChange)="onAlertsChange($event)"
                                selectedItemsLabel="{0} {{ 'alertsSelected' | translate }}"
                                defaultLabel="{{ 'selectAlerts' | translate }}"
                                [disabled]="selectedTriggers && selectedTriggers.length > 0">
                        <ng-template let-item pTemplate="item">
                            <span>{{item.label}}</span>
                        </ng-template>
                    </p-multiSelect>
                </div>
                <div class="report-option-section">
                    <span>{{ 'trigger' | translate }}</span>
                    <p-multiSelect [options]="triggerOptions" [(ngModel)]="selectedTriggers" placeholder="{{ 'selectTriggers' | translate }}"
                                styleClass="dark-dropdown" [showClear]="true" [filter]="true"
                                (ngModelChange)="onTriggersChange($event)"
                                selectedItemsLabel="{0} {{ 'triggersSelected' | translate }}"
                                defaultLabel="{{ 'selectTriggers' | translate }}"
                                [disabled]="selectedAlerts && selectedAlerts.length > 0">
                        <ng-template let-item pTemplate="item">
                            <span>{{item.label}}</span>
                        </ng-template>
                    </p-multiSelect>
                </div>
            </div>
        </div>
    </div>

    <div class="action-buttons">
        <button class="action-btn" (click)="generateReport()" [disabled]="disableExceuteBtn || reportQueryId" *ngIf="!reportQueryId"
               title="{{ 'generateReport' | translate }}">
            <i class="pi" [ngClass]="{'pi-spin pi-spinner': reportQueryId, 'pi-file': !reportQueryId}"></i>
        </button>
        <button class="action-btn" *ngIf="reportQueryId" (click)="onCancelReport()"
               title="{{ 'cancel' | translate }}">
            <i class="pi pi-times"></i>
        </button>
        <button class="action-btn" *ngIf="!reportQueryId && !reportGenerationCancelled" (click)="retryReport()"
               title="{{ 'retryReport' | translate }}">
            <i class="pi pi-refresh"></i>
        </button>
        <button class="action-btn" (click)="exportToPdf()"
               title="{{ 'exportPDF' | translate }}">
            <i class="fa fa-file-pdf-o"></i>
        </button>
        <button class="action-btn" (click)="exportToCsv()"
               title="{{ 'exportToCSV' | translate }}">
            <i class="fa fa-file-excel-o"></i>
        </button>
        <button class="action-btn" *ngIf="selectedReport === 'addNew'" (click)="onSaveTemplate()"
               title="{{ 'saveTemplate' | translate }}">
            <i class="pi pi-save"></i>
        </button>
        <button class="action-btn" *ngIf="selectedReport !== 'addNew'" (click)="onUpdateTemplate()"
               title="{{ 'updateTemplate' | translate }}">
            <i class="pi pi-save"></i>
        </button>
        <button class="action-btn" *ngIf="selectedReport !== 'addNew'" (click)="onRemoveTemplate()"
               title="{{ 'removeTemplate' | translate }}">
            <i class="pi pi-trash"></i>
        </button>
        <button class="action-btn" (click)="initVariables()"
               title="{{ 'clear' | translate }}">
            <i class="pi pi-times-circle"></i>
        </button>
    </div>
</div>

<p-confirmDialog appendTo="body" acceptButtonStyleClass="btn-primary"
    rejectButtonStyleClass="btn-secondary"></p-confirmDialog>
