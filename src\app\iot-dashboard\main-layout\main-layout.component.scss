@import '../../../styles/app';

.page-format {
  padding: 15px 0px !important;
}

/* Main layout container */
.responsive-layout-container {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #E4ECE6;
  font-family: "Rounded Mplus 1c Bold", ui-sans-serif, system-ui, sans-serif;
  font-size: 12px;
}

/* Sidebar styles */
.sidebar-container {
  width: 60px;
  min-width: 60px;
  height: 100vh;
  overflow: hidden;
  transition: width 0.3s ease;
  z-index: 10;
  background-color: #E4ECE6;
  display: flex;
  flex-direction: column;
  // box-shadow: 5px 0 10px -5px rgba(0, 0, 0, 0.1);

  /* Expanded state */
  &.expanded {
    width: 130px;
    min-width: 130px;
  }

  .sidebar-header {
    padding: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    background: none;
    height: 80px;
    box-sizing: border-box;

    .header-content {
      display: flex;
      align-items: center;
      width: 100%;

      .logo-container {
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          max-width: 100%;
          height: auto;
          max-height: 25px;
          object-fit: contain;
        }
      }

      .dashboard-title {
        margin: 0 0 0 8px;
        font-size: 12px;
        font-weight: 500;
        color: #3a3a3a;
        white-space: nowrap;
      }
    }

    .user-info {
      margin-top: 10px;
      text-align: center;

      .user-name {
        font-size: 14px;
        font-weight: 500;
        margin: 0;
        color: #3a3a3a;
      }
    }

    /* Sidebar toggle button (appears on both files) */
    .sidebar-toggle {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      width: 24px;
      height: 24px;
      border-radius: 50%;
      border: 1px solid rgba(0, 0, 0, 0.1);
      background-color: #E4ECE6;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      padding: 0;

      &:hover {
        background-color: #00d603;

        i {
          color: white;
        }
      }

      i {
        color: #8c8c8c;
        font-size: 12px;
        transition: color 0.3s ease;
      }
    }
  }

  .sidebar-footer-space {
    height: 60px; 
    width: 100%;
  }

  /* Scrollable sidebar content */
  .sidebar-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
  }
}

/* Content area styles */
.content-container {
  flex: 1;
  height: 100vh;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: #E4ECE6;
  font-family: inherit;
  font-size: inherit;
}

/* User menu without background */
.user-menu-container {
  padding: 15px 20px; /* from second file */
  display: flex;
  justify-content: flex-end;
  height: 80px; 
  box-sizing: border-box;

  .user-menu {
    display: flex;
    align-items: center;

    .user-info {
      margin-right: 15px;

      .user-greeting {
        font-size: 14px;
        font-weight: 500;
        color: #3a3a3a;
      }
    }

    .logout-button-container {
      background: none;
      border: none;
      padding: 0;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        opacity: 0.8;
      }

      .logout-button {
        width: 40px;
        height: 40px;
      }
    }
  }
}

/* Scrollable content wrapper */
.content-wrapper {
  flex: 1;
  width: auto;
  padding: 0 10px; 
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  background-color: #E4ECE6;
  font-family: inherit;
  font-size: inherit;
  display: block;
  min-height: 0;
  position: relative;
}

/* Footer container for equal spacing */
.footer-container {
  height: 60px;
  width: 100%;
  box-sizing: border-box;
}

/* Dashboard IoT logo */
.dashboard-iot-total-logo {
  object-fit: contain;
  object-position: center;
  max-width: 100%;
  height: auto;
  max-height: 25px;
  flex-shrink: 0;
}

/* Logo container (global) */
.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Accessibility - Screen reader only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Utility classes */
.full-height {
  height: 100%;
}

.full-width {
  width: 100%;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

/* Force scroll on logs container in all layouts */
.logs-container {
  overflow-y: scroll !important;
}


/* Ensure content is visible in the viewport */
.viewport-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.viewport-content {
  flex: 1;
}

/* Iframe container styles */
.iframe-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.iframe-content {
  width: 100%;
  height: 100%;
  border: none;
}
