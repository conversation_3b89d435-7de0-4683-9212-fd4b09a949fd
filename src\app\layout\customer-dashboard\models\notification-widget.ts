import { Widget } from "./widget";
import { DashboardWidget } from "./dashboard-widget.interface";
import { WidgetSize } from "../enums/widget-size.enum";

export class NotificationWidget extends Widget {
    selectedNotificationNumber: number;

    constructor(obj?: DashboardWidget){
        super(obj);
        if(!this.selectedNotificationNumber){
            this.selectedNotificationNumber = 10;
        }
    }

    getWidgetSize(): WidgetSize[]{
        return [WidgetSize.medium, WidgetSize.big, WidgetSize.tall]
    }

}