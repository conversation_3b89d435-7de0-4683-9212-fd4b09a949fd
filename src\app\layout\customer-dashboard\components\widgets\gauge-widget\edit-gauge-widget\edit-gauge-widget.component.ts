import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, FormBuilder, FormGroup } from '@angular/forms';
import { DashboardActionType } from 'app/layout/customer-dashboard/enums/dashboard-action-type.enum';
import { GaugeDisplayResults } from 'app/layout/customer-dashboard/enums/gauge-widget-display-results.enum';
import { GaugeWidget, ThresholdPoints } from 'app/layout/customer-dashboard/models/gauge-widget';
import { DashboardUtilsService } from 'app/layout/customer-dashboard/services/dashboard-utils.service';
import { Guid } from 'app/shared/enum/guid';
import { ResourceGroup } from 'app/shared/modules/data-layer/models/resource-group';
import { ResourceTriggerGroup } from 'app/shared/modules/data-layer/models/resource-trigger-group';
import { RuleEvent } from 'app/shared/modules/data-layer/models/rule-event';
import { ResourceGroupService } from 'app/shared/modules/data-layer/services/resource-group/resource-group.service';
import { ResourceTriggersService } from 'app/shared/modules/data-layer/services/resource-triggers/resource-triggers.service';
import { SelectItem } from 'primeng/api';
import { OverlayPanel } from 'primeng/overlaypanel';
import { EMPTY, Subject, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';
import { DefaultWidgetEditorComponent } from '../../default-widget/default-widget-editor.component';
import {RuleEventService} from "app/shared/modules/data-layer/services/rule-event/rule-event.service";

@Component({
  selector: 'app-edit-gauge-widget',
  templateUrl: './edit-gauge-widget.component.html',
  styleUrls: ['./edit-gauge-widget.component.scss']
})
export class EditGaugeWidgetComponent extends DefaultWidgetEditorComponent implements OnInit, OnDestroy {

  data: GaugeWidget;
  private modelStore: { [id: string] : ResourceGroup };
  private modelStoreArray: ResourceGroup[] = [];
  measurmentUnitList: SelectItem[] = [
    {label: 'customerDashboard.number', value: 'number'},
    {label: 'customerDashboard.percentage', value: 'percentage'}
  ];
  onMinMaxValue$ = new Subject<{value: string, option: string}>();
  subscriptions: Subscription[] = [];
  thresholdPointsForm: FormGroup;
  thresholdClasses: string[] = ['default', 'blue', 'green', 'yellow', 'red'];
  thresholdIndex: number = null;
  @ViewChild('colorsOp', {static: false}) colorsOp: OverlayPanel;
  groupList: SelectItem[] = [];
  resourceList: SelectItem[] = [];
  triggerList: SelectItem[] = [];
  eventsNameData: SelectItem[] = [];
  displayResultList: SelectItem[] = [
    {label: "customerDashboard.countResults", value: GaugeDisplayResults.countResults},
    {label: "customerDashboard.resultValue", value: GaugeDisplayResults.resultValue}
  ]

  constructor(
    private dashboardUtilsService: DashboardUtilsService,
    private formBuilder: FormBuilder,
    private resourceGroupService: ResourceGroupService,
    private resourceTriggerService: ResourceTriggersService,
    private ruleService: RuleEventService
  ){
    super();
    let maxValueSubscription = this.onMinMaxValue$.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      switchMap(values => {
        this.setMinMaxValue(values);
        return EMPTY;
      })
    ).subscribe();
    this.subscriptions.push(maxValueSubscription);
  }

  ngOnInit(): void {
    this.data = new GaugeWidget(this.data);

    this.thresholdPointsForm = this.formBuilder.group({items: this.formBuilder.array(this.data.thresholdPoints.map(item => {return this.createThresholdItems(item);}))});
    let thresholdPointsFormSubscriber = this.thresholdPointsForm.valueChanges.pipe(debounceTime(500), distinctUntilChanged()).subscribe(res => {
      this.data.thresholdPoints.map((item, index) => {
        item.start = res.items[index].start;
        item.end = res.items[index].end;
        item.class = res.items[index].class;
        item.eventName = res.items[index].eventName;
      });
      this.dashboardUtilsService.setSidebarActionState({actionType: DashboardActionType.save, disabled: !this.thresholdPointsForm.valid});
    });
    this.subscriptions.push(thresholdPointsFormSubscriber);

    let resourceGroupSubscription = this.resourceGroupService.getAll().subscribe(res => {
      this.modelStore = res;
      this.modelStoreArray = this.buildResourceGroupArray(res);
      this.groupList = this.buildGroupList(res);
      this.resourceList = this.buildResourceList(this.data.selectedGroupId ? [this.modelStore[this.data.selectedGroupId]] : this.modelStoreArray);
      if(this.data.selectedResourceId){
        this.getResourceTriggers(this.data.selectedResourceId);
      }
    });
    this.subscriptions.push(resourceGroupSubscription);
    this.getRuleEvents();
  }

  getRuleEvents(): void{
    this.ruleService.getAll().subscribe((response) => {
      let eventsData: RuleEvent[] = Object["values"](response);
      eventsData.forEach((item) => {
        this.eventsNameData.push({ label: item.name, value: item.identity, disabled: false });
      });
    });
  }

  eventNameChange(): void {
    this.dashboardUtilsService.setWidgetDataChange(this.data);
  }

  getResourceTriggers(triggerId: string): void {
    let resourceTriggerSubscription = this.resourceTriggerService.get(triggerId).subscribe(res => {
        this.triggerList = this.buildTriggerList(res);
    });
    this.subscriptions.push(resourceTriggerSubscription);
  }

  setMinMaxValue(values: {value: string, option: string}): void {
    this.data[values.option] = values.value;
    this.dashboardUtilsService.setWidgetDataChange(this.data);
  }

  createThresholdItems(item: ThresholdPoints): FormGroup {
    return this.formBuilder.group({start: item.start, end: item.end, class: item.class, eventName: item.eventName});
  }

  addThresholdItem(): void {
    let point = this.returnNewThresholdValues();
    this.data.thresholdPoints.push(point);
    (this.thresholdPointsForm.controls['items'] as FormArray).push(this.createThresholdItems(point));
  }

  returnNewThresholdValues(): ThresholdPoints {
    let points = [...this.data.thresholdPoints];
    let newPoint = {start: null, end: null, class: 'default', eventName: null};
    let lastPoint = points.pop();

    if(lastPoint){
      newPoint.start = lastPoint.end + 1;
      newPoint.end = this.data.maxValue ? this.data.maxValue : lastPoint.end + 2;
    }
    else {
      newPoint.start = this.data.minValue ? this.data.minValue : 0;
      newPoint.end = this.data.maxValue ? this.data.maxValue : newPoint.start + 1;
    }

    return newPoint;
  }

  removeThresholdItem(index: number): void {
    this.data.thresholdPoints.splice(index, 1);
    (this.thresholdPointsForm.controls['items'] as FormArray).removeAt(index);
    // TODO
    // call api method when deleting a treshold with an event name
  }

  showOp(index: number): void {
    this.thresholdIndex = index;
    this.colorsOp.show(event);
  }

  selectClass(item:string): void {
    if(this.thresholdIndex !== null){
      const fControl = (<FormArray>this.thresholdPointsForm.controls['items']).at(this.thresholdIndex);
      fControl['controls'].class.setValue(item);
    }
    this.colorsOp.hide();
  }

  selectGroup(event: SelectItem): void {
    this.data.selectedGroupId = event.value;
    this.data.selectedResourceId = Guid.EMPTY;
    this.resourceList = this.buildResourceList(this.data.selectedGroupId !== Guid.EMPTY ? [this.modelStore[this.data.selectedGroupId]] : this.modelStoreArray);
    this.dashboardUtilsService.setWidgetDataChange(this.data);
  }

  selectResource(event: SelectItem): void {
    this.data.selectedResourceId = event.value;
    this.getResourceTriggers(event.value);

    this.dashboardUtilsService.setWidgetDataChange(this.data);
  }

  selectResourceTrigger(event: SelectItem): void {
    this.data.selectedTrigger = event.value;
    this.dashboardUtilsService.setWidgetDataChange(this.data);
  }

  buildResourceGroupArray(data: {[id: string] : ResourceGroup}): ResourceGroup[]{
    let dataArray: ResourceGroup[] = [];
    for(let i in data){
      dataArray.push(data[i]);
    }
    return dataArray;
  }

  buildGroupList(data: {[id: string] : ResourceGroup}): SelectItem[] {
    let dataArray: SelectItem[] = [];
    for(let i in data){
      dataArray.push({label: data[i].name, value: data[i].identity});
    }
    return dataArray;
  }

  buildResourceList(groups: ResourceGroup[]): SelectItem[] {
    let dataArray: SelectItem[] = [];
    groups.forEach(group => {

        group.resources.forEach(resource => {

          if(this.dashboardUtilsService.includesServerTypes(resource.resourceType) &&
            dataArray.findIndex(el => {return el.value === resource.identity;}) === -1){
            dataArray.push({label: resource.name, value: resource.Guid});
          }
        });

    });
    return dataArray;
  }

  buildTriggerList(data: Map<string,ResourceTriggerGroup>): SelectItem[]{
    let dataArray: SelectItem[] = [];
    data.forEach((value, key) => {
        dataArray.push({label: value.Triggers[0].Name, value: value.Triggers[0].SurpriseCode});
    })

    return dataArray;
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(subscription => { return subscription.unsubscribe();});
  }

  selectDisplayResult(event: SelectItem): void {
    this.data.displayResultsMethod = event.value;
    this.dashboardUtilsService.setWidgetDataChange(this.data);
  }
}
