<p-dialog [(visible)]="visible" [style]="{width: '70vw'}" [modal]="true" [draggable]="false" [resizable]="false">
    <ng-template pTemplate="header">
        <div class="iot-modal-search">
            <div class="iot-modal-header">
                <h3 [pTooltip]="dossier.name" [tooltipOptions]="tooltipOptions">
                    <span class="dossier-name">{{ dossier.name }}</span>
                    <span class="translated-text">{{ 'dossierHistory.title' | translate }}</span>
                </h3>
            </div>

            <div class="filters-content">
                <div class="filter-element-container">
                    <div class="filter-element">
                        <i class="fa fa-search green"></i>
                        <input type="text" pInputText placeholder="{{ 'dossierHistory.search' | translate }}"
                            [(ngModel)]="searchQuery" (keyup)="onSearch()" #searchInput>
                    </div>
                </div>

                <div class="right-filter">
                    <div class="filter-element">
                        <i class="fa fa-calendar green"></i>
                        <span>{{ 'dossierHistory.startTime' | translate }}</span>
                        <p-calendar [(ngModel)]="startDate" [showIcon]="false" [showTime]="true" [showSeconds]="true"
                            [showClear]="false" [yearNavigator]="true" [monthNavigator]="true" [yearRange]="'2000:2030'"
                            [view]="'date'" dateFormat="dd/mm/yy" placeholder="--/--/----" (onSelect)="onDateSelect()"
                            appendTo="body" styleClass="date-filter">
                        </p-calendar>
                    </div>

                    <div class="filter-element">
                        <i class="fa fa-calendar green"></i>
                        <span>{{ 'dossierHistory.endTime' | translate }}</span>
                        <p-calendar [(ngModel)]="endDate" [showIcon]="false" [showTime]="true" [showSeconds]="true"
                            [showClear]="false" [yearNavigator]="true" [monthNavigator]="true" [yearRange]="'2000:2030'"
                            [view]="'date'" dateFormat="dd/mm/yy" placeholder="--/--/----" (onSelect)="onDateSelect()"
                            appendTo="body" styleClass="date-filter">
                        </p-calendar>
                    </div>

                    <div class="filter-element min-width">
                        <button pButton label="" class="p-button-rounded p-button-text"
                            [pTooltip]="'dossierHistory.reset' | translate" [tooltipOptions]="tooltipOptions"
                            [disabled]="isResetDisabled" (click)="resetFilterTable()">
                                <span><i class="fa fa-refresh red"></i></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </ng-template>

    <div class="iot-table">
        <p-table
            [value]="historyItems"
            [loading]="loading"
            [paginator]="true"
            [rows]="pageSize"
            [totalRecords]="totalRecords"
            [showCurrentPageReport]="true"
            currentPageReportTemplate="{{ 'dossierHistory.showingEntries' | translate }}"
            (onPage)="onPageChange($event)"
            styleClass="p-datatable-striped"
            [lazy]="true">
            <ng-template pTemplate="header">
                <tr>
                    <th>{{ 'dossierHistory.details' | translate }}</th>
                    <th>{{ 'dossierHistory.timestamp' | translate }}</th>
                    <th style="width: 100px">{{ 'dossierHistory.actions' | translate }}</th>
                </tr>
            </ng-template>

            <ng-template pTemplate="body" let-item>
                <tr>
                    <td>
                        <div class="flex">
                            <i class="fa fa-file-o"></i>
                            <span class="bold">{{item.details}}</span>
                        </div>
                    </td>
                    <td>{{item.timeStamp | iotTimestamp }}</td>
                    <td>
                        <div class="action-buttons">
                            <button pButton
                                icon="fa fa-download"
                                class="p-button-text"
                                [disabled]="dossier.deletionTime"
                                (click)="onDownload($event, item)"
                                pTooltip="{{ 'dossierHistory.download' | translate }}">
                            </button>
                        </div>
                    </td>
                </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="4" class="text-center">{{ 'dossierHistory.noHistoryFound' | translate }}</td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</p-dialog>