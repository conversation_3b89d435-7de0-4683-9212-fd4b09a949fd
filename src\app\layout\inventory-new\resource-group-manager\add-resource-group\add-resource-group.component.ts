import { <PERSON>mpo<PERSON>, OnInit, <PERSON><PERSON>hild, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { CymsidebarService } from 'app/shared/components/cym-sidebar/cym-sidebar.service';
import { ResourceGroupModel } from '../models/add-resource-group.model';
import { ResourceGroupService } from 'app/shared/modules/data-layer/services/resource-group/resource-group.service';
import { ResourceGroup } from 'app/shared/modules/data-layer/models/resource-group';
import { resourceGroupsData } from '../../data/data';

@Component({
  selector: 'app-add-resource-group',
  templateUrl: './add-resource-group.component.html',
  styleUrls: ['./add-resource-group.component.scss']
})
export class AddResourceGroupComponent implements OnInit, OnDestroy {

  resourceGroupAddForm: FormGroup;
  resourceGroupList: { [id: string]: ResourceGroup; } = null;

  resourceGroupAddModel: ResourceGroupModel = new ResourceGroupModel();
  groups = [];

  constructor(private cymSidebarService: CymsidebarService, private resourceGroupService: ResourceGroupService) {
    this.groups = resourceGroupsData;

  }

  ngOnInit() {
    this.setResouceGroupForm();
    this.getResourceGroupAll();
  }

  getResourceGroupAll() {
    this.resourceGroupService.getAll().subscribe((response) => {
      this.resourceGroupList = response;
    });
  }


  cancel() {
    this.cymSidebarService.closeSidebar();
    this.resourceGroupAddForm.reset();
    this.resourceGroupAddModel = new ResourceGroupModel();
  }

  private setResouceGroupForm() {
    this.resourceGroupAddForm = new FormGroup({
      newOrOldResource: new FormControl(null, [Validators.required]),
      selectGroup: new FormControl(null, [Validators.required]),
      selectProfiles: new FormControl(null, [Validators.required]),
    });
  }


  ngOnDestroy() {
    this.resourceGroupAddForm.reset();
    this.resourceGroupAddModel = new ResourceGroupModel();
  }

}
