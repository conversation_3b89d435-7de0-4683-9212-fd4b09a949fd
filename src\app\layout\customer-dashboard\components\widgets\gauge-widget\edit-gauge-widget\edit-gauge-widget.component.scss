@import '~styles/colors';
@import '~styles/skins/skins';

:host {
    .form-group {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 3px;
        position: relative;
        > * {
            border-style: solid;
            border-color: var(--secondary-2);
            border-radius: inherit;     
        }
        .thrColor {
            width: 14px;
            height: 14px;
            border-radius: 7px;
            border-width: 0;
            margin:0;
        }

        .threshold {
            padding: 12px 10px;
            max-width: 115px;
            &.number {
                max-width: 65px;
            }
        }
    }
    .form-item {
        position: relative;
    }
    .add-button {
        position: absolute;
        top: -3px;
        right: 0;
        border: 0;
        line-height: 1rem;
    }
    .group {
        width: 32%;
        position: relative;
    }
}
.default, .blue, .green, .red, .yellow {
    display: inline-block;
    height: 24px;
    width: 24px;
    border-radius: 50%;
    margin-right: 5px;
    cursor: pointer;
}
.default {
    background-color: $widgetDefault;
}
.blue {
    background-color: $widgetBlue;
}
.green {
    background-color: $widgetGreen;
}
.red {
    background-color: $widgetRed;
}
.yellow {
    background-color: $widgetYellow;
}

::ng-deep {
    .thr-option {
        .ui-overlaypanel-content {
            padding: 5px;
            margin: 5px;
            font-size: 1px;
        }
    }
}