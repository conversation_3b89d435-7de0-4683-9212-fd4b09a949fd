import { Audit } from './../models/audit.model';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-audit-table',
  templateUrl: './audit-table.component.html',
  styleUrls: ['./audit-table.component.scss']
})
export class AuditTableComponent implements OnInit {
  @Input() set audits(value: Audit[]) {
    this._audits = value;
  }
  get audits(): Audit[] {
    return this._audits;
  }
  private _audits: Audit[] = [];
  @Input() loading = false;
  @Input() totalRecords = 0;
  @Input() pageIndex: number = 0;
  @Input() pageSize: number = 12;
  @Output() pageChange = new EventEmitter<number>();

  constructor() { }

  ngOnInit(): void {
  }


  onPageChange(event: any): void {
    const pageIndex = event.first / event.rows;
    this.pageChange.emit(pageIndex);
  }


}
