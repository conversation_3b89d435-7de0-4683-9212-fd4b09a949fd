import { Component } from '@angular/core';
import { Widget } from 'app/layout/customer-dashboard/models/widget';

@Component({
  selector: 'app-default-widget',
  templateUrl: './default-widget.component.html',
  styleUrls: ['./default-widget.component.scss']
})
export class DefaultWidgetComponent {
  data: {widgetData: Widget, index: number, widgetSettings?: {[propertyName: string]: string }};
  constructor() { }
}
