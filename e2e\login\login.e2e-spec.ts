import { LoginPage } from './login.po';
import { browser, logging } from 'protractor';
import { MockService } from 'protractor-xmlhttprequest-mock';
import { loginMockSearchTemplates, loginMockSetAccountConfiguration, loginMockgenerateEntitiesReport, loginMockDashboard, loginMockGetDashboardTree, loginMockGetuserdetails, loginMockLogin, loginMockGetStatuses, loginMockResourceGroups, loginMockDashboardCanvas, loginMockGetDashboard } from './login-mock-data';

describe('LoginPage', () => {
  let page: LoginPage;

  beforeEach(() => {
    page = new LoginPage();
    page.navigateTo();
  });

  it('password should be empty', () => {
    expect<any>(page.getInputPasswordValue()).toEqual('');
  });

  it('good login should redirect', async () => {

      var express = require('express');
      var SignalRJS = require('signalrjs');
      
      var signalR = SignalRJS();
      
      var server = express();
      var cors = require('cors')
      server.use(cors())
      
      server.get(/^\/token\/dashboardCanvas?.*$/, function (req, res) {
        res.send('')
      })
      server.use(signalR.createListener())
      server.listen(24687);
      
      
      await MockService.setup(browser);
      await MockService.addMock('login', {
        path: '/token/login',
        response: {status: 200, data: JSON.stringify(loginMockLogin)}
      });
      await MockService.addMock('getuserdetails', {
        path: '/token/userData/getUserDetails',
        response: {status: 200, data: JSON.stringify(loginMockGetuserdetails)}
      });
      await MockService.addMock('getDashboardsTree', {
        path: '/token/dashboard/getDashboardsTree',
        response: {status: 200, data: JSON.stringify(loginMockGetDashboardTree)}
      });
      await MockService.addMock('dashboard', {
        path: '/token/dashboard/getDashboard/06181e60-130a-af2d-6c1f-18c6b7ce6766',
        response: {status: 200, data: JSON.stringify(loginMockDashboard)}
      });
      await MockService.addMock('SearchTemplates', {
        path: '/token/reports/SearchTemplates',
        response: {status: 200, data: JSON.stringify(loginMockSearchTemplates)}
      });
      await MockService.addMock('generateEntitiesReport', {
        path: '/token/reports/generateEntitiesReport/40',
        response: {status: 200, data: JSON.stringify(loginMockgenerateEntitiesReport)}
      });
      await MockService.addMock('SetAccountConfiguration', {
        path: '/token/userData/SetAccountConfiguration',
        response: {status: 200, data: JSON.stringify(loginMockSetAccountConfiguration)}
      });
      await MockService.addMock('getStatuses', {
        path: '/token/userData/getStatuses',
        response: {status: 200, data: JSON.stringify(loginMockGetStatuses)}
      });
      await MockService.addMock('resourceGroups', {
        path: '/token/userData/ResourceGroups',
        response: {status: 200, data: JSON.stringify(loginMockResourceGroups)}
      });
      await MockService.addMock('getDashboard', {
        path: '/token/dashboard/getDashboard',
        response: {status: 200, data: JSON.stringify(loginMockGetDashboard)}
      });

      let pwd = 'petre'

      let p1 = page.setUserName('petre');
      let p2 = page.setPassword(pwd);

      Promise.all([p1, p2]).then(_ => {
        expect<any>(page.getInputPasswordValue()).toEqual(pwd);
        
        page.doLogin().then(_ => {

          browser.driver.getCurrentUrl().then(function(url) {
            expect(url.endsWith('/login')).toBeFalsy();
          });
        });
      });
  });

  afterEach(async () => {
    //Assert that there are no errors emitted from the browser
    const logs = await browser.manage().logs().get(logging.Type.BROWSER);
    expect(logs).not.toContain(jasmine.objectContaining({
      level: logging.Level.SEVERE,
    } as logging.Entry));
  });
});
