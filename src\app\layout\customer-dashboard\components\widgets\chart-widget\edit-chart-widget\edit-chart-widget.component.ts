import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { ChartWidget } from 'app/layout/customer-dashboard/models/chart-widget';
import { DefaultWidgetEditorComponent } from '../../default-widget/default-widget-editor.component';
import { SelectItem } from 'primeng/api';
import { Guid } from 'app/shared/enum/guid';
import { LineChartDataType } from 'app/layout/customer-dashboard/enums/line-chart-data.enum';
import { Subject, Subscription, EMPTY } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';
import { ResourceGroupService } from 'app/shared/modules/data-layer/services/resource-group/resource-group.service';
import { DashboardUtilsService } from 'app/layout/customer-dashboard/services/dashboard-utils.service';
import { ResourceTriggersService } from 'app/shared/modules/data-layer/services/resource-triggers/resource-triggers.service';
import { ResourceTriggerGroup } from 'app/shared/modules/data-layer/models/resource-trigger-group';
import { ResourceGroup } from 'app/shared/modules/data-layer/models/resource-group';
import * as moment from 'moment';
import { RuleEvent } from 'app/shared/modules/data-layer/models/rule-event';
import { ResourceState } from 'app/shared/modules/data-layer/enum/resource/resource-state.enum';
import { LineChartDisplayResults } from 'app/layout/customer-dashboard/enums/line-chart-display-results.enum';
import { ChartType } from 'app/layout/customer-dashboard/enums/chart-type.enum';
import {RuleEventService} from "app/shared/modules/data-layer/services/rule-event/rule-event.service";
import {DateRangeSearchType,DateUnit} from "app/shared/enum/enum";


@Component({
  selector: 'app-edit-chart-widget',
  templateUrl: './edit-chart-widget.component.html',
  styleUrls: ['./edit-chart-widget.component.scss']
})
export class EditChartWidgetComponent extends DefaultWidgetEditorComponent implements OnInit, OnDestroy {

  private resourceGroupModelStore: {[id: string] : ResourceGroup };
  public resourceGroupArray: ResourceGroup[];

  data: ChartWidget;
  groupList: SelectItem[] = [{label: "customerDashboard.showAllGroups", value: Guid.EMPTY}];
  resourceList: SelectItem[] = [];

  triggersList: SelectItem[] = [];
  eventsList: SelectItem[] = [];

  lineChartDataTypeList: SelectItem[] = [
    {label: "customerDashboard.selectDataType", value: Guid.EMPTY},
    {label: "events", value: LineChartDataType.events},
    {label: "triggers", value: LineChartDataType.triggers},
    {label: "status", value: LineChartDataType.status}
  ];

  dateFilterOptions: SelectItem[] = [
    { label: "latest", value: DateRangeSearchType.Latest },
    { label: "date", value: DateRangeSearchType.Date },
    { label: "last", value: DateRangeSearchType.Last },
    { label: "dateRange", value: DateRangeSearchType.Range}
  ];

  dateFilterLastOptions: SelectItem[] = [
    { label: "seconds", value: DateUnit.Seconds },
    { label: "minutes", value: DateUnit.Minutes },
    { label: "hours", value: DateUnit.Hours },
    { label: "days", value: DateUnit.Days},
    { label: "weeks", value: DateUnit.Weeks },
    { label: "months", value: DateUnit.Months }
  ];

  displayResultList: SelectItem[] = [
    {label: "customerDashboard.countResults", value: LineChartDisplayResults.countResults},
    {label: "customerDashboard.resultValue", value: LineChartDisplayResults.resultValue},
    {label: "customerDashboard.groupedValue", value: LineChartDisplayResults.groupedValue}
  ]

  chartTypeOptions: SelectItem[] = [
    {label: "line", value: ChartType.line},
    {label: "bar", value: ChartType.bar}
  ]

  eventsNameData: SelectItem[] = [];
  unitLastValue$ = new Subject<number>();
  subscriptions: Subscription[] = [];
  statusList: SelectItem[] = [];
  constructor(
    private resourceGroupService: ResourceGroupService,
    private dashboardUtilsService: DashboardUtilsService,
    private resourceTriggersService: ResourceTriggersService,
    private ruleService: RuleEventService
  ) {
    super();
  }

  ngOnInit(): void {
    this.initDefaultDates();
    this.returnResourceStatuses();
    this.data = new ChartWidget(this.data);
    this.getData();
    this.getRuleEvents();
    let searchTermSubscription = this.unitLastValue$.pipe(
      debounceTime(500),
      distinctUntilChanged(),
      switchMap(number => {
        this.data.unitLastValue = number;
        this.dashboardUtilsService.setWidgetDataChange(this.data);
        return EMPTY;
      })
    ).subscribe();
    this.subscriptions.push(searchTermSubscription);
  }

  onWidgetDataChange(): void{
    this.getData();
    this.dashboardUtilsService.setWidgetDataChange(this.data);
  }

  getRuleEvents(): void{
   let ruleEventSubscription= this.ruleService.getAll().subscribe((response) => {
      let eventsData: RuleEvent[] = Object["values"](response);
      eventsData.forEach((item) => {
        this.eventsNameData.push({ label: item.name, value: item.identity, disabled: false });
      });
    });
    this.subscriptions.push(ruleEventSubscription);
  }

  initDefaultDates(): void{
    this.data.currentDate = moment().toDate();
    this.data.fromDate = this.data.fromDate ? moment(this.data.fromDate).toDate() : moment().startOf('day').toDate();
    this.data.toDate = this.data.toDate ? moment(this.data.toDate).toDate() : moment().endOf('day').toDate();
  }

  getData(): void{
    this.getResourceGroups();

    switch(this.data.lineChartDataType){
      case LineChartDataType.triggers:
        this.getUserTriggers();
        this.data.fromDate = this.data.fromDate ? new Date(this.data.fromDate) : new Date();
        this.data.toDate = this.data.toDate ? new Date(this.data.toDate) : new Date();
        this.data.currentDate = this.data.currentDate ? new Date(this.data.currentDate) : new Date();
        this.data.selectedResourceStates = [];
      break;
      case LineChartDataType.events:
        this.data.selectedResourceStates = [];
      break;
      default:
        console.error('Cannot find line chart data type value', this.data.lineChartDataType);
      break;
   }
 }

  private returnResourceStatuses(): SelectItem[] {
    for(let state in ResourceState){
        this.statusList.push({label: state, value: state});
    }
    return this.statusList;
  }

  private getResourceGroups(): void {
   let reourceGroupSubscription= this.resourceGroupService.getAll().subscribe(res => {
      this.resourceGroupModelStore = res;
      this.resourceGroupArray = this.returnResourceGroupArray(res);
      this.buildGroupList(res);
      this.buildResourceList(this.resourceGroupArray);
    });
    this.subscriptions.push(reourceGroupSubscription);
  }

  private getUserTriggers(): void {
    let resourceTriggersSubscription = this.resourceTriggersService.getAll().subscribe(res => {
      this.buildTriggersData(res);
    });
    this.subscriptions.push(resourceTriggersSubscription);
  }

  private buildTriggersData(data):void {
    this.triggersList = [];
    let reducer: ResourceTriggerGroup  = data[Guid.EMPTY];
    reducer.Triggers.forEach(trigger => {
      this.triggersList.push({label: trigger.Name, value: trigger.SurpriseCode});
    });
  }

  private returnResourceGroupArray(data): ResourceGroup[]{
    let groupList:ResourceGroup[] = [];
    for (const key in data) {
        groupList.push(data[key]);
    }
    return groupList;
  }

  private buildGroupList(data:{[id:string]:ResourceGroup}): void {
    this.groupList = this.groupList.slice(0, 1);
    for (const key in data) {
        this.groupList.push({label: data[key].name, value: data[key].identity});
    }
  }

  private buildResourceList(data: ResourceGroup[]): void {
    this.resourceList = [];
    data.forEach(resourceGroup => {
      if(this.data.selectedGroupId !== Guid.EMPTY && resourceGroup.identity !== this.data.selectedGroupId){
        return;
      }
      resourceGroup.resources.forEach(resource => {

        if(this.resourceList.findIndex(el => {return el.value === resource.identity;}) === -1){
          this.resourceList.push({label: resource.name, value: resource.identity});
        }
      });
    });
  }

  selectDisplayResult(event: LineChartDisplayResults): void {
    if(!event ){
      event= LineChartDisplayResults.groupedValue;
    }
    this.data.displayResultsMethod = event;
    this.dashboardUtilsService.setWidgetDataChange(this.data);
  }

  onDatePickerValueChange(val: Date, model: string): void {
    switch(model){
      case 'from':
        this.data.fromDate = val;
        break;
      case 'to':
        this.data.toDate = val;
        break;
      case 'current':
        this.data.currentDate = val;
        break;
      default:
        console.error('Cannot find date time value', val);
      break;
    }
    this.dashboardUtilsService.setWidgetDataChange(this.data);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(subscription => {return subscription.unsubscribe();});
  }

}
