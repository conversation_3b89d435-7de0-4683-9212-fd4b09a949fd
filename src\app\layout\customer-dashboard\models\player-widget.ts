import { Widget } from "./widget";
import { DashboardWidget } from "./dashboard-widget.interface";
import { WidgetSize } from "../enums/widget-size.enum";
import { PlayerConfig } from 'app/shared/components/player/player-config.interface';

export class PlayerWidget extends Widget {
    config: PlayerConfig;

    constructor(obj?: DashboardWidget){
        super(obj);
        if(!this.config){
            this.config = {} as PlayerConfig;
        }
    }

    getWidgetSize(): WidgetSize[]{
        return [WidgetSize.medium, WidgetSize.big];
    }
}