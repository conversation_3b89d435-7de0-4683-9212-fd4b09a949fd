import { TriggerTypes } from 'app/shared/enum/trigger-types';
import { ReportCollectOption } from 'app/shared/modules/data-layer/enum/reports/report-collect-options.enum';

export interface ReportsQueryParams {
  selectedReport: string,
    Name: string,
    Type: string,
    TriggerTypes: string[] | number[] | TriggerTypes[],
    EventIds?: string[],
    TextFilter: string,
    DateRangeType: string,
    Value: number,
    DateLastUnit: number,
    Transformation: string,
    From: string,
    To: string,
    ResourceGroup: string,
    Resource: string,
    Identity: string,
    CollectOption: ReportCollectOption, 
    UserSessionId?: string,
    QueryId?: number
}