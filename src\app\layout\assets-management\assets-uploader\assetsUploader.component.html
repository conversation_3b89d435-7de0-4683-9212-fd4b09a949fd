<app-config-uploader #configUploader [ServerKeys]="serverKeys" (JsonData)="onDataUploaded($event)"></app-config-uploader>
<app-modal #summaryModal [title]="'summary'">
    <ng-container ngProjectAs="contentModal">
        <table class="table table-striped">
            <tr>
                <td>{{ 'totalProcessedItems' | translate }}</td>
                <td>{{totalAssets}}</td>
            </tr>
            <tr>
                <td>{{ 'added' | translate }}</td>
                <td>{{assetsAdded}}</td>
            </tr>
            <tr *ngIf='assetsFailed != 0'>
                <td>{{ 'failed' | translate }}</td>
                <td>{{assetsFailed}}</td>
            </tr>
            <tr *ngIf='rowsMissingMandatoryFields != 0'>
                <td>{{ 'missingMandatoryFields' | translate }}</td>
                <td>{{rowsMissingMandatoryFields}}</td>
            </tr>
            <tr *ngIf='rowsWithWrongFieldType != 0'>
                <td>{{ 'wrongFieldType' | translate }}</td>
                <td>{{rowsWithWrongFieldType}}</td>
            </tr>
            <tr *ngIf='assetsAlreadyExist != 0'>
                <td>{{ 'alreadyExist' | translate }}</td>
                <td>{{assetsAlreadyExist}}</td>
            </tr>
        </table>
        <a class="fa fa-download" (click)="downloadErrorsFile()" href="javascript:void(0);" title='{{ "downloadSummary" | translate }}'></a>
    </ng-container>
    <ng-container ngProjectAs="footerModal">
        <button type="button" class="btn btn-primary" (click)="onModalConfirm()">{{ 'ok' | translate }}</button>    
    </ng-container>
</app-modal>