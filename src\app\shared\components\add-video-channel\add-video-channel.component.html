<div class="camera-channels-inner" *ngIf="filteredCameras" [ngClass]="{'has-actions': showActions}">
  <div class="header-section">
    <h2 class="main-title">{{ 'vmsPageTitle' | translate }}</h2>
  </div>

  <div class="search-section">
    <div class="search-box">
      <i class="fa fa-search search-icon"></i>
      <input type="text"
             name="filter"
             [(ngModel)]="searchText"
             (ngModelChange)="searchTerm$.next($event)"
             placeholder="{{ 'search' | translate }}" />
    </div>

    <div class="status-filter">
      <button type="button"
              *ngFor="let filter of showBooleanCameras; let i = index"
              (click)="toggleFilterButtons(i)"
              [ngClass]="{'active': filter.show}"
              class="status-btn">
        <i class="fa" [ngClass]="{'fa-check-circle': filter.show, 'fa-circle-o': !filter.show}"></i>
        {{filter.displayText | translate}}
      </button>
    </div>
  </div>

  <div class="content-section">
    <div class="section-toggle">
      <button type="button" class="toggle-btn" (click)="showGroups()"
        [ngClass]="{'active': showResourceGroups}">
        <span>{{ 'selectFromResourceGroup' | translate }}</span>
        <i class="fa {{showResourceGroups ? 'fa-chevron-up' : 'fa-chevron-down'}}"></i>
      </button>
    </div>

    <div class="cameras-grid" [class.hidden]="showResourceGroups">
      <div class="camera-card" *ngFor="let camera of filteredCameras | keyvalue | searchFilter: searchText"
           (click)="onCameraSelect(camera.value)"
           pDraggable="vmsCameraDrag"
           (onDragStart)="onDragStartCamera(camera.value)">
        <div class="camera-status {{camera.value.status}}">
          <i class="fa fa-video-camera"></i>
        </div>
        <div class="camera-info">
          <h3>{{ camera?.value?.name }}</h3>
          <span class="status-badge" [ngClass]="camera.value.status">
            {{camera.value.status}}
          </span>
        </div>
        <div class="selection-indicator" *ngIf="selectedChannelId"
             [ngClass]="{'selected': selectedChannelId === camera.value.identity}">
        </div>
      </div>
    </div>

    <div class="section-toggle">
      <button type="button" class="toggle-btn" (click)="toggleChannelTours()"
        [ngClass]="{'active': showChannelTours}">
        <span>{{ 'selectChannelTour' | translate }}</span>
        <i class="fa {{showChannelTours ? 'fa-chevron-up' : 'fa-chevron-down'}}"></i>
      </button>
    </div>

    <div class="channel-tours" [class.hidden]="!showChannelTours">
      <div class="cameras-grid">
        <div class="camera-card"
             *ngFor="let channelTour of channelTours"
             (click)="onChannelTourSelect(channelTour.endId.toString())"
             pDraggable="vmsCameraDrag">
          <div class="camera-status">
            <i class="fa fa-film"></i>
          </div>
          <div class="camera-info">
            <h3>{{channelTour.endName}}</h3>
            <span class="status-badge">{{ 'tour' | translate }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="video-wall-settings" *ngIf="showVideoWallSettings">
      <div class="section-toggle">
        <button type="button" class="toggle-btn" (click)="toggleVideoWallSettings()"
          [ngClass]="{'active': showVideoWallSettings}">
          <span>{{ 'videoWallSettings' | translate }}</span>
          <i class="fa" [ngClass]="showVideoWallSettings ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
        </button>
      </div>
      <div class="settings-content" [class.hidden]="!showVideoWallSettings">
        <div class="setting-group">
          <label>{{ 'layout' | translate }}</label>
          <div class="layout-options">
            <button type="button"
                    *ngFor="let layout of videoWalllayouts"
                    (click)="selectLayout(layout[1].id)"
                    [ngClass]="{'active': selectedLayout === layout[1].id}">
              {{layout[1].name}}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="footer-section">
    <div class="actions-wrapper" *ngIf="showActions">
      <button type="button" class="btn-cancel" (click)="cancelAction()">
        <i class="fa fa-times"></i>
        {{ 'cancel' | translate }}
      </button>
    </div>
  </div>
</div>
