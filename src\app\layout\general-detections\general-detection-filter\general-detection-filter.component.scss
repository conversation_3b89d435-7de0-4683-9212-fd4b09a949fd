$red: #FF0000;
$green: #00D600;
$textColor: #808381;

.green  {
    color: $green;
}

.red  {
    color: $red;
}

.bold {
    font-weight: bold;
}

.filters-container {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.filters-wrapper {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 20px;
    align-items: center;
}

.title {
    font-size: 20px;
    color: #3a3a3a;
    font-weight: 600;
    white-space: nowrap;
}

.filters-content {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr auto;
    width: 100%;
    gap: 10px;
    align-items: center;
}

.filter-element-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    text-align: center;
}

.filter-element {
    display: flex;
    align-items: center;
    background: #F2F6F3;
    padding: 5px;
    border-radius: 8px;
    gap: 5px;
    width: 100%;
}

.p-inputtext {
    &:focus {
        box-shadow: 0 0 0 0.2rem rgba($green, 0.5);
        border: 0px;
    }
    border: 1px solid #E4ECE6;
}

.p-inputtext,
    p-dropdown,
    p-calendar {
    flex: 1;
    font-weight: 500;
    background: #F2F6F3;
}

:host ::ng-deep .p-dropdown {
    background: #F2F6F3;
    border: 1px solid #E4ECE6;
    font-weight: 500;
    .p-dropdown-label,
    .p-dropdown-trigger{
        color: $textColor;
    }
}

.filter-element i,
.filter-element img {
    font-size: 1.1rem;
    padding: 0px 5px 0px 5px;
}


:host ::ng-deep {

    p-splitbutton {
        .p-splitbutton-defaultbutton, .p-splitbutton-menubutton {
            background: $green;
            border-color: $green;
            
        }
        .p-button:enabled:hover{
            background: $textColor;
            border-color: $textColor;
        }
    }
    
    .p-calendar {
       width: 100%;
       border: 1px solid #E4ECE6;
       .p-inputtext {
           background: transparent;
           border: 0px;
       }
       &:focus {
           box-shadow: 0 0 0 0.2rem $green
       }
   }

   // Override PrimeNG button styles to ensure perfect icon centering
   .action-btn {
       .p-button-label {
           display: none !important;
       }

       .p-button-icon {
           margin: 0 !important;
           padding: 0 !important;
       }

       &.p-button-text {
           padding: 0 !important;
       }
   }
}

// Action buttons styling
.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
    background: transparent !important;
    padding: 0 !important;

    .action-btn {
        background: #E4ECE6;
        border: 1px solid #D9D9D9;
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 !important;
        margin: 0;

        &:hover:not(:disabled) {
            background: $green;
            color: white;
            border-color: $green;
        }

        &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        i {
            font-size: 1rem;
            margin: 0 !important;
            padding: 0 !important;
            line-height: 1;
            display: block;
            text-align: center;
            vertical-align: middle;
        }

        &:focus {
            box-shadow: 0 0 0 0.2rem rgba($green, 0.25);
        }

        &.reset-btn {
            color: $red;

            &:hover:not(:disabled) {
                background: $red;
                color: white;
                border-color: $red;
            }

            &:focus {
                box-shadow: 0 0 0 0.2rem rgba($red, 0.25);
            }
        }

        &.export-btn {
            color: $green;
        }
    }
}


@media (max-width: 1200px) {
    .filters-content {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .filters-wrapper {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        justify-content: flex-start;
        margin-top: 10px;
    }
}
