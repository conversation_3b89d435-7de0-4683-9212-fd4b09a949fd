import { Component, OnInit, Input, OnChanges, SimpleChanges, Output, EventEmitter } from '@angular/core';
import { DashboardTreeItem } from '../../models/dashboard-tree-item.interface';
import { NavigationService, Pages } from 'app/shared/services/navigation.service';
import { DashboardUtilsService } from '../../services/dashboard-utils.service';
import { Dashboard } from 'app/shared/modules/data-layer/models/dashboard';
import { DashboardPath } from '../../models/dashboard-path';
import { Guid } from 'app/shared/enum/guid';
import { AuthService } from 'app/shared/services/auth.service';


@Component({
  selector: 'app-dashboard-breadcrumb',
  templateUrl: './dashboard-breadcrumb.component.html',
  styleUrls: ['./dashboard-breadcrumb.component.scss']
})
export class DashboardBreadcrumbComponent implements OnInit, OnChanges {
  @Input() dashboardsTree: Dashboard[];
  @Input() selectedDashboardId: string;
  @Output('onSelectDashboard') onSelectDashboard: EventEmitter<Guid> = new EventEmitter<Guid>();
  public breadcrumbTreePath: DashboardPath[];
  public breadcrumbTree: Dashboard;

  constructor(
    private navigationService: NavigationService,
    private dashboardUtilsService: DashboardUtilsService,
    private authService:AuthService
  ) { }

  ngOnInit() {
    this.buildBreadcrumbs();  
  }

  ngOnChanges(changes: SimpleChanges){
    if((changes.dashboardsTree && changes.dashboardsTree.currentValue) || (changes.selectedDashboardId && changes.selectedDashboardId.currentValue)){
      this.buildBreadcrumbs();
    }
  }

  private buildBreadcrumbs(): void {
    this.breadcrumbTreePath = this.dashboardUtilsService.buildPathTree(this.dashboardsTree.map(el => new DashboardPath(el)));
    this.breadcrumbTree = this.dashboardUtilsService.buildBreadcrumbTree(this.breadcrumbTreePath, this.selectedDashboardId);
  }

  navigateTo(dashboardTreeItem: DashboardTreeItem): void {
    let dashboardId = Guid.parse(dashboardTreeItem.data);
    if (this.authService.isAllowedPage(Pages.dashboard))
    {
      this.navigationService.navigate(Pages.dashboard, { dashboard: dashboardTreeItem.data });
    }
    else
    {
      throw new Error("Dashboard feature is disabled");
    }
  }

  selectDashboard(dashboardId: Guid): void{
    this.onSelectDashboard.next(dashboardId);
  }
    
}
