import { Component, EventEmitter, Input, Output, OnInit, OnDestroy } from '@angular/core';
import { GeneralDetection } from "../../../shared/modules/data-layer/models/general-detections/general-detection";
import { Subject, Subscription } from 'rxjs';
import { debounceTime, filter, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-notification-list',
  templateUrl: './notification-list.component.html',
  styleUrls: ['./notification-list.component.scss']
})
export class NotificationListComponent implements OnInit, OnDestroy {
  @Input() alarms: GeneralDetection[] = [];
  @Input() totalAlarmRecords = 0;
  @Input() alarmPageIndex = 0;

  @Input() alerts: GeneralDetection[] = [];
  @Input() totalAlertRecords = 0;
  @Input() alertPageIndex = 0;

  @Input() loading = false;
  @Input() alertLoading = false;
  @Input() pageSize = 10;

  @Output() alarmsLazyLoad = new EventEmitter<number>();
  @Output() alertsLazyLoad = new EventEmitter<number>();
  @Output() rowSelect = new EventEmitter<GeneralDetection>();

  
  private destroy$ = new Subject<void>();
  private alarmsScroll$ = new Subject<Event>();
  private alertsScroll$ = new Subject<Event>();
  private subscriptions: Subscription[] = [];

  constructor() { }

  ngOnInit(): void {
    this.setupScrollHandlers();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }


  private setupScrollHandlers(): void {
    const alarmsSubscription = this.alarmsScroll$
      .pipe(
        debounceTime(200),
        filter(event => this.isScrolledToBottom(event.target as HTMLElement)),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        if (this.alarms.length < this.totalAlarmRecords) {
          this.alarmPageIndex++;
          this.alarmsLazyLoad.emit(this.alarmPageIndex);
        }
      });

    const alertsSubscription = this.alertsScroll$
      .pipe(
        debounceTime(200),
        filter(event => this.isScrolledToBottom(event.target as HTMLElement)),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        if (this.alerts.length < this.totalAlertRecords) {
          this.alertPageIndex++;
          this.alertsLazyLoad.emit(this.alertPageIndex);
        }
      });

    this.subscriptions.push(alarmsSubscription, alertsSubscription);
  }


  private isScrolledToBottom(element: HTMLElement): boolean {
    return element.scrollHeight - element.scrollTop - element.clientHeight < 50;
  }


  onScroll(event: Event, type: 'alarms' | 'alerts'): void {
    if (type === 'alarms') {
      this.alarmsScroll$.next(event);
    } else {
      this.alertsScroll$.next(event);
    }
  }


  onRowClick(item: GeneralDetection): void {
    this.rowSelect.emit(item);
  }
}