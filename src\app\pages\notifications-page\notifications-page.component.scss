$red: #FF0000;
$green: #00D600;
$textColor: #808381;
$tableColor: #667085;

.green  {
    color: $green;
}

.red  {
    color: $red;
}

.bold {
    font-weight: bold;
}

:host {
    display: block;
    width: 100%;
}


.entity-id {
    border: 1px solid #667085;
    padding: 5px 10px;
    border-radius: 8px;
    max-width: 100px;
    text-align: center;
}

.loading-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 1000;
    border-radius: 8px;
}

.loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.loading-text {
    margin-top: 1rem;
    font-size: 1rem;
    font-weight: 500;
    color: $green;
}

:host ::ng-deep {
    .iot-table{
        background: #F2F6F3;
        border-radius: 24px;
        padding: 15px;

        .p-datatable {
            width: 100%;
            background: #ffffff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12);
            border-radius: 8px;
            position: relative;
            z-index: 1;
            height: 100%;
            display: table;
            table-layout: fixed;

            .p-datatable-header {
                border: none;
                padding: 1rem;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }

            .message-cell {
                padding: 0.3rem 0.5rem !important;
                word-break: normal;
                overflow: visible;
            }

            .wrap-text {
                white-space: normal;
                word-wrap: break-word;
                word-break: normal;
                max-width: 100%;
                display: block;
                line-height: 1.4;
                max-height: 5rem;
                overflow-y: auto;
            }

            .error-text {
                color: #f44336;
                font-size: 0.8rem;
                font-style: italic;
            }

            .p-datatable-thead th {
                position: sticky;
                top: 0;
                z-index: 1;
                background-color: #f8f9fa;
            }

            .p-datatable-tbody > tr > td {
                padding: 0.5rem 0.5rem;
                font-size: 0.9rem;
                vertical-align: top;
                word-break: normal;
                overflow: visible;
                border-bottom: 1px solid #e9ecef;
                line-height: 1.5;
            }

            .p-datatable-tbody > tr {
                height: auto;
                min-height: 3rem;
            }

            .p-datatable-tbody > tr:hover {
                background-color: #f8f9fa;
            }

            .p-datatable-thead > tr > th {
                padding: 0.4rem 0.5rem;
                font-size: 0.95rem;
                vertical-align: middle;
                font-weight: bold;
            }

            .p-paginator {
                position: sticky;
                bottom: 0;
                background: white;
                z-index: 2;
            }

            .p-datatable-thead > tr > th {
                border: none;
                border-bottom: 1px solid #e0e0e0;
                color: $tableColor;
                font-weight: 500;
                padding: 1rem;
                font-size: 0.875rem;

                &:first-child {
                    border-top-left-radius: 8px;
                }

                &:last-child {
                    border-top-right-radius: 8px;
                }

                .p-checkbox {
                    width: 1.25rem;
                    height: 1.25rem;

                    .p-checkbox-box {
                        border-radius: 4px;
                        border: 2px solid #6c757d;

                        &.p-highlight {
                            background: $green;
                            border-color: $green;
                        }
                    }
                }
            }

            .p-datatable-tbody > tr {
                border: 1px solid #EAECF0;

                &:nth-child(even) {
                    background: #F9FAFB;
                }

                &.p-highlight {
                    background: #e8f5e9 !important;
                }

                > td {
                    padding: 0.875rem 1rem;
                    font-size: 0.875rem;
                    color: $tableColor;
                    text-align: left;
                    .p-checkbox {
                        width: 1.25rem;
                        height: 1.25rem;

                        .p-checkbox-box {
                            border-radius: 4px;
                            border: 2px solid #6c757d;

                            &.p-highlight {
                                background: $green;
                                border-color: $green;
                            }
                        }
                    }
                }

                &:hover {
                    background: #f0f7ff !important;
                }
            }

            .p-paginator {
                background: #ffffff;
                border: none;
                padding: 1rem;
                display: flex;
                align-items: center;
                justify-content: center;
                border-top: 1px solid #e0e0e0;
                position: relative;
                border-bottom-left-radius: 8px;
                border-bottom-right-radius: 8px;

                .p-paginator-left-content {
                    position: absolute;
                    left: 1rem;
                    font-size: 0.875rem;
                    color: #6c757d;
                }

                .p-paginator-current {
                    display: none;
                }

                .p-paginator-pages {
                    display: flex;
                    gap: 0.25rem;

                    .p-paginator-page {
                        min-width: 2rem;
                        height: 2rem;
                        margin: 0;
                        border-radius: 4px;
                        color: #6c757d;
                        font-weight: 500;
                        border: none;
                        background: #E4ECE6;
                        font-size: 0.875rem;

                        &.p-highlight {
                            background: $green;
                            color: #ffffff;
                        }

                        &:hover:not(.p-highlight) {
                            background: $green;
                            color: #ffffff;
                        }
                    }
                }

                .p-paginator-first,
                .p-paginator-prev,
                .p-paginator-next,
                .p-paginator-last {
                    width: 2rem;
                    height: 2rem;
                    margin: 0 0.25rem;
                    border-radius: 4px;
                    color: $green;
                    border: none;
                    background: #E4ECE6;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    &:hover {
                        background: $green;
                        color: #ffffff;
                    }

                    &.p-disabled {
                        opacity: 0.5;
                        background: #E4ECE6;
                        color: #6c757d;
                    }
                }
            }
        }
    }

    .custom-spinner {
        .p-progress-spinner-circle {
            stroke: $green !important;
            stroke-linecap: round;
        }
    }
}


