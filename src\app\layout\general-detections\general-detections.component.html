
<app-general-detection-filter 
    [routeEventId]="routeEventId"
    (detectionsFilter)="onDetectionsFilter($event)">
</app-general-detection-filter>

<div class="iot-table">
    <div class="general-detection-container" [ngClass]="detectionDetailsTitle !== '' && detectionDetailsTitle ? 'divided' : 'full' ">
        <general-detections-table 
            [data]="data"
            [totalRecords]="totalRecords"
            [pageIndex]="currentPageIndex"
            [pageSize]="pageSize"
            [loading]="loading"
            (selectedRowDetection)="onSelectedRowDetection($event)"
            [(selectedRow)]="selectedDetection"
            (pageChange)="onPageChange($event)">
        </general-detections-table>

        <app-general-detections-details *ngIf="detectionDetailsTitle !== ''"
            [title]="detectionDetailsTitle"
            [data]="detectionDetails"
            [totalRecords]="totalDetailsRecords"
            [pageIndex]="detailsCurrentPageIndex"
            [pageSize]="detailsPageSize"
            [loading]="loadingDetails"
            (closeDetails)="onCloseDetails()">
        </app-general-detections-details> 
    </div>
</div>
