<form [formGroup]="entityForm" (ngSubmit)="onSubmit()">
    <div class="container">
        <div class="form-group">
            <div class="col text-center">
                <img class="thumbnail rounded" [src]="imgSrc">
            </div>
            <div class="col text-center">
                <app-file-uploader #uploader mode="basic" customUpload="true" auto="true" (uploadHandler)="onUpload($event)" (onSelect)="onSelectFile($event)"
                    chooseFilesLabel="choose"></app-file-uploader>
            </div>
        </div>
        <div class="form-group row">
            <div class="col-6">
                <label for="firstName" class="col-form-label">{{ "firstName" | translate }}*</label>
                <input type="text" id="firstName" class="form-control" placeholder='{{ "firstName" | translate }}' formControlName="firstName">
            </div>
            <div class="col-6">
                <label for="lastName" class="col-form-label">{{ "lastName" | translate }}*</label>
                <input type="text" id="lastName" class="form-control" placeholder='{{ "lastName" | translate }}' formControlName="lastName">
            </div>
        </div>
        <div class="form-group row">
            <div class="col-6">
                <label for="username" class="col-form-label">{{ "username" | translate }}</label>
                <input type="text" id="username" class="form-control" placeholder='{{ "username" | translate }}' formControlName="username">
            </div>
            <div class="col-6">
                <label for="id" class="col-form-label">{{ "id" | translate }}</label>
                <input type="text" id="id" class="form-control" placeholder='{{ "id" | translate }}' formControlName="id">
            </div>
        </div>
        <div class="form-group row">
            <div class="col-6">
                <label for="phone" class="col-form-label">{{ "phone" | translate }}</label>
                <input type="tel" id="phone" class="form-control" placeholder='{{ "phone" | translate }}' formControlName="phone">
            </div>
            <div class="col-6">
                <label for="email" class="col-form-label">{{ "email" | translate }}</label>
                <input type="email" id="email" class="form-control" placeholder='{{ "email" | translate }}' formControlName="email">
                <span *ngIf="!entityForm.get('email').valid && entityForm.get('email').touched" class="help-block">{{ 'invalidEmail' | translate }}</span>
            </div>
        </div>
        <div class="form-group row" [class.invalid-dates]="invalidDates">
            <div class="col-6 ">
                <label for="startDate" class="col-form-label">{{ "startDate" | translate }}</label>
                <app-datetime-picker appendTo="body" class="datetime-block" [(date)]="startDate" [maxDate]="maxDate" placeholder='{{ "startDate" | translate }}'
                    [showDateInForm]="true" [showTime]="false" (valueChange)="onSatrtDateChange($event)"></app-datetime-picker>

            </div>
            <div class="col-6">
                <label for="expirationDate"  class="col-form-label">{{ "expirationDate" | translate }}</label>
                <app-datetime-picker appendTo="body" class="invalidDates datetime-block" [(date)]="expirationDate" [maxDate]="maxDate" placeholder='{{ "expirationDate" | translate }}'
                    [showDateInForm]="true" [showTime]="false" (valueChange)="onExpirationDateChange($event)"></app-datetime-picker>

            </div>
            <span *ngIf="invalidDates" class="help-block">{{ 'startDateShouldBeBeforeExpirationDate' | translate }}</span>
        </div>
        <div class="panel panel-default container" formGroupName="associatedDevices">
            <div class="row panel-body">
                <div class="col-10">
                    <h6 class="center-vertical-txt">{{ 'associatedDevices' | translate }}</h6>
                </div>
                <div class="col-2 text-right center-vertical-btn">
                    <button type="button" #button class="btn btn-secondary btn-action associatedDevicesAdd" (click)="onAddAssociatedDevice()">
                        <i class="fa fa-plus" aria-hidden="true" ></i>
                      </button>
                </div>
            </div>
            <div formArrayName="devices">
                <div class="form-group panel-body" *ngFor="let device of devices.controls; let i = index;">
                    <div class="row" formGroupName="{{i}}">
                        <div class="col-1">
                            <button [disabled]="defaultDevice != null  && defaultDevice.deviceId && !i" type="button" #button class="btn btn-secondary btn-action" (click)="onRemoveDevice(i)">
                                <i class="fa fa-trash-o" aria-hidden="true" ></i>
                              </button>
                        </div>
                        <div class="col-5">
                            <select class="custom-select form-control custom-select-sm" [ngClass]="{'dropdown-rtl': isRtl}" formControlName="name"
                                (ngModelChange)="onChange($event, i)">
                                <option class="form-control" [disabled]="true">{{ 'deviceName' | translate}}</option>
                                <option class="form-control" *ngFor="let d of serverDevices" [value]="d.endName">{{d.endName}}</option>
                            </select>
                        </div>
                        <div class="col-5">
                            <input type="text" formControlName="value" class="form-control" placeholder="{{ 'deviceValue' | translate }}"
                                [value]=device.controls.value.value *ngIf="!isCorticaDevice(device)">

                            <app-file-uploader #photoUploaders  mode="basic" customUpload="true" auto="true"
                                (onSelect)="onSelectPhoto($event, i)"
                                chooseFilesLabel="choose" *ngIf="isCorticaDevice(device)"></app-file-uploader>

                            <input type="text" formControlName="photoValue"
                                [value]=device.controls.value.photoValue *ngIf="isCorticaDevice(device)" style="display: none">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default container" formGroupName="associatedResourceGroups">
            <div class="row panel-body">
                <div class="col-10">
                    <h6 class="center-vertical-txt">{{ 'associatedResourceGroups' | translate }}</h6>
                </div>
            </div>
            <div class="form-group panel-body form-check" formArrayName="groups">
                <div *ngFor="let group of resources.controls; let i = index">
                    <div formGroupName="{{i}}">
                        <input type="checkbox" class="form-check-input" [checked]=group.controls.value.value (change)="onToggleCheckbox(!group.controls.value.value, i)">
                        <label class="form-check-label">{{ group.controls.name.value }}</label>
                    </div>
                </div>
            </div>
        </div>
        <p class="font-italic">{{ 'fieldsMarkedWithAreMandatory' | translate }}</p>
        <div class="form-group row rtl-support">
            <div class="col" [ngClass]="isRtl ? 'text-right' : 'text-left'">
                <button type="button" [disabled]="!entity.Identity" class="btn btn-danger" (click)="onDelete()">{{ "delete" | translate }}</button>
            </div>
            <div class="col text-center">
                <button type="button" class="btn btn-secondary" (click)="onCancel()">{{ "cancel" | translate }}</button>
            </div>
            <div class="col" [ngClass]="isRtl ? 'text-left' : 'text-right'">
                <button type="submit" [disabled]="entityForm.invalid || invalidDates" class="btn btn-success">{{ "save" | translate }}</button>
            </div>
        </div>
    </div>
</form>
