import { Component, Input, Output, EventEmitter, ViewChild } from '@angular/core';
import { DossierHistoryComponent } from '../dossier-history/dossier-history.component';
import { DossierAction } from '../enum/dossier-action.enum';
import { Subscription } from 'rxjs';
import { Dossier } from '../models/dosier.model';
import { NavigationService, Pages } from '../../../shared/services/navigation.service';
import { MessageService } from 'primeng/api';
import { TranslateService } from '@ngx-translate/core';
import { ToastTypes } from '../../../shared/enum/toast-types';
import { IotTimestampPipe } from '../../../shared/pipes/to-time.pipe';

@Component({
  selector: 'app-dossier-table',
  templateUrl: './dossier-table.component.html',
  styleUrls: ['./dossier-table.component.scss'],
  providers: [IotTimestampPipe]
})
export class DossierTableComponent {
  @Input() dossiers: Dossier[] = [];
  @Input() loading = false;
  @Input() showActions = true;
  @Input() totalRecords = 0;
  @Input() pageIndex: number = 0;
  @Input() pageSize: number = 12;
  @Output() toggleAutoDelete = new EventEmitter<any>();
  @Output() toggleReadOnly = new EventEmitter<any>();
  @Output() uploadDossier = new EventEmitter<Dossier>();
  @Output() downloadDossier = new EventEmitter<any>();
  @Output() viewHistory = new EventEmitter<any>();
  @Output() viewDetails = new EventEmitter<any>();
  @Output() pageChange = new EventEmitter<number>();
  @ViewChild('historyDialog') historyDialog!: DossierHistoryComponent;

  selectedDossiers: Dossier[] = [];
  selectedDossier: Dossier;
  dossierAction = DossierAction;
  subscriptions: Subscription[] = [];

  constructor(
    private navigationService: NavigationService,
    private iotTimestampPipe: IotTimestampPipe,
    private messageService: MessageService,
    private translateService: TranslateService) {}


  onPageChange(event: any): void {
    const pageIndex = event.first / event.rows;
    this.pageChange.emit(pageIndex);
  }


  navigateToDetections(dossier: Dossier): void {
    const eventId = dossier.eventId;
    if(eventId) {
      this.navigationService.navigate(Pages.generalDetections, { eventId });
    }
  }

  onActionClick(action: DossierAction, dossier: Dossier): void {
    this.selectedDossier = dossier;
    if (dossier?.deletionTime && action !== DossierAction.History) {
      const dateFormatted = this.iotTimestampPipe.transform(dossier.deletionTime);
        this.messageService.add({
            severity: 'info',
            summary: this.translateService.instant(ToastTypes.info),
            detail: this.translateService.instant( 'dossierTable.alreadyDeleted', { timestamp: dateFormatted })
        });
        return;
    }

    switch(action) {
      case DossierAction.Search:
        this.navigateToDetections(dossier);
        break;
      case DossierAction.Notification:
        break;
      case DossierAction.Lock:
        this.toggleReadOnly.emit(dossier);
        break;
      case DossierAction.Upload:
        this.uploadDossier.emit(dossier);
        break;
      case DossierAction.Download:
        this.downloadDossier.emit(dossier);
        break;
      case DossierAction.History:
        this.viewHistory.emit(dossier);
        break;
      case DossierAction.Details:
        this.viewDetails.emit(dossier);
        break;
      case DossierAction.Menu:
        this.toggleAutoDelete.emit(dossier);
        break;
        default:
        break;
    }
  }
}