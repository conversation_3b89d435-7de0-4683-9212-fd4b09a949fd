<ng-container>
    <div class="container entity" >
        <header class="header" (mouseenter)="showImage(entity, $event)" (mouseleave)="hideImage()">
            <img [src]="parseSafeUrl(entity.images[0])" alt="Profile Image" class="image" />
            <div class="user-details">
                <h2 class="label">NAME</h2>
                <p class="value">{{ entity.entityInfo.FirstName }} {{ entity.entityInfo.LastName }}</p>
            </div>
        </header>
        <section class="details">
            <h3 class="sr-only">User Details</h3>
            <div class="detail-item">
                <span class="label">COUNTRY</span>
                <p class="value">Romania</p>
            </div>
            <div class="detail-item">
                <span class="label">CONFIDENCE</span>
                <p class="value">{{ entity.confidence }}%</p>
            </div>
            <div class="detail-item">
                <span class="label">RESOURCE NAME</span>
                <p class="value">{{ entity.resourceName }}</p>
            </div>
            <div class="detail-item">
                <span class="label">DEVICE ID</span>
                <p class="value">{{ entity.deviceId }}</p>
            </div>
            <div class="detail-item">
                <span class="label">EVENT ID</span>
                <p class="value">{{ entity.eventId }}</p>
            </div>
            <div class="detail-item">
                <span class="label">ENTITY</span>
                <p class="value">{{ entity.entity }}</p>
            </div>
            <div class="detail-item">
                <span class="label">ENTITY VALUE</span>
                <p class="value">{{ entity.entityValue }}</p>
            </div>
            <div class="detail-item">
                <span class="label">TRIGGER CODE</span>
                <p class="value">{{ entity.triggerCode }}</p>
            </div>
            <ng-container *ngIf="parseDescription(entity.description) as desc">
                <ng-container *ngIf="desc.extra_data && desc.extra_data.gender">
                    <div class="detail-item">
                        <span class="label">GENDER</span>
                        <p class="value">{{ desc.extra_data.gender }}</p>
                    </div>
                </ng-container>
                <ng-container *ngIf="desc.extra_data && desc.extra_data.min_age && desc.extra_data.max_age">
                    <div class="detail-item">
                        <span class="label">AGE RANGE</span>
                        <p class="value">{{ desc.extra_data.min_age }} - {{ desc.extra_data.max_age }}</p>
                    </div>
                </ng-container>
            </ng-container>
        </section>
        <aside class="icon-set">
            <h3 class="sr-only">Icons</h3>

            <img src="../../../assets/images/entity/add_edit.png" alt="Icon 2" class="icon" (click)="onAddEntity()" />
            <img src="../../../assets/images/entity/delete.png" alt="Icon 3" class="icon"  />
            <img src="../../../assets/images/entity/toggle_dropdown.png" alt="Icon 4" class="icon" (click)="toggleExpand()" />
        </aside>
    </div>
    <div *ngIf="isExpanded" class="expanded-details">
        <table>
            <thead>
            <tr>
                <th>Event ID</th>
                <th>Timestamp</th>
                <th>Device ID</th>
                <th>Confidence</th>
                <th>Trigger Code</th>
                <th>Resource Name</th>
                <th>Entity</th>
                <th>Entity Value</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let event of entity.events">
                <td>{{ event.eventId }}</td>
                <td>{{ event.timestamp }}</td>
                <td>{{ event.deviceId }}</td>
                <td>{{ event.confidence }}%</td>
                <td>{{ event.triggerCode }}</td>
                <td>{{ event.resourceName }}</td>
                <td>{{ event.entity }}</td>
                <td>{{ event.entityValue }}</td>
            </tr>
            </tbody>
        </table>
    </div>

    <!-- Larger image preview on hover -->
    <div *ngIf="hoveredImage" [ngStyle]="hoveredImageStyle" class="image-preview">
        <img [src]="parseSafeUrl(hoveredImage)" alt="Large Profile Image" class="large-image" />
    </div>
</ng-container>
