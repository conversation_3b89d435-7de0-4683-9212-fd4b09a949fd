<app-cym-sidebar [(opened)]="opened" [dockedSize]="'0px'"  [modeNum]="'push'" inputClass="second-sidebar">

    <ng-container side>
        <ng-container #editComponentFactory></ng-container>
    </ng-container>

    <div class="page-format dashboard-content" content>
        <div class="container-fluid">
            <div class="row border-bottom">
                <app-events-actions [viewOptions]="viewOptions" [dropDownActions]="dropDownActions" (selectView)="onSelectView($event)" (selectAction)="performEventsActionOnData($event)"></app-events-actions>
            </div>
            <div class="row content-router">
                <app-events-live #eventsLive *ngIf="viewOptions[0].isActive" [notificationsModelStore]="notificationsModelStore" [newNotifications]="newNotifications" (selectAction)="performEventsActionOnData($event)"></app-events-live>
                <app-identifications #identifications *ngIf="viewOptions[1].isActive"></app-identifications>
            </div>
        </div>
    </div>
</app-cym-sidebar>

<p-confirmDialog appendTo="body" acceptButtonStyleClass="btn-primary" rejectButtonStyleClass="btn-secondary"></p-confirmDialog>


