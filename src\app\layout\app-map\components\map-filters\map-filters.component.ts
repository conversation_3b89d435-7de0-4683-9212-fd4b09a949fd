import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { ResourceGroup } from 'app/shared/modules/data-layer/models/resource-group';
import { ResourceGroupService } from 'app/shared/modules/data-layer/services/resource-group/resource-group.service';
import * as _ from 'lodash';
import { SelectItem } from 'primeng/api';
import { Subscription } from 'rxjs/internal/Subscription';
import { MapForm } from '../../enums/map-form.enum';
import { MapFilters } from '../../models/map-filters.interface';
import { MapObject } from '../../models/map-object.interface';
import { MapState } from '../../models/map-state.interface';
import { MapUtilsService } from '../../services/map-utils.service';
import { DefaultMapEditorComponent } from '../default-map-editor/default-map-editor.component';
@Component({
  selector: 'app-map-filters',
  templateUrl: './map-filters.component.html',
  styleUrls: ['./map-filters.component.scss']
})

export class MapFiltersComponent extends DefaultMapEditorComponent implements OnInit {
  @Output('onFilter') onFilter: EventEmitter<string> = new EventEmitter();
  filterMapForm: FormGroup;
  resourceGroupModelStore: { [id: string]: ResourceGroup }
  resourceGroups: ResourceGroup[] = null;
  resourceItems: SelectItem[] = [];
  subscriptions: Subscription[] = [];
  data: {
    state: MapState,
    selectedMap: MapObject,
    filters: MapFilters,
    mapInstanceId: string
  }

  constructor(private formBuilder: FormBuilder, private resourceGroupService: ResourceGroupService, private mapUtilsService: MapUtilsService, ) {
    super();
  }
  ngOnDestroy(): void {
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
  }

  ngOnInit() {
    this.generateForm(this.data.filters);
    this.getResourceData();
  }

  getResourceData(): void {
   let resourceGroupSubscription= this.resourceGroupService.getAll().subscribe((response) => {
      this.resourceGroupModelStore = response;
      this.resourceGroups = _.values(response);
      this.resourceGroups.forEach(group => {
        this.resourceItems.push({ label: group.name, value: group.identity, disabled: false });
      });
    })
    this.subscriptions.push(resourceGroupSubscription);
  }

  private generateForm(data: MapFilters): void {
    this.filterMapForm = this.formBuilder.group({
      selectedResourceGroups: new FormControl(data && data.selectedResourceGroups ? data.selectedResourceGroups : []),
    });
    this.mapUtilsService.setMapValidatorChange({ [MapForm.FILTER]: this.filterMapForm.status === 'VALID' ? true : false });
  }

  onSubmit(): void {
    this.mapUtilsService.setFilterDataChange(this.filterMapForm.value.selectedResourceGroups, this.data.mapInstanceId);
    this.onFilter.next(this.filterMapForm.value);
  }

  onCancel(): void {
    this.onFilter.next(null);
  }
}
