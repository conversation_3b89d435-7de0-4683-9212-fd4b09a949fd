export class ResourceGroupModel {
   guid?: string;
   name: string;
   homogeneousGroup: boolean;
   newOrOldResource: boolean;
   groupId: number;
   profileId: number;

   constructor(resource?: ResourceGroupModel) {
      if (resource) {
         this.guid = null;
         this.name = null;
         this.homogeneousGroup = null;
         this.newOrOldResource = null;
         this.groupId = null;
         this.profileId = null;
      }
   }
}


