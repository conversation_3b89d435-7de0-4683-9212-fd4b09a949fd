import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { apiMap } from 'app/shared/services/api.map';
import { environment } from 'environments/environment';
import { Observable, Subject } from 'rxjs';
import { Guid } from '../enum/guid';
import { CloseVideoWallChannel } from '../models/clseChannel.model';
import { SetMonitorChannel } from '../models/setMonitorChannel.model';
import { SetMonitorLayout } from '../models/setMonitorLayout.models';
import { VideoWall } from '../models/videoWall.model';
import { MonitorChannel } from '../models/videoWallChannels.model';
import { VideoWallLayouts } from '../models/videoWallLayouts.model';

@Injectable({
  providedIn: 'root'
})
export class VideoWallService {

  selectedChannelId:Subject<{channelId:string,playerId:string,monitorIndex:number}>=new Subject<{channelId:string,playerId:string,monitorIndex:number}>();
  // TODO:  selectedChanelID should be delete


  closePlayerSubject:Subject<string> =new Subject<string>();
  constructor(private httpClient:HttpClient) { }


  getLayout(videoWall,layoutId):Observable<Guid>{
     return  this.httpClient.get<Guid>(environment.apiUrl + apiMap.getLayoutId.url+videoWall +"/monitor/"+layoutId+"/layout");
  }

  getLayouts():Observable<VideoWallLayouts[]>{
    return this.httpClient.get<VideoWallLayouts[]>(environment.apiUrl + apiMap.getLayouts.url);
  }

  getVideoWallDetails(videoWallId:string):Observable<VideoWall>{
    return this.httpClient.get<VideoWall>(environment.apiUrl + apiMap.getVideoWallDetails.url+ videoWallId);
    
  }

  getVideoWalls():Observable<string[]>{
    return this.httpClient.get<string[]>(environment.apiUrl + apiMap.getVideoWalls.url);
  }

  saveLayoutForMonitor(data:SetMonitorLayout){
    return this.httpClient.post(environment.apiUrl + apiMap.saveLayoutForMonitor.url,data);
  }

  getVideoWallChannels(videoWallId:string):Observable<MonitorChannel[]>{
    return this.httpClient.get<MonitorChannel[]>(environment.apiUrl + `/token/videoWalls/${videoWallId}/channels`)
  }


  addChannelToMonitor(data:SetMonitorChannel,videoWallId:string) {
    return this.httpClient.post(environment.apiUrl + `/token/videoWalls/${videoWallId}/channel/open`,data)
  }


  closeAndRemoveChannel(data:CloseVideoWallChannel,videoWallId:string){
    return this.httpClient.post(environment.apiUrl + `/token/videoWalls/${videoWallId}/channel/close`,data)
  }
}
