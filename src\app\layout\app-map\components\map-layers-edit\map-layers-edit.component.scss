:host {
    
    button {
        color: #8F8F8F;
        text-decoration: none;
        &:focus {
            box-shadow: none;
        }
    }

    .map-edit-layers-wrapper {
        height: 100%;
    }

    .title-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        
        h2 {
            margin: 0;
        }

        button {
            margin-left: auto;
            font-size: 0.8125rem;
            padding: 0;
            
        }
    }

    .add-layer {
        padding: 0;
        i {
            font-family: 'icomoon';
        }
    }

    

    .elements {
        margin: 0;
        padding: 0;
        
        li {
            font-size: 1rem;
            list-style-type: none;
            display: flex;
            align-items: center;
        }
        
        .actions {
            margin-left: auto;
            
            .edit , .visibility, .delete {
                font-family: 'FontAwesome';
                padding: 0 3px;
                margin-left: 10px;
            }
            
            .visibility {
                background: none;
                color: #000000;
                .fa-eye-slash{
                    color: #8F8F8F;    
                }    
            }

            .edit, .delete {
                background-color: #EBEBEB;
                color: #646262;
                &:hover {
                    background-color: #0275D8;
                    color: #FFFFFF;
                }
            }
        }

        .edited {
            position: relative;
            border-bottom: 0;

            .element-title {
                display: none;
            }
            .actions {
                position: absolute;
                top: 10px;
                right: 0;
                z-index: 1;
            }
            .edit {
                background-color: #0275D8;
                color: #FFFFFF;    
            }
            ::ng-deep{
                .form-item {
                    margin-top: 0px;
                }

                .name-control {
                    border-width: 0 0 1px 0;
                    color: #0275D8;
                    padding-left: 0;
                    padding-top: 2px;
                }
            }
        }
    }

    .title-wrapper, .elements li {
        border-bottom: 1px solid #EEF0F2;
        flex-wrap: wrap;
        padding: 10px 0;
    }

    .dynamic-wrapper {
        flex-basis: 100%;
        height: 0;
    }
    


}