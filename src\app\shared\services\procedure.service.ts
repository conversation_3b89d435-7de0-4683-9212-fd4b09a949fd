import { HttpClient, HttpPara<PERSON>, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';
import { Observable, Subject } from 'rxjs';
import { Guid } from '../enum/guid';

import { ProcedureCompleteStep } from '../models/procedureCompleteStep.model';
import { ProcedureModel } from '../models/procedures/procedure.model';
import { ProcTemplate } from '../models/procedures/proctemplate.model';
import {ProceduresPageExportRequest, ProceduresPageRequest} from "app/shared/models/procedures/procedures-page-request.model";
import {ProceduresPage} from "app/shared/models/procedures/procedures-page.model";
import { apiMap } from './api.map';


@Injectable({
  providedIn: 'root'
})
export class ProcedureService {

  procedureMessageSubject = new Subject<{message:string,category:number}>();
  constructor(private httpClient: HttpClient) { }

  getProcedures():Observable<ProcedureModel[]> {
    return this.httpClient.get<ProcedureModel[]>(environment.apiUrl + "/token/procedures");
  }

  completeStep(data:ProcedureCompleteStep):Observable<ProcedureCompleteStep>{
    let completeProcedureStep={
      "ProcStepId": data.ProcStepId,
      "ProcedureId": data.ProcedureId,
      "StepIndex": data.StepIndex,
      "ProcOptionId": data.ProcOptionId,
      "SelectedOptionVal": data.SelectedOptionVal,
      "Time": data.Time,
      "Description": data.Description,
      "Comment": data.Comment,
      "NextIndex": data.NextIndex,
      "HandledBy":  data.HandledBy,
  }

   return this.httpClient.post<ProcedureCompleteStep>(environment.apiUrl + "/token/procedures/completeStep", completeProcedureStep);
  }


  getTemplates():Observable<ProcTemplate[]>{
    return this.httpClient.get<ProcTemplate[]>(environment.apiUrl + "/token/procedures/templates");
  }

  createProcedure(procedure:Guid):Observable<null>{
    return this.httpClient.post<null>(environment.apiUrl + "/token/procedures/templates/"+procedure.toString(),null);
  }

  getProceduresPage(request: ProceduresPageRequest): Observable<ProceduresPage> {
    let params = new HttpParams()
      .set('PageIndex', request.PageIndex.toString())
      .set('PageSize', request.PageSize.toString());

    if (request.StartTimeStamp) {
      params = params.set('StartTimeStamp', request.StartTimeStamp);
    }
    if (request.EndTimeStamp) {
      params = params.set('EndTimeStamp', request.EndTimeStamp);
    }
    if (request.NameFilter) {
      params = params.set('NameFilter', request.NameFilter);
    }
    if (request.StatusFilter && request.StatusFilter > 0) {
      params = params.set('StatusFilter', request.StatusFilter.toString());
    }
    if (request.EventIdFilter) {
      params = params.set('EventIdFilter', request.EventIdFilter);
    }
    if (request.ProcedureTemplateFilter) {
      params = params.set('ProcedureTemplateFilter', request.ProcedureTemplateFilter);
    }
    if (request.StepDescriptionFilter) {
      params = params.set('StepDescriptionFilter', request.StepDescriptionFilter);
    }

    return this.httpClient.get<ProceduresPage>(environment.apiUrl + "/token/procedurespage", { params });
  }

  
  exportProcedures(request: ProceduresPageExportRequest): Observable<HttpResponse<Blob>> {
    const url = environment.apiUrl + apiMap.procedureExport.url;
    return this.httpClient.post(url, request, {
      responseType: 'blob',
      observe: 'response'
    })
  }


}
