import { DashboardWidget } from "./dashboard-widget.interface";
import { WidgetType } from "../enums/widget-type.enum";
import { WidgetSize } from "../enums/widget-size.enum";
import { Guid } from "app/shared/enum/guid";
import { DashboardAction } from './dashboard-action.interface';

export class Widget implements DashboardWidget {
    type: WidgetType;
    size: WidgetSize;
    componentName: string;
    id?: string;
    title?: string;
    titleKey?: string;
    isClosable?: boolean;
    showDetailsIcon?: boolean;
    currentIndex?: number;
    hasTitle?: boolean;
    hideActionsMenu?: boolean;
    hasInfoWrapper?: boolean;

    constructor(obj?: Partial<Widget>){
        Object.assign(this, obj);
        if(!this.id){
            this.id = Guid.create().toString();
        }
    }

    getWidgetSize():  WidgetSize[] {
        return [WidgetSize.big];
    }

    getWidgetExtraActions(): DashboardAction[] {
        return [];
    }
}
