import { GlobalConstants } from '../../shared/modules/global-constants';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class MainLayoutService {
  private sidebarExpandedSubject = new BehaviorSubject<boolean>(false);
  sidebarExpanded$: Observable<boolean> = this.sidebarExpandedSubject.asObservable();
  windowSize = GlobalConstants[1280];
  constructor() {
    const initialState = window.innerWidth >= this.windowSize;
    this.sidebarExpandedSubject.next(initialState);
  }

  toggleSidebar(): void {
    this.sidebarExpandedSubject.next(!this.sidebarExpandedSubject.value);
  }

  setSidebarExpanded(expanded: boolean): void {
    this.sidebarExpandedSubject.next(expanded);
  }

  getSidebarExpanded(): boolean {
    return this.sidebarExpandedSubject.value;
  }
}
