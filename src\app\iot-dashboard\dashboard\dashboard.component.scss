/* The content wrapper in the parent component already handles scrolling */
$green: #00D600;
$tableColor: #667085;

.viewport-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-height: 100%;
  min-height: 0; /* Allow container to shrink */
}


:host ::ng-deep {
  .dashboard-maps {
    .popup-wrapper {
        position: absolute;
        bottom: -20px;
        left: -278px;
    }
  }
}


/* Responsive viewport container */
@media (max-width: 1200px) {
  .viewport-container {
    height: auto;
    overflow: visible;
  }
}

.viewport-content {
  flex: 1;
  height: 100%;
  max-height: 100%;
  overflow: hidden;
  min-height: 0; 
}


@media (max-width: 1200px) {
  .viewport-content {
    height: auto;
    max-height: none;
    overflow: visible;
  }
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 24px;
    margin-bottom: 5px; 
    background: none;
    height: 30px; 
}

.dashboard-title {
    font-size: 20px;
    font-weight: 600;
    color: #3a3a3a;
    margin: 0;
}



.dashboard-system-structure-map {
    display: flex;
    gap: 20px;
}

.dashboard-map-layout {
    display: flex;
    flex-direction: row;
    gap: 20px;
}

.dashboard-section-layout {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Simple hover effect for components */
app-cameras,
app-system-structure,
app-alarm-summary,
app-notification-list,
.dashboard-maps {
    display: block;
    border-radius: 24px;
    transition: transform 0.3s ease;

    &:hover {
        transform: scale(1.01);
    }
}

.dashboard-maps {
    height: 100%;
    border-radius: 12px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .popup-wrapper {
      position: inherit;
    }
}


.switch-position {
    display: inline-flex;
    margin: 12px 0px;
    width: 100%;
    justify-content: flex-end;
    align-items: center;
    color: #3A3A3A;
    background: none;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      color: $green;

    }

    &:focus {
        outline: 2px solid rgba(0, 214, 3, 0.5);
    }

    img {
        padding-left: 6px;
        width: 16px;
        height: 16px;
    }
}


.container-dashboard {
    display: flex;
    gap: 12px; 
    width: 100%;
    padding: 12px;
    box-sizing: border-box;
    font-family: "Rounded Mplus 1c Bold", ui-sans-serif, system-ui, sans-serif;
    font-size: 12px;
    height: 100%;
    max-height: calc(100vh - 80px);
  }

  .container-dashboard .column {
    display: flex;
    flex-direction: column;
    gap: 16px; /* Equal gap between all panels */
    min-width: 0;
    height: calc(100% - 60px);
    width: 100%;
    box-sizing: border-box;
  }

  .container-dashboard .column:nth-child(1) {
    flex: 1;
    max-width: 25%;
  }

  .container-dashboard .column:nth-child(2) {
    flex: 2;
    max-width: 50%;
  }

  .container-dashboard .column:nth-child(3) {
    flex: 1;
    max-width: 25%;
  }

  .container-dashboard .dash-panel {
    background: inherit;
    border-radius: 8px;
    box-shadow: none;
    min-width: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden; 
    box-sizing: border-box;
  }

  /* First column panels - cameras biggest, weather smallest */
  .container-dashboard .panel-1 {
    height: 55%; /* Further increased for cameras status */
    max-height: 55%;
    overflow: hidden;
  }

  .container-dashboard .panel-2 {
    height: 30%; /* Reduced to give more space to cameras */
    max-height: 30%;
    overflow: hidden;
  }

  /* Panel-3 in first column (weather) - smaller to fit content */
  .container-dashboard .column:nth-child(1) .panel-3 {
    height: 15%; /* Further reduced for weather to fit content */
    max-height: 15%;
    overflow: hidden;
  }

  /* Panel-3 in second column (alarm summary) */
  .container-dashboard .column:nth-child(2) .panel-3 {
    height: 15%;
    max-height: 15%;
    overflow: hidden;
  }

  /* Panel-4 in second column (map) - takes remaining space */
  .container-dashboard .panel-4 {
    height: 85%;
    max-height: 85%;
    overflow: hidden;
  }

  .container-dashboard .panel-5 {
    height: 100%;
    max-height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 0;
  }

  .container-dashboard .panel-5 warning-list,
  .container-dashboard .panel-5 app-notification-list {
    flex: 0 0 calc(50% - 20px); 
    min-height: 0; 
    overflow: hidden;
    display: block;
  }


