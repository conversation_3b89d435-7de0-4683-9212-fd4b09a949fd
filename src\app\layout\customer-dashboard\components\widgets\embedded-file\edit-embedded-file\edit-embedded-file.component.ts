import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { DefaultWidgetEditorComponent } from '../../default-widget/default-widget-editor.component';
import { EmbeddedFileWidget } from 'app/layout/customer-dashboard/models/embedded-file-widget';
import { DashboardUtilsService } from 'app/layout/customer-dashboard/services/dashboard-utils.service';
import { MessageService } from 'primeng/api';
import { TranslateService } from '@ngx-translate/core';
import { ToastTypes } from 'app/shared/enum/toast-types';
import { StringService } from 'app/shared/services/string.service';
import { FileType } from 'app/shared/enum/file-type.enum';

@Component({
  selector: 'app-edit-embedded-file',
  templateUrl: './edit-embedded-file.component.html',
  styleUrls: ['./edit-embedded-file.component.scss']
})
export class EditEmbeddedFileComponent extends DefaultWidgetEditorComponent implements OnInit {
  @ViewChild('uploadInput', {static: false}) uploadInput: ElementRef;
  data: EmbeddedFileWidget;
  allowedFileEnum = FileType;

  constructor(private dashboardUtilsService: DashboardUtilsService, private messageService: MessageService, 
  private translateService: TranslateService, private stringService: StringService) {
    super();
  }

  ngOnInit() {
    this.data = new EmbeddedFileWidget(this.data);
  }


  uploadFile(event): void {
    let file = event.target.files[0];
    const reader: FileReader  = new FileReader();
    reader.readAsDataURL(file);
    this.data.title = file.name;
    reader.onload = (e: Event ) => {
      let fileName : string = <string>reader.result;
      let fileExtension = this.stringService.extractFileTypeFromUrlFileName(fileName);
      switch (fileExtension)
      {
        case FileType.pdf:
          {
            this.data.resource = reader.result;
            this.dashboardUtilsService.setWidgetDataChange(this.data);
            this.uploadInput.nativeElement.value = "";
            break;
          }

          default:
            {
              this.messageService.add({severity: 'error', summary: this.translateService.instant(ToastTypes.error), 
                detail: this.translateService.instant('invalidFileType')});
              this.uploadInput.nativeElement.value = "";
            }
      }
    }
  }


}
