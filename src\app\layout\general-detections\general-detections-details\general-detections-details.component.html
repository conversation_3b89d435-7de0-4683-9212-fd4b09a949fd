<p-table
    [value]="data"
    [loading]="loading"
    [paginator]="true"
    [rows]="pageSize"
    [totalRecords]="totalRecords"
    [lazy]="false"
    [first]="pageIndex * pageSize" 
    [showCurrentPageReport]="true"
    [(selection)]="selectedDossiers"
    styleClass="p-datatable-striped">

    <ng-template pTemplate="caption">
        <div class="table-title">
            <p>{{ title }}</p>
            <div class="action-buttons">
                <button pButton 
                icon="fa fa-close" 
                class="p-button-rounded p-button-text"
                (click)="close(item)"
                [tooltipOptions]="tooltipOptions"
                pTooltip="{{ 'generalDetectionsDetails.closeDetails' | translate }}">
             </button>
            </div>
        </div>
      </ng-template>

    <ng-template pTemplate="header">
        <tr>
            <th>{{ 'generalDetectionsDetails.type' | translate }}</th>
            <th>{{ 'generalDetectionsDetails.timestamp' | translate }}</th>
            <th *ngIf="showActions" style="text-align: right">{{ 'generalDetectionsDetails.actions' | translate }}</th>
        </tr>
    </ng-template>

    <ng-template pTemplate="body" let-item>
        <tr>
            <td>{{item.Types[0] }} </td>
            <td>{{item.TimeStamp | iotTimestamp }} </td>
            <td *ngIf="showActions" class="action-buttons">
                <button pButton 
                    icon="fa fa-check" 
                    class="p-button-rounded p-button-text"
                    (click)="ack(item)"
                    [tooltipOptions]="tooltipOptions"
                    pTooltip="{{ 'generalDetectionsDetails.ack' | translate }}">
                </button>
            </td>
        </tr>
    </ng-template>

    <ng-template pTemplate="emptymessage">
        <tr>
            <td colspan="6" class="text-center">{{ 'generalDetectionsDetails.noDetectionsDetailsFound' | translate }}</td>
        </tr>
    </ng-template>
</p-table>
