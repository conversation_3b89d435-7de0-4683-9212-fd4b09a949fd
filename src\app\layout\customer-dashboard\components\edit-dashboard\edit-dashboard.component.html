<div class="dashboardSettings" *ngIf="data.selectedDashboard && data.selectedDashboard.item">
  <div class="form-item">
    <h2>{{ (data.state.addNew ? 'customerDashboard.addDashboardTitle' : 'customerDashboard.editDashboardTitle') |
      translate }}</h2>

    <input type="text" name="dashboardName" id="dashboardName" [(ngModel)]="data.selectedDashboard.item.name"
      (ngModelChange)="name$.next($event)" #dashboardName="ngModel" required />

    <div *ngIf="dashboardName.errors" class="input-error">
      <span *ngIf="dashboardName.errors.required && dashboardName.touched">{{'formErrors.required' | translate}}</span>
    </div>

  </div>
  <div class="form-item">
    <h2>{{ 'customerDashboard.dashboardPriority' | translate }}</h2>
    <p-dropdown [options]="dashboardPriorityOptions" [(ngModel)]="data.selectedDashboard.item.priority"
      [styleClass]="'input-element'">

      <ng-template let-item pTemplate="selectedItem">
        <span>{{item.label| translate}}</span>
      </ng-template>
      <ng-template let-item pTemplate="item">
        <div class="ui-helper-clearfix">
          <div>{{item.label | translate}}</div>
        </div>
      </ng-template>
    </p-dropdown>
  </div>
  <div class="form-item">
    <h2>{{ 'customerDashboard.selectHierarchyLevel' | translate }}</h2>

    <button type="button" class="btn btn-secondary btn-block" (click)="toggleDashboardTree()"
      [ngClass]="{'groups-active': showDashboardTree}">
      <span *ngIf="data.parentDashboardTree" class="pull-left">{{ data.parentDashboardTree.name }}</span>
      <span class="pull-right">
        <i class="fa {{showDashboardTree ? 'fa-chevron-up' : 'fa-chevron-down'}}"></i>
      </span>
    </button>

    <div class="dashboard-tree" *ngIf="showDashboardTree">
      <ng-container *ngFor="let item of filterPossibleParents()">
        <app-nested-tree [item]="item" [hasParent]="false" class="app-nested-tree"></app-nested-tree>
      </ng-container>
    </div>
  </div>
  <div class="form-item">
    <h2>{{ 'customerDashboard.addWidgetsTitle' | translate }}</h2>
    <div class="widgetWrapper">

      <div *ngFor="let widget of availableWidgetTypes" pDraggable="dashboardWidgetDrag"
        (onDragStart)="onDragStartWidget($event, widget)" (click)="addWidget(widget)" class="widget">
        <span class="widget-icon {{widget.type}}"></span>
        {{'customerDashboard.widgetType.'+widget.type | translate}}<br />
      </div>
    </div>
  </div>
</div>