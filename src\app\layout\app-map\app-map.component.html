<div class="page-format">
  <div class="content-wrapper">
    <app-page-title title="maps"></app-page-title>
    <div class="iot-map">
      <app-cym-sidebar [(opened)]="opened" [dockedSize]="dockedSize" [modeNum]="'push'" inputClass="vms-sidebar">

        <div class="side-container" side>
          <p-confirmDialog appendTo="body" acceptButtonStyleClass="btn-primary"
            rejectButtonStyleClass="btn-secondary"></p-confirmDialog>
          <ng-container #editComponentFactory></ng-container>

          <div class="sidebar-actions">
            <ng-container *ngFor="let action of sidebarActions">
              <button class="btn btn-{{action.importance}}" *ngIf="action.isVisible" [disabled]="action.disabled"
                (click)="onMapAction(action.type)">
                {{ action.name | translate }}
              </button>
            </ng-container>
          </div>
        </div>

        <div class="map-layout-content" content id="mapLayout">
          <div class="map-actions-wrapper">
            <app-map-navigation [mapsModelStore]="mapsModelStore" [selectedMap]="selectedMap"></app-map-navigation>
            <app-map-actions [mapFilterOptions]="mapFilterOptions" [mapSearchProperties]="mapSearchProperties"
              [selectedMapId]="selectedMap.id" [mapSelector]="mapWrapper" [selectedMapAction]="selectedMapAction"
              (mapAction)="onMapAction($event)"></app-map-actions>
          </div>
          <div class="map-wrapper" #mapWrapper>
            <app-cymbiot-map [selectedMapId]="selectedMap.id" *ngIf="mapLayers" [mapLayers]="mapLayers"
              [mapFilterOptions]="mapFilterOptions" [mapSearchProperties]="mapSearchProperties"
              (extentChanged)="onMapExtentChanged($event)" [defaultItemId]="defaultItemId"
              #cymbiotMap></app-cymbiot-map>
          </div>
        </div>
      </app-cym-sidebar>

    </div>
  </div>
</div>
