import { Component, EventEmitter, Input, OnDestroy,  Output } from '@angular/core';
import { INotificationData } from '../../../shared/components/header/notifications/INotificationData';
import { NotificationsService } from '../../../shared/components/header/notifications/notifications.service';

@Component({
  selector: 'app-general-detections-details',
  templateUrl: './general-detections-details.component.html',
  styleUrls: ['./general-detections-details.component.scss']
})
export class GeneralDetectionsDetailsComponent implements OnDestroy {
  @Input() title: string = '';
  @Input() data: INotificationData[] = [];
  @Input() totalRecords: number = 0;
  @Input() pageSize: number = 11;
  @Input() showActions: boolean = true;
  @Input() pageIndex: number = 0;
  @Input() loading: boolean = false;
  @Output() closeDetails = new EventEmitter<void>();

  tooltipOptions = {
    showDelay: 150,
    autoHide: false,
    tooltipEvent: 'hover',
    tooltipPosition: 'top'
  };

  constructor(private notificationsService: NotificationsService) { }

  ack(item: INotificationData): void {
    this.notificationsService.acknowledge(item).subscribe((response) => {
        if(response) {
          this.data = this.data.filter((i) => i !== item);
        }
    });
  }

  close() : void {
    this.closeDetails.emit();
  }

  ngOnDestroy(): void {
    this.data = [];
    this.totalRecords = 0;
  }

}
