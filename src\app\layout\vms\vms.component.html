<app-cym-sidebar [(opened)]="opened" [dockedSize]="dockedSize" [modeNum]="'push'" inputClass="vms-sidebar">

    <sub-header content [PrimarySelectOptions]="layouts" [Type]="type" [pageTitle]="'cameras'" class="h-100">

        <div header class="menu-items-wrapper">
            <app-page-title title="cameras"></app-page-title>

            <div class="menu-item view-options">
                <ul class="menu-options">
                    <li class="item">
                        <img src="assets/public/assets/cameraToLeft.svg"
                        pTooltip="{{'lastOpenedCameras' | translate }}" tooltipPosition="bottom"
                             (click)="reopenCameras()"/>

                    </li>
                    <li class="item">
                        <img src="assets/public/assets/timeline.svg" class="dashboard-cameras"
                        pTooltip="{{(isTimelineOpen? 'closeToggleTimeline' : 'openToggleTimeline') | translate }}" tooltipPosition="bottom"
                             (click)="openTimeline()"/>
                    </li>
                    <li class="item">
                        <img src="assets/public/assets/layouts.svg" class="dashboard-cameras"
                        pTooltip="{{'layouts' | translate}}" tooltipPosition="bottom"
                         (click)="openLayouts()"/>
                    </li>
                    <li class="item">
                        <p-inputSwitch [(ngModel)]="showPTZControls" (onChange)="togglePTZControls()"
                            pTooltip="{{ 'togglePTZControls' | translate }}" tooltipPosition="bottom"
                            [styleClass]="showPTZControls ? 'ptz-active' : ''"></p-inputSwitch>
                    </li>
                    <li class="item">
                        <img src="assets/public/assets/ellipsis.svg" class="dashboard-cameras"
                            pTooltip="{{ 'settings' | translate }}" tooltipPosition="bottom"
                            (click)="openVCAoptions()"/>
                    </li>
                </ul>
            </div>
         </div>

        <ng-container body [ngSwitch]="playerCountNew">
            <div #playersWrapper class="players-wrapper h-100"
                [ngClass]="{ 'timeline-open': isTimelineOpen, 'no-ptz': !showPTZControls }">
                <ng-container *ngSwitchCase="1">
                    <div class="row h-100">
                        <div class="w-100 p-1" #container></div>
                    </div>
                </ng-container>
                <ng-container *ngSwitchCase="4">
                    <div class="row h-50">
                        <div class="w-50 p-1" #container></div>
                        <div class="w-50 p-1" #container></div>
                    </div>
                    <div class="row h-50">
                        <div class="w-50 p-1" #container></div>
                        <div class="w-50 p-1" #container></div>
                    </div>
                </ng-container>
                <ng-container *ngSwitchCase="9">
                    <div class="row h-33">
                        <div class="w-33 p-1" #container></div>
                        <div class="w-33 p-1" #container></div>
                        <div class="w-33 p-1" #container></div>
                    </div>
                    <div class="row h-33">
                        <div class="w-33 p-1" #container></div>
                        <div class="w-33 p-1" #container></div>
                        <div class="w-33 p-1" #container></div>
                    </div>
                    <div class="row h-33">
                        <div class="w-33 p-1" #container></div>
                        <div class="w-33 p-1" #container></div>
                        <div class="w-33 p-1" #container></div>
                    </div>
                </ng-container>
                <ng-container *ngSwitchCase="16">
                    <div class="row h-25">
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                    </div>
                    <div class="row h-25">
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                    </div>
                    <div class="row h-25">
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                    </div>
                    <div class="row h-25">
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                    </div>
                </ng-container>
            </div>
        </ng-container>
        <ng-container appTimeline>
            <div #timelineWrapper [hidden]="!isTimelineOpen" class="timeline-wrapper">
                <app-timeline *ngIf="isTimelineOpen" class="timeline" #timeline></app-timeline>
            </div>
        </ng-container>
    </sub-header>
    <p-confirmDialog appendTo="body" acceptButtonStyleClass="btn-primary" rejectButtonStyleClass="btn-secondary"></p-confirmDialog>
</app-cym-sidebar>



<app-modal #modal [title]="'settings'" styleClass="modal-content" [position]="position">
    <ng-container ngProjectAs="contentModal">
        <div class="d-flex justify-content-center">
            <span>{{ "vcaOptions" | translate }}</span>
        </div>
        <br />
        <p-checkbox label="{{ 'showVCAConfig' | translate }}" binary="true" [(ngModel)]="showVCAConfig"
            pTooltip="{{ 'showVCAConfigTooltip' | translate }}" tooltipPosition="right">
        </p-checkbox>
        <br />
        <br />
        <p-checkbox label="{{ 'showVCAObjects' | translate }}" binary="true" [(ngModel)]="showVCAObjects"
            pTooltip="{{ 'showVCAObjectsTooltip' | translate }}" tooltipPosition="right">
        </p-checkbox>
        <br /><br />
        <!-- PTZ controls are now managed by the toggle switch in the header -->
    </ng-container>
    <ng-container ngProjectAs="footerModal">
        <button class="btn btn-primary" (click)="onConfirm()"
            pTooltip="{{ 'saveSettings' | translate }}" tooltipPosition="top">
            {{ 'ok' | translate }}
        </button>
        <button class="btn btn-secondary ml-2" (click)="onCancel()"
            pTooltip="{{ 'cancelChanges' | translate }}" tooltipPosition="top">
            {{ 'cancel' | translate }}
        </button>
    </ng-container>
</app-modal>

<app-modal #layoutsModal [title]="'layouts'" styleClass="modal-content" [position]="position">
    <ng-container ngProjectAs="contentModal">
        <div>
            <ul class="layouts">
                <li *ngFor="let option of layouts" class="layout-item">
                    <span class="menu-icon {{ option.label }}" pTooltip="{{ option.label  | translate }}"
                    tooltipPosition="bottom" [ngClass]="{
                            active: option.value == playerCountNew
                        }" (click)="onLayoutChanged(option)"></span>
                </li>
            </ul>
        </div>
    </ng-container>
    <ng-container ngProjectAs="footerModal">
        <button class="btn btn-secondary" (click)="layoutsModal.closeModal()"
            pTooltip="{{ 'closeLayouts' | translate }}" tooltipPosition="top">
            {{ 'close' | translate }}
        </button>
    </ng-container>
</app-modal>

<app-modal #camerasModal [title]="'cameras'" styleClass="modal-content camera-selection-modal" [position]="position">
    <ng-container ngProjectAs="contentModal">
        <div class="camera-selection-wrapper">
            <app-add-video-channel
                class="camera-channels camera-selection-content"
                [playerId]="selectedPlayerId"
                [showActions]="true"
                [sidebarAutoClose]="false"
                (onChannelSelect)="onChannelSelect($event)">
            </app-add-video-channel>
        </div>
    </ng-container>
    <ng-container ngProjectAs="footerModal">
        <button class="btn btn-secondary" (click)="camerasModal.closeModal()"
            pTooltip="{{ 'closeCameraSelection' | translate }}" tooltipPosition="top">
            {{ 'close' | translate }}
        </button>
    </ng-container>
</app-modal>


