import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { SelectItem } from 'primeng/api';
import { Guid } from 'app/shared/enum/guid';
import { DefaultWidgetEditorComponent } from '../../default-widget/default-widget-editor.component';
import { MapWidget } from '../../../../models/map-widget';

import { DashboardUtilsService } from 'app/layout/customer-dashboard/services/dashboard-utils.service';
import { Subscription } from 'rxjs';
import { MapLayer } from 'app/shared/modules/data-layer/models/map-layer';
import * as _ from "lodash";
import { MapLayerService } from 'app/shared/modules/data-layer/services/map-layer/map-layer.service';
import {MapService} from "app/services/map/map.service";
import {DefaultResources} from "app/shared/enum/enum";
@Component({
  selector: 'app-edit-map-widget',
  templateUrl: './edit-map-widget.component.html',
  styleUrls: ['./edit-map-widget.component.scss']
})
export class EditMapWidgetComponent extends DefaultWidgetEditorComponent implements OnInit, OnDestroy {

  data: MapWidget;
  mapList: SelectItem[] = [{label: "customerDashboard.defaultMap", value: DefaultResources.DefaultMap}];
  selectedMapId: string;
  subscriptions: Subscription[] = [];
  public mapLayersModelStore: {[id:string]: MapLayer} = null;

  constructor(
    private mapService: MapService,
    private dashboardUtilsService: DashboardUtilsService,
    private mapLayerService: MapLayerService
  ) {
    super();
  }

  ngOnInit(): void {
    this.data = new MapWidget(this.data);
    let mapServiceSubscription = this.mapService.getAll().subscribe(res => {
      for (const key in res) {
        this.mapList.push({label: res[key].name, value: res[key].identity});
      }
      this.selectedMapId = this.mapList.find(element => {return element.value === this.data.selectedMapId;}).value ||
      DefaultResources.DefaultMap;
    });
    this.subscriptions.push(mapServiceSubscription);
    if(!this.data.mapLayers){
      this.data.mapLayers = [];
      let mapLayersSubscription = this.mapLayerService.getAll().subscribe(res => {
        for(const key in res){
          this.data.mapLayers.push(_.cloneDeep(res[key]));
        }
        this.dashboardUtilsService.setWidgetDataChange(this.data);
      });
      this.subscriptions.push(mapLayersSubscription);
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(subscription => {return subscription.unsubscribe();});
  }

  selectMap(event:SelectItem): void {
    this.data.selectedMapId = event.value;
    this.dashboardUtilsService.setWidgetDataChange(this.data);
  }

  toggleMapLayerVisibility(index: number): void {
    this.data.mapLayers[index].hidden = !this.data.mapLayers[index].hidden;
    this.dashboardUtilsService.setWidgetDataChange(this.data);
  }

}
