import { AuthService } from 'app/shared/services/auth.service';
import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { SettingsService } from "app/shared/services/settings.service";
import { environment } from "environments/environment";
import { Subject } from "rxjs";
import { VideoWall } from "../models/videoWall.model";
import { VideoWallService } from "./video-wall.service";

export const Pages = {
  dashboard: "dashboard",
  newDashboard: "new-dashboard",
  vms: "/new-dashboard/vms",
  inventory: "/new-dashboard/inventory",
  mapNew: "/new-dashboard/map-new",
  reports: "new-dashboard/reports",
  entities: "/new-dashboard/entities",
  scheduler: "scheduler",
  assetsManagement: "/new-dashboard/assetsManagement",
  dashboardEditor: "dashboardEditor",
  login: "login",
  events: "events",//TODO this reoute is destroyed, we no longer need it?
  swarmLPR: "/new-dashboard/swarmLPR",
  parkingLPR: "parkingLPR",
  videoWall: "video-wall",
  procedures: "new-dashboard/procedures",
  scada: "mobile-location",//TODO this reoute is destroyed, we no longer need it?
  generalDetections: "new-dashboard/generalDetections",
  notifications: "new-dashboard/notifications",
  ai: "new-dashboard/ai",
  vehicleTraffic: "new-dashboard/vehicleTraffic",
  dossier: "new-dashboard/dossier",
  audit: 'new-dashboard/audit'
};

@Injectable()
export class NavigationService {
  public currentPage = new Subject<string>();

  constructor(private router: Router, private route: ActivatedRoute, private httpClient: HttpClient, 
  private videoWallService: VideoWallService, private settingsService: SettingsService,
  private authService: AuthService) {}

  readonly default = "default_";

  public getParams() {
    return this.route.queryParams;
  }

  getCurrentRoute() {
    return this.router.url.split("?")[0].replace("/", "");
  }

  isInclude(param) {
    if (this.router.url.includes(param)) {
      return true;
    }
    return false;
  }

  isEmptyUrl() {
    if (this.router.url === "/") {
      return true;
    } else {
      return false;
    }
  }

  addParam(paramName, paramValue) {
    this.route.queryParams["value"]["paramName"] = paramValue;
  }

  getSelectedOrDefault(page) {
    let objetToSend = {};

    if (this.route.queryParams && Object.keys(this.route.queryParams["value"]).length > 0 && this.route.queryParams["value"][page]) {
      objetToSend[page] = this.route.queryParams["value"][page];
      return objetToSend;
    }
    let lastVisitedSession = this.settingsService.get("lastVisitedSession");

    if (lastVisitedSession && lastVisitedSession[page] && lastVisitedSession[page] !== page) {
      objetToSend[page] = lastVisitedSession[page];
      return objetToSend;
    }

    objetToSend[page] = this.settingsService.get(this.default + page);
    return objetToSend;
  }

  getDefault(page) {
    return this.settingsService.get(this.default + page);
  }

  setDefault(key, data) {
    this.settingsService.set(this.default + key, data);
  }

  removeDefault(key) {
    this.settingsService.remove(this.default + key);
  }

  navigateFromLogin() {
    /*Reset lastVisitedSession when login*/
    this.settingsService.set("lastVisitedSession", Pages);

    if (this.settingsService.get("lastVisited")) {

      let page = this.settingsService.get("lastVisited").page;
      let params = {};
      params[page] = this.settingsService.get("lastVisited").params;
      if (page === Pages.videoWall) {
        this.videoWallService.getVideoWalls().subscribe((videoWalls: string[]) => {
          this.videoWallService.getVideoWallDetails(videoWalls[0]).subscribe((videoWall: VideoWall) => {
            for (let x = 0; x <= videoWall.MonitorCount; x++) {
              this.router.navigate([]).then((result) => {
                window.open(page + "?videoMonitorId=" + x, "_blank");
              });
            }
          });
        });
      }else if (page === Pages.login) {
        const dashboardPage = this.resolveDashboardPage();
        this.navigate(dashboardPage, null, true);
        return
      }
      this.navigate(page, params, true);
    } else {
      const dashboardPage = this.resolveDashboardPage();
      this.navigate(dashboardPage, null, true);
    }
  }

  resolveDashboardPage():string {
    if (this.authService.isAllowedPage(Pages.newDashboard)) {
        return Pages.newDashboard;
    }
    if (this.authService.isAllowedPage(Pages.dashboard))
    {
      return Pages.dashboard;
    }
    throw new Error("All the dashboard features are disabled");
  }

  navigate(page, params = null, isFromLogin = false) {
    let jsonToSave = {};
    if (!params) {
      params = this.getSelectedOrDefault(page);
    }

    if (page !== Pages.login) {
      let lastVisitedSession = this.settingsService.get("lastVisitedSession");

      if (!lastVisitedSession) {
        lastVisitedSession = {};
        Object.assign(lastVisitedSession, Pages);
      }
      lastVisitedSession[page] = params[page];

      this.settingsService.set("lastVisitedSession", lastVisitedSession);
    }
    if (page === Pages.videoWall) {
      this.videoWallService.getVideoWalls().subscribe((videoWalls: string[]) => {
       if(videoWalls.length === 0){
          return;
       }
        this.videoWallService.getVideoWallDetails(videoWalls[0]).subscribe((videoWall: VideoWall) => {
          for (let x = 1; x <= videoWall.MonitorCount; x++) {
            window.open(page + "?videoMonitorId=" + x, "_blank", "windowFeatures");
          }
        });
      });
    } else {
      jsonToSave = { page: page, params: params[page] };
      this.settingsService.set("lastVisited", jsonToSave);
      this.router.navigate([page], { queryParams: params });

      this.currentPage.next(page);
    }
  }
}
