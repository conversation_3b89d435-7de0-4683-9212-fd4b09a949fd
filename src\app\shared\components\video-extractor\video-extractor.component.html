<app-modal #extractorModal [title]="'extractVideo'">
    <ng-container ngProjectAs="contentModal">
        <div class="row">
            <div class="col-12">
                <app-range #rangePicker [fromTime]="fromTime" [inline]="true" class="p-autocomplete" (valueChange)="onRangePickerValueChange($event)"></app-range>
            </div>
        </div>
        <div class="row">
            <div class="col-12 text-center" style="margin-top:30px;">
                <label>{{ 'selectVideoFormat' | translate }}:</label>
            </div>

            <div class="col-12 text-center" style="margin-top:20px;">
                <select class="form-control" [(ngModel)]="selectedVideoFormat">
                    <option value='2'>avi</option>
                    <option value='3'>mp4</option>
                    <option value='4'>MKV</option>
                </select>
            </div>
        </div>



        <div class="row" *ngIf="errorMsg!=''">
            <div class="col-12 text-center greyBgColor" style="margin-top:30px;">
                <i class="fa fa-exclamation-circle" style="color:red;"></i>
                <label>{{errorMsg | translate}}</label>
            </div>
        </div>
    </ng-container>
    <ng-container ngProjectAs="footerModal">
        <div class="row">
            <div class="col-12 text-center">
                <button type="button" class="btn btn-primary" [disabled]="isDisabled" (click)="onModalConfirm()">{{ "ok" | translate }}</button>
                <button type="button" class="btn" (click)="onModalCancel()">{{ "cancel" | translate }}</button>
            </div>
        </div>
    </ng-container>
</app-modal>
