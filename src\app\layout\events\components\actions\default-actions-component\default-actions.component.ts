import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { TableCell, TableActionsComponent } from 'app/shared/components/ng-turbo-table/models/table.models';
import { EventsActionType } from 'app/layout/events/enums/events-action-type.enum';
import { Entity } from 'app/shared/modules/data-layer/models/entity';

@Component({
  selector: 'app-default-actions',
  templateUrl: './default-actions.component.html',
  styleUrls: ['./default-actions.component.scss']
})
export class DefaultActionsComponent extends TableActionsComponent implements OnInit {
  
  @Input('row') row: { [columnField: string]: TableCell | string };
  @Output('selectedItem') selectedItem: EventEmitter<{action: string, data: { [columnField: string]: TableCell | string }}> = new EventEmitter();
  eventsActionType = EventsActionType;
  
  constructor() { 
    super();
  }

  ngOnInit(): void { }

  rowItem(item: { [columnField: string]: TableCell | string }) :void{
    switch (item.entityInfo) {
      case null:
        this.selectedItem.next({action: this.eventsActionType.addEntity, data: item}); 
        break;
      default:
        this.selectedItem.next({action: this.eventsActionType.editEntity, data: item});
        break;
    }
  }

}
