import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { MapFilters } from 'app/layout/app-map/models/map-filters.interface';
import { DashboxData } from 'app/layout/customer-dashboard/models/dashbox-data';
import { DashboxWidget } from 'app/layout/customer-dashboard/models/dashbox-widget';
import { DashboardUtilsService } from 'app/layout/customer-dashboard/services/dashboard-utils.service';
import { ResourceService } from "app/services/resource/resource.service";
import { AppModal } from 'app/shared/components/app-modal/app-modal.component';
import { Guid } from 'app/shared/enum/guid';
import { ServerTypes } from 'app/shared/modules/data-layer/enum/resource/resource-server-types.enum';
import { DataChangeType } from 'app/shared/modules/data-layer/models/data-change-type.enum';
import { Resource } from 'app/shared/modules/data-layer/models/resource';
import { ResourceCacheService } from "app/shared/modules/data-layer/services/resource/resource.cache.service";
import { Subscription } from 'rxjs';
import {DefaultResources} from "app/shared/enum/enum";

@Component({
  selector: 'app-dashbox-widget',
  templateUrl: './dashbox-widget.component.html',
  styleUrls: ['./dashbox-widget.component.scss']
})
export class DashboxWidgetComponent implements OnInit, OnDestroy {
  @ViewChild('dashboxModal', {static: false}) dashboxModal: AppModal;
  data: {index: number, widgetData: DashboxWidget};
  public dashboxData: DashboxData = null;
  private filteredResources: Map<string, Resource> = null;
  private subscriptions: Subscription[] = [];
  dashboxTitle: string = '';

  modalViewIsTable: boolean = true;
  selectedMapId: string = DefaultResources.DefaultMap;
  mapFilterOptions: MapFilters = {
    selectedResources: []
  }
  constructor(
    private dashboardUtilsService: DashboardUtilsService,
    private resourceService:ResourceService,
    private i18n: TranslateService,
    private resourceCacheService: ResourceCacheService
  ) { }

   ngOnInit(): void {

    this.data.widgetData = new DashboxWidget(this.data.widgetData);

    let widgetDataChangeSubscription = this.dashboardUtilsService.getWidgetDataChange().subscribe(widgetData => {

      if(this.data.widgetData.id === widgetData.widget.id){
        this.changeWidgetColor(widgetData.widget);
      }
    });
    this.subscriptions.push(widgetDataChangeSubscription);

    this.getAllData();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(subscription => {return subscription.unsubscribe();});
  }

  mapStatusesToResources(statuses){
    let operation = { type:DataChangeType.Update, models: []};

        statuses.forEach(status => {
          let idAsString = status.Id.toString();
         if(this.filteredResources.has(idAsString)){
            this.filteredResources.get(idAsString).status = status.State;
         }
        });

  }
    getAllData() {
      let subscription = this.resourceCacheService.storageCompleted.subscribe(() => {
          let resources = this.resourceCacheService.getAll();
          let filteredResults: Map<string, Resource> = new Map<string, Resource>();

          resources.forEach((resource) => {
              let resourceGuid=resource.identity
              filteredResults.set(resourceGuid, resource);
          });
          this.filteredResources = filteredResults;
          this.mapFilterOptions.selectedResources = Array.from(this.filteredResources.keys()).map(key => key.toString());
          this.resourceService.getAllStatuses().subscribe(statuses => {
              this.mapStatusesToResources(statuses);
              this.filteredResources = this.filterResources(this.filteredResources);

              this.dashboxData = this.returnDashboxData(this.filteredResources);
              if(this.dashboxData.resourceName){
                  if(this.dashboxData.deviceNumber > 1){
                      this.dashboxTitle = this.i18n.instant(this.dashboxData.resourceName + '_Plural');
                  }else {
                      this.dashboxTitle = this.i18n.instant(this.dashboxData.resourceName);
                  }
              }
              if(this.data.widgetData.title && this.data.widgetData.title !== ''){
                  this.dashboxTitle = this.data.widgetData.title;
              }
          });

      });
        this.subscriptions.push(subscription);
  }

  filterResources(resources: Map<string, Resource>): Map<string, Resource>{
    let returnedResources: Map<string, Resource> = new Map();
    resources.forEach((resource, key) => {
      if(!this.applyFilterOnResource(resource)){
        return;
      }
      returnedResources.set(key, resource);
    });
    return returnedResources;
  }

  applyFilterOnResource(resource: Resource): boolean {
    let group = resource.groups ? resource.groups.find(group => {return group.identity === this.data.widgetData.selectedGroupId;}) : null;
    let isHomogenousGroup = group ? group.isHomogeneous : this.data.widgetData.selectedGroupId === Guid.EMPTY.toString() ? true : false;
    let isSelectedResourceType = resource.resourceType === this.data.widgetData.selectedResourceType;
    let isSelectedResourceStatus = this.data.widgetData.selectedResourceStates && this.data.widgetData.selectedResourceStates.length > 0 ? this.data.widgetData.selectedResourceStates.some(el => {return el === resource.status;}) : true;
    let isSelectedResourceId = this.data.widgetData.selectedResourceIds && this.data.widgetData.selectedResourceIds.length > 0 ? this.data.widgetData.selectedResourceIds.some(el => {return el === resource.identity;}) : true;
    if(!isHomogenousGroup || !isSelectedResourceType || !isSelectedResourceStatus || !isSelectedResourceId){
      return false;
    }
    return true;
  }

  returnDashboxData(resources: Map<string, Resource>): DashboxData{
    let dashboxData: DashboxData;

    dashboxData = {
      resourceType: this.data.widgetData.selectedResourceType,
      resourceName: ServerTypes[this.data.widgetData.selectedResourceType],
      deviceNumber: resources.size,
      showStatus: this.data.widgetData.selectedResourceStates && this.data.widgetData.selectedResourceStates.length === 1 ? false : true,
      deviceStatuses: []
    };
    resources.forEach((resource, key) => {
      let index = dashboxData.deviceStatuses.findIndex(obj => {return Object.prototype.hasOwnProperty.call(obj, resource.status);});
      if(index > -1){
        dashboxData.deviceStatuses[index][resource.status]++;
      }
      else {
        dashboxData.deviceStatuses.push({[resource.status]: 1});
      }
    });

    return dashboxData;
  }

  private changeWidgetColor(widget){
    this.data.widgetData.selectedBackgroundColourCode = widget.selectedBackgroundColourCode || null;
  }

  initDashboxModal(): void{
    this.dashboxModal.openModal();
  }

  updateOrCreateResources(resources: Resource[]): void {
    let hasNewResources: boolean = false;
    resources.forEach(element => {
      if(this.applyFilterOnResource(element)){
          let elementGuid=element.identity;
          this.filteredResources.set(elementGuid, element);
        hasNewResources = true;
      }
    });
    if(hasNewResources){
      this.dashboxData = this.returnDashboxData(this.filteredResources);
    }
  }

  deleteResources(resources: Resource[]): void {
    let hasDeletedResources: boolean = false;
    resources.forEach(element => {
      if(this.filteredResources.has(element.identity)){
        this.filteredResources.delete(element.identity);
        hasDeletedResources = true;
      }
    });
    if(hasDeletedResources){
      this.dashboxData = this.returnDashboxData(this.filteredResources);
    }
  }
}
