import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { SignalRService } from './signalR.service';
import { DispatcherService } from '../modules/data-layer/services/dispacher/dispatcher.service';
import { NotificationMessageService } from '../modules/data-layer/services/notification-message/notification-message.service';
import { Subscription } from 'rxjs';
import { ISignalRConnection } from 'ng2-signalr';
import { ExtractVideoService } from './extract-video.service';

@Injectable({
  providedIn: 'root'
})
export class WebSocketManagerService implements OnDestroy {
  //TODO subscribing to signalr from a service is wrong, services can be cofigured transient, 
  // which means any signalR connection can be closed at any time during user navigation
  //more than that,what if a part of the application does not need to listen for some specific SignalR notifications?
  //this needs extensive refactoring
  private subscriptions: Subscription[] = [];
  private videoHubConnection: ISignalRConnection;
  private mapHubConnection: ISignalRConnection;
  private notificationHubConnection: ISignalRConnection;

  constructor(
    private signalRService: SignalRService,
    private dispatcherService: DispatcherService,
    private notificationMessageService: NotificationMessageService,
    private extractVideoService: ExtractVideoService,
  ) {}

  initializeConnections(): void {
    
    this.signalRService.GetConnection('VideoHub').subscribe(connection => {
      this.videoHubConnection = connection;
      
      this.subscriptions.push(
        connection.listenFor<any>('webServerEvents').subscribe(event => {
          
          this.dispatcherService.handleVideoHubEvent(event);
        })
      );

      connection.invoke('subscribeServerEvents', 'webServerEvents');//make sure we are listening before asking something

      this.subscriptions.push(
        connection.listenFor<any>("videoExtractionResults").subscribe((res) => {
                this.extractVideoService.videoExtractionFinished(
                    JSON.parse(res)
                );
            })
      )
    });

    
    this.signalRService.GetConnection('MapHub').subscribe(connection => {
      this.mapHubConnection = connection;
      connection.invoke('subscribeServerEvents', 'webServerEvents');
      this.subscriptions.push(
        connection.listenFor<any>('webServerEvents').subscribe(event => {
          
          this.dispatcherService.handleMapHubEvent(event);
        })
      );
    });

    
    this.signalRService.GetConnection('NotificationHub').subscribe(connection => {
      this.notificationHubConnection = connection;
      this.subscriptions.push(
        connection.listenFor<any>('onNotify').subscribe(notification => {
          this.notificationMessageService.handleNotification(notification);
        })
      );
    });


  }

  ngOnDestroy(): void {
    
    this.subscriptions.forEach(sub => sub.unsubscribe());
    
    
    if (this.videoHubConnection) {
      this.videoHubConnection.stop();
    }
    if (this.mapHubConnection) {
      this.mapHubConnection.stop();
    }
    if (this.notificationHubConnection) {
      this.notificationHubConnection.stop();
    }
  }
} 