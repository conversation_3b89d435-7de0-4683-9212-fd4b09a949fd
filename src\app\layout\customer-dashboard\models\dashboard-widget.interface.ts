import { WidgetType } from "../enums/widget-type.enum";
import { WidgetSize } from "../enums/widget-size.enum";
import { DashboardAction } from './dashboard-action.interface';

export interface DashboardWidget {
    type: WidgetType;
    size: WidgetSize;
    componentName: string;
    id?: string;
    title?: string;
    titleKey?: string;
    isClosable?: boolean;
    showDetailsIcon?: boolean;
    currentIndex?: number;
    hasTitle?: boolean;
    getWidgetSize():  WidgetSize[];
    getWidgetExtraActions(): DashboardAction[];
}