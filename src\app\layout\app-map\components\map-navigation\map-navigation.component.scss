   .selected {
        color: var(--secondary-3);
        padding: 0 0 0 20px;
        :host-context(.rtl) &{
            padding: 0px 20px 0 0px;
        }
    }
    ul {
        padding: 0;
        margin: 0;
        li {
            list-style-type: none;
        }
    }
    
    a {
         color: var(--primary-1);
         &:hover {
            color: var(--secondary-3);
            text-decoration: none;
         }
    }