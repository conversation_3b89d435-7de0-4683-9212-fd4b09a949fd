import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { TranslateService } from '@ngx-translate/core';
import { EntityUploaderComponent } from 'app/layout/entities/entity-uploader/entityUploader.component';
import { CymsidebarService } from "app/shared/components/cym-sidebar/cym-sidebar.service";
import { CymUploaderComponent } from 'app/shared/components/cym-uploader/cym-uploader.component';
import { LightsUploaderComponent } from "app/shared/components/lights-uploader/lightsUploader.component";
import { TableCell } from "app/shared/components/ng-turbo-table/models/table.models";
import { Guid } from "app/shared/enum/guid";
import { ToastTypes } from 'app/shared/enum/toast-types';
import { Resource } from 'app/shared/modules/data-layer/models/resource';
import { InventoryTableComponent } from "app/shared/modules/inventory-table/components/inventory-table.component";
import { BusyIndicatorService } from "app/shared/services/busy-indicator.service";
import { DownloadTemplatesService } from "app/shared/services/download-templates.service";
import { EventService, EventType } from "app/shared/services/event.service";
import { ConfirmationService, MessageService } from 'primeng/api';
import { Subscription, forkJoin } from "rxjs";
import { InventoryActionType } from "./enums/inventory-action-type.enum";

import { ResourceKey } from "app/shared/models/ResourceKey.model";
import { DataChangeType } from "app/shared/modules/data-layer/models/data-change-type.enum";
import { MenolinxioService } from "app/shared/modules/data-layer/services/menolinx-io/menolinxio.service";
import { FactorCompensationService } from "app/shared/services/factor-compensation.service";
import { LumenFactorService } from "app/shared/services/lumen-factor.service";

import { FactorsModel } from "app/shared/models/factors.model";
import { IoResourceModel } from "app/shared/modules/data-layer/models/IoResource.model";
import { FactorSelectedItem } from "app/shared/modules/data-layer/models/inventory/factorSelectedItem.model";
import { LumenFactor } from "app/shared/modules/data-layer/models/lumenFactor.model";
import { ResourceCacheService } from "app/shared/modules/data-layer/services/resource/resource.cache.service";
import { TrafficFactorService } from "app/shared/services/traffic-factor.service";
import { WeatherFactorService } from "app/shared/services/weather-factor.service";
import {ResourceService} from "app/services/resource/resource.service";
import {ResourcePort} from "app/shared/modules/data-layer/services/resource/resource-port";
import { ResourceStateInfo } from "../../shared/modules/data-layer/models/resource-state-info";
import {InventoryOperationModel} from "app/shared/modules/data-layer/models/inventoryOperation.model";

@Component({
  selector: 'app-inventory-new',
  templateUrl: './inventory-new.component.html',
  styleUrls: ['./inventory-new.component.scss'],
  providers: [CymsidebarService, ConfirmationService],
})

export class InventoryNewComponent implements OnInit, OnDestroy {
  @ViewChild('overlayActions', {static: false}) overlayActions;
  @ViewChild('entityUploader', {static: false}) private entityUploader: EntityUploaderComponent;
  @ViewChild('lightsUploader', {static: false}) private lightsUploader: LightsUploaderComponent;
  @ViewChild('cymUploader', {static: false}) cymUploader: CymUploaderComponent;
  @ViewChild('inventory', {static: false}) inventory: InventoryTableComponent;

  resourceId: Guid = null;
  resourceManagerIsActive: boolean = false;
  showAddResource: boolean = false;
  selectedItems: { [columnField: string]: TableCell | string }[] = [];
  InventoryActionType = InventoryActionType;
  isFilterActive: boolean = false;
  resourceModelStore: Map<string,Resource> = null;
  subscriptions: Subscription[] = [];
  filterProperties: {[id: string]: string } = null;
  selectedFactorItems:FactorSelectedItem[]=[]
  itemsToRemove:ResourceKey[]=[];

  resources: Map<string,Resource> = null;

  public dropDownActions = [
    { type: InventoryActionType.downloadLightsTemplate },
    { type: InventoryActionType.downloadEntitiesTemplate },
    { type: InventoryActionType.upload }
  ]

  constructor(
    private cymsidebarService: CymsidebarService,
    private resourceCacheService:ResourceCacheService,
    private eventService: EventService,
    private resourceService: ResourceService,
    private downloadService: DownloadTemplatesService,
    private confirmationService: ConfirmationService,
    private translateService: TranslateService,
    private messageService: MessageService,
    private activeRouter: ActivatedRoute,
    public busyIndicator: BusyIndicatorService,
    private factorCompensationService:FactorCompensationService,
    private menolinxService:MenolinxioService,//TODO remove this coupling
    private lumenFactorService: LumenFactorService
    ) {

      this.factorCompensationService.itemsSelectedSubject.subscribe((response:FactorSelectedItem)=>{
        this.selectedFactorItems.map((item,index)=>{
          if(item.identity == response.identity && item.factorType == response.factorType){
            this.selectedFactorItems.splice(index, 1)

            return;
          }
        })
        this.selectedFactorItems.push(response);
      })

  }

  ngOnInit(){
    let activeRouteSubscription = this.activeRouter.queryParams.subscribe((params) => {
      if(!params){
        return;
      }
      this.filterProperties = params;

        this.initializeResources();

    });
    this.subscriptions.push(activeRouteSubscription);
  }

  private initializeResources():void
  {
      let subscription = this.resourceCacheService.storageCompleted.subscribe(() => {
          const resourcesArray: Resource[] = this.resourceCacheService.getAll();

          const hash = Object.assign({}, ...resourcesArray.map(s => ({[s.identity]: s})));
          this.resources=hash;

          let subscription = this.menolinxService.getMenolinxIoOutputs().subscribe((outputs:IoResourceModel[]) => {
              this.mapOutputsToResources(outputs);
          });
          this.subscriptions.push(subscription);

          subscription = this.menolinxService.getMenolinxIoInputs().subscribe((inputs:IoResourceModel[]) => {
              this.mapInputsToResources(inputs);
          });
          this.subscriptions.push(subscription);

          subscription = this.resourceService.getAllStatuses().subscribe((statuses:ResourceStateInfo[]) => {
              this.mapStatusesToResources(statuses);
          });
          this.subscriptions.push(subscription);
      });
      this.subscriptions.push(subscription);
  }

  ngOnDestroy() :void{
    this.subscriptions.forEach(subscription => {return subscription.unsubscribe();});
  }

  onSelectedElements(event: { [columnField: string]: TableCell | string }[]): void {
    this.selectedItems = event;

  }

  mapOutputsToResources(outputs:IoResourceModel[]): void{
    outputs.map(item=>{

            if(this.resources[item.guid]){

              if(item.metadata){
                this.resources[item.guid]['metadata'] = JSON.parse(item.metadata);
              }
            }
            });
            this.resourceModelStore=this.resources;
  }

  mapInputsToResources(inputs:IoResourceModel[]):void{
    inputs.map(item=>{
        if(this.resources[item.guid]){
          if(item.metadata && item.metadata.length > 0){
          this.resources[item.guid]['metadata'] = JSON.parse(item.metadata);
          }
        }
        });
      this.resourceModelStore = this.resources;

  }


  mapStatusesToResources(statuses):void{
    let operation = { type:DataChangeType.Update, models: []};

        statuses.forEach(status => {
          let idAsString = status.Id.toString();
          let resource = this.resources[idAsString];

          if(resource){
          let model:InventoryOperationModel = {identity: idAsString, status: status.State, name: resource.name, strength:{state: status.state}};
          resource.status = status.state;
          resource.strength =  status.strength;

          operation.models.push(model);
          }
        });

        this.inventory.notifyOperation(operation);
  }
  onSelectedItem(event: Guid): void {
    this.resourceId = null;
    setTimeout(() => {
      this.resourceId = event;
      if(this.showAddResource){
        this.showAddResource = false;
      }
    }, 0);
  }

  onFilterEvent(value: boolean): void{

    this.isFilterActive = value;
  }

  addDevice(): void{
    this.showAddResource = true;
    this.openSidebar();
  }

  delete(): void{
    this.confirmationService.confirm({
      message: this.translateService.instant('inventory.deleteConfirmationMessage'),
      header: this.translateService.instant('inventory.deleteConfirmationHeader'),
      icon: 'fa fa-trash',
      acceptLabel: this.translateService.instant('confirm'),
      rejectLabel: this.translateService.instant('cancel'),
      accept: () => {
        let itemIds=[];
        let index = 1;
        this.selectedItems.map((item) => {

          const resource = this.inventory.availableDevices.find((el: Resource) => {
            if(item && item.identity && (el.identity === item.identity)){

              let key:ResourceKey =new ResourceKey(el.identity,el.resourceType);

              this.itemsToRemove.push(key);
            }
            return  item  && item.identity && (el.identity === item.identity);
          });


        });

        this.resourceService.deleteResources(this.itemsToRemove).subscribe(() => {
          this.itemsToRemove=[];

          }, (e) => {
            console.error("Unable to delete the resources:", e);
            this.messageService.add({ severity: 'error', summary: this.translateService.instant(ToastTypes.error), detail: this.translateService.instant('cannotDeleteResources') });
        });


      }
    });
  }

  setComponent(route: InventoryActionType): void{
    switch (route) {
      case InventoryActionType.inventory:
        this.resourceManagerIsActive = false;
        break;
      case InventoryActionType.resourceManager:
        this.resourceManagerIsActive = true;
        break;
      default:
        this.resourceManagerIsActive = false;
        break;
    }
  }

  downloadFileLayout(name: InventoryActionType): void{
    switch (name) {
      case InventoryActionType.downloadLightsTemplate:
        this.downloadService.DownloadLightsTemplate();
        break;
      case InventoryActionType.downloadEntitiesTemplate:
        this.entityUploader.DownloadTemplate();
        break;
      case InventoryActionType.upload:
        this.cymUploader.open();
        break;
      default:
        console.error("Trying to download a file with unkown type:", name);
        break;
    }
  }
  resetTable(): void {
    this.inventory.resetTable();
    this.isFilterActive = false;
  }

  exportToCSV(): void{
    this.eventService.emit(EventType.ExportCSV);
  }

  uploadCSV(): void {
    this.lightsUploader.UploadFile();
  }

  openSidebar(): void{
    this.cymsidebarService.toggleDockSidebar("push");
    this.cymsidebarService.openSidebar();
  }

  openLumenFactorModal()
  {
    this.lumenFactorService.modalSubject.next(true);
  }

  openResourceGroupSelector()
  {
    this.lumenFactorService.resourceGroupModalSubject.next(true);
  }


}
