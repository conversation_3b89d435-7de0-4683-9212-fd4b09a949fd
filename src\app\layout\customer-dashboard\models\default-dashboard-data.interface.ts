import { Dashboard } from 'app/shared/modules/data-layer/models/dashboard';
import { DashboardObject } from './dashboard-object.interface';
import { DashboardState } from './dashboard-state.interface';

export interface DefaultDashboardData {
    state?: DashboardState;
    selectedDashboard?: DashboardObject;
    dashboardsTree?: Dashboard[];
    parentDashboardTree?: Dashboard;
    dashboardsObject?: {[id: string]: Dashboard};
}