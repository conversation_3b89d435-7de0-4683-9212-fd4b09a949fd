import { Widget } from "./widget";
import { DashboardWidget } from "./dashboard-widget.interface";
import { WidgetSize } from "../enums/widget-size.enum";
import { Guid } from "app/shared/enum/guid";
import { ServerTypes } from 'app/shared/modules/data-layer/enum/resource/resource-server-types.enum';

export class DashboxWidget extends Widget {

    selectedGroupId: string;
    selectedResourceStates: string[];
    selectedResourceType: ServerTypes;
    selectedBackgroundColourCode: string;
    selectedResourceIds: string[];

    constructor(obj?: DashboardWidget){
        super(obj);
        if(!this.selectedGroupId){
            this.selectedGroupId = Guid.EMPTY;
        }
        if(!this.selectedResourceIds){
            this.selectedResourceIds = [];
        }
        if(!this.selectedResourceType){
            this.selectedResourceType = null;
        }
    }

    getWidgetSize(): WidgetSize[]{
        return [WidgetSize.small, WidgetSize.xMedium];
    }

}