<div class="table-container">
    <div class="inventory-container" [ngClass]="selectedResourceId ? 'divided' : 'full'">
        <p-table 
            [value]="data"
            [loading]="loading"
            [paginator]="true"
            [rows]="10"
            [totalRecords]="data ? data.length : 0"
            [lazy]="false"
            selectionMode="multiple"
            [(selection)]="selectedRows"
            dataKey="identity"
            (onRowSelect)="onRowSelect($event.data)"
            (selectionChange)="onSelectionChange($event)"
            (onFilter)="filterEvent.next($event)"
            (onPage)="onPageChange($event)"
            [first]="pageIndex * 10"
            [showCurrentPageReport]="true"
            styleClass="p-datatable-striped"
            [scrollable]="false"
            [resizableColumns]="true"
            [reorderableColumns]="true">

            <ng-template pTemplate="caption" *ngIf="showTableCaption">
                <div class="p-d-flex p-jc-between p-ai-center table-header">
                    <div class="table-title">{{ 'inventory.results' | translate }}: {{data ? data.length : 0}}</div>
                    <div class="table-actions">
                        <p-multiSelect [options]="columnSelectionOptions" [(ngModel)]="selectedColumns" 
                            optionLabel="header" [showToggleAll]="true" [showHeader]="false"
                            selectedItemsLabel="{{ 'colsSelected' | translate }}" 
                            (onChange)="onColumnToggle($event)"
                            styleClass="column-selector">
                            <ng-template let-column pTemplate="item">
                                <div class="p-multiselect-column-option">
                                    <span>{{column.header | translate}}</span>
                                </div>
                            </ng-template>
                        </p-multiSelect>
                    </div>
                </div>
            </ng-template>

            <ng-template pTemplate="header">
                <tr>
                    <th *ngFor="let col of visibleColumns" [pSortableColumn]="col.field">
                        {{ col.header | translate }}
                        <p-sortIcon [field]="col.field"></p-sortIcon>
                    </th>
                    <th style="text-align: right; width: 150px;">{{ 'actions' | translate }}</th>
                </tr>
                <tr>
                    <th *ngFor="let col of visibleColumns">
                        <p-columnFilter *ngIf="col.isFilterable" [field]="col.field" [matchMode]="col.filterMatchMode"></p-columnFilter>
                    </th>
                    <th></th>
                </tr>
            </ng-template>

            <ng-template pTemplate="body" let-rowData>
                <tr [pSelectableRow]="rowData" [pSelectableRowDisabled]="!checkIfRender(rowData)">
                    <td *ngFor="let col of visibleColumns">
                        <ng-container *ngIf="col.field === 'status' && rowData.status">
                            <ng-container *ngIf="rowData.status.template">
                                <ng-container *ngTemplateOutlet="rowData.status.template; context: {$implicit: rowData.status.data}"></ng-container>
                            </ng-container>
                            <ng-container *ngIf="!rowData.status.template">
                                {{rowData.status}}
                            </ng-container>
                        </ng-container>
                        <ng-container *ngIf="col.field === 'IO_Status' && rowData.IO_Status">
                            <ng-container *ngIf="rowData.IO_Status.template">
                                <ng-container *ngTemplateOutlet="rowData.IO_Status.template; context: {$implicit: rowData.IO_Status.data}"></ng-container>
                            </ng-container>
                            <ng-container *ngIf="!rowData.IO_Status.template">
                                {{rowData.IO_Status}}
                            </ng-container>
                        </ng-container>
                        <ng-container *ngIf="col.field === 'identity'">
                            <p class="entity-id" [pTooltip]="rowData[col.field]" tooltipPosition="top">{{rowData[col.field]}}</p>
                        </ng-container>
                        <ng-container *ngIf="col.field !== 'status' && col.field !== 'IO_Status' && col.field !== 'identity' && col.field !== 'actions'">
                            {{rowData[col.field]}}
                        </ng-container>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <ng-container *ngIf="rowData.actions && rowData.actions.template">
                                <ng-container *ngTemplateOutlet="rowData.actions.template; context: {$implicit: rowData}"></ng-container>
                            </ng-container>
                            <ng-container *ngIf="!rowData.actions || !rowData.actions.template">
                                <ng-container *ngIf="rowData.resourceType === 'Light' || rowData.resourceType === 'MercuryHalideLight' || rowData.resourceType === 'LED'">
                                    <ng-container *ngTemplateOutlet="actionsLight; context: {$implicit: rowData}"></ng-container>
                                </ng-container>
                                <ng-container *ngIf="rowData.resourceType === 'DigitalInput' || rowData.resourceType === 'DigitalOutput'">
                                    <ng-container *ngTemplateOutlet="actionsIOcommands; context: {$implicit: rowData}"></ng-container>
                                </ng-container>
                                <ng-container *ngIf="rowData.resourceType === 'Channel'">
                                    <ng-container *ngTemplateOutlet="actionsChannel; context: {$implicit: rowData}"></ng-container>
                                </ng-container>
                                <ng-container *ngIf="rowData.resourceType === 'IO_Input'">
                                    <ng-container *ngTemplateOutlet="actionsIOInput; context: {$implicit: rowData}"></ng-container>
                                </ng-container>
                                <ng-container *ngIf="rowData.resourceType === 'PowerMeter'">
                                    <ng-container *ngTemplateOutlet="actionsPowerMeter; context: {$implicit: rowData}"></ng-container>
                                </ng-container>
                                <ng-container *ngIf="rowData.resourceType === 'Map'">
                                    <ng-container *ngTemplateOutlet="actionsMap; context: {$implicit: rowData}"></ng-container>
                                </ng-container>
                                <ng-container *ngIf="rowData.resourceType === 'ResetResource'">
                                    <ng-container *ngTemplateOutlet="resetResourceActions; context: {$implicit: rowData}"></ng-container>
                                </ng-container>
                                <ng-container *ngIf="rowData.resourceType !== 'Light' && rowData.resourceType !== 'MercuryHalideLight' && rowData.resourceType !== 'LED' && rowData.resourceType !== 'Channel' && rowData.resourceType !== 'PowerMeter' && rowData.resourceType !== 'Map' && rowData.resourceType !== 'ResetResource' && rowData.resourceType !== 'DigitalInput' && rowData.resourceType !== 'DigitalOutput' && rowData.resourceType !== 'IO_Input'">
                                    <ng-container *ngTemplateOutlet="actionsDefault; context: {$implicit: rowData}"></ng-container>
                                </ng-container>
                            </ng-container>
                        </div>
                    </td>
                </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="{{visibleColumns.length + 1}}" class="text-center">{{ 'inventory.noItemsFound' | translate }}</td>
                </tr>
            </ng-template>
        </p-table>
        
        <app-inventory-details *ngIf="selectedResourceId"
            [title]="selectedResourceDetails?.name || 'Resource Details'"
            [resource]="selectedResourceDetails"
            [resourceId]="selectedResourceId"
            [loading]="detailsLoading"
            [isEditMode]="editingResourceId === selectedResourceId"
            (closeDetails)="onCloseDetails()"
            (saveResource)="onSaveResource($event)">
        </app-inventory-details>
    </div>
</div>

<ng-template #actionsLight let-rowData>
    <app-default-actions [row]='rowData' (selectedItem)="selectedItem.next($event)" ></app-default-actions>
    <app-light-actions [row]='rowData' ></app-light-actions>

    <div class="buttonGroupLightAction">
        <app-check-weather [row]='rowData' [selectedItems]="selectedFactorItems" ></app-check-weather>
        <app-check-traffic [row]='rowData' [selectedItems]="selectedFactorItems"></app-check-traffic>
         <app-ioligntinfo [row]='rowData'></app-ioligntinfo>
        <app-light-power-action [row]='rowData'></app-light-power-action>
    </div>
</ng-template>


<ng-template #actionsChannel let-rowData>
    <app-default-actions [row]='rowData' (selectedItem)="selectedItem.next($event)" ></app-default-actions>
    <app-channel-actions [row]='rowData'></app-channel-actions>
</ng-template>

<ng-template #resetResourceActions let-rowData>
    <app-default-actions [row]='rowData' (selectedItem)="selectedItem.next($event)" ></app-default-actions>
    <app-reset-resource [row]='rowData'></app-reset-resource>

</ng-template>



<ng-template #actionsIOcommands let-rowData>
    <app-iocommands [row]='rowData' (selectedItem)="selectedItem.next($event)" ></app-iocommands>
   <app-ioligntinfo [row]='rowData'></app-ioligntinfo>
   <app-default-actions [row]='rowData' (selectedItem)="selectedItem.next($event)" ></app-default-actions>

</ng-template>

<ng-template #actionsIOInput let-rowData>
   <app-ioligntinfo [row]='rowData'></app-ioligntinfo>
   <app-default-actions [row]='rowData' (selectedItem)="selectedItem.next($event)" ></app-default-actions>

</ng-template>


<ng-template #actionsMap let-rowData>
    <app-default-actions [row]='rowData' (selectedItem)="selectedItem.next($event)" ></app-default-actions>
    <app-map-actions [row]='rowData'></app-map-actions>

</ng-template>

<ng-template #actionsPowerMeter let-rowData>
    <app-default-actions [row]='rowData' (selectedItem)="selectedItem.next($event)" ></app-default-actions>
    <app-power-meter-actions [row]='rowData'></app-power-meter-actions>
    <app-ioligntinfo [row]='rowData'></app-ioligntinfo>
</ng-template>

<ng-template #actionsDefault let-rowData>
    <app-default-actions [row]='rowData' (selectedItem)="selectedItem.next($event)" ></app-default-actions>
</ng-template>


<ng-template #lightGw let-rowData>
    <app-default-actions [row]='rowData' (selectedItem)="selectedItem.next($event)" ></app-default-actions>
    <app-ioligntinfo [row]='rowData'></app-ioligntinfo>
</ng-template>

<ng-template #statusCell let-rowData>
    <app-resource-status [status]='rowData'></app-resource-status>
</ng-template>

<ng-template #IostatusCell let-rowData>
    <app-resource-status [status]='rowData'></app-resource-status>
</ng-template>


<app-modal [title]="'setLumenFactor'" #lumenFactorModal>
    <ng-container ngProjectAs="contentModal">
      <app-lumen-modal></app-lumen-modal>
    </ng-container>
</app-modal>


<app-modal [title]="'setLumenFactor'" #resourceGroupSelector>
    <ng-container ngProjectAs="contentModal">
      <app-resource-group-selector ></app-resource-group-selector>
    </ng-container>
</app-modal>
