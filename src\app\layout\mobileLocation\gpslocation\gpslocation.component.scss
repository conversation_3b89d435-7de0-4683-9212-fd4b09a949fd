:host {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.map-container {
    flex-grow: 1;
}
footer {
    background-color: var(--header-color);
    padding: 1em;
}

.mainContainer {
    padding-left: 10px;
    padding-right: 10px;
    display: flex;
    flex-direction: column;
    height: 100vh;
}
.mainDropdown{
    margin-bottom: 10px;
}
.mainTitle{
    padding-left: 10px;
    margin-top: 10px;
}
.mainButton{
    margin-bottom: 10px;
    margin-top: 10px;
}

.map-wrapper {
    width: 100%;
    height: 100%;
}

.map-layout-content, .map-wrapper {
    width: 100%;
    height: 100%;
}
.buttonContainer{
    display: flex;
    align-items: center;
    margin-top: 10px;
    margin-bottom: 10px;
}
.buttonContainer p{
    padding: 0px;
    margin: 0px;
    margin-left: 10px;
}
.buttonContainer i{
    border: 1px solid var(--secondary-8);
    padding: 5px;
    border-color: var(--secondary-8);
    background-color: var(--secondary-8);
    color: #fff;
}
