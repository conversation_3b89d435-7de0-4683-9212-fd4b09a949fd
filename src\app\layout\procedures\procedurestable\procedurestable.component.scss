:host {
    display: block; // Ensures the component behaves like a block element
    width: 100%;    // Sets width to 100% of its parent container
  }
.procedureHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    margin-bottom: 1rem;
}

.header-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    span {
        font-weight: 500;
        color: #666;
    }

    input, select {
        padding: 0.75rem;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        min-width: 200px;
        background-color: #f8f9fa;
    }

    button {
        padding: 0.75rem 1.5rem;
        background-color: #00c853;
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        transition: background-color 0.2s;
        font-weight: 500;

        &:hover {
            background-color: #00a844;
        }
    }
}

.procedure-row {
    display: flex;
    align-items: center;
    padding: 0.2rem 1rem;
    gap: 1rem;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    color: white;
    width: 100%;
    justify-content: space-between;

    .procedure-status {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        min-width: 200px;
    }

    .procedure-date {
        color: rgba(255, 255, 255, 0.9);
        white-space: nowrap;
    }
}

.procedures-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);

    .header-left {
        display: flex;
        align-items: center;
        gap: 24px;
        flex: 1;

        .header-item {
            display: flex;
            flex-direction: row !important;
            gap: 8px;

            label {
                color: #00c853;
                font-size: 14px;
                font-weight: 500;
            }

            &:nth-child(2) {
                flex: 1;
                min-width: 200px;
            }
        }
    }

    .header-right {
        display: flex;
        align-items: center;
        gap: 16px;
    }

    .search-input {
        width: 100%;
        height: 40px;
        padding: 8px 16px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        background: #fff;
        font-size: 14px;

        &::placeholder {
            color: #757575;
        }

        &:focus {
            outline: none;
            border-color: #00c853;
        }
    }

    .template-select {
        height: 40px;
        padding: 8px 12px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        background: #fff;
        min-width: 160px;
        font-size: 14px;
        color: #212121;

        &:focus {
            outline: none;
            border-color: #00c853;
        }
    }

    .create-button {
        display: flex;
        align-items: center;
        gap: 8px;
        height: 40px;
        padding: 0 26px;
        background: #00c853;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;

        i {
            font-size: 14px;
        }

        &:hover {
            background: #00b248;
        }
    }
}

:host ::ng-deep {
    .status-dropdown {
        .p-dropdown {
            height: 40px;
            min-width: 160px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background: #fff;

            .p-dropdown-label {
                padding: 8px 16px;
                font-size: 14px;
                color: #212121;
            }

            .p-dropdown-trigger {
                width: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #757575;
            }

            &:not(.p-disabled).p-focus {
                border-color: #00c853;
                box-shadow: none;
            }
        }
    }
}

.table {
    width: 100%;
    margin: 1rem 0;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;

    th {
        padding: 1rem;
        font-weight: 500;
        color: #333;
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
    }

    td {
        padding: 1rem;
        vertical-align: middle;
        border-bottom: 1px solid #e9ecef;

        input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f8f9fa;
        }

        select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f8f9fa;
        }

        button {
            padding: 0.75rem 1rem;
            background-color: #00c853;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            width: auto;
            min-width: 100px;
            font-weight: 500;

            &:hover {
                background-color: #00a844;
            }
        }
    }
}

.headAllignCenter {
    text-align: center;
}


.None {
    background-color: #e0e0e0;
    color: #333;
}

.Open {
    background-color: #ff9800;
    color: white;
}

.Close {
    background-color: #f44336;
    color: white;
}

.InProgress {
    background-color: #4caf50;
    color: white;
}

.Waiting {
    background-color: #2196f3;
    color: white;
}

.Snoozed {
    background-color: #9c27b0;
    color: white;
}

.Archive {
    background-color: #757575;
    color: white;
}

.mainPaginationContainer {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}


.status-icon {
    width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;

    img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
}

.chevron-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    cursor: pointer;
    margin-left: auto;
    transition: all 0.2s ease;

    &:hover {
        background: rgba(255, 255, 255, 0.3);
    }

    i {
        color: white;
        font-size: 14px;
    }
}


:host ::ng-deep {
    .p-panel {
        margin-bottom: 1rem;
        border: none;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);

        &.p-panel-toggleable {
            .p-panel-header {
                cursor: pointer;
            }
        }

        .p-panel-header {
            border: none;
            padding: 0;
            background: transparent;
            width: 100%;

            &:hover {
                .chevron-button {
                    background: rgba(255, 255, 255, 0.3);
                }
            }
        }

        .p-panel-content {
            border: none;
            background-color: #fff;
            padding: 1rem;
        }


        &.p-panel-expanded {
            .chevron-button {
                i {
                    transform: rotate(180deg);
                }
            }
        }


        &.None {
            .p-panel-header {
                background-color: #e0e0e0;
                .procedure-row {
                    color: #333;
                    .procedure-date {
                        color: rgba(0, 0, 0, 0.6);
                    }
                    .chevron-button {
                        background: rgba(0, 0, 0, 0.1);
                        i {
                            color: #333;
                        }
                        &:hover {
                            background: rgba(0, 0, 0, 0.15);
                        }
                    }
                }
            }
        }

        &.Open {
            .p-panel-header {
                background-color: #ff9800;
            }
        }

        &.Close {
            .p-panel-header {
                background-color: #f44336;
            }
        }

        &.InProgress {
            .p-panel-header {
                background-color: #4caf50;
            }
        }

        &.Waiting {
            .p-panel-header {
                background-color: #2196f3;
            }
        }

        &.Snoozed {
            .p-panel-header {
                background-color: #9c27b0;
            }
        }

        &.Archive {
            .p-panel-header {
                background-color: #757575;
            }
        }
    }


    .fa-check {
        color: #ffffff;
        font-size: 20px;
        padding: 10px;
        border-radius: 6px;
        text-align: center;
        background-color: #4caf50;
    }
}


:host ::ng-deep .p-panel .p-panel-header .p-panel-header-icon {
    width: 2rem !important;
    height: 2rem !important;
    color: #6c757d !important;
    border: 0 none !important;
    background: #ffffff !important;
    border-radius: 10px;
    transition: box-shadow 0.15s !important;
    margin-right: 10px !important;
}

.actionList {
    list-style-type: none;
    display: flex;
    gap: 0.5rem;
    margin: 0;
    padding: 0;
    justify-content: center;

    li {
        width: 36px;
        height: 36px;
        border: none;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s;
        background-color: #e2fcef;

        &:hover {
            background-color: #ccf8e3;
        }

        i {
            font-size: 16px;
            color: #00c853;
        }
    }
}

.table-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 16px 24px;
        background: #fff;
        border-radius: 4px;
        margin-bottom: 16px;
        flex-direction: column;
        width: 100%;

    .header-left {
        display: flex;
            align-items: center;
            gap: 48px;
            width: 100%;
            justify-content: space-between;

        .header-item {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 8px;

            span {
                color: #00c853;
                font-size: 14px;
                font-weight: 500;
            }

            &.search-item {
                min-width: 300px;
            }
        }
    }

    .header-right {
        display: flex;
        align-items: center;
        gap: 12px;
        width: 100%;
        margin-top: 30px;

        .export-buttons {
            display: flex;
            gap: 8px;
            align-items: center;
            margin-left: auto;

            .export-btn {
                background: #E4ECE6;
                border: 1px solid #D9D9D9;
                color: #00D600;
                width: 2.5rem !important;
                height: 2.5rem !important;
                border-radius: 8px;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                padding: 0 !important;
                margin: 0 !important;
                min-width: auto !important;
                box-sizing: border-box;

                &:hover:not(:disabled) {
                    background: #00D600;
                    color: white;
                    border-color: #00D600;
                }

                &:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                }

                i {
                    font-size: 1rem !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    line-height: 1 !important;
                    display: block !important;
                    text-align: center !important;
                    vertical-align: middle !important;
                    position: static !important;
                    left: auto !important;
                    top: auto !important;
                    transform: none !important;
                }

                &:focus {
                    box-shadow: 0 0 0 0.2rem rgba(0, 214, 0, 0.25);
                }
            }
        }
    }

    .search-input {
        width: 100%;
        height: 36px;
        padding: 0 12px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        font-size: 14px;
        color: #212121;

        &::placeholder {
            color: #757575;
        }

        &:focus {
            outline: none;
            border-color: #00c853;
        }
    }

    .template-select {
        height: 36px;
        padding: 0 12px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        min-width: 160px;
        font-size: 14px;
        color: #212121;
        background: #fff;

        &:focus {
            outline: none;
            border-color: #00c853;
        }
    }

    .create-button {
        display: flex;
        align-items: center;
        gap: 8px;
        height: 36px;
        padding: 0 16px;
        background: #00c853;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;

        i {
            font-size: 14px;
        }

        &:hover {
            background: #00b248;
        }
    }
}

:host ::ng-deep {
    .status-dropdown {
        .p-dropdown {
            height: 36px;
            min-width: 140px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background: #fff;

            .p-dropdown-label {
                padding: 0 12px;
                font-size: 14px;
                color: #212121;
                display: flex;
                align-items: center;
            }

            .p-dropdown-trigger {
                width: 36px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #757575;
            }

            &:not(.p-disabled).p-focus {
                border-color: #00c853;
                box-shadow: none;
            }
        }
    }
}

thead tr th{
    background-color: transparent;
    font-weight: bold;
}

.step-progress-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 16px;
    flex-wrap: wrap;

    .step-circle {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        cursor: pointer;
        transition: transform 0.2s ease;
        position: relative;
        z-index: 1;

        &:hover {
            transform: scale(1.1);
        }
    }

    .step-connector {
        color: rgba(255, 255, 255, 0.7);
        font-size: 14px;
        display: flex;
        align-items: center;
        padding: 0 4px;
        cursor: pointer;
        transition: color 0.2s ease;

        &:hover {
            color: white;
        }
    }
}

.procedure-button{
    background-color: #00c854;
    color: #fff;
    padding: 5px;
    font-weight: bold;
    border-radius: 5px;
    margin-top: 9px;
}
.procedure-header{
    width: 100%;
}
.generalDetectionButton{
    padding: 5px;
}

// Override PrimeNG button styles to ensure perfect icon centering
:host ::ng-deep {
    .export-btn {
        .p-button-label {
            display: none !important;
        }

        .p-button-icon {
            margin: 0 !important;
            padding: 0 !important;
            position: static !important;
            left: auto !important;
            top: auto !important;
            transform: none !important;
        }

        &.p-button-text {
            padding: 0 !important;
            min-width: auto !important;
            width: 2.5rem !important;
            height: 2.5rem !important;
        }

        // Force flexbox centering
        &.p-button {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            padding: 0 !important;
            margin: 0 !important;

            .p-button-icon-left {
                margin-right: 0 !important;
            }

            .p-button-icon-right {
                margin-left: 0 !important;
            }
        }
    }
}
