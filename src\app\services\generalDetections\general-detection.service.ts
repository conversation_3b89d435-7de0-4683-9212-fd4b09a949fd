import { DetectionTypeEnum } from './../../shared/enum/detection-types.enum';
import { SumalDetectionState } from './../../shared/models/sumal-detection-state';
import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";
import { GeneralDetection } from "../../shared/modules/data-layer/models/general-detections/general-detection";
import { PageResult } from "../../shared/modules/data-layer/models/paging/page-result";
import { environment } from "../../../environments/environment";
import { apiMap } from "../../shared/services/api.map";
import { HttpClient, HttpResponse } from "@angular/common/http";
import { map } from "rxjs/operators";
import { DownloadEventArchiveResponse } from "../../layout/general-detections/models/download-event-archive-response.interface";
import { Guid } from "app/shared/enum/guid";
import { GeneralDetectionExport } from '../../layout/general-detections/models/general-detections-filter.model';

@Injectable({
    providedIn: 'root'
})
export class GeneralDetectionService {

    constructor(private httpClient: HttpClient) {
    }

    public getPage(pageIndex: number, pageSize: number, startTimeStamp:Date, endTimeStamp:Date, resourceGroupId: string, searchValue1: string, searchValue2: string, eventId?: string)
    : Observable<PageResult<GeneralDetection>> {

        const startTimeStampString = startTimeStamp.toISOString();
        const endTimeStampString = endTimeStamp.toISOString();
        resourceGroupId = resourceGroupId ? resourceGroupId : Guid.EMPTY;
        searchValue1 = searchValue1?.trim() || "-";
        searchValue2 = searchValue2?.trim() || "-";
        eventId = eventId?.trim() || "-";

        const url = environment.apiUrl + apiMap.generalDetectionsPage.url + pageIndex + "/" + pageSize + "/" + startTimeStampString + "/" + endTimeStampString +
            "/" + resourceGroupId + "/" + searchValue1 + "/" + searchValue2 + '/' + eventId;

        return this.httpClient.get(url).pipe(map((detectionsPage: any)=>{

            return this.parsePage(detectionsPage);
        }));
    }

    public exportGeneralDetections(request: GeneralDetectionExport): Observable<HttpResponse<Blob>> {
        const url = environment.apiUrl + apiMap.generalDetectionsExport.url;
        return this.httpClient.post(url, request, {
            responseType: 'blob',
            observe: 'response'
        });
    }

    public getDetectionByState(pageIndex: number, pageSize: number, startTimeStamp:Date, endTimeStamp:Date, state: DetectionTypeEnum) : Observable<PageResult<GeneralDetection>> {
        const startTimeStampString = startTimeStamp.toISOString();
        const endTimeStampString = endTimeStamp.toISOString();
        const url = environment.apiUrl + apiMap.generalDetectionsPageByState.url + pageIndex + "/" + pageSize + "/" + startTimeStampString + "/" + endTimeStampString + '/' + state || "-";
        return this.httpClient.get(url).pipe(map((detectionsPage: any)=>{
            return this.parsePage(detectionsPage);
        }));
    }

    public getPageByDeviceId(pageIndex: number, pageSize: number, deviceId: string): Observable<PageResult<GeneralDetection>>
    {
        let url = environment.apiUrl + apiMap.generalDetectionsPage.url + pageIndex + "/" + pageSize + "/" +deviceId;

        return this.httpClient.get(url).pipe(map((detectionsPage: any) => {

            return this.parsePage(detectionsPage);
        }));
    }

    public downloadEventArchive(eventId: string): Observable<DownloadEventArchiveResponse> {

        let url = environment.apiUrl + apiMap.downloadDetectionArchive.url + eventId;
      
        return this.httpClient.get(url, { responseType: 'blob', observe: 'response' }).pipe(
          map((response: HttpResponse<Blob>) => ({
            data: response.body as Blob,
            contentType: response.headers.get('Content-Type') || 'application/octet-stream',
            contentDisposition: response.headers.get('Content-Disposition') || '',
          }))
        );
      }

    public getDetectionsCount(lastHours: number): Observable<number>
    {
        let url = environment.apiUrl + apiMap.generalDetectionsCount.url + lastHours;

        return this.httpClient.get<number>(url);
    }

    public getAlarmsCount(): Observable<number>{
        const state : number = Number(SumalDetectionState.Alarm);

        const url = `${environment.apiUrl}${apiMap.detectionCountByState.url}${state}`;

        return this.httpClient.get<number>(url);
    }

    public getFelonyCount(): Observable<number>{
        const url = `${environment.apiUrl}${apiMap.felonyCount.url}`;

        return this.httpClient.get<number>(url);
    }

    private parsePage(detectionsPage:any): PageResult<GeneralDetection>
    {
        let totalCount: number = detectionsPage.totalCount;
        let items: any[] = <any[]>detectionsPage.items;
        let detections: GeneralDetection[] = items.map(item=>new GeneralDetection(item));
        let pageResult: PageResult<GeneralDetection> = new PageResult(totalCount,detections);

        return pageResult;
    }
}