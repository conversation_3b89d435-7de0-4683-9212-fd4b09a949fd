export enum InventoryActionType {
    downloadLightsTemplate = "downloadLightsTemplate",
    downloadEntitiesTemplate = "downloadEntitiesTemplate",
    edit = 'edit',
    dimLevel = 'dimLevel', 
    refresh = "refresh",
    save = "save",
    delete = "delete",
    upload = "upload",
    download = "download",
    camera = "camera",
    inventory = 'inventory',
    resourceManager = 'resourceManager',
    jumpToMap = "jumpToMap",
    resetResource = "resetResource",
    uploadEntities = "uploadEntities",
    uploadRTSP = 'uploadRTSP'
}
