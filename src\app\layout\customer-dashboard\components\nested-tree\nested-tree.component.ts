import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { Dashboard } from 'app/shared/modules/data-layer/models/dashboard';
import { DashboardUtilsService } from '../../services/dashboard-utils.service';



@Component({
  selector: 'app-nested-tree',
  templateUrl: './nested-tree.component.html',
  styleUrls: ['./nested-tree.component.scss']
})
export class NestedTreeComponent implements OnInit {
  hasChildren: boolean;
  @Input() hasParent: boolean;
  @Input() item: Dashboard;
  public isChildVisible: boolean = false;

  constructor(
    private dashboardUtilsService: DashboardUtilsService
  ) { }

  ngOnInit() {
    this.hasChildren = this.item.children? this.item.children.length > 0: false;
  }

  selectTreeItem(item: Dashboard):void {
    this.dashboardUtilsService.setSelectParentDashboardTreeitem(item);
  }

  showChildren(){
    this.isChildVisible = !this.isChildVisible;  
  }

}
