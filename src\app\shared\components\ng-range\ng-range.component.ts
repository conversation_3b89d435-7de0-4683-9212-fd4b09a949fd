import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-range',
  templateUrl: './ng-range.component.html',
  styleUrls: ['./ng-range.component.scss']
})
export class DatetimeRangePickerComponent {

  @Input()
  displayFormat: string = 'DD/MM/YYYY HH:mm';

  @Input()
  fromTime = new Date();

  @Input()
  locale: any;

  @Input()
  toTime = new Date();

  @Output()
  valueChange = new EventEmitter();

  @Input()
  inline: boolean = true;

  @Input()
  showDate: boolean = true;

  @Input()
  showTime: boolean = true;

  @Input()
  isDateRangePicker: boolean = false;

  onFromTimeChange(val) {
    if (val) {
      this.fromTime.setFullYear(val.getFullYear(), val.getMonth(), val.getDate());
      this.fromTime.setHours(val.getHours(), val.getMinutes(), val.getSeconds());
      this.valueChange.emit({ from: this.fromTime, to: this.toTime });
    }else{
      this.valueChange.emit({ from: null, to: this.toTime });
    }
  }


  onToTimeChange(val) {
    if(val){
      this.toTime.setFullYear(val.getFullYear(), val.getMonth(), val.getDate());
      this.toTime.setHours(val.getHours(), val.getMinutes(), val.getSeconds());
      this.valueChange.emit({ from: this.fromTime, to: this.toTime });
    }else{
      this.valueChange.emit({ from: this.fromTime, to: null });
    }
  }
}
