<div class="entities-container">
    <app-page-title title="entities"></app-page-title>
    <div class="container-fluid">
        <div class="row border-bottom">
            <div class="col inline-headers align-self-center">
                <ul class="list-inline heading-list list-format">
                    <li class="list-inline-item li-heading">
                        <a class="active" href="javascript:void(0)">
                            {{ 'entities' | translate }}
                        </a>
                    </li>
                </ul>
            </div>
            <div class="col structure-floating align-self-center">
                <ul class="list-inline icon-list list-format">
                    <li class="list-inline-item">
                        <button class="btn btn-link" (click)="entityUploader.UploadFile()" pTooltip="{{ 'uploadEntities' | translate }}"
                                tooltipPosition="left">
                            <i class="fa fa-upload"></i>
                        </button>
                    </li>
                    <li class="list-inline-item">
                        <button class="btn btn-link" (click)="toggleLiveUpdates()" pTooltip="{{ liveUpdates ? 'Pause Live Updates' : 'Resume Live Updates' | translate }}"
                                tooltipPosition="left">
                            <i [class]="liveUpdates ? 'fa fa-pause' : 'fa fa-play'"></i>
                        </button>
                    </li>
                </ul>
            </div>
        </div>
        <div class="row content-router">
            <div class="col-md-6">
                <input type="text" placeholder="Filter by name" [(ngModel)]="filterText" (input)="applyFilter()">
            </div>
            <p-dataView #dv [value]="data" [paginator]="true" [rows]="9" filterBy="entity"
                        [lazy]="true" [totalRecords]="totalRecords"  (onLazyLoad)="pageLoad($event)" [loading]="loading"
                        layout="list" class="gridDataview">
                <ng-template let-entity pTemplate="listItem">
                    <entity-item [entity]="entity" class="entityItem" (addEntity)="addEntity($event)"></entity-item>
                </ng-template>
            </p-dataView>
        </div>
    </div>

    <p-dialog [header]="formHeader" [(visible)]="displayForm" [modal]="true"  [baseZIndex]="10000" [draggable]="false" [resizable]="false" class="custom-dialog" (onHide)="closeForm()">
        <app-entity-form #form (displayForm)="closeForm()" (saveEntity)="saveEntity($event)" (deleteEntity)="deleteEntity($event)" [newEntity]="newEntity" [newEntityDevice]="newEntityDevice">
        </app-entity-form>
    </p-dialog>
    <app-entity-uploader #entityUploader></app-entity-uploader>
</div>
