import { Component, ComponentFactoryResolver, ComponentRef, EventEmitter, OnDestroy, OnInit, Output, QueryList, ViewChild, ViewChildren, ViewContainerRef } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { ResourceService } from 'app/services/resource/resource.service';
import { ToastTypes } from 'app/shared/enum/toast-types';
import { MapLayer } from 'app/shared/modules/data-layer/models/map-layer';
import { Resource } from 'app/shared/modules/data-layer/models/resource';
import { MapLayerService } from 'app/shared/modules/data-layer/services/map-layer/map-layer.service';
import * as _ from 'lodash';
import { MessageService } from 'primeng/api';
import { Subscription } from 'rxjs';
import { MapActionType } from '../../enums/map-action-type.enum';
import { MapObject } from '../../models/map-object.interface';
import { MapState } from '../../models/map-state.interface';
import { MapUtilsService } from '../../services/map-utils.service';
import { DefaultMapEditorComponent } from '../default-map-editor/default-map-editor.component';
import { MapLayersFormComponent } from '../map-layers-form/map-layers-form.component';



@Component({
  selector: 'app-map-layers-edit',
  templateUrl: './map-layers-edit.component.html',
  styleUrls: ['./map-layers-edit.component.scss']
})
export class MapLayersEditComponent extends DefaultMapEditorComponent implements OnInit, OnDestroy {

  data: {
    state: MapState,
    selectedMap: MapObject
  }
  public manageLayers: boolean = false;

  public layerForm: FormGroup = null;
  @Output('onAddLayer') onAddLayer: EventEmitter<MapLayer[]> = new EventEmitter();
  private subscriptions: Subscription[] = [];

  public mapLayersModelStore: {[id:string]: MapLayer} = null;
  public resourceModelStore: {[id:string]: Resource} = null;
  public mapLayers: MapLayer[] = [];
  public resources: Resource[] = [];
  private selectedMapLayer: MapLayer = null;

  componentRef: ComponentRef<any>;
  @ViewChild('newDynamicForm', {read: ViewContainerRef, static: false}) public newDynamicFormtarget: ViewContainerRef;
  @ViewChildren('dynamicForm', {read: ViewContainerRef}) public dynamicFormTargets: QueryList<ViewContainerRef>;

  constructor(
    private mapLayerService: MapLayerService,
    private resourceService: ResourceService,
    private componentFactoryResolver: ComponentFactoryResolver,
    private mapUtilsService: MapUtilsService,
    private messageService: MessageService,
    private translateService: TranslateService
  ) {
    super();

  }

  ngOnInit() {

    let mapLayersSubscription = this.mapLayerService.getAll().subscribe(res => {
      this.mapLayersModelStore = res;
      this.mapLayers = _.values(res);
    })
    this.subscriptions.push(mapLayersSubscription);

    let resourcesSubscription = this.resourceService.getAll().subscribe(res => {
      this.resources = res;
    })
    this.subscriptions.push(resourcesSubscription);
  }

  ngOnDestroy(){
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
  }



  public editLayer(mapLayer: MapLayer, index: number){
    let container = this.dynamicFormTargets.toArray();
    this.selectedMapLayer = mapLayer;
    this.createFormComponent(container[index], false, mapLayer);
  }

  public addLayer(){
    this.selectedMapLayer = null;
    this.createFormComponent(this.newDynamicFormtarget, true)
  }

  private createFormComponent(containerRef: ViewContainerRef, isNew:boolean, data?: MapLayer): void {
    if(this.componentRef){
      this.componentRef.destroy();
    }
    const factory = this.componentFactoryResolver.resolveComponentFactory(MapLayersFormComponent);
    this.componentRef = containerRef.createComponent(factory);
    this.componentRef.instance.data = {resources: this.resources, isNew: isNew, layer: data };
    this.componentRef.instance['onFormAction'].subscribe(data => {
      if(null === data){
        this.resetComponentState();
        return;
      }
      if(this.selectedMapLayer){
        this.selectedMapLayer = data;
        this.updateMapLayer(this.selectedMapLayer);
      }
      else {
        this.createMapElement(this.mapUtilsService.returnNewMapLayer(data, MapActionType.addLayer, this.data.selectedMap.id))
      }
      this.resetComponentState();
    });
  }

  private resetComponentState(){
    this.selectedMapLayer = null;
    this.onAddLayer.next(this.mapLayers);
    if(this.componentRef){
      this.componentRef.destroy();
    }
  }

  updateMapLayer(mapElement: MapLayer){
   let mapLayersSubscription= this.mapLayerService.update(mapElement).subscribe(_ => {
      this.mapLayersModelStore[mapElement.identity] = mapElement;
    }, (err) => {
      this.messageService.add({severity: 'error', summary: this.translateService.instant(ToastTypes.error), detail: this.translateService.instant('appMap.failedToUpdateMapElement')});
    })
    this.subscriptions.push(mapLayersSubscription);
  }

  createMapElement(mapElement: MapLayer){
    this.mapLayerService.create(mapElement).subscribe(_ => {
      this.mapLayersModelStore[mapElement.identity] = mapElement;
      this.mapLayers.push(mapElement);
      this.messageService.add({severity: 'success', summary: this.translateService.instant(ToastTypes.success), detail: this.translateService.instant('appMap.mapElementSaved')});
    }, (err) => {
      this.messageService.add({severity: 'error', summary: this.translateService.instant(ToastTypes.error), detail: this.translateService.instant('appMap.failedToSaveMapElement')});
    })
  }

  deleteLayer(mapElement: MapLayer, index){
   let mapLayerDeleteSubscription= this.mapLayerService.delete(mapElement).subscribe(_ => {
      delete this.mapLayersModelStore[mapElement.identity];
      this.mapLayers.splice(index, 1);
      this.messageService.add({severity: 'success', summary: this.translateService.instant(ToastTypes.success), detail: this.translateService.instant('appMap.mapElementDeleted')});
    }, (err) => {
      this.messageService.add({severity: 'error', summary: this.translateService.instant(ToastTypes.error), detail: this.translateService.instant('appMap.failedToDeleteMapElement')});
    })
    this.subscriptions.push(mapLayerDeleteSubscription);
  }

  toggleVisibility(index){
    this.mapLayers[index].hidden = !this.mapLayers[index].hidden;
    this.updateMapLayer(this.mapLayers[index]);
  }

  toggleManage(){
    this.manageLayers = !this.manageLayers
    if(!this.manageLayers){
      this.resetComponentState();
    }
  }

}
