import { Component, Renderer2 } from "@angular/core";
import { Router } from "@angular/router";
import { Store } from "@ngrx/store";
import { TranslateService } from '@ngx-translate/core';
import { AuthActions } from "app/services/data_actions/auth_actions";
import { AuthStoreService } from "app/services/store_servcies/auth-store.service";
import { CymsidebarService } from 'app/shared/components/cym-sidebar/cym-sidebar.service';
import { NotificationsService } from 'app/shared/components/header/notifications/notifications.service';
import { MapsService } from "app/shared/components/map/map.service";
import { AuthService } from 'app/shared/services/auth.service';
import * as fromAppReducers from 'app/store/app.reducers';
import { environment } from "environments/environment";



@Component({
    selector: 'app-root',
    templateUrl: './cc-wrapper.component.html',
    providers: [NotificationsService, CymsidebarService]
})
export class CCComponent {
    
    initialized: boolean = false;

    constructor(
        private renderer2: Renderer2,
        private authService: AuthService,
        private router: Router,
        private store: Store<fromAppReducers.AppState>,
        private mapsService: MapsService,
        private i18n: TranslateService,
        private authStoreService:AuthStoreService
        ) {
        this.initAppData(environment.provisionData);
        this.i18n.use("en");
        window.addEventListener("loadMapUri",(ev) => {this.changeMap(ev);}, false);
        window.addEventListener("loadDashboard", (ev) => {this.changeDashboard(ev);}, false);
    }

    public initAppData(data) {
        if (null == data) {
            console.error("Application is not provisioned");
        }
        this.getLogin(data.sessionId, data.systemIpPort, data.username);
    }

    private getLogin(ssid: string, ip: string, username: string) {
        
        this.authService.loginFlow(ip,username,null,ssid).subscribe(() => {    
            this.authStoreService.dispatch({ type: AuthActions.SIGNIN, payload: {ip: ip, userName: ssid}});       
            
            this.renderer2.addClass(document.body, 'menuless-app');
            this.initialized = true;
            this.router.navigate(['dashboard']);
         },(err) => {
             console.error("Login failed with", err);
            }
        )
    }
    
    changeDashboard(ev: any) : void {
        if(ev.data && ev.data.uri){
            this.router.navigate(['/dashboard'], { queryParams: { 'dashboard': ev.data.uri } });
        }
    }

    changeMap(ev: any) : void {
        if(ev.data && ev.data.uri){
            this.router.navigate(['/map-new'], { queryParams: { 'map': ev.data.uri } });
        } 
    }

}