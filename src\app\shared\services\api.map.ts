/**
 * This map defines the set-up of all the API calls the app can make. It is primarily
 * used by the DataService.
 */
export interface ApiMap {
    [name: string]: {
        url: string,
        method: 'get' | 'post' | 'delete',
        errors?: {
            [name: number]: string   // error-code: error-message
        },
        headers?: {
            [name: string]: string
        },
        localCopyExist?: boolean,
        isAsync?: boolean
    }
}

export const apiMap: ApiMap = {
    login: {
        url: '/token/login/',
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        errors: {
            '400': 'badCredentialsMsg',
            '0': 'noResponseFromServer'
        }
    },
    logout: {
        url: '/token/logout',
        method: 'post'
    },
    renew: {
        url: '/token/renew',
        method: 'post'
    },
    getUserDetails: {
        url: '/token/userData/getUserDetails',
        method: 'get'
    },
    getUserResources: {
        url: '/token/userData/Resources',
        method: 'get'
    },
    getUserResourceGroups: {
        url: '/token/userData/ResourceGroups',
        method: 'get'
    },
    getUserResourceTriggers: {
        url: '/token/userData/ResourceTriggers',
        method: 'get'
    },
    getReportTransformation: {
        url: '/token/reports/TransformationsList',
        method: 'get'
    },
    getSearchTemplates: {
        url: '/token/reports/SearchTemplates',
        method: 'get'
    },
    setSearchTemplate: {
        url: '/token/reports/SearchTemplate',
        method: 'post',
        errors: {
            '417': 'reportInvalidData',
            '502': 'saveFail',
            '400': 'cantFindSession',
            '403': 'forbidden'
        }
    },
    removeSearchTemplate: {
        url: '/token/reports/RemoveSearchTemplate/',
        method: 'post',
        errors: {
            '502': 'removeFail',
            '404': 'cantFindTemplate',
            '400': 'cantFindSession'
        }
    },
    openChannel: {
        url: '/token/openChannel/',
        method: 'get',
        errors: {
            '404': 'failToOpenChannel'
        }
    },
    getReport: {
        url: '/token/reports/report/',
        method: 'get'
    },
    generateReport: {
        url: '/token/reports/GenerateReport/',
        method: 'post',
        errors: {
            '502': 'generateReportFail',
            '417': 'generateReportFail',
            '400': 'cantFindSession'
        }
    },
    cancelReport: {
        url: '/token/reports/CancelReport/',
        method: 'post'
    },
    extract: {
        url: '/token/extract',
        method: 'post'
    },
    movePTZ: {
        url: '/token/moveptz',
        method: 'post'
    },
    zoomPTZ: {
        url: '/token/zoomptz',
        method: 'post'
    },
    stopPTZ: {
        url: '/token/stopptz/',
        method: 'get'
    },
    videoPlayPause: {
        url: '/token/PlayPause',
        method: 'post'
    },
    videoPlayback: {
        url: '/token/OpenChannelPlayback',
        method: 'post'
    },
    getStatuses: {
        url: '/token/userData/getStatuses',
        method: 'get'
    },

    getLayoutId: {
        url: '/token/videoWalls/',
        method: 'get'
    },
    getLayouts: {
        url: '/token/layouts',
        method: 'get'
    },
    getVideoWallDetails: {
        url: '/token/videoWalls/',
        method: 'get'
    },
    getVideoWalls: {
        url: '/token/videowalls',
        method: 'get'
    },

    saveLayoutForMonitor: {
        url: '/token/videowalls',
        method: 'post'
    },
    setResourceTypesEDField: {
        url: '/token/userData/ResourceTypesEDField',
        method: 'post'
    },
    setResourceExtraData: {
        url: '/token/userData/ResourceExtraData',
        method: 'post'
    },
    setInventoryEntry: {
        url: '/token/userData/InventoryEntry',
        method: 'post'
    },
    getResourceTypeEDField: {
        url: '/token/userData/ResourceTypeEDField',
        method: 'get'
    },
    getResourceExtraData: {
        url: '/token/userData/ResourceExtraData',
        method: 'get'
    },
    toggleOutput: {
        url: '/token/userData/ToggleOutput/',
        method: 'get'
    },
    getDeviceInfo: {
        url: '/token/userData/DeviceInfo/',
        method: 'get'
    },
    searchExternalEntity: {
        url: '/token/entities/searchExternalEntity',
        method: 'post'
    },
    addUpdateExternalEntity: {
        url: '/token/entities/addUpdateExternalEntity',
        method: 'post'
    },
    addUpdateExternalEntities: {
        url: '/token/entities/addUpdateExternalEntities',
        method: 'post'
    },
    addEntity: {
        url: '/token/Entity',
        method: 'post',
        isAsync: true
    },
    addEntities: {
        url: '/token/Entities/',
        method: 'post',
        isAsync: true
    },
    deleteEntity: {
        url: '/token/RemoveEntity/',
        method: 'post'
    },
    entityDevices: {
        url: '/token/EntityDevices',
        method: 'get'
    },
    hResourceGroups: {
        url: '/token/HomogeneousResourceGroups/',
        method: 'get'
    },
    addResourceToGroup: {
        url: '/token/ResourceGroupAddResource/',
        method: 'post'
    },
    removeResourceFromGroup: {
        url: "/token/ResourceGroupRemoveResource/",
        method: 'post'
    },
    resourceGroupRemoveResources: {
        url: "/token/ResourceGroupRemoveResources/",
        method: 'post'
    },
    executeAction: {
        url: "/token/procedures/executeAction/",
        method: 'post'
    },
    getMapInfo: {
        url: "/token/maps/getMapInfo/",
        method: 'get'
    },
    getMapsInfo: {
        url: "/token/maps/getMapsInfo",
        method: 'get'
    },
    getMapElements: {
        url: "/token/maps/getMapElements/",
        method: 'get'
    },
    getRasterImage: {
        url: "/token/maps/getRasterImage/",
        method: 'get'
    },
    getChannelRecordingRanges: {
        url: "/token/ChannelRecordingRanges/",
        method: 'get'
    },
    configFileUpload: {
        url: "/token/userData/ConfigFileUpload/",
        method: 'post'
    },
    getSchedules: {
        url: "/token/schedules/GetSchedules",
        method: 'get'
    },
    addSchedule: {
        url: "/token/schedules/AddSchedule",
        method: 'post'
    },
    updateSchedule: {
        url: "/token/schedules/UpdateSchedule",
        method: 'post'
    },
    removeSchedule: {
        url: "/token/schedules/RemoveSchedule/",
        method: 'post'
    },
    getActionsTypes: {
        url: "/token/schedules/GetActionsTypes",
        method: 'get'
    },
    getActionInfo: {
        url: "/token/schedules/GetActionInfo/",
        method: 'get'
    },
    getLocations: {
        url: "/token/maps/getLocations",
        method: 'get'
    },
    getResourceInfo: {
        url: "/token/userData/ResourceInfo/",
        method: 'get'
    },
    getLaneInfo: {
        url: "/token/userData/LaneInfo/",
        method: 'get'
    },
    setAssets: {
        url: "/token/setAssets",
        method: 'post'
    },
    getAssets: {
        url: "/token/getAssets",
        method: 'get'
    },
    getEntities: {
        url: "/token/getEntities",
        method: 'get'
    },
    getEntityImage:{
        url:"/token/getEntityImage",
        method: 'get'
    },
    setAsset: {
        url: "/token/SetAsset",
        method: 'post'
    },
    removeAsset: {
        url: "/token/RemoveAsset/",
        method: 'post'
    },
    getAssetsReservations: {
        url: "/token/GetOrders",
        method: 'post'
    },
    setReservation: {
        url: "/token/OrderAsset",
        method: 'post'
    },
    removeReservation: {
        url: "/token/RemoveReservation/",
        method: 'post'
    },
    resourceGroupAddResources: {
        url: "/token/ResourceGroupAddResources",
        method: 'post'
    },
    saveWebConfig: {
        url: "/token/userData/SetAccountConfiguration",
        method: 'post'
    },
    updateResourceTypesEDField: {
        url: "/token/userData/UpdateResourceTypesEDField",
        method: 'post'
    },
    loginByRedirect: {
        url: '/token/loginByRedirect/',
        method: 'post'
    },
    createNewResourceGroup: {
        url: "/token/ResourceGroup/",
        method: 'post'
    },
    getProfiles: {
        url: '/token/userData/Profiles',
        method: 'get'
    },
    getProfilesWithResourceGroups: {
        url: '/token/userData/ProfilesWithResourceGroups',
        method: 'get'
    },
    updateResourceGroupProfiles: {
        url: '/token/UpdateResourceGroupProfiles',
        method: 'post'
    },
    addRTSPDevice: {
        url: '/token/userData/addRTSPDevice',
        method: 'post'
    },
    generateEntitiesReport: {
        url: '/token/reports/generateEntitiesReport',
        method: 'get'
    },
    GetUploadEntitiesResults: {
        url: '/token/entities/GetUploadEntitiesResults/',
        method: 'get'
    },
    DeleteUploadEntitiesResults: {
        url: '/token/entities/DeleteUploadEntitiesResults/',
        method: 'post'
    },
    createInventoryCSV: {
        url: '/token/userData/createInventoryCSV',
        method: 'post'
    },
    changeCommandStatus: {
        url: '/token/sscommands/menolinxsetstatus',
        method: 'post'
    },
    addUpdateLights: {
        url: '/token/userData/addUpdateLights',
        method: 'post'
    },
    addUpdateResourcesLocation: {
        url: '/token/userData/addUpdateResourcesLocation',
        method: 'post'
    },
    addUpdateResourcesED: {
        url: '/token/userData/addUpdateResourcesED',
        method: 'post'
    },
    getDashboardsTree: {
        url: '/token/dashboard/getDashboardsTree',
        method: 'get'
    },
    resetCounter: {
        url: '/token/resetCounter/',
        method: 'get'
    },
    AddUpdateDashboard: {
        url: '/token/dashboard/addUpdateDashboard/',
        method: 'post'
    },
    DeleteDashboard: {
        url: '/token/dashboard/deleteDashboard/',
        method: 'post'
    },
    GetDashboard: {
        url: '/token/dashboard/getDashboard/',
        method: 'get'
    },
    GetDashboards: {
        url: '/token/dashboard/getDashboard',
        method: 'get'
    },
    GetDashboardsNoData: {
        url: '/token/dashboard/getDashboardsNoData',
        method: 'get'
    },
    GetResourceTriggersById: {
        url: '/token/api/data/ResourceTriggers/',
        method: 'get'
    },
    GetPreconfiguredData: {
        url: '/token/dashboard/getPreconfiguredData/',
        method: 'get'
    },
    Reset:{
        url: '/token/ResetSensorDevice',
        method: 'post'
    },
    deleteResource: {
        url: '/token/userData/DeleteResources',
        method: 'post'
    },
    getDetectionsPage:{
        url: '/token/swarmLPR/detections',
        method: 'post'
    },
    addNewLumenFactor: {
        url: '/token/lumen/factor',
        method: 'post'
    },

    enableResourceGroupTraffic: {
        url: '/token/traffic/factor/statuses',
        method: 'post'
    },

    enableResourceGroupWeather: {
        url: '/token/weather/factor/statuses',
        method: 'post'
    },
    getTrafficFactorStatuses: {
        url: '/token/traffic/factor/statuses',
        method: 'get'
    },
    getWeatherFactorStatuses: {
        url: '/token/weather/factor/statuses',
        method: 'get'
    },
    addNewTrafficFactor: {
        url: '/token/traffic/factor',
        method: 'post'
    },

    addNewWeatherFactor: {
        url: '/token/weather/factor',
        method: 'post'
    },

    getWeatherFactor: {
        url: '/token/weather/factor',
        method: 'get'
    },

    getTrafficFactor: {
        url: '/token/traffic/factor',
        method: 'get'
    },

    getLumenFactor: {
        url: '/token/lumen/factor',
        method: 'get'
    },
    downloadDetectionPhotos:{
        url: '/token/swarmLPR/downloadDetectionPhotos',
        method: 'post'
    },
    getDetectionPhoto:{
        url:'/token/swarmLPR/photo',
        method: 'get'
    },
    unsubscribeFromDetectionNotifications:{
        url: '/token/swarmLPR/unsubscribe',
        method: 'post'
    },
    deleteResources: {
        url: '/token/deleteResources',
        method: 'post'
    },
    locationInfringement:{
        url: '/token/swarmLPR/locationInfringement',
        method: 'get'
    },
    vehicleInfringement:{
        url: '/token/swarmLPR/vehicleInfringement',
        method: 'get'
    },

    getMenolinxIoOutputs:{
        url: '/token/io/outputs/',
        method: 'get'
    },

    getMenolinxIoOutputItem:{
        url: '/token/io/outputs/',
        method: 'get'
    },
    getMenolinxIoInputs:{
        url: '/token/io/inputs/',
        method: 'get'
    },
    getChannelTours:{
        url: '/token/channelTours/',
        method: 'get'
    },
    getChannelTour:{
        url: '/token/channelTours/',
        method: 'get'
    },
    getNotifications: {
        url: '/token/notifications/',
        method: 'get'
    },
    getEntityNotifications: {
        url: '/token/entitynotifications/',
        method: 'get'
    },
    removeNotifications: {
        url: '/token/notifications/remove/',
        method: 'post'
    },
    getResourcesToGroups: {
        url: '/token/ResourcesToGroups/',
        method: 'get'
    },
    getResourcesGroups: {
        url: '/token/ResourceGroups/',
        method: 'get'
    },
    getResources: {
        url: '/token/Resources/',
        method: 'get'
    },
    generalDetectionsPage:{
        url: '/token/general-detections/',
        method: 'get'
    },
    getChannelRecordingInfo: {
        url: '/token/QueryChannelRecordingInfo/',
        method: 'get'
    },
    downloadDetectionArchive:{
        url: '/token/detections/',
        method: 'get'
    },
    generalDetectionsCount:{
        url: '/token/general-detections-count/',
        method: 'get'
    },
    detectionCountByState:{
        url: '/token/detection-count-by-state/',
        method: 'get'
    },
    felonyCount:{
        url: '/token/felony-count/',
        method: 'get'
    },
    generalDetectionsPageByState:{
        url: '/token/general-detections-by-state/',
        method: 'get'
    },
    generalDetectionsExport:{
        url: '/token/general-detections/export',
        method: 'post'
    },
    auditExport:  {
        url: '/token/audit/export',
        method: 'post'
    },
     procedureExport:  {
        url: '/token/procedures/export',
        method: 'post'
    },
    getWeatherInfo:{
        url: '/token/weather/GetLatestWeatherInfo/',
        method: 'get'
    },
};
