import { Widget } from "./widget";
import { DashboardWidget } from "./dashboard-widget.interface";
import { WidgetSize } from "../enums/widget-size.enum";
import { GaugeDisplayResults } from '../enums/gauge-widget-display-results.enum';

export interface ThresholdPoints {
  class: string;
  start: number;
  end: number;
  eventName: string;
};

export class GaugeWidget extends Widget {
    
    selectedGroupId: string;    
    selectedResourceId: string;    
    selectedTrigger: string;
    eventIdsSelectVal: string[];
    thresholdPoints: ThresholdPoints[];
    measurmentUnit: string;
    minValue: number;
    maxValue: number;
    dialStartAngle: number;
    dialEndAngle: boolean;
    displayResultsMethod: GaugeDisplayResults;
     
    constructor(obj?: DashboardWidget){
        super(obj);
        if(!this.thresholdPoints){
            this.thresholdPoints = [];
        }
    }

    getWidgetSize(): WidgetSize[]{
        return [WidgetSize.xSmall, WidgetSize.medium, WidgetSize.big]
    }

}