import { Component, OnInit, Input } from '@angular/core';
import { DashboardState } from '../../../models/dashboard-state.interface';
import { WidgetState } from '../../../models/widget-state.interface';
import { SelectItem } from 'primeng/api';
import { Widget } from 'app/layout/customer-dashboard/models/widget';
import { DashboardUtilsService } from 'app/layout/customer-dashboard/services/dashboard-utils.service';
import { Subject, EMPTY, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';

@Component({
  selector: 'app-edit-widget',
  templateUrl: './edit-widget.component.html',
  styleUrls: ['./edit-widget.component.scss']
})
export class EditWidgetComponent implements OnInit {

  @Input() selectedWidget: Widget;
  public widgetSizeOption: SelectItem[] = [];
  subscriptions: Subscription[] = [];
  title$ = new Subject<string>();

  constructor(public dashboardUtilsService: DashboardUtilsService) { 
    let widgetTitleSubscription = this.title$.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      switchMap(title => {
        this.selectedWidget.title = title;
        this.dashboardUtilsService.setWidgetDataChange(this.selectedWidget);
        return EMPTY;
      })
    ).subscribe();
    this.subscriptions.push(widgetTitleSubscription);
  }

  ngOnInit() {
    this.buildwidgetSizeOption();
  }

  private buildwidgetSizeOption(){
    this.selectedWidget.getWidgetSize().forEach(element => {
      this.widgetSizeOption.push({label: 'customerDashboard.widgetSize.'+this.selectedWidget.type+'.'+element, value: element})
    });
  }

  onChangeWidgetSize(event){
    this.dashboardUtilsService.setWidgetDataChange(this.selectedWidget);
  }

}
