import {
    <PERSON><PERSON>iew<PERSON>nit,
    ChangeDetector<PERSON>ef,
    Component,
    On<PERSON>estroy,
    OnInit,
    TemplateRef,
    ViewChild,
    ViewEncapsulation
} from '@angular/core';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
import { AppModal } from 'app/shared/components/app-modal/app-modal.component';
import { NotificationsService } from 'app/shared/components/header/notifications/notifications.service';
import { LayoutDividerComponent } from 'app/shared/components/layout-divider/layout-divider.component';
import { TableData } from 'app/shared/components/ng-turbo-table/models/table-data';
import { TableCell, TableColumnProperties } from 'app/shared/components/ng-turbo-table/models/table.models';
import { NgTurboTableComponent } from 'app/shared/components/ng-turbo-table/ng-turbo-table.component';
import { ToastTypes } from 'app/shared/enum/toast-types';
import { Entity } from 'app/shared/modules/data-layer/models/entity';
import * as _ from 'lodash';
import { LazyLoadEvent, MessageService } from 'primeng/api';
import { Subscription } from 'rxjs';
import { SaveEntity } from '../app-map/models/saveEntity.model';

import { EventDataInterface } from "app/layout/entities/event-data.interface";
import { DefaultActionsComponent } from './actions/default-actions/default-actions.component';
import { EntityFormComponent } from './entity-form/entity-form.component';
import { EntitiesActionType } from './enums/entitiesActionType.enum';
import { EventData } from './event-data';
import { EntityNotificationsPage } from './models/entity-notifications-page.interface';
import {en} from "@fullcalendar/core/internal-common";
import {Icons} from "app/shared/enum/enum";


@Component({
    selector:'app-entities',
    templateUrl:'./entities.component.html',
    encapsulation:ViewEncapsulation.None,
    styleUrls:['./entities.component.scss']
})
export class EntitiesComponent implements OnDestroy,OnInit,AfterViewInit {
    @ViewChild('divider',{static:true}) private divider: LayoutDividerComponent;
    @ViewChild('form',{static:false}) private form: EntityFormComponent;
    @ViewChild('actionsDefault',{static:false}) actionsDefault: TemplateRef<DefaultActionsComponent>;
    @ViewChild('photosModal',{static:false}) photosModal: AppModal;

    newEntitySubscription: Subscription;
    loadEntitiesSubscription: Subscription;
    eventsMap:EventData[] = [];
    displayForm = false;
    formHeader:string;
    newEntity:EventDataInterface;
    newEntityDevice:{ deviceValue: string; deviceId: string; };
    isRtl = false;
    table: NgTurboTableComponent;
    availableDevices: EventData[];
    columns: TableColumnProperties[] = [];
    data:any[];
    totalRecords: number;
    currentPage: number;
    loading: boolean;
    entityNotifications:Map<string,EventData>
    liveUpdates: boolean = true;
    filterText: string = '';
    constructor(private notificationsService: NotificationsService,
                private cdr: ChangeDetectorRef,
                private i18n: TranslateService,
                private messageService: MessageService,
                private sanitizer: DomSanitizer,
    ) {
    }

    ngOnInit() {
        this.entityNotifications = new Map();
        this.currentPage = 0;
        this.pageLoad({ first: 0, rows: 9 });
    }
    toggleLiveUpdates():void {
        this.liveUpdates = !this.liveUpdates;
        if (!this.liveUpdates) {
            this.pageLoad({ first: 0, rows: 9 });
        }else{
            this.data=[];
        }
    }

    ngAfterViewInit() {
        this.availableDevices = _.values([]);
        this.entityNotifications = this.entityNotifications || new Map();
        const result = this.getFlattenObject(this.availableDevices);

        this.data = result.items;
        this.newEntitySubscription = this.notificationsService.newEntityNotification.subscribe((entityNotification :EventData) => {
            if (this.liveUpdates && this.currentPage == 0)
            {


                if(entityNotification.images[0].source)
                {
                    entityNotification.images[0]=entityNotification.images[0].source;
                }

                this.eventsMap.push(entityNotification);
                this.availableDevices = _.values([entityNotification]);
                let newData = this.getFlattenObject(this.availableDevices);
                if (this.entityNotifications.has(entityNotification.userId) && entityNotification.entityValue !='unknown') {
                    let existingNotification = this.entityNotifications.get(entityNotification.userId);
                    existingNotification.events.push(entityNotification);
                } else {
                    if(entityNotification.entityValue =='unknown'){
                        this.entityNotifications.set(entityNotification.entityInfo.FirstName, entityNotification);
                    }else{
                        entityNotification.events = [entityNotification];
                        this.entityNotifications.set(entityNotification.userId, entityNotification);
                    }


                }
                this.updateEntityData(entityNotification);
            }
        },err=>{
            this.messageService.add({
                severity:'error',
                summary:this.i18n.instant(ToastTypes.error),
                detail:this.i18n.instant('unableToLoadData')
            });
        });

        this.cdr.detectChanges();
    }

    parseSafeUrl(url): SafeUrl {
        return this.sanitizer.bypassSecurityTrustUrl(url);
    }

    updateEntityData(entityNotification?: EventData): void {
        if (entityNotification) {
            const newMap = new Map([[entityNotification.userId, entityNotification], ...this.entityNotifications]);
            this.entityNotifications = newMap;
        }
        if (this.currentPage === 0 || !entityNotification) {
            this.data = Array.from(this.entityNotifications.values());
        }
        this.cdr.detectChanges();
    }



    getFlattenObject(data: EventData[]): TableData {
        const results: { [columnField: string]: TableCell | string }[] = [];
        const fields: { [field: number]: boolean } = {};
        data.map((item: EventData)=>{
            let itemDescription  = item.description ? JSON.parse(item.description) : null;
            let temp: { [columnField: string]: TableCell | string } = {};
            temp.eventId = item.eventId;
            fields['eventId'] = true;
            let images = item.images && item.images.length > 0 ? item.images[0].source : '';

            if (item.images.length > 0 && !images) {
                images = item.images[0];
            }

            temp.images = images;
            temp.imageData = images;
            fields['imageData'] = true;

            fields['images'] = true;
            temp.entity = item.entityInfo.FirstName + " " + item.entityInfo.LastName;
            fields['entity'] = true;
            temp.timestamp = item.timestamp;
            fields['timestamp'] = true;
            temp.deviceId = item.deviceId;
            fields['deviceId'] = true;
            temp.resourceName = item.resourceName;
            fields['resourceName'] = true;
            temp.confidence = item.confidence ? item.confidence : 0;
            fields['confidence'] = true;
            temp.entityValue = item.entityValue;
            fields['entityValue'] = true;
            temp.entityInfo = item.entityInfo;
            fields['entityInfo'] = true;
            let resourceInfo = item.resourceInfo ? item.resourceInfo.map(resourceInfo=>{
                return resourceInfo;
            }) : [];
            item.resourceInfo;
            temp.DynamicAttributesJsonString= itemDescription && itemDescription.description ? itemDescription.description.extra_data : null;
            temp.resourceInfo = resourceInfo.join(', ');
            fields['resourceInfo'] = true;
            temp.triggerCode = item.triggerCode;
            fields['status'] = true;
            fields['addEntity'] = true;
            results.push(temp);
        });
        return {
            items:results,
            fields:fields
        };
    }

    addEntity(event:EventData): void {

        let entity = this.eventsMap.find(el=>el.eventId == event.eventId);
        this.formHeader = this.i18n.instant("addEntity");
        this.newEntity = {...event};

        this.newEntityDevice = {
            deviceValue:entity.entityValue,
            deviceId:entity.deviceId
        };
        this.displayForm = true;
    }



    closeForm():void {
        this.form.associatedDevices = [];
        this.form.resourceGroups = [];
        this.displayForm = false;
    }



    saveEntity(entity: SaveEntity): void {
        this.changeEntityValue(entity.entity,entity.action);
    }

    deleteEntity(entity: Entity): void {
        this.changeEntityValue(entity,EntitiesActionType.deleteEntity);
    }

    changeEntityValue(newEntity:Entity , action:EntitiesActionType):void {


        this.eventsMap.forEach((event, index) => {
            switch (action) {
                case EntitiesActionType.deleteEntity:
                case EntitiesActionType.editEntity:
                    if (event.entityInfo) {
                        if (event.entityInfo.Identity === newEntity.Identity) {
                            event.entityInfo = action ? newEntity : undefined;
                            event.resourceInfo[0] = this.setResourceInfo(event, action, newEntity);

                        }
                    }
                    break;
                case EntitiesActionType.addEntity:
                    if (!event.entityInfo && !_.isEmpty(newEntity.AssociatedDevices)) {
                        if (event.entityValue === newEntity.AssociatedDevices[0].value && event.entityValue != 'unknown') {
                            event["entityInfo"] = newEntity;
                            event.resourceInfo[0] = this.setResourceInfo(event, action, newEntity);
                        }
                    }
                    break;
            }
        });
    }

    setResourceInfo(event: EventData,action: EntitiesActionType,entity?: Entity): void {
        let info = event.resourceInfo[0];

        if (action == EntitiesActionType.addEntity) {
            info.entity = {};
            info.icon = Icons.AddEntity;
            info.name = "addNewEntity";
            info.new = false;
        } else {
            info.entity = entity;
            info.icon = Icons.EditEntity;
            info.name = event.entityValue;
            info.new = true;
        }

        info.type = "addEntity";//TODO why is this needed even if we're not adding?
        return info;
    }

    pageLoad(pager: LazyLoadEvent): void {
        // Ensure pager.first is a number and default to 0 if undefined
        const first = typeof pager.first === 'number' ? pager.first : 0;
        const rows = pager.rows || 9;
        const newPage = Math.floor(first / rows);
        if (this.loading) {
            return;
        }
        this.loading = true;
        this.loadEntitiesSubscription = this.notificationsService.GetEntityNotifications(pager)
            .subscribe({
                next: (entityNotificationsPage: EntityNotificationsPage) => {
                    if (this.currentPage !== newPage) {
                        this.eventsMap = [];
                        this.data = [];
                        this.entityNotifications.clear();
                    }
                    entityNotificationsPage.IdTagDTOs.forEach((entityNotification: EventData) => {
                        this.eventsMap.push(entityNotification);
                        if (this.entityNotifications.has(entityNotification.userId) &&
                            entityNotification.entityValue !== 'unknown') {
                            let existingNotification = this.entityNotifications.get(entityNotification.userId);
                            if (!existingNotification.events) {
                                existingNotification.events = [];
                            }
                            existingNotification.events.push(entityNotification);
                        } else {
                            if (entityNotification.entityValue === 'unknown') {
                                this.entityNotifications.set(entityNotification.entityInfo.FirstName, entityNotification);
                            } else {
                                entityNotification.events = [entityNotification];
                                this.entityNotifications.set(entityNotification.userId, entityNotification);
                            }
                        }
                    });
                    this.data = Array.from(this.entityNotifications.values());
                    this.getFlattenObject(this.data);
                    this.currentPage = newPage;
                    this.totalRecords = entityNotificationsPage.TotalRowsCount;

                    this.loading = false;
                    this.cdr.detectChanges();
                },
                error: (error) => {
                    console.error('Error loading data:', error);
                    this.messageService.add({
                        severity: 'error',
                        summary: this.i18n.instant(ToastTypes.error),
                        detail: this.i18n.instant('unableToLoadData')
                    });
                    this.loading = false;
                }
            });
    }



    applyFilter():void {
        if (this.filterText) {
            this.data = Array.from(this.entityNotifications.values()).filter(entity =>
                entity.entityInfo.FirstName.toLowerCase().includes(this.filterText.toLowerCase()) ||
                entity.entityInfo.LastName.toLowerCase().includes(this.filterText.toLowerCase())
            );
        } else {
            this.data = Array.from(this.entityNotifications.values());
        }
    }
    private getEntityValue(event: EventData): string {
        if (!event) {
            return "";
        }

        if (event.entityInfo && (event.entityInfo.FirstName != "" || event.entityInfo.LastName != "") && !_.isEmpty(event.entityInfo)) {
            return event.entityInfo.FirstName + " " + event.entityInfo.LastName;
        } else {
            return (event.entityValue) ? event.entityValue : event.UnknownEntityID;
        }
    }

    public calculateEntityIndex(eventId: number): string {
        return this.eventsMap.findIndex((object: EventData)=>{
            return object.eventId === eventId;
        }).toString();
    }

    public ngOnDestroy(): void {
        if (this.newEntitySubscription) {
            this.newEntitySubscription.unsubscribe();
        }

        if (this.loadEntitiesSubscription) {
            this.loadEntitiesSubscription.unsubscribe();
        }
    }

    isExpanded: boolean = false;
}
