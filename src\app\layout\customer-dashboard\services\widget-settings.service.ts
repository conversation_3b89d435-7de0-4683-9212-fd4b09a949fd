import { Injectable } from '@angular/core';
import { LocalStorageService } from 'app/shared/services/local-storage.service';

@Injectable({
  providedIn: 'root'
})
export class WidgetSettingsService {
  widgetSettingsData: {[widgetId: string]: {[propertyName: string]: string}};
  localStorageWidgetSettingsProperty: string = 'dashboardWidgetSettings';

  constructor (
    private localStorageService: LocalStorageService
  ) {
    this.widgetSettingsData = this.localStorageService.get('dashboardWidgetSettings') || {};
  }

  public setWidgetSettings(widgetId: string, settings: {key: string, value:string}[]): void {
    let widgetCurrentSetting = this.getWidgetSettings(widgetId);
    settings.forEach((item) => {
      widgetCurrentSetting[item.key] = item.value;
    });
    this.widgetSettingsData[widgetId] = widgetCurrentSetting;
    this.localStorageService.set(this.localStorageWidgetSettingsProperty, this.widgetSettingsData);
  }

  public deleteWidgetSettings(widgetIds: string[]): void {
    if(widgetIds.length === 0){
      return;
    }
    widgetIds.forEach(widgetId => {
      if(!this.widgetSettingsData[widgetId]){
        return;
      }
      delete this.widgetSettingsData[widgetId];
    });
    this.localStorageService.set(this.localStorageWidgetSettingsProperty, this.widgetSettingsData);
  }

  public getWidgetSettings(widgetId: string): {[propertyName: string]: string} {
    return this.widgetSettingsData[widgetId] || {};
  }
}
