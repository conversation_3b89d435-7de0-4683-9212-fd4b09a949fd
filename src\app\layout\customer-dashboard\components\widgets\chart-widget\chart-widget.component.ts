import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { <PERSON><PERSON>aniti<PERSON>, SafeHtml } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
import { DashboardActionType } from 'app/layout/customer-dashboard/enums/dashboard-action-type.enum';
import { LineChartDataType } from 'app/layout/customer-dashboard/enums/line-chart-data.enum';
import { LineChartDisplayResults } from 'app/layout/customer-dashboard/enums/line-chart-display-results.enum';
import { ChartData } from 'app/layout/customer-dashboard/models/chart-data.interface';
import { ChartWidget } from 'app/layout/customer-dashboard/models/chart-widget';
import { ReportsQueryParams } from 'app/layout/customer-dashboard/models/reports-query-params.interface';
import { DashboardUtilsService } from 'app/layout/customer-dashboard/services/dashboard-utils.service';
import { WidgetSettingsService } from 'app/layout/customer-dashboard/services/widget-settings.service';
import { AppModal } from 'app/shared/components/app-modal/app-modal.component';
import { TableColumnProperties } from 'app/shared/components/ng-turbo-table/models/table.models';
import { Guid } from 'app/shared/enum/guid';
import { ReportCollectOption } from 'app/shared/modules/data-layer/enum/reports/report-collect-options.enum';
import { NotificationMessageStatus } from 'app/shared/modules/data-layer/enum/reports/report-data-chunk-status.enum';
import { ReportDataType } from 'app/shared/modules/data-layer/enum/reports/report-data-type.enum';
import { AuthService } from 'app/shared/services/auth.service';
import { ReportsTurboTableService } from 'app/shared/services/reports-turbo-table.service';
import { ReportsService } from 'app/shared/services/reports.service';
import { Chart, LineController } from 'chart.js';
import * as moment from 'moment';
import { BaseChartDirective } from 'ng2-charts';
import { SelectItem } from 'primeng/api';
import { Subscription } from 'rxjs';
import {DateRangeSearchType,DateUnit,GuidUtils} from "app/shared/enum/enum";
import {TriggerTypeUtils} from "app/shared/helpers/trigger-type-utils";


@Component({
  selector: 'app-chart-widget',
  templateUrl: './chart-widget.component.html',
  styleUrls: ['./chart-widget.component.scss']
})
export class ChartWidgetComponent implements OnInit, OnDestroy {
  @ViewChild(BaseChartDirective, { static: false }) chart: BaseChartDirective;
  @ViewChild('legendContainer', { static: true }) legendContainer: ElementRef;
  @ViewChild('turboTableModal', {static: false}) turboTableModal: AppModal;

  public data: {index: number, widgetData: ChartWidget, widgetSettings:{displayedDataTimeSettings:string}};
  private subscriptions: Subscription[] = [];
  public emptyDataSet: ChartData[] = [{data: [], label: 'no-data', translateLabel: 'no-data', dateDictionary: {}, hidden: false, backgroundColor: this.generateRandomRgba(0.2)}]
  public lineChartDataSets: ChartData[] = [];
  public lineChartLabels: string[] = [];
  public translatedLineChartLabels: string[] = [];
  public concatenatedTableData: {[propertyName: string]: string}[] = [];
  public lineChartLegendHtml: SafeHtml;
  public chartData={};

  public lineChartOptions = {
    responsive: true,
    aspectRatio: 2.15,
    maintainAspectRatio: false,
    legend: {
      labels: {
        usePointStyle: true,
        fontSize: 9
      }
    }
  };

  public lineChartLegend = false;
  public lineChartPlugins = [];

  //Took out some options because when testing the functionality i didn't find them particullary neccessary
  public displayedDataTimeOptions: SelectItem[] = [
    // {label: 'seconds', value: Enums.DateUnit.SECONDS},
    // {label: 'minutes', value: Enums.DateUnit.MINUTES},
    {label: 'hours', value: DateUnit.Hours},
    {label: 'days', value: DateUnit.Days},
    // {label: 'weeks', value: Enums.DateUnit.WEEKS},
    // {label: 'months', value: Enums.DateUnit.MONTHS}
  ]
  legendAnchors: HTMLAnchorElement[] = [];
  reportRequestInProgress: boolean = false;
  filteredResultCount: number = 0;
  turboTableData: { tableColumns: TableColumnProperties[], tableData: {[key: string] : string}[] } = {tableColumns: [], tableData: []};
  datax={};
  constructor(
    private translateService: TranslateService,
    private reportsService: ReportsService,
    private authService: AuthService,
    private sanitizer: DomSanitizer,
    private elementRef: ElementRef,
    private dashboardUtilsService: DashboardUtilsService,
    private reportsTurboTableService: ReportsTurboTableService,
    private widgetSettingStorage: WidgetSettingsService
  ) {
    Chart.register(LineController);
  }

  ngOnInit(): void {
    this.data.widgetData.displayedDataTimeSettings= parseInt(this.data.widgetSettings.displayedDataTimeSettings);
    this.data.widgetData = new ChartWidget(this.data.widgetData);
    let langChangeSubscription = this.translateService.onLangChange.subscribe(() => {
      this.lineChartLabels = this.returnUpdatedLineChartLabelsByTime([], this.concatenatedTableData);
      this.lineChartDataSets = this.returnUpdatedLineChartData([], this.concatenatedTableData, this.lineChartLabels);
      this.redrawLineChartWithUpdatedData(this.lineChartDataSets, this.lineChartLabels);
    });
    this.subscriptions.push(langChangeSubscription);

    let widgetActionsSubscription = this.dashboardUtilsService.getWidgetAction().subscribe(res => {
      if(this.data.widgetData.id === res.widget.id && res.actionType === DashboardActionType.viewInTable){
        this.turboTableModal.openModal();
      }
    });
    this.subscriptions.push(widgetActionsSubscription);

    this.generateReport();
  }

  ngOnDestroy() : void {
    this.legendAnchors.forEach((anchor: HTMLAnchorElement, index: number) => {
      anchor.removeEventListener('click', (event) => {
        this.onLegendAnchorClick(event, index);
      });
    });
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
  }

  generateReport(): void{
    this.lineChartDataSets = [];
    this.lineChartLabels = [];
    this.concatenatedTableData = [];
    this.reportRequestInProgress = true;
    this.filteredResultCount = 0;
    this.reportsService.generateChunkedReport(this.returnReportsQueryParams(ReportCollectOption.Notify)).subscribe(res => {
      this.concatenatedTableData = [...this.concatenatedTableData, ...res.payload.newTableData];
      this.lineChartLabels = this.returnUpdatedLineChartLabelsByTime(this.lineChartLabels, res.payload.newTableData);
      this.lineChartDataSets = this.returnUpdatedLineChartData(this.lineChartDataSets, res.payload.newTableData, this.lineChartLabels);
      this.redrawLineChartWithUpdatedData(this.lineChartDataSets, this.lineChartLabels);

      //Turbo table Data
      this.turboTableData.tableColumns = this.turboTableData.tableColumns && this.turboTableData.tableColumns.length === 0 ? this.reportsTurboTableService.buildTableColumns(res.payload.tableColumns) : this.turboTableData.tableColumns;
      this.turboTableData.tableData = [...this.turboTableData.tableData, ...this.reportsTurboTableService.buildTableData(res.payload.newTableData)];

      if(res.header.status === NotificationMessageStatus.Success){
        this.addLegendClickEventHandlers();
        this.reportRequestInProgress = false;
      }
    });
  }

  onWidgetTimeOptionChange(): void{
    this.filteredResultCount = 0;
    this.lineChartLabels = this.returnUpdatedLineChartLabelsByTime([], this.concatenatedTableData);
    this.lineChartDataSets = this.returnUpdatedLineChartData([], this.concatenatedTableData, this.lineChartLabels);
    let chartDataTime: {key: string, value:string}[] = [
      {key: 'displayedDataTimeSettings', value: this.data.widgetData.displayedDataTimeSettings.toString()},
    ];
    this.widgetSettingStorage.setWidgetSettings(this.data.widgetData.id, chartDataTime);
    this.redrawLineChartWithUpdatedData(this.lineChartDataSets, this.lineChartLabels);
    this.addLegendClickEventHandlers();
  }

  redrawLineChartWithUpdatedData(dataSets: ChartData[], labels: string[]): void {
    // This is a hack for updatting line chart data
    // normally this line would sufice this.lineChartComponent.chart.update();
    if(this.chart.chart){

      this.chart.chart.config.data = {
      datasets: dataSets,
      labels: labels.map(label => {return this.returnFormatedLabel(label);})
    };
    this.chartData={
        datasets: dataSets,
        labels: labels.map(label => {return this.returnFormatedLabel(label);})
      }
    this.chart.chart.update();

    }


  }

  addLegendClickEventHandlers(): void {
    this.removeLegendClickEventHandlers();
    //hack to make sure that innerHtml of .line-chart-legend element is populated with data
    setTimeout(() => {
      this.legendAnchors = this.elementRef.nativeElement.querySelectorAll('.line-chart-legend > ul > li');
      this.legendAnchors.forEach((anchor: HTMLAnchorElement, index) => {
        anchor.addEventListener('click', (event) => {
          this.onLegendAnchorClick(event, index);
        });
      });
    }, 100);
  }

  removeLegendClickEventHandlers(): void{
    this.legendAnchors.forEach((anchor: HTMLAnchorElement, index: number) => {
      anchor.removeEventListener('click', (event) => {
        this.onLegendAnchorClick(event, index);
      });
    });
  }

  public onLegendAnchorClick(event: Event, index: number): void{
    const anchor = event.target as HTMLAnchorElement;
    anchor.classList.toggle('disabled');
    this.chart.chart.data.datasets[index].hidden = !this.chart.chart.data.datasets[index].hidden;
    this.chart.chart.update();
  }

  returnUpdatedLineChartData(currentData:ChartData[], newData: {[propertyName: string]: string}[], lineChartlabels: string[]) : ChartData[] {
    let newLineChartData: ChartData[] = currentData || [];
    newData.forEach(element => {
      if(this.data.widgetData.selectedResourceIds.length > 0 && this.data.widgetData.selectedResourceIds.findIndex(id => {return id === element['resource_id'];}) === -1){
        return;
      }

      let elementName = this.returnDataLabel(element, this.data.widgetData.lineChartDataType);
      if(!elementName || !element['time_stamp_formatted']){
        return;
      }
      this.filteredResultCount++;
      let elementTime = this.returnElementTime(element['time_stamp_formatted']);
      let elementIndex = currentData.findIndex(el => {return el.translateLabel === elementName;});

      if(elementIndex === -1){
        let data = {
          data: [],
          label: elementName,
          dateDictionary: {},
          translateLabel: elementName,
          hidden: false,
          backgroundColor: this.generateRandomRgba(0.2)
        };
        data.dateDictionary[elementTime] = 1;
        newLineChartData.push(data);
      }
      else {
        newLineChartData[elementIndex].dateDictionary[elementTime] = this.returnTriggerValueOrCounter(newLineChartData[elementIndex].dateDictionary[elementTime], element);
      }
    });
    newLineChartData.map(element => {
      lineChartlabels.forEach((label, key) => {
        element.data[key] = element.dateDictionary[label] || 0;
      });
    });
    if(this.data.widgetData.displayResultsMethod == 2){
      return this.returnTotalElementsChart(newLineChartData);
    }else{
      return newLineChartData;
    }


  }

  returnTotalElementsChart(chartLineData){
    let x={
      data:[]
    }
    if(chartLineData.length > 1){

    chartLineData.map(element=>{
        element.data.forEach((label, key) => {
          if(x.data[key]){
            x.data[key]=x.data[key]+label;
          }else{
            x.data[key]=label;
          }
        });
      })
      let firstItem=chartLineData[0];
      firstItem.data=x.data;
      firstItem.label="Total";
      chartLineData.map((element,index)=>{
        if(index > 0) {
          element.data.forEach((lable,key)=>{
            element.data[key]=0;
          })
        }
      });

    }
      return chartLineData;
  }

  returnUpdatedLineChartLabelsByTime(currentData: string[], newData: {[propertyName: string]: string}[]) : string[]{
    let newLabels: string[] = currentData || [];
    newData.forEach(element => {
      if(!element['time_stamp_formatted']){
        return;
      }
      let elementTime = this.returnElementTime(element['time_stamp_formatted']);
      if(newLabels.indexOf(elementTime) > -1 || newLabels.indexOf(elementTime) > -1){
        return;
      }
      newLabels.push(elementTime);
    });
    return newLabels.sort();
  }

  returnDataLabel(data: {[propertyName: string]: string;}, lineChartDataType: LineChartDataType): string {
    let label: string;
    switch(lineChartDataType){
      case LineChartDataType.events:
      case LineChartDataType.triggers:
        label = this.data.widgetData.displayResultsMethod === LineChartDisplayResults.countResults ? data['resource_name'] : this.returnComposedTranslatedLabel(data['trigger_type'], data['resource_name']);
        break;
      case LineChartDataType.status:
        label = data['status'];
        break;
      default:
        label = data['resource_name'];
        break;
    }
    return label;
  }

  returnTriggerValueOrCounter(oldValue: number, data: {[propertyName: string]: string}): number {
    let value: number;
    switch(this.data.widgetData.displayResultsMethod){
      case LineChartDisplayResults.countResults:
      default:
        value = !oldValue ? 1 : ++oldValue;
        break;
      case  LineChartDisplayResults.resultValue:
        value = parseFloat(data['field_value']);
        break;
    }
    return value;
  }

  returnComposedTranslatedLabel(triggerType: string, resourceName: string): string {
    return TriggerTypeUtils.getSwappedTriggerType()[triggerType] ? this.translateService.instant(TriggerTypeUtils.getSwappedTriggerType()[triggerType]) + '-' + resourceName : triggerType + '-' + 'resourceName';
  }

  returnFormatedLabel(label:string): string {
    let newLabel:string = '';
    let split = label.split('-');
    switch(this.data.widgetData.displayedDataTimeSettings){
      case DateUnit.Days:
        newLabel = this.translateService.instant(split[1].toLowerCase()) + '-' + split[2];
        break;
      case DateUnit.Hours:
        newLabel = parseInt(split[1]) < 10 ? '0'+split[1] + ':' + split[2] : split[1] + ':' + split[2];
        break;
      case DateUnit.Weeks:
        newLabel = split[1];
        break;
      case DateUnit.Months:
        newLabel = this.translateService.instant(split[1].toLowerCase());
        break;
      case DateUnit.Minutes:
      case DateUnit.Seconds:
        newLabel = label;
        break;
      default:
        newLabel = this.translateService.instant(split[1].toLowerCase()) + '-' + split[2];
        break;
    }
    return newLabel;
  }

  returnElementTime(elementDate: string): string {
    let elementTime:string = '';
    switch(this.data.widgetData.displayedDataTimeSettings){
      case DateUnit.Days:
        elementTime = moment(elementDate).format('DDDD-ddd-D');
        break;
      case DateUnit.Hours:
        elementTime = moment(elementDate).format('kk-H-m');
        break;
      case DateUnit.Weeks:
        elementTime = moment(elementDate).format('ww-Wo');
        break;
      case DateUnit.Months:
        elementTime = moment(elementDate).format('M-MMMM');
        break;
      case DateUnit.Minutes:
        elementTime = moment(elementDate).format('m');
        break;
      case DateUnit.Seconds:
        elementTime = moment(elementDate).format('s');
        break;
      default:
        elementTime = moment(elementDate).format('DDDD-ddd-D');
        break;
    }
    return elementTime;
  }

  returnReportsQueryParams(collectOption?: ReportCollectOption): ReportsQueryParams {

    let queryParams: ReportsQueryParams;
    let currentDate = moment(this.data.widgetData.currentDate);
    let fromDate = moment(this.data.widgetData.fromDate);
    let toDate = moment(this.data.widgetData.toDate);

    switch(this.data.widgetData.selectedDateRangeType){
      case DateRangeSearchType.Date:
        fromDate = moment(currentDate).startOf('day');
        toDate = moment(currentDate).endOf('day');
        break;
      case DateRangeSearchType.Last:
        fromDate = moment();
        toDate = moment();
        break;
      case DateRangeSearchType.Range:
        //TODO
        //check date validation
        break;
      default:
        console.info("Selected data range type not available: ", this.data.widgetData.selectedDateRangeType);
        break;
    }

    queryParams = {
      DateLastUnit: this.data.widgetData.selectedDateUnitLast,
      DateRangeType: this.data.widgetData.selectedDateRangeType.toString(),
      From: fromDate.toISOString(),
      To: toDate.toISOString(),
      Identity: GuidUtils.newGuid(),
      Name: "",
      Resource: Guid.EMPTY,
      EventIds: this.data.widgetData.selectedEventsIds,
      ResourceGroup: this.data.widgetData.selectedGroupId,
      TextFilter: "",
      Transformation: '',
      TriggerTypes: this.data.widgetData.selectedTriggers,
      Type: this.returnReportDataType(this.data.widgetData.lineChartDataType), //only triggers
      Value: this.data.widgetData.unitLastValue,
      selectedReport: "addNew",
      CollectOption: collectOption,
      QueryId: Math.floor(Math.random() * 9999),
      UserSessionId: this.authService.user ? this.authService.user.Identity : null
    };

    return queryParams;
  }

  returnReportDataType(lineChartDataType: LineChartDataType): string{
    let reportDataType: ReportDataType;
    switch(lineChartDataType){
      case LineChartDataType.events:
      case LineChartDataType.status:
        reportDataType = ReportDataType.Events;
        break;
      case LineChartDataType.triggers:
        reportDataType = ReportDataType.Triggers;
        break;
      default:
        reportDataType = ReportDataType.Triggers;
        break;
    }
    return reportDataType.toString();
  }

  generateRandomRgba(transparency: number): string {
    const o = Math.round, r = Math.random, s = 255;
    return 'rgba(' + o(r()*s) + ',' + o(r()*s) + ',' + o(r()*s) + ',' + transparency + ')';
  }

  initReportTableModal(): void {
    this.turboTableData = {
      tableColumns: [],
      tableData: this.reportsTurboTableService.buildTableData(this.concatenatedTableData)
    };
    this.turboTableModal.openModal();
  }

}
