import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AuditRoutingModule } from './audit-routing.module';
import { AuditComponent } from './audit.component';
import { AuditFilterComponent } from './audit-filter/audit-filter.component';
import { AuditTableComponent } from './audit-table/audit-table.component';
import { SharedModule } from '../../shared/modules/shared.module';


@NgModule({
  declarations: [
    AuditComponent,
    AuditFilterComponent,
    AuditTableComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    AuditRoutingModule
  ]
})
export class AuditModule { }
