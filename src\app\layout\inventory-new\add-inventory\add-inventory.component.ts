import { Component, Input, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { CymsidebarService } from 'app/shared/components/cym-sidebar/cym-sidebar.service';
import { EditableField } from 'app/shared/modules/data-layer/models/editable-field';
import { Resource } from 'app/shared/modules/data-layer/models/resource';
import { MessageService } from 'primeng/api';
import { ResourceService } from "../../../services/resource/resource.service";
import { ResourceCacheService } from "../../../shared/modules/data-layer/services/resource/resource.cache.service";
@Component({
  selector: 'app-add-inventory',
  templateUrl: './add-inventory.component.html',
  styleUrls: ['./add-inventory.component.scss'],
})
export class AddInventoryComponent {//TODO gather all the subscriptions and dispose them on delete
  @Input('resourceId') resourceId: string = null;
  resource: Resource = new Resource();
  public resourceForm: FormGroup = null;
  editableFields: EditableField;

  constructor(
    private cymSidebarService: CymsidebarService,
    private resourceService: ResourceService,
    private resourceCacheService: ResourceCacheService,
    private formBuilder: FormBuilder,
    private messageService: MessageService,
    private i18n: TranslateService
  ) {


  }

  private ngOnChanges(changes: SimpleChanges): void {
    if (changes && changes.resourceId && changes.resourceId.currentValue) {
      this.resourceForm = null;
      let idAsString = changes.resourceId.currentValue.toString();
      this.getResource(idAsString);
    }
  }

  private getResource(guid: string): void {
    if (this.resourceForm) {
      this.resourceForm.reset();
    }

      let resourcesSubscription = this.resourceCacheService.storageCompleted.subscribe(() =>{
          this.resource = this.resourceCacheService.get(guid);

          this.resourceService.getEditableFields(this.resource.resourceType).subscribe((res)=>{
              this.editableFields = res;
              this.generateForm(this.resource,this.editableFields);
              this.openSideBar();

          });
      });

  }

  private generateForm(resource: Resource, editableFields: EditableField): void {
    let propertiesFormGroup = new FormGroup({});
    this.resourceForm = this.formBuilder.group({
      Identity: new FormControl(resource.identity ? resource.identity : ''),
      name: new FormControl(resource.name ? resource.name : ''),
      resourceType: new FormControl(resource.resourceType ? resource.resourceType : ''),
      groups: new FormControl(resource.groups ? resource.groups : ''),
      status: new FormControl(resource.status ? resource.status : ''),
      location: new FormGroup({
        lat: new FormControl(resource.location.lat ? resource.location.lat : ''),
        lng: new FormControl(resource.location.lng ? resource.location.lng : ''),
      }),
      properties: propertiesFormGroup
    });
    if (editableFields && editableFields.fields.length > 0 && (resource.extraData && resource.extraData.length > 0)) {
      editableFields.fields.forEach((el, index) => {
        propertiesFormGroup.addControl(el, new FormControl(resource.extraData[index].value));
      });
    }
  }

  get props(): FormGroup {
    return this.resourceForm.get('properties') as FormGroup;
  }

  get location(): FormGroup {
    return this.resourceForm.get('location') as FormGroup;
  }

  private openSideBar(): void {
    this.cymSidebarService.toggleDockSidebar("push");
    this.cymSidebarService.openSidebar();
  }

  private updateResource(): void {
    let resource = new Resource({
      identity: this.resourceForm.value.Identity,
      resourceType: this.resourceForm.value.resourceType,
      name: this.resourceForm.value.name,
      location: {
        lat: this.resourceForm.value.location.lat,
        lng: this.resourceForm.value.location.lng
      },
      extraData: []
    });
    for (let key in this.resourceForm.value.properties) {
      resource.extraData.push({
        name: key,
        value: this.resourceForm.value.properties[key]
      });
    }
    this.resourceService.update(resource).subscribe(() => {
      this.messageService.add({severity: 'success', detail: this.i18n.instant('successfullyDownloaded'), summary: this.i18n.instant('success') });
      this.cancel();
    }, () => {
      this.messageService.add({severity: 'error', detail: this.i18n.instant('rowUpdateFailed'), summary: this.i18n.instant('error') });
    });
  }

  private cancel(): void{
    this.resourceId = null;
    this.resourceForm.reset();
    this.cymSidebarService.closeSidebar();
  }
}
