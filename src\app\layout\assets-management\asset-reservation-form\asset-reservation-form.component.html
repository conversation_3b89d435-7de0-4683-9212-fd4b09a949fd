<form (ngSubmit)="onSubmit()">
    <div class="container">
        <div class="form-group panel-body form-check">
            <div class="row panel-body">
                <div class="col-6">
                    <label class="col-form-label">{{ 'asset' | translate }}*</label>
                </div>
                <div class="col-6">
                    <p-dropdown [(ngModel)]="selectedAsset" [ngModelOptions]="{standalone: true}" [style]="{'width': '140px'}" placeholder="{{ 'asset' | translate }}"
                        [options]="assets" optionLabel="name" (onChange)="onSelectAsset($event)"></p-dropdown>
                </div>
            </div>
            <div class="row panel-body">
                <div class="col-6">
                    <label class="col-form-label">{{ 'entity' | translate }}*</label>
                </div>
                <div class="col-6">
                    <p-dropdown [(ngModel)]="selectedEntity" [ngModelOptions]="{standalone: true}" [style]="{'width': '140px'}" placeholder="{{ 'entity' | translate }}"
                        [options]="entities" optionLabel="name" (onChange)="onSelectEntity($event)"></p-dropdown>
                </div>
            </div>
        </div>
        <div class="form-group row" [class.invalid-dates]="invalidDates">
            <div class="col-6">
                <label for="startDate" class="col-form-label">{{ "startDate" | translate }}*</label>
                <app-datetime-picker [(date)]="startDate" [maxDate]="maxDate" [minDate]="minDate" placeholder='{{ "startDate" | translate }}'
                    [showDateInForm]="true" [showTime]="true" (valueChange)="onStartDateChange($event)"></app-datetime-picker>
            </div>
            <div class="col-6">
                <label for="expirationDate" class="col-form-label">{{ "endDate" | translate }}*</label>
                <app-datetime-picker [(date)]="endDate" [maxDate]="maxDate" [minDate]="minDate" placeholder='{{ "endDate" | translate }}'
                    [showDateInForm]="true" [showTime]="true" (valueChange)="onEndDateChange($event)"></app-datetime-picker>
            </div>
            <span *ngIf="invalidDates" class="help-block">{{ 'startTimeShouldBeBeforeEndTime' | translate }}</span>
        </div>
        <div *ngIf="selectedAsset.type !== assetTypes.PARKING && !_reservation.Identity" class="row panel-body">
            <div class="col-12">
                <p-checkbox binary="true" [ngModelOptions]="{standalone: true}" [ngModel]="reserveParking" label="{{ 'reserveParking' | translate }}"
                    (onChange)="onCheckboxClicked($event)"></p-checkbox>
            </div>
        </div>
        <br>
        <div class="form-group row">
            <div class="col-12">
                <label class="form-label">{{ "comments" | translate }}</label>
                <textarea pInputTextarea rows="4" [(ngModel)]="comments" [ngModelOptions]="{standalone: true}"></textarea>
            </div>
        </div>
        <p class="font-italic">{{ 'fieldsMarkedWithAreMandatory' | translate }}</p>
        <div class="form-group row">
            <div class="col" [ngClass]="isRtl ? 'text-right' : 'text-left'">
                <button type="button" [disabled]="!_reservation.Identity" class="btn btn-danger" (click)="onDelete()">{{ "delete" | translate }}</button>
            </div>
            <div class="col text-center">
                <button type="button" class="btn btn-secondary" (click)="onCancel()">{{ "cancel" | translate }}</button>
            </div>
            <div class="col" [ngClass]="isRtl ? 'text-left' : 'text-right'">
                <button type="submit" [disabled]="invalidDates || invalidForm" class="btn btn-success">{{ "save" | translate }}</button>
            </div>
        </div>
    </div>
</form>
