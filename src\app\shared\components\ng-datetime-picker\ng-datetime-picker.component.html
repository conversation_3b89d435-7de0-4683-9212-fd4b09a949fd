<p-calendar  *ngIf="showDateInForm" [(ngModel)]="date" [showTime]="showTime" [hourFormat]="hourFormat"
            (ngModelChange)="onPickerChange()" [locale]="locale" [inline]="inline" [timeOnly]="timeOnly" [dateFormat]="dateFormat" [showIcon]="showIcon"
            [maxDate]="maxDate" [minDate]="minDate"
            [placeholder]="placeholder" [icon]="icon"  [showSeconds]="showSeconds" appendTo="body"></p-calendar>

<p-calendar  *ngIf="!showDateInForm" [(ngModel)]="date"  [(ngModel)]="date" [showTime]="showTime" [hourFormat]="hourFormat"
            (ngModelChange)="onPickerChange()" [locale]="locale" [inline]="inline" [timeOnly]="true" [dateFormat]="dateFormat" [showIcon]="showIcon"
            [maxDate]="maxDate" [minDate]="minDate"
            [placeholder]="placeholder" [icon]="icon" [showSeconds]="showSeconds" appendTo="body"></p-calendar>
