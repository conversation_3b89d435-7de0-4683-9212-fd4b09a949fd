import { Component, OnInit } from '@angular/core';
import { EmptySpaceWidget } from 'app/layout/customer-dashboard/models/empty-space-widget';
import { DefaultWidgetComponent } from '../default-widget/default-widget.component';

@Component({
  selector: 'app-empty-space-widget',
  templateUrl: './empty-space-widget.component.html',
  styleUrls: ['./empty-space-widget.component.scss']
})
export class EmptySpaceWidgetComponent extends DefaultWidgetComponent implements OnInit {
  data: { index: number, widgetData: EmptySpaceWidget };

  constructor() {
    super();
  }

  ngOnInit(): void {
    this.data.widgetData = new EmptySpaceWidget(this.data.widgetData);
  }
}
