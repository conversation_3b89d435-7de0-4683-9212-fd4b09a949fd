$red: #FF0000;
$green: #00D600;
$textColor: #808381;

.green  {
    color: $green;
}

.red  {
    color: $red;
}

.bold {
    font-weight: bold;
}

.filters-container {
    padding: 0.5rem;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.filters-wrapper {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 20px;
    align-items: center;
}

.title {
    font-size: 20px;
    color: #3a3a3a;
    font-weight: 600;
    white-space: nowrap;
}

.filters-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
    width: 100%;
}

.filter-element-container {
    width: 100%;
    text-align: center;
    flex: 1;
    min-width: 200px;
}

.date-range {
    min-width: 220px;
}

.filter-element {
    display: flex;
    align-items: center;
    background: #F2F6F3;
    padding: 5px;
    border-radius: 8px;
    gap: 5px;
    flex: 1;
}

.reset-button {
    width: min-content;
    flex: 0;
    min-width: auto;
}

.export-buttons {
    width: min-content;
    flex: 0;
    min-width: auto;

    .filter-element {
        gap: 8px;
        display: flex;
    }

    .export-btn {
        background: #E4ECE6;
        border: 1px solid #D9D9D9;
        color: $green;
        width: 2.5rem !important;
        height: 2.5rem !important;
        border-radius: 8px;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 0 !important;
        margin: 0 !important;
        min-width: auto !important;
        box-sizing: border-box;

        &:hover:not(:disabled) {
            background: $green;
            color: white;
            border-color: $green;
        }

        &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        i {
            font-size: 1rem !important;
            margin: 0 !important;
            padding: 0 !important;
            line-height: 1 !important;
            display: block !important;
            text-align: center !important;
            vertical-align: middle !important;
            position: static !important;
            left: auto !important;
            top: auto !important;
            transform: none !important;
        }

        &:focus {
            box-shadow: 0 0 0 0.2rem rgba($green, 0.25);
        }
    }
}

.p-inputtext {
    &:focus {
        box-shadow: 0 0 0 0.2rem rgba($green, 0.5);
        border: 0px;
    }
    border: 1px solid #E4ECE6;
}

.p-inputtext,
    p-dropdown,
    p-calendar {
    flex: 1;
    font-weight: 500;
    background: #F2F6F3;
}

::ng-deep .p-dropdown {
    background: #F2F6F3;
    border: 1px solid #E4ECE6;
    font-weight: 500;
    .p-dropdown-label,
    .p-dropdown-trigger{
        color: $textColor;
    }
}

.filter-element i,
.filter-element img {
    font-size: 1.1rem;
    padding: 0px 5px 0px 5px;
}


:host ::ng-deep {
    p-splitbutton {
            .p-splitbutton-defaultbutton, .p-splitbutton-menubutton {
                background: $green;
                border-color: $green;
             
            }
            .p-button:enabled:hover{
                background: $textColor;
                border-color: $textColor;
            }
        }

    .p-calendar {
       width: 100%;
       border: 1px solid #E4ECE6;
       .p-inputtext {
           background: transparent;
           border: 0px;
       }
       &:focus {
           box-shadow: 0 0 0 0.2rem $green
       }
   }

   .p-datepicker {
       .p-datepicker-header {
           .p-datepicker-title {
               .p-datepicker-month,
               .p-datepicker-year {
                   color: #3a3a3a;
               }
           }
       }

       .p-datepicker-calendar {
           td {
               &.p-datepicker-today > span {
                   background: rgba($green, 0.2);
                   color: $green;
                   border-color: transparent;
               }

               > span.p-highlight {
                   background: $green;
                   color: #ffffff;
               }
           }
       }

       .p-timepicker {
           button {
               &:hover {
                   color: $green;
               }

               &.p-highlight {
                   color: $green;
               }
           }
       }
   }
}


@media (max-width: 1200px) {
    .filters-content,
    .filters-wrapper {
        grid-template-columns: 1fr;
    }
}

// Override PrimeNG button styles to ensure perfect icon centering
:host ::ng-deep {
    .export-btn {
        .p-button-label {
            display: none !important;
        }

        .p-button-icon {
            margin: 0 !important;
            padding: 0 !important;
            position: static !important;
            left: auto !important;
            top: auto !important;
            transform: none !important;
        }

        &.p-button-text {
            padding: 0 !important;
            min-width: auto !important;
            width: 2.5rem !important;
            height: 2.5rem !important;
        }

        // Force flexbox centering
        &.p-button {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            padding: 0 !important;
            margin: 0 !important;

            .p-button-icon-left {
                margin-right: 0 !important;
            }

            .p-button-icon-right {
                margin-left: 0 !important;
            }
        }
    }
}
