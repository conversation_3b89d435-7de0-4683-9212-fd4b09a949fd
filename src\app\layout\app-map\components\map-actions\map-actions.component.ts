import { Component, OnInit, Input, Output, EventEmitter, OnChanges, SimpleChanges, ViewChild, Inject, ElementRef } from '@angular/core';
import { GlobalAction } from '../../../../shared/models/global-action.interface';
import { MapActionType } from '../../enums/map-action-type.enum';
import { MapFilters } from '../../models/map-filters.interface';
import { MapSearchProperties } from '../../models/map-search-properties.interface';
import {MapUtilsService} from "app/layout/app-map/services/map-utils.service";

@Component({
  selector: 'app-map-actions',
  templateUrl: './map-actions.component.html',
  styleUrls: ['./map-actions.component.scss']
})
export class MapActionsComponent implements OnInit, OnChanges {
  @ViewChild('overlayActions', {static: false}) overlayActions;
  @Input('selectedMapId') selectedMapId: string;
  @Input('mapSelector') mapSelector: ElementRef;
  @Input('selectedMapAction') selectedMapAction: MapActionType;
  @Input('mapFilterOptions') mapFilterOptions: MapFilters;
  @Input('mapSearchProperties') mapSearchProperties: MapSearchProperties;
  @Output() mapAction:EventEmitter<string> = new EventEmitter()

  public dropDownActions: GlobalAction[] = [];
  public editActions: GlobalAction[] = [];


  constructor(
    @Inject('mapDropDownActions') dropDownActions: GlobalAction[],
    @Inject('mapEditActions') editActions: GlobalAction[],
  ) {
    this.dropDownActions = dropDownActions;
    this.editActions = editActions;

  }

  ngOnInit(): void {

    this.buildDropdownActionMenu();
    this.updateEditActions();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.selectedMapId) {

      this.buildDropdownActionMenu();
    }
    if (changes.mapFilterOptions) {
      const action = this.editActions.find((item: GlobalAction) => {
        return item.type === MapActionType.filter;
      });
      if(!action){
        return;
      }
      if (changes.mapFilterOptions.currentValue && changes.mapFilterOptions.currentValue.selectedResourceGroups && changes.mapFilterOptions.currentValue.selectedResourceGroups.length > 0) {
        action.isActive = true;
      } else {
        action.isActive = false;
      }
    }

    if (changes.selectedMapAction && changes.selectedMapAction.currentValue) {
      const action = this.editActions.find((item: GlobalAction) => {
        return item.type === changes.selectedMapAction.currentValue;
      });
      if (action) {
        action.isActive = true;
      }
    }

    if(changes.mapSearchProperties){
      const action = this.editActions.find((item: GlobalAction) => {
        return item.type === MapActionType.resetSearch;
      });
      if(changes.mapSearchProperties.currentValue && changes.mapSearchProperties.currentValue.fieldName && changes.mapSearchProperties.currentValue.operand && changes.mapSearchProperties.currentValue.freeText){
        action.isVisible = true;
        return;
      }
      action.isVisible = false;
    }
  }

  updateEditActions(): void {
      this.editActions.map((item) => {
        item.isActive = this.selectedMapAction === item.type &&
        (this.mapFilterOptions.selectedResourceGroups && this.mapFilterOptions.selectedResourceGroups.length > 0) ? true : false;
      });
  }

  buildDropdownActionMenu(): void {
    this.dropDownActions.map(item => {
      item.isVisible = !this.selectedMapId && item.type !== MapActionType.addNew ? false : true;
    });
  }

  emitMapAction(option: MapActionType):void {
    this.selectedMapAction = option;
    this.editActions.map(item=>{
            if(this.selectedMapAction !== item.type){
              item.isActive = false;
            }
    });
    this.mapAction.emit(option);
    this.overlayActions.hide();
  }

}
