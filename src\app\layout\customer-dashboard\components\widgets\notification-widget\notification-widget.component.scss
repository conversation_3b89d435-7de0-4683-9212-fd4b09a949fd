:host {
    .notification-container{
        display: flex;
        flex-direction:column;
        gap:10px;
    }
    .notification-wrapper {

        .scroll-wrapper {
            padding: 6px;
            height: 100%;
            overflow-y: scroll;
        }

        .notification-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 0.8125rem;
            &:hover {
                background-color: var(--secondary-highlight-3);
                color: var(--secondary-3);
                cursor: pointer;
            }

            .item-description {
                font-weight: 700;
                margin-right: auto;
                white-space: normal;
            }

            .btn {
                line-height: 0.8125rem;
                font-size:  0.8125rem;
                border-radius: 50%;
                padding: 0;
                width: 30px;
                height: 30px;
                text-align: center;
                background: transparent;
                border: 1px solid #787878;
                color: #787878;
                &.notification-acknowledged {
                    border-color: var(--secondary-3);
                }
                &.empty {
                    .icon::before {
                        content: '\f12a';
                    }
                }
                &.channel {
                    border-color: #55DC99;
                    color: #55DC99;
                    .icon::before {
                        content: '\f03d';
                    }
                }
                &.map {
                    border-color: #9391FC;
                    color: #9391FC;
                    .icon::before {
                        content: '\f279';
                    }
                }
                &.opendashboard {
                    border-color: #94BBFB;
                    color: #94BBFB;

                    .icon {
                        font-family: 'icomoon';

                        &::before {
                            content: '\e907';
                        }
                    }
                }
                //TODO
                //Update list with icons and colors

                .icon {
                    font-family: 'FontAwesome';
                    &::before {
                        content: '\f12a';
                    }
                }
            }

            .event-link {
                margin-right: 10px;
                i {
                    color: var(--secondary-3);
                }
            }

            .timestamp {
                font-weight: 300;
            }
        }
    }
}

:host-context(.widget.big) {
    .notification-item {
        align-items: center;
        justify-content: start;

        .item-description {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-right: 0;
            width: stretch;
        }
    }

}
.dismissAll{
    float: right;
    cursor: pointer;
}
