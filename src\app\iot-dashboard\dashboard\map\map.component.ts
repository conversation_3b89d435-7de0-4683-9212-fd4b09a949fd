import { AfterViewInit, ApplicationRef, Component, ComponentFactoryResolver, ElementRef, Injector, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { ResourceService } from 'app/services/resource/resource.service';
import Feature from 'ol/Feature';
import Map from 'ol/Map';
import Overlay from 'ol/Overlay';
import View from 'ol/View';
import { Geometry } from 'ol/geom';
import Point from 'ol/geom/Point';
import TileLayer from 'ol/layer/Tile';
import VectorLayer from 'ol/layer/Vector';
import { fromLonLat } from 'ol/proj';
import OSM from 'ol/source/OSM';
import VectorSource from 'ol/source/Vector';
import { Circle as CircleStyle, Fill, Stroke, Style } from 'ol/style';
import { Subscription } from 'rxjs';
import { CustomPopupComponent } from '../../../shared/modules/cymbiot-map/components/custom-popup/custom-popup.component';
import { ResourceState } from '../../../shared/modules/data-layer/enum/resource/resource-state.enum';
import { DataChangeType } from '../../../shared/modules/data-layer/models/data-change-type.enum';
import { Resource } from '../../../shared/modules/data-layer/models/resource';
import { ResourceCacheService } from '../../../shared/modules/data-layer/services/resource/resource.cache.service';

@Component({
  selector: 'app-map',
  templateUrl: './map.component.html',
  styleUrls: ['./map.component.scss']
})
export class MapComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('map') mapElement!: ElementRef;

  private map: Map;
  private vectorSource: VectorSource;
  private vectorLayer: VectorLayer<any>;
  private resources: { [key: string]: Resource } = {};
  private subscriptions: Subscription[] = [];
  private mapInitialized = false;
  private pendingResources: Resource[] = [];
  private overlay: Overlay;
  private popupContainer: HTMLElement;

  constructor(
    private resourceService: ResourceService,
    private resourceCacheService: ResourceCacheService,
    private componentFactoryResolver: ComponentFactoryResolver,
    private injector: Injector,
    private appRef: ApplicationRef
  ) {}

  ngOnInit(): void {
    this.setupResourceSubscriptions();
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.initializeMap();
      this.mapInitialized = true;
      if (this.pendingResources.length > 0) {
        this.updateResources(this.pendingResources);
        this.pendingResources = [];
      }
    });
  }

  private initializeMap(): void {
    if (!this.mapElement?.nativeElement) {
      return;
    }

    this.initializePopup();
    this.initializeVectorLayer();
    this.createMap();
    this.setupMapClickHandler();


    setTimeout(() => this.map.updateSize(), 100);
  }

  private initializePopup(): void {
    this.popupContainer = document.createElement('div');
    this.popupContainer.className = 'ol-popup';
    document.body.appendChild(this.popupContainer);

    this.overlay = new Overlay({
      element: this.popupContainer,
      positioning: 'bottom-center',
      offset: [0, -40],
      stopEvent: true,
      autoPan: {
        animation: {
          duration: 250
        }
      }
    });
  }

  private initializeVectorLayer(): void {
    this.vectorSource = new VectorSource();
    this.vectorLayer = new VectorLayer({
      source: this.vectorSource,
      style: (feature) => this.getResourceStyle(feature as Feature<Geometry>)
    });
  }

  private createMap(): void {
    this.map = new Map({
      target: this.mapElement.nativeElement,
      layers: [
        new TileLayer({
          source: new OSM()
        }),
        this.vectorLayer
      ],
      overlays: [this.overlay],
      view: new View({
        center: fromLonLat([25.0094303, 45.9442858]),
        zoom: 6,
        maxZoom: 19
      })
    });
  }

  private setupMapClickHandler(): void {
    this.map.on('click', (evt) => {
      const feature = this.map.forEachFeatureAtPixel(evt.pixel, (feature) => feature);

      if (feature && feature instanceof Feature) {
        const geometry = feature.getGeometry();
        if (geometry instanceof Point) {
          const resource = feature.get('properties')?.resource;
          if (resource) {
            const coordinates = geometry.getCoordinates();
            this.showPopup(resource, coordinates);
          }
        }
      } else {
        this.hidePopup();
      }
    });
  }

  private updateResources(resources: Resource[]): void {
    if (!this.mapInitialized) {
      this.pendingResources = resources;
      return;
    }

    this.resources = {};
    resources.forEach(resource => {
      const coordinates = this.getResourceCoordinates(resource);
      if (coordinates) {
        this.resources[resource.identity] = resource;
      }
    });

    this.updateMapFeatures();
  }

  private updateMapFeatures(): void {
    if (!this.vectorSource || !this.mapInitialized) {
      return;
    }

    this.vectorSource.clear();
    const resources = Object.values(this.resources);
    let featuresAdded = 0;

    resources.forEach(resource => {
      const coordinates = this.getResourceCoordinates(resource);
      if (coordinates) {
        const lonLat = [coordinates[1], coordinates[0]];
        const transformed = fromLonLat(lonLat);

        const feature = new Feature({
          geometry: new Point(transformed),
          properties: {
            resource: resource
          }
        });

        this.vectorSource.addFeature(feature);
        featuresAdded++;
      }
    });

    if (featuresAdded > 0) {
      const extent = this.vectorSource.getExtent();
      const mapSize = this.map.getSize();
      if (!mapSize) return;


      this.map.getView().fit(extent, {
        padding: [150, 50, 50, 50],
        maxZoom: 18
      });
    }
  }

  private getResourceStyle(feature: Feature<Geometry>): Style {
    const resource = feature.get('properties')?.resource as Resource;
    if (!resource) {
      return this.getDefaultStyle();
    }

    let fillColor = '#3399CC';
    let strokeColor = '#FFFFFF';
    let radius = 7;

    switch (resource.status) {
      case ResourceState.online:
      case ResourceState.on:
        fillColor = '#4CAF50';
        break;
      case ResourceState.offline:
      case ResourceState.off:
        fillColor = '#9E9E9E';
        break;
      case ResourceState.alarmed:
        fillColor = '#FFA500';
        radius = 8;
        break;
      case ResourceState.error:
        fillColor = '#FF0000';
        radius = 8;
        break;
      case ResourceState.unknown:
        fillColor = '#673AB7';
        break;
      case ResourceState.withoutCommunication:
        fillColor = '#795548';
        break;
      case ResourceState.tempError:
        fillColor = '#FF5722';
        break;
      case ResourceState.simIssue:
        fillColor = '#E91E63';
        break;
      default:
        fillColor = '#3399CC';
        break;
    }

    return new Style({
      image: new CircleStyle({
        radius: radius,
        fill: new Fill({
          color: fillColor
        }),
        stroke: new Stroke({
          color: strokeColor,
          width: 2
        })
      })
    });
  }

  private getDefaultStyle(): Style {
    return new Style({
      image: new CircleStyle({
        radius: 7,
        fill: new Fill({
          color: '#3399CC'
        }),
        stroke: new Stroke({
          color: '#FFFFFF',
          width: 2
        })
      })
    });
  }

  private getResourceCoordinates(resource: Resource): [number, number] | null {
    if (!resource.location?.lat && !resource.location?.lng) {
      return null;
    }
    return [
      resource.location.lat,
      resource.location.lng
    ];
  }

  private handleResourceChange(change: any): void {
    switch (change.type) {
      case DataChangeType.Create:
      case DataChangeType.Update:
        change.models.forEach((resource: Resource) => {
          this.resources[resource.identity] = resource;
        });
        this.updateMapFeatures();
        break;
      case DataChangeType.Delete:
        change.models.forEach((resource: Resource) => {
          delete this.resources[resource.identity];
        });
        this.updateMapFeatures();
        break;
    }
  }

  private showPopup(resource: Resource, coordinates: number[]): void {
    if (this.overlay && this.popupContainer) {
      const popup = this.componentFactoryResolver
        .resolveComponentFactory(CustomPopupComponent)
        .create(this.injector);

      popup.instance.resource = resource;
      popup.instance.removePopUp.subscribe(() => {
        this.hidePopup();
      });

      this.appRef.attachView(popup.hostView);
      this.popupContainer.innerHTML = '';
      this.popupContainer.appendChild(popup.location.nativeElement);
      this.overlay.setPosition(coordinates);
      this.map.addOverlay(this.overlay);
    }
  }

  private hidePopup(): void {
    this.overlay.setPosition(undefined);
    this.popupContainer.innerHTML = '';
  }

  private setupResourceSubscriptions(): void {
    this.subscriptions.push(
      this.resourceCacheService.storageCompleted.subscribe(() => {
        const resources = this.resourceCacheService.getAll();
        this.updateResources(resources);
      })
    );

    this.subscriptions.push(
      this.resourceService.resourceChanged.subscribe((change) => {
        this.handleResourceChange(change);
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    
  }
}
