import { Component, EventEmitter, Input, OnChanges, OnInit, Output, ViewChild } from '@angular/core';
import { EventService, EventType } from 'app/shared/services/event.service';
import { Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { DashboardActionType } from '../../enums/dashboard-action-type.enum';
import { DashboardAction } from '../../models/dashboard-action.interface';
import { DashboardTreeItem } from '../../models/dashboard-tree-item.interface';


@Component({
  selector: 'app-dashboard-actions',
  templateUrl: './dashboard-actions.component.html',
  styleUrls: ['./dashboard-actions.component.scss']
})
export class DashboardActionsComponent implements OnInit, OnChanges {
  @ViewChild('overlayActions', {static: false}) overlayActions;
  @Input() dashboardsTree: DashboardTreeItem[] = [];
  @Input() selectedDashboardId: string;
  @Output() dashboardAction:EventEmitter<string> = new EventEmitter();
  private subscriptions: Subscription[] = [];
  public dropDownActions: DashboardAction[] = [
    {name: 'customerDashboard.editDashboard', type: DashboardActionType.edit},
    {name: 'customerDashboard.addNewDashboard', type: DashboardActionType.addNew},
    {name: 'customerDashboard.saveAsDefault', type: DashboardActionType.saveAsDefault},
    // {name: 'customerDashboard.saveTemplateDashboard', type: DashboardActionsTypes.addNew},
    {name: 'customerDashboard.delete', type: DashboardActionType.delete}
  ]


  constructor(
    private eventService: EventService
  ) { }

  ngOnInit(): void {
    this.buildActionMenu();
    let contentResizeSubscription = this.eventService.observe(EventType.ContentResize).pipe(debounceTime(500), distinctUntilChanged()).subscribe(() => {
      this.buildActionMenu();
    });
    this.subscriptions.push(contentResizeSubscription);
  }

  ngOnChanges(): void {
    this.buildActionMenu();
  }

  emitDashboardAction(option: DashboardActionType): void {
    switch (option)
    {
      case DashboardActionType.addNew:
        this.selectedDashboardId = null;
        break;

      default:
        break;
    }

    this.dashboardAction.emit(option);
    this.overlayActions.hide();
    this.buildActionMenu();
  }

  buildActionMenu(): void {
    
    this.dropDownActions.map(item => {
      return this.returnUpdatedActionVisibility(item);
    });
  }

  returnUpdatedActionVisibility(action: DashboardAction): DashboardAction {
    switch(action.type){
      case DashboardActionType.edit:
        action.isVisible  = !this.selectedDashboardId  ? false : true; 
        break;
      case DashboardActionType.addNew:
        action.isVisible =true; 
        break;
      case DashboardActionType.saveAsDefault:
      case DashboardActionType.delete:
        action.isVisible  = !this.selectedDashboardId  ? false : true; 
        break;
      default:
        console.error('Invalid action type');
        break;
    }
    return action;
  }

}
