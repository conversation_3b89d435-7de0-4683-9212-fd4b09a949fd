import {
    AfterViewInit,
    Component,
    ComponentFactoryResolver,
    ComponentRef,
    ElementRef,
    OnDestroy,
    OnInit,
    QueryList,
    ViewChild,
    ViewChildren,
    ViewContainerRef
} from "@angular/core";
import { PlayerComponent } from "../../shared/components/player/player.component";

import { ActivatedRoute } from "@angular/router";
import { TranslateService } from "@ngx-translate/core";
import { AppModal } from '../..//shared/components/app-modal/app-modal.component';
import { Guid } from "../..//shared/enum/guid";
import { ToastTypes } from '../..//shared/enum/toast-types';
import { CloseVideoWallChannel } from "../..//shared/models/clseChannel.model";
import { SetMonitorChannel } from "../..//shared/models/setMonitorChannel.model";
import { VideoWall } from "../..//shared/models/videoWall.model";
import { MonitorChannel } from "../../shared/models/videoWallChannels.model";
import { SettingsService } from "../..//shared/services/settings.service";
import { MessageService } from "primeng/api";
import { ResourceStoreService } from "../../services/store_servcies/resource-store.service";
import { VideoWallLayouts } from "../../shared/models/videoWallLayouts.model";
import { FullScreenService } from "../../shared/services/fullscreen.service";
import { PlayersService } from "../../shared/services/players.service";
import { VideoWallService } from "../../shared/services/video-wall.service";
import { AppModalPosition } from "../../shared/components/app-modal/app-modal-position";
import { FullscreenComponent } from '../../shared/components/fullscreen/fullscreen.component';
import { OverlayPanel } from 'primeng/overlaypanel';
import { CameraSelectionOptions } from './../../shared/models/camera-selection.model';
import { switchMap, takeUntil, tap } from "rxjs/operators";
import { forkJoin, Subject } from "rxjs";

@Component({
  selector: 'app-video-wall',
  templateUrl: './video-wall.component.html',
  entryComponents: [PlayerComponent],
  styleUrls: ['./video-wall.component.scss'],
})
export class VideoWallComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChildren("container") containers: QueryList<ElementRef>;
    @ViewChild("timeline", {static: false}) timeline;
    @ViewChild("playersWrapper", {static: false}) playersWrapper;
    @ViewChild("timelineWrapper", {static: false}) timelineWrapper;
    @ViewChild('camerasModal') camerasModal: AppModal;
    @ViewChild('fullScreen') fullScreen: FullscreenComponent;
    @ViewChild('settingOverlayPanel', {static: false}) settingOverlayPanel: OverlayPanel;
    @ViewChild('layoutOverlayPanel', {static: false}) layoutOverlayPanel: OverlayPanel;

    selectedPlayerId: string;

    pComponents: PlayerComponent[] = [];
    players:ComponentRef<PlayerComponent>[] = [];
    
    playerCountNew:number = 1;
    
    monitorLayoutId="53bbd21b-0435-45b9-8925-f99385d11b8d";

    layouts = [
        { label: "icon-view-1", id: 1, layoutId: '53bbd21b-0435-45b9-8925-f99385d11b8d' },
        { label: "icon-view-4", id: 4, layoutId: '0f32361b-1a14-4103-9551-f0afb8377b03' },
        { label: "icon-view-9", id: 9, layoutId: '741e579f-d77a-4234-9f5a-5219f9f6f33b' },
        { label: "icon-view-16", id: 16, layoutId: '594c6f4d-be3f-4377-a7a9-d4804ad34dcb' }
    ];

    isTimelineOpen: boolean = false;
    playersJson = {};
    
    videoWallGuid:string;
    vcaOptions = { showConfig: true, showObjects: true };
    showVCAConfig = true;
    showVCAObjects = true;
    ptzOptions = { show: true };
    showPTZControls = false;
    videoWalllayouts:any[]=[];
    videoLayouts:any[]=[];

    videoMonitorIndex:number;

    position: AppModalPosition = AppModalPosition.Center;
    channelsList: MonitorChannel[] = [];
    private destroy$ = new Subject<void>();

    constructor(
        private playersService: PlayersService,
        private viewContainer: ViewContainerRef,
        private componentFactoryResolver: ComponentFactoryResolver,
        private settingsService: SettingsService,
        private fullScreenService: FullScreenService,
        private i18n: TranslateService,
        private messageService: MessageService,
        private videoWallService: VideoWallService,
        private playerService: PlayersService,
        private route: ActivatedRoute,
        private resourceStoreService:ResourceStoreService) {
        
        this.videoWallService.closePlayerSubject.subscribe((playerId) => {
            this.closePlayer(playerId);
        })

        this.route.queryParams.subscribe(params => {
           this.videoMonitorIndex = params['videoMonitorId'];
        });

        this.fullScreenService.fullScreenCall$.subscribe(isFullScreen => {
            if (isFullScreen) {
                this.isTimelineOpen = false;
            } else {
                this.isTimelineOpen = this.settingsService.get("isTimelineOpen");
            }
        });

        this.videoLayouts['53bbd21b-0435-45b9-8925-f99385d11b8d']=1;
        this.videoLayouts['0f32361b-1a14-4103-9551-f0afb8377b03']=4;
        this.videoLayouts['786c4aa4-a33b-b321-2ff3-bcdc4123aaa4']=6;
        this.videoLayouts['741e579f-d77a-4234-9f5a-5219f9f6f33b']=9;
        this.videoLayouts['1ef2a814-1d4e-43e2-9392-5ccdbdcbbcf4']=10;
        this.videoLayouts['16d05cb1-9b85-04a0-9ab1-b98d774a8e92']=12;
        this.videoLayouts['594c6f4d-be3f-4377-a7a9-d4804ad34dcb']=16;
        this.videoLayouts['c388a654-a44d-a307-3d4d-44add3488dd2']=20;
        this.videoLayouts['ab654c44-cb3b-77d7-c33c-dd3456dd2a1a']=21;
        this.videoLayouts['9897c01b-de66-456d-b234-0cb941245e70']=100;
        this.videoLayouts['14aa92f3-cd78-75b3-b7b4-df54aaff456a']=111;
     }

    // ngOnInit(): void {
    //     console.log('ngOnInit');
    //     this.videoWallService.getLayouts().subscribe((response:VideoWallLayouts[])=>{
    //         this.videoWalllayouts = response;
    //         console.log('videoWalllayouts', this.videoWalllayouts);
    //     });
    //     this.videoWallService.getVideoWalls().subscribe(response=>{
    //         this.videoWallGuid=response[0];
    //         console.log('videoWallGuid', this.videoWallGuid);
    //         this.videoWallService.getLayout(this.videoWallGuid, this.videoMonitorIndex-1).subscribe((response:Guid)=>{
    //             this.monitorLayoutId = response.toString();
    //             console.log('monitorLayoutId', this.monitorLayoutId);
    //             this.videoWalllayouts.map(item=>{
    //                 if(item[1].id == response){
    //                     this.playerCountNew=this.videoLayouts[item[1].id];
    //                 }
    //             })
    //         });
    //         this.videoWallService.getVideoWallChannels(this.videoWallGuid).subscribe((channels:MonitorChannel[])=>{
    //                channels.map((channel)=>{
    //                 if(channel.MonitorId == this.videoMonitorIndex -1 ){
    //                     this.openLastLayoutChannels(channel)
    //                 }
    //             })
    //         });
    //     })
    //     this.videoWallOptionsSettings();
    // }


    ngOnInit(): void {
        forkJoin({
            layouts: this.videoWallService.getLayouts(),
            walls: this.videoWallService.getVideoWalls()
        }).pipe(
            takeUntil(this.destroy$),
            tap(({ layouts, walls }) => {
                this.videoWalllayouts = layouts;
                this.videoWallGuid = walls[0];
            }),
            switchMap(() =>
                forkJoin({
                    layoutId: this.videoWallService.getLayout(this.videoWallGuid, this.videoMonitorIndex - 1),
                    channels: this.videoWallService.getVideoWallChannels(this.videoWallGuid)
                })
            ),
            tap(({ layoutId, channels }) => {
                this.monitorLayoutId = layoutId.toString();

                const matchTuple = this.videoWalllayouts.find(item => item[1].id === layoutId);
                if (matchTuple) {
                    const layout = matchTuple[1];
                    this.playerCountNew = this.videoLayouts[layout.id];
                }
                this.channelsList = channels;
            })
        ).subscribe({
            next: () => {
                this.videoWallOptionsSettings();
                // After data is loaded, restore channels if players are ready
                if (this.players && this.players.length > 0) {
                    this.restoreChannelsAfterLayoutChange();
                }
            }
        });
}

    ngAfterViewInit(): void {
        this.containers.changes.subscribe(() => {
            this.initData();
        });
    }



    videoWallOptionsSettings(): void {
        this.isTimelineOpen = this.settingsService.get("isTimelineOpen");
        if (!this.isTimelineOpen) {
            this.isTimelineOpen = false;
        }
        let vcaOptions = this.settingsService.get("vcaOptions");
        if (vcaOptions) {
            this.showVCAConfig = vcaOptions.showConfig;
            this.showVCAObjects = vcaOptions.showObjects;
        }
        let ptzOptions = this.settingsService.get("ptzOptions");
        if (ptzOptions) {
            this.showPTZControls = ptzOptions.show;
        }
        setTimeout(() => {
            this.notify();
            this.messageService.add({key: 'vmsOpen', life: 5000, severity: this.i18n.instant(ToastTypes.info), summary: this.i18n.instant('cameras'),
            detail: this.i18n.instant('reopenChannels')});
        }, 0);
    }

    initData(): void {
        this.updateVideoWallLayoutBoxes();
        this.playersService.setPlayersRef(this.pComponents);

        // Only restore channels if data is already loaded
        if (this.channelsList && this.channelsList.length > 0) {
            setTimeout(() => {
                this.restoreChannelsAfterLayoutChange();
            }, 50);
        }
    }


    onVideoChannelSelect(event: CameraSelectionOptions): void {
        this.addChannelToPlayer(event);
    }

    updateVideoWallLayoutBoxes(): void {
        let containers = this.containers.toArray();
        console.log('updateComponents', containers);
        for (let i = 0; i < containers.length; i++) {
            this.addPlayer(i);
            this.pComponents.push(this.players[i].instance); 
        }
        this.setPlayersInContainers();
    }

    setPlayersInContainers(): void {
        let containers=this.containers.toArray();
        for (let i = 0; i < containers.length; i++) {
           let player= this.players[i].location.nativeElement
            containers[i].nativeElement.append(player); 
        }
    }

    addPlayer(index: number): void {
        let factory = this.componentFactoryResolver.resolveComponentFactory(
            PlayerComponent
        );
        let compRef = this.viewContainer.createComponent(factory);
        compRef.instance.showPlayerActions = false;
        compRef.instance.playerIndex = "v_" + index;
        compRef.instance.playerId = "v_" + index;
        compRef.instance.onContainerClick.subscribe(() => this.onContainerClick(compRef.instance.playerId));
        compRef.changeDetectorRef.detectChanges();
        this.players.push(compRef);
    }

    onContainerClick(playerId: string): void {
        this.selectedPlayerId = playerId;
        if (this.camerasModal) {
            this.camerasModal.openModal();
        }
    }

    onLayoutChanged(event: {label: string, value: number, layoutId: string}): void {
        this.playerCountNew = this.videoLayouts[event.layoutId.trim()];
        this.videoWallService.saveLayoutForMonitor({
            "MonitorId": this.videoMonitorIndex - 1,
            "VideoWallId": this.videoWallGuid,
            "LayoutId": event.layoutId
        }).subscribe(() => {
            this.messageService.add({
                severity: 'success',
                summary: this.i18n.instant(ToastTypes.success),
                detail: this.i18n.instant('layoutSelected') });

            // After saving the layout, restore the channels
            setTimeout(() => {
                this.restoreChannelsAfterLayoutChange();
            }, 100);
        });
        this.layoutOverlayPanel.hide();
    }


    closeAllPlayers(): void {
        this.pComponents.forEach(component => {component.closeChannel();});
    }

   
    openVCAoptions(event: MouseEvent): void {
        this.settingOverlayPanel.toggle(event);
    }

    onConfirm(): void {
        this.setVCAOptions();
        this.setPTZOptions();
        this.notify();
        this.settingOverlayPanel.hide();
    }

    notify(): void {
        this.resourceStoreService.tryGetUserResourceGroups([{
            vcaOptions: this.vcaOptions
        }]);
       
    }


    setVCAOptions(): void {
        this.vcaOptions = {
            showConfig: this.showVCAConfig,
            showObjects: this.showVCAObjects
        };
        this.settingsService.set("vcaOptions", this.vcaOptions);
    }

    setPTZOptions(): void {
        this.ptzOptions = {
            show: this.showPTZControls
        };
        this.settingsService.set("ptzOptions", this.ptzOptions);
    }

    
    viewLayout(event: any): void {
        this.playerCountNew = this.videoLayouts[event.trim()];

        // After layout change, we need to wait for the view to update
        // and then restore the channels
        setTimeout(() => {
            this.restoreChannelsAfterLayoutChange();
        }, 100);
    }

    restoreChannelsAfterLayoutChange(): void {
        // Only restore channels for the current monitor
        if (this.channelsList && this.channelsList.length > 0) {
            this.channelsList
                .filter(channel => channel.MonitorId === this.videoMonitorIndex - 1)
                .forEach(channel => {
                    this.openLastLayoutChannels(channel);
                });
        }
    }


    openLastLayoutChannels(channel: MonitorChannel): void {
        // Minimal validation
        if (!channel || !this.players) {
            return;
        }

        // Get the player directly - let it fail gracefully if index is wrong
        const player = this.players[channel.PlayerId];
        const channelId = channel.ChannelUri?.[3]?.id;
        const playerId = player?.instance?.playerId;

        // If we have all required data, open the channel
        if (player && channelId && playerId) {
            this.playerService.openChannel(channelId, playerId);
        }
    }


    addChannelToPlayer(cameraSelectionOptions: CameraSelectionOptions): void {
        this.players.map((player, index)=>{
            if(player.instance.playerId == cameraSelectionOptions.playerId){
                let data:SetMonitorChannel={
                    "ChannelId":cameraSelectionOptions.channelId,
                    "PlayerId":index,
                    "MonitorId": cameraSelectionOptions.monitorIndex-1
                }
                this.videoWallService.addChannelToMonitor(data, this.videoWallGuid).subscribe(result=>{
                    this.messageService.add({ severity: 'success', summary: this.i18n.instant(ToastTypes.success), detail: this.i18n.instant('playerAdded') });
                 
                    // Refactor   this.playerService.openChannel ca sa stie e din video wall sau nu.
                    this.playerService.openChannel(cameraSelectionOptions.channelId, cameraSelectionOptions.playerId);
                    // this.playerService.openChannelRefactor(cameraSelectionOptions);
                });
            }
        })
    }


    closePlayer(playerId): void {
        this.players.map((player,index)=>{
            if(player.instance.playerId == playerId){
                let data:CloseVideoWallChannel={
                    "MonitorId":this.videoMonitorIndex -1,
                    "PlayerId":index
                }
                this.videoWallService.closeAndRemoveChannel(data,this.videoWallGuid).subscribe(result=>{
                });
            }
        
        });
    }

    openLayouts(event: MouseEvent): void {
        this.layoutOverlayPanel.toggle(event);
    }

    toggleFullscreen(): void {
        this.fullScreen.toggleFullscreen();
    }

    ngOnDestroy(): void {
        this.playersService.clear();
        this.messageService.clear('vmsOpen');
        this.destroy$.next();
        this.destroy$.complete();
    }

}
