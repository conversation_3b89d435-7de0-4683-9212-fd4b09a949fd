import {
    Component,
    ComponentFactoryResolver,
    ComponentRef,
    ElementRef,
    HostListener,
    OnInit,
    QueryList,
    Renderer2,
    ViewChild,
    ViewChildren,
    ViewContainerRef
} from "@angular/core";
import { PlayerComponent } from "app/shared/components/player/player.component";

import { ActivatedRoute } from "@angular/router";
import { Store } from "@ngrx/store";
import { TranslateService } from "@ngx-translate/core";
import { AppModal } from 'app/shared/components/app-modal/app-modal.component';
import { CymsidebarService } from "app/shared/components/cym-sidebar/cym-sidebar.service";
import { Guid } from "app/shared/enum/guid";
import { ToastTypes } from 'app/shared/enum/toast-types';
import { CloseVideoWallChannel } from "app/shared/models/clseChannel.model";
import { SetMonitorChannel } from "app/shared/models/setMonitorChannel.model";
import { VideoWall } from "app/shared/models/videoWall.model";
import { MonitorChannel } from "app/shared/models/videoWallChannels.model";
import {
    NavigationService,
    Pages
} from "app/shared/services/navigation.service";
import { SettingsService } from "app/shared/services/settings.service";
import * as fromAppReducers from "app/store/app.reducers";
import { ConfirmationService, MessageService, SelectItemGroup } from "primeng/api";
import { ResourceStoreService } from "../../services/store_servcies/resource-store.service";
import { VideoWallLayouts } from "../../shared/models/videoWallLayouts.model";
import { FullScreenService } from "../../shared/services/fullscreen.service";
import { PlayersService } from "../../shared/services/players.service";
import { VideoWallService } from "../../shared/services/video-wall.service";
import { AppModalPosition } from "../../shared/components/app-modal/app-modal-position";
import { ChannelSelectionEvent } from "app/interfaces/channel-selection.interface";
import { FullscreenComponent } from '../../shared/components/fullscreen/fullscreen.component';
import { OverlayPanel } from 'primeng/overlaypanel';

@Component({
  selector: 'app-video-wall',
  templateUrl: './video-wall.component.html',
  entryComponents: [PlayerComponent],
  styleUrls: ['./video-wall.component.scss'],
  providers: [CymsidebarService, ConfirmationService]
})
export class VideoWallComponent implements OnInit {
  storeObservable;
    selectedCamera: string;
    selectedPlayerId: string;
    @ViewChildren("container") containers: QueryList<ElementRef>;
    groupedCameras: SelectItemGroup[];
    currentTheme: "dark" | "light" = "light";
    pComponents: PlayerComponent[] = [];
    players:ComponentRef<PlayerComponent>[] = [];
    playerCount:number = 0;
    playerCountNew:number = 1;
    isSidearOpen:boolean=true;


    monitorLayoutId="53bbd21b-0435-45b9-8925-f99385d11b8d";
    type = "vms";
    layouts = [
        { label: "icon-view-1", value: 1 },
        { label: "icon-view-4", value: 4 },
        { label: "icon-view-9", value: 9 },
        { label: "icon-view-16", value: 16 }
    ];
    @ViewChild("sideBar", {static: false}) sideBar;
    @ViewChild("timeline", {static: false}) timeline;
    @ViewChild("playersWrapper", {static: false}) playersWrapper;
    @ViewChild("timelineWrapper", {static: false}) timelineWrapper;
    @ViewChild('camerasModal') camerasModal: AppModal;
    @ViewChild('fullScreen') fullScreen: FullscreenComponent;
    @ViewChild('settingOverlayPanel', {static: false}) settingOverlayPanel: OverlayPanel;
    @ViewChild('layoutOverlayPanel', {static: false}) layoutOverlayPanel: OverlayPanel;
    isTimelineOpen: boolean = false;
    playersJson = {};
    public opened: boolean = false;
    public barsEnabled: boolean = true;
    public dockedSize: string = "0px";
    public isSidebarDocked: boolean = true;
    public isMainSidebarOpen: boolean = true;
    public isChecked: boolean = false;
    public videoWallGuid:string;
    vcaOptions = { showConfig: true, showObjects: true };
    showVCAConfig = true;
    showVCAObjects = true;
    ptzOptions = { show: true };
    showPTZControls = false;
    videoWalllayouts:any[]=[];
    videoLayouts:any[]=[];
    monitorCount:number=0;
    queryParam:number;
    counter:number=0;
    position: AppModalPosition = AppModalPosition.Center;

    constructor(
        private playersService: PlayersService,
        private viewContainer: ViewContainerRef,
        private componentFactoryResolver: ComponentFactoryResolver,
        private settingsService: SettingsService,
        private navigationService: NavigationService,
        private fullScreenService: FullScreenService,
        private cymsidebarService: CymsidebarService,
        private i18n: TranslateService,
        private store: Store<fromAppReducers.AppState>,
        private messageService: MessageService,
        private renderer: Renderer2,
        private videoWallService: VideoWallService,
        private playerService: PlayersService,
        private route: ActivatedRoute,
        private resourceStoreService:ResourceStoreService
    ) {
        this.videoWallService.closePlayerSubject.subscribe((playerId) => {
            this.closePlayer(playerId);
        })
        this.videoWallService.selectedChannelId.subscribe(data=>{
            this.addChannelToPlayer(data);
        });
        this.cymsidebarService.showSideBarSubject.subscribe(response=>{
            this.isSidearOpen=response;
        })
        this.route.queryParams.subscribe(params => {
           this.queryParam=params['videoMonitorId'];
        });
     }

    ngOnInit(): void {
        // Video wall layouts - maximum 16 players supported
        this.videoLayouts['53bbd21b-0435-45b9-8925-f99385d11b8d']=1;   // 1 player
        this.videoLayouts['786c4aa4-a33b-b321-2ff3-bcdc4123aaa4']=6;   // 6 players
        this.videoLayouts['14aa92f3-cd78-75b3-b7b4-df54aaff456a']=11;  // 11 players (changed from 111)
        this.videoLayouts['9897c01b-de66-456d-b234-0cb941245e70']=8;   // 8 players (changed from 100)
        this.videoLayouts['0f32361b-1a14-4103-9551-f0afb8377b03']=4;   // 4 players
        this.videoLayouts['1ef2a814-1d4e-43e2-9392-5ccdbdcbbcf4']=10;  // 10 players
        this.videoLayouts['741e579f-d77a-4234-9f5a-5219f9f6f33b']=9;   // 9 players
        this.videoLayouts['594c6f4d-be3f-4377-a7a9-d4804ad34dcb']=16;  // 16 players
        this.videoLayouts['16d05cb1-9b85-04a0-9ab1-b98d774a8e92']=12;  // 12 players
        // Removed layouts with more than 16 players (21, 20, etc.)
        this.videoWallService.getLayouts().subscribe((response:VideoWallLayouts[])=>{
           
            this.videoWalllayouts=response;
        });
        this.videoWallService.getVideoWalls().subscribe(response=>{
            this.videoWallGuid=response[0];

            this.videoWallService.getLayout(this.videoWallGuid,this.queryParam-1).subscribe((response:Guid)=>{
                this.monitorLayoutId =response.toString();
               
               
                this.videoWalllayouts.map(item=>{
                    if(item[1].id == response){
    
                        this.playerCountNew=this.videoLayouts[item[1].id];
                      
                    }
                })
            });

            this.videoWallService.getVideoWallChannels(this.videoWallGuid).subscribe((channels:MonitorChannel[])=>{
                   channels.map((channel)=>{
                    if(channel.MonitorId == this.queryParam -1 ){
                        this.openLastLayoutChannels(channel)
                    }
                    
                   })
            });
            this.videoWallService.getVideoWallDetails(response[0]).subscribe((response:VideoWall)=>{
                this.monitorCount=response.MonitorCount;
            })
        })
        let selected = this.navigationService.getSelectedOrDefault(this.type);
        if (selected[this.type]) {
            this.playerCountNew = selected[this.type];
        }

        this.isTimelineOpen = this.settingsService.get("isTimelineOpen");
        if (!this.isTimelineOpen) {
            this.isTimelineOpen = false;
        }

        this.playersService.ChannelOpend.subscribe(res => {
            this.playersJson[res.playerId] = res.playerId;
            this.controlTimeline();
        });

        this.playersService.ChannelClosed.subscribe(res => {
            delete this.playersJson[res.playerId];
            this.controlTimeline();
        });

        this.fullScreenService.fullScreenCall$.subscribe(isFullScreen => {
            if (isFullScreen) {
                this.isTimelineOpen = false;
            } else {
                this.isTimelineOpen = this.settingsService.get(
                    "isTimelineOpen"
                );
            }
        });

        let vcaOptions = this.settingsService.get("vcaOptions");
        if (vcaOptions) {
            this.showVCAConfig = vcaOptions.showConfig;
            this.showVCAObjects = vcaOptions.showObjects;
        }

        let ptzOptions = this.settingsService.get("ptzOptions");
        if (ptzOptions) {
            this.showPTZControls = ptzOptions.show;
        }

        setTimeout(() => {
            this.notify();
            this.messageService.add({key: 'vmsOpen', life: 5000, severity: this.i18n.instant(ToastTypes.info), summary: this.i18n.instant('cameras'),
            detail: this.i18n.instant('reopenChannels')});//TODO reopenChannels is missing as a translation key from some translation files
        }, 0);

    }



    @HostListener('document:keypress', ['$event'])
    handleKeyboardEvent(event: KeyboardEvent) { 
        this.isSidearOpen=!this.isSidearOpen;
    }
    openTimeline(): void {
        this.isTimelineOpen = !this.isTimelineOpen;
        this.settingsService.set("isTimelineOpen", this.isTimelineOpen);
    }

   
    controlTimeline(): void {
        if (
            this.isTimelineOpen &&
            this.timeline &&
            Object.keys(this.playersJson).length === 0
        ) {
            this.timeline._timeline.destroy();
            this.timeline.initTimeline();
        }
    }
    updateComponents(): void {
        if (this.playerCountNew === this.playerCount) { return; }

        this.clearAllPlayers();

        let containers = this.containers.toArray();

        for (let i = 0; i < containers.length; i++) {
            this.addPlayer(i);
            this.pComponents.push(this.players[i].instance);
        }
        this.setPlayersInContainers();
        this.playerCount = this.playerCountNew;
    }

    setPlayersInContainers(): void {
        let containers=this.containers
        .toArray();
        for (let i = 0; i < containers.length; i++) {
           let player= this.players[i].location.nativeElement
            containers[i].nativeElement.append(player); 
            
        }
    }

    addPlayer(index: number): void {
        let factory = this.componentFactoryResolver.resolveComponentFactory(
            PlayerComponent
        );
        let compRef = this.viewContainer.createComponent(factory);

        const playerInstance = compRef.instance as PlayerComponent;
        playerInstance.showPlayerActions = false;
        playerInstance.playerIndex = "v_" + index;
        playerInstance.playerId = "v_" + index;
        playerInstance.onContainerClick.subscribe(() => this.onContainerClick(playerInstance.playerId));

        this.players.push(compRef);
        compRef.changeDetectorRef.detectChanges();
    }

    onContainerClick(playerId: string): void {
        this.selectedPlayerId = playerId;
        if (this.camerasModal) {
            this.camerasModal.openModal();
        }
    }

    onChannelSelect(event: ChannelSelectionEvent): void {
        if (event.channelId) {
            this.playerService.openChannel(event.channelId, this.selectedPlayerId);
            if (this.camerasModal) {
                this.camerasModal.closeModal();
            }
        }
    }

    public ngOnDestroy(): void {
        this.playersService.clear();
        this.messageService.clear('vmsOpen');
    }

    ngAfterViewInit(): void {
  
        if (!this.containers.dirty) {
            this.initData();
        }
        //update on change
        this.containers.changes.subscribe(() => {
            this.initData();
        });
    }

    reopenCameras(): void {
        this.playersService.loadPlayers();
        this.messageService.clear('vmsOpen');
    }

    onClose(): void {
        this.messageService.clear('vmsOpen');
    }

    initData(): void {
        this.updateComponents();
        this.playersService.setPlayersRef(this.pComponents);
    }

    onLayoutChanged(event: {label: string, value: number}): void {
        this.playerCountNew = event.value;
        this.layoutOverlayPanel.hide();
    }

    openSideBar(): void {
        this.cymsidebarService.toggleDockSidebar("push");
        this.cymsidebarService.openSidebar();
        this.isSidearOpen=true;
    }

    closeAllPlayers(): void {
        this.pComponents.forEach(component => {component.closeChannel();});
    }

    clearAllPlayers(): void {
        this.players.forEach(player => {
            if (player) {
                player.destroy();
            }
        });

        this.players = [];
        this.pComponents = [];
        this.playersJson = {};
    }

    isCloseAllDisabled(): boolean {
        return Object.keys(this.playersJson).length === 0;
    }

    public show(): void {
        this.settingOverlayPanel.hide();
    }

    openVCAoptions(event: MouseEvent): void {
        this.settingOverlayPanel.toggle(event);
    }

    onConfirm(): void {
        this.setVCAOptions();
        this.setPTZOptions();
        this.notify();
        this.settingOverlayPanel.hide();
    }

    notify(): void {
        this.resourceStoreService.tryGetUserResourceGroups([{
            vcaOptions: this.vcaOptions
        }]);
       
    }


    setVCAOptions(): void {
        this.vcaOptions = {
            showConfig: this.showVCAConfig,
            showObjects: this.showVCAObjects
        };
        this.settingsService.set("vcaOptions", this.vcaOptions);
    }

    setPTZOptions(): void {
        this.ptzOptions = {
            show: this.showPTZControls
        };
        this.settingsService.set("ptzOptions", this.ptzOptions);
    }

    
    setTheme(): void {
        this.renderer.addClass(document.body, "theme-" + this.currentTheme);
    }
       
    getMonitorIndex(index: number): void {

        this.videoWallService.getLayout(this.videoWallGuid,index).subscribe((response:Guid)=>{
            this.monitorLayoutId =response.toString();
           
           
            this.videoWalllayouts.map(item=>{
                if(item[1].id == response){

                    this.playerCountNew=this.videoLayouts[item[1].id];
                }
            })
            this.clearAllPlayers();
            this.videoWallService.getVideoWallChannels(this.videoWallGuid).subscribe((channels:MonitorChannel[])=>{
               
                channels.map(channel=>{
                    if(channel.MonitorId == this.queryParam -1 ){
                        this.openLastLayoutChannels(channel)
                    }
                    
                })
               
            })
        });
    }

    viewLayout(event){
        this.playerCountNew=this.videoLayouts[event.trim()];
    }


    openLastLayoutChannels(channel){
        let containers=this.containers.toArray();
        if(channel.PlayerId <= containers.length){
                let playerId=this.players[channel.PlayerId].instance.playerId;
                let channelId=channel.ChannelUri[3].id;
                 this.playerService.openChannel(channelId, playerId);
            }
       
    }


    addChannelToPlayer(channelId){
        this.players.map((player, index)=>{
            if(player.instance.playerId == channelId.playerId){
                let data:SetMonitorChannel={
                    "ChannelId":channelId.channelId,
                    "PlayerId":index,
                    "MonitorId":channelId.monitorIndex-1
                }
                this.videoWallService.addChannelToMonitor(data,this.videoWallGuid).subscribe(result=>{
                    this.messageService.add({ severity: 'success', summary: this.i18n.instant(ToastTypes.success), detail: this.i18n.instant('playerAdded') });
                });
          
            }
        })
    }


    closePlayer(playerId){
        this.players.map((player,index)=>{
            if(player.instance.playerId == playerId){
                let data:CloseVideoWallChannel={
                    "MonitorId":this.queryParam -1,
                    "PlayerId":index
                }
                this.videoWallService.closeAndRemoveChannel(data,this.videoWallGuid).subscribe(result=>{
                });
            }
        
        });
    }

    openCameraSelection():void {
        if (this.camerasModal) {
            this.camerasModal.openModal();
        }
    }

    openLayouts(event: MouseEvent): void {
        this.layoutOverlayPanel.toggle(event);
        // if (this.layoutsModal) {
        //     this.layoutsModal.openModal();
        // }
    }

    toggleFullscreen(): void {
        this.fullScreen.toggleFullscreen();
    }
}
