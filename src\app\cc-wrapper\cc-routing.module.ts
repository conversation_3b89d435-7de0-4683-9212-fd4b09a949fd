import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AppMapComponent } from 'app/layout/app-map/app-map.component';
import { CustomerDashboardComponent } from 'app/layout/customer-dashboard/customer-dashboard.component';
import { CCComponent } from './cc.component';

let routes: Routes = [
    { path: '', component: CCComponent },
    { path: 'map-new', component: AppMapComponent },
    { path: 'map', component: AppMapComponent },
    { path: 'dashboard', component: CustomerDashboardComponent }
];
@NgModule({
    imports: [RouterModule.forRoot(routes)],
    exports: [RouterModule],
    providers: []
})
export class CCRoutingModule { }
