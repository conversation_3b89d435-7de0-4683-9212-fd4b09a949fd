<div class="inventory-add-sidebar" *ngIf="resource && resourceForm">
  <form role="form" class="form-format" [formGroup]="resourceForm">

    <div class="row">
      <div class="col col-md-12">
        <h2 class="h2-format">{{ 'inventory.editDevice' | translate }} </h2>     
      </div>
    </div>

     <div [formGroup]="location">
      <div class="row" *ngFor="let item of location.controls | keyvalue">
        <div class="col col-md-12 form-group">
          <label>{{ item.key | translate }}</label>
          <input type="text" formControlName="{{item.key}}">
        </div>
      </div>
    </div>

    <div [formGroup]="props">
      <div class="row" *ngFor="let item of props.controls | keyvalue">
        <div class="col col-md-12 form-group">  
          <label>{{ item.key | translate }}</label>
          <input type="text" placeholder="{{item.key}}" formControlName="{{item.key}}">
        </div>
      </div>
    </div> 

    <div class="row">
      <div class="col col-md-12">
        <div class="btn-toolbar pull-right">
          <button type="button" class="btn btn-secondary"
            (click)="cancel()">{{ 'cancel' | translate }}</button>
          <button type="button" class="btn btn-primary btn-save" (click)="updateResource()">
            <i *ngIf="loading" class="fa fa-spinner fa-spin"></i>
            {{ 'add' | translate }}</button>
        </div>
      </div>
    </div>
  </form>
</div>