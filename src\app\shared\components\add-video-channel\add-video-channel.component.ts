import { CameraSelectionOptions } from './../../models/camera-selection.model';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { VideoDeviceGroup } from 'app/shared/models/video-device-group';
import { ServerTypes } from 'app/shared/modules/data-layer/enum/resource/resource-server-types.enum';
import { ResourceState } from 'app/shared/modules/data-layer/enum/resource/resource-state.enum';
import { DataChangeType } from 'app/shared/modules/data-layer/models/data-change-type.enum';
import { Resource } from 'app/shared/modules/data-layer/models/resource';
import { ResourceGroup } from 'app/shared/modules/data-layer/models/resource-group';
import { PlayersService } from 'app/shared/services/players.service';
import * as _ from 'lodash';
import { EMPTY, Subject, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';

import { ActivatedRoute } from '@angular/router';
import { ResourceService } from "app/services/resource/resource.service";
import { Guid } from 'app/shared/enum/guid';
import { URI } from 'app/shared/enum/uri';
import { DataEntry } from 'app/shared/modules/data-layer/models/dataentry.model';
import { Entry } from 'app/shared/modules/data-layer/models/entry.model';
import { ChannelTourService } from 'app/shared/modules/data-layer/services/channel-tour/channel-tour.service';
import { ResourceCacheService } from "app/shared/modules/data-layer/services/resource/resource.cache.service";
import { VideoWallService } from 'app/shared/services/video-wall.service';
import { PlayerComponent } from '../player/player.component';
import { CameraCountService } from 'app/shared/services/camera-count.service';
import { MessageService } from 'primeng/api';
import { ToastTypes } from 'app/shared/enum/toast-types';
import { TranslateService } from '@ngx-translate/core';


@Component({
  selector: 'app-add-video-channel',
  templateUrl: './add-video-channel.component.html',
  styleUrls: ['./add-video-channel.component.scss']
})
export class AddVideoChannelComponent implements OnInit, OnDestroy {
  @Input('selectedWidgetId') selectedWidgetId: string;
  @Input('playerId') playerId: string;



  @Output() videoChannelSelect: EventEmitter<CameraSelectionOptions> = new EventEmitter();

  modelStore: Map<string, Resource> = new Map<string, Resource>();
  groupedCameras: VideoDeviceGroup[] = [];
  filteredCameras:  Map<string, Resource> = new  Map<string, Resource>();

  searchText: string = "";
  searchTerm$ = new Subject<string>();

  channelTourArray:DataEntry[]=[];

  showBooleanCameras: {show: boolean, displayText: string}[] = [{show: true, displayText: "online"}, {show: false, displayText: "offline"}];

  showResourceGroups:boolean = false;
  showChannelTours:boolean = false;
  channelTours:URI[]=[];
  videoMonitorIndex:number;
  isChannelTour?: boolean;
  channelId?: Guid;
  monitorIndex;
  
  channelTourMap:Map<string,Entry[]>=new Map<string,Entry[]>();

  playerIdOn:Map<string,boolean>=new Map<string,boolean>();

  subscriptions: Subscription[] = [];

  constructor(
    private playerService: PlayersService,
    private resourceService: ResourceService,
    private videoWallService: VideoWallService,
    private route: ActivatedRoute,
    private channelTourService: ChannelTourService,
    private resourceCacheService: ResourceCacheService,
    private messageService: MessageService, 
    private translateService: TranslateService,
    private cameraCountService: CameraCountService){

    this.route.queryParams.subscribe(params => {
      this.videoMonitorIndex = params['videoMonitorId'];
      this.isChannelTour = params['isChannelTour'] === undefined || params['isChannelTour'].toLowerCase() === 'false' ? false : true;
      this.channelId = params['channel'];
    });

    this.playerService.playersInitialized.subscribe(playerIds => {
      this.playerId = playerIds[0].toString();

      if (this.channelId && this.isChannelTour) {
           var currentChannelTourKey = this.channelTourMap.keys().next().value;

          if (currentChannelTourKey != this.channelId.toString())
          {
            this.onChannelTourSelect(this.channelId.toString());
          }
      }
    });

    this.playerService.initialChannelTour.subscribe(res=>{
      let item:[{ChannelId:string,Duration:number}]=JSON.parse(res.channelTour);

      this.loadInitialChannelTour(item,res.player);
    })
    
    this.playerService.closeChannelTour.subscribe((res:PlayerComponent) => {
      this.makeVideoLoop(null,null,res);
    })
    
    let searchTermSubscription = this.searchTerm$.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      switchMap(() => {
        return EMPTY;
      })
    ).subscribe();
    this.subscriptions.push(searchTermSubscription);

    this.resourceService.resourceChanged.subscribe((resource: any) => {
      this.notifyOperation(resource);
  })
}

  ngOnInit(): void {
    this.getAllData();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(element => {return element.unsubscribe();});
    this.playerIdOn.clear();
  }

  private convertResources(resources: Resource[]): Map<string, Resource> {
    let result = new Map<string, Resource>();
    resources.forEach(resource => result.set(resource.identity, resource));
    return result;
  }

  getAllData(): void {
    let resourcesSubscription = this.resourceCacheService.storageCompleted.subscribe(() => {
      const res: Resource[] = this.resourceCacheService.getAll();
      this.modelStore = this.convertResources(res);

      res.forEach((resource: Resource) => {
        if (resource.resourceType === ServerTypes.Core_RES_ChannelTour) {
          let resourceUri = new URI([{ id: resource.identity, name: resource.name, type: resource.resourceType }]);
          let channelTourUri = resourceUri;
          this.channelTours.push(channelTourUri);
        }
      });
    });
    this.subscriptions.push(resourcesSubscription);


    let statusesSubscription = this.resourceService.getAllStatuses().subscribe(statuses => {
      statuses.forEach(status => {
        let idAsString = status.Id.toString();
        let resource = this.modelStore.get(idAsString);
        if (!resource) {
          return;
        }
        let changed: boolean = (resource.status !== status.State);
        if (!changed) {
          return;
        }
        resource.status = status.State;

        this.resourceService.update(resource);

      });
      let filteredCameras = this.filterCameras(this.modelStore);
      this.filteredCameras = _.cloneDeep(filteredCameras);
      this.groupedCameras = this.buildVideoDeviceGroup(this.filteredCameras);
      this.updateCameraCount();
    });
    this.subscriptions.push(statusesSubscription);
  }

  toggleFilterButtons(index: number):void {
    this.showBooleanCameras.forEach((item, i) => {
      item.show = index === i;
    });

    this.filteredCameras = this.filterCameras(this.modelStore);
    this.groupedCameras = this.buildVideoDeviceGroup(this.filteredCameras);
  }

  filterCameras(cameras:Map<string, Resource>):Map<string, Resource> {
    let filteredCameras: Map<string, Resource> = new Map<string, Resource>();

    cameras.forEach(resource => {
      if (resource.resourceType === ServerTypes.Core_RES_InputChannel) {
        const isOnline = resource.status === ResourceState.online;
        const showOnline = this.showBooleanCameras[0].show && isOnline;
        const showOffline = this.showBooleanCameras[1].show && !isOnline;

        if (showOnline || showOffline) {
          filteredCameras.set(resource.identity, resource);
        }
      }
    });

    return filteredCameras;
  }

  onCameraSelect(channel: Resource): void {  
    if (channel.status !== 'online') {
        return;
    }

    if(this.playerId === Guid.EMPTY) {
      this.messageService.add({severity: 'info', summary: this.translateService.instant(ToastTypes.info), detail: this.translateService.instant('selectPlayerContainer')});
      return;
    }

    const cameraSelectionOptions: CameraSelectionOptions = {
      playerId: this.playerId,
      channelId: channel.identity,
      isVideoWall: this.videoMonitorIndex ? true : false,
      isChannelTour: this.isChannelTour,
      monitorIndex: this.videoMonitorIndex
    };

    if(this.selectedWidgetId){
      cameraSelectionOptions.widgetId = this.selectedWidgetId;
    }
    
    this.videoChannelSelect.next(cameraSelectionOptions);
  }


  buildVideoDeviceGroup(resources: Map<string, Resource>): VideoDeviceGroup[]{
    let array: VideoDeviceGroup[] = [];
    for(let key in resources){
      let resource = resources.get(key);
      resource.groups.forEach((group:ResourceGroup) => {
        let index = array.findIndex(el => { return el.identity === group.identity; });
        if(index === -1){
          let videoDeviceGroup = new VideoDeviceGroup(group);
          videoDeviceGroup.videoDevices = {};
          videoDeviceGroup.videoDevices[resource.identity] = resource;
          array.push(videoDeviceGroup);
        }
        else {
          array[index].videoDevices[resource.identity] = resource;
        }
      });
    }
    return array;
  }

  showGroups(): void {
    this.showResourceGroups = !this.showResourceGroups;
  }

  public toggleChannelTours(): void  {
    this.showChannelTours = !this.showChannelTours;
  }


  private updateOrCreateCameras(resources: Resource[]): void{
    resources.forEach(resource => {
      //TODO
      //This check should be deleted when api filtering is implemented
      if(resource.resourceType !== ServerTypes.Core_RES_InputChannel){
        return;
      }
      this.modelStore.set(resource.identity, resource);
      let filteredCameras = this.filterCameras(this.modelStore);
      this.filteredCameras = _.cloneDeep(filteredCameras);
      this.groupedCameras = this.buildVideoDeviceGroup(this.filteredCameras);
    });
  }

  private deleteCameras(resources: Resource[]): void{
    resources.forEach(resource => {
      //TODO
      //This check should be deleted when api filtering is implemented
      if(resource.resourceType !== ServerTypes.Core_RES_InputChannel){
        return;
      }
      this.modelStore.delete(resource.identity);
      let filteredCameras = this.filterCameras(this.modelStore);
      this.filteredCameras = _.cloneDeep(filteredCameras);
      this.groupedCameras = this.buildVideoDeviceGroup(this.filteredCameras);
    });
  }


  onChannelTourSelect(channelTour:string){
    this.channelTourService.getChannelTour(channelTour).subscribe(res=>{
      this.channelTourMap.set(res.Identity,res.Entries);
      this.makeVideoLoop(res.Entries,this.playerId);
      this.playerIdOn.set(this.playerId,true);
    });
  }

  makeVideoLoop(entries?,playerId?,playerMap?:PlayerComponent){
    if(playerMap != null){
      this.playerIdOn.set(playerMap.playerId,false);
    }
    if(playerMap){
      let activePlayer=this.playerIdOn.get(playerMap.playerId);
      if(!activePlayer){
        return;
      }
    }

    if(playerId){
      if (this.playerIdOn.size === 0){
        this.channelTourArray.splice(0);
        return;
      }

      let activePlayer=this.playerIdOn.get(playerId);
      if(!activePlayer){
        let player=this.playerService.findPlayer(playerId);
        this.playerService.ChannelClosed.next({ playerId: playerId, channelId: player.channelId });
        return;
      }
    }

    let entryTime=0;
    entries.map((entry,index) => {

        entryTime+=entry.Duration * 1000;
        let dataEntry:DataEntry={
          "ChannelId":entry.ChannelURI[entry.ChannelURI.length-1].id,
          "Duration":entryTime,
        }
        this.channelTourArray[index] = dataEntry;

        if(index == 0){
          this.playerService.openChannel(entry.ChannelURI[entry.ChannelURI.length-1].id, playerId,true);
        }

        if(index > 0){
          setTimeout(() =>{
            if (this.playerIdOn.size === 0){
              this.channelTourArray.splice(0);
              return;
            }

            let activePlayer=this.playerIdOn.get(playerId);
            if(!activePlayer){
              let player=this.playerService.findPlayer(playerId);
              this.playerService.ChannelClosed.next({ playerId: playerId, channelId: player.channelId });
              return;
            }
            this.playerService.openChannel(entry.ChannelURI[entry.ChannelURI.length-1].id, playerId,true);
            if(index === entries.length-1){
              setTimeout(() =>{
                this.makeVideoLoop(entries,playerId);
              },entryTime);

            }
          },entryTime);
        }
    })
      let player=this.playerService.findPlayer(playerId);
      if (player){
        this.playerService.setChannelClosed(player,true);
        let channelTourString = JSON.stringify(this.channelTourArray);
        this.playerService.setChannelTour(playerId, channelTourString);
      }
  }

  loadInitialChannelTour(data:[{ChannelId:string,Duration:number}],playerId:string,playerMap?:PlayerComponent):void{
    if(playerMap != null){
      this.playerIdOn.set(playerMap.playerId,false);

    }
    if(playerMap){
      let activePlayer=this.playerIdOn.get(playerMap.playerId);
      if(!activePlayer){
        return;
      }
    }

    if(playerId){
      let activePlayer=this.playerIdOn.get(playerId);
      if(!activePlayer){
        let player=this.playerService.findPlayer(playerId);
        this.playerService.ChannelClosed.next({ playerId: playerId, channelId: player.channelId });
        return;
      }
    }

    let entryTime=0;
    data.map((entry,index) => {

        entryTime+=entry.Duration * 1000;
        let dataEntry:DataEntry={
          "ChannelId":entry.ChannelId,
          "Duration":entry.Duration,
        }
        this.channelTourArray[index] = dataEntry;

        if(index == 0){
          this.playerService.openChannel(entry.ChannelId, playerId,true);
        }

        if(index > 0){
          setTimeout(() =>{
            let activePlayer=this.playerIdOn.get(playerId);
            if(!activePlayer){
              let player=this.playerService.findPlayer(playerId);
              this.playerService.ChannelClosed.next({ playerId: playerId, channelId: player.channelId });
              return;
            }
            this.playerService.openChannel(entry.ChannelId, playerId,true);
            if(index === data.length-1){
              setTimeout(() =>{
                this.loadInitialChannelTour(data,playerId);
              },entryTime);

            }
          },entryTime);
        }
    })
      let player=this.playerService.findPlayer(playerId);
      this.playerService.setChannelClosed(player,true);
      let channelTourString=JSON.stringify(this.channelTourArray);
      this.playerService.setChannelTour(playerId, channelTourString);
  }

  //TODO: make resource strongly typed
  //TODO this needs to be called from signalr handler
  async notifyOperation(resource: any): Promise<void>{

    let resourcesWithGroups = this.filterResourcesWithGroups(resource.models);
    let resourcesWithoutGroups = this.filterResourcesWithoutGroups(resource.models);

    switch(resource.type) {
        case DataChangeType.Update:
          if (resourcesWithoutGroups.length > 0){
            this.updateCameras(resourcesWithoutGroups);
          }

          if (resourcesWithGroups.length > 0){
            this.updateOrCreateCameras(resourcesWithGroups);
          }
          break;
        case DataChangeType.Create:
          if (resourcesWithGroups.length > 0) {
            this.updateOrCreateCameras(resourcesWithGroups);
          }
          break;
        case DataChangeType.Delete:
          this.deleteCameras(resource.models);
          break;
        default:
          console.error("operation type is not supported: ", resource.type);
          break;
     }
  }

  private updateCameras(resources: Resource[]): void {

    resources.forEach((resource: Resource) => {
      let cameraToUpdate = this.modelStore.get(resource.identity);

      if (cameraToUpdate)
      {
        cameraToUpdate.status = resource.status;
        let filteredCameras = this.filterCameras(this.modelStore);
        this.filteredCameras = _.cloneDeep(filteredCameras);
        this.updateCameraCount();
      }
    })
  }

  private filterResourcesWithGroups(resources: Resource[]): Resource[] {

    let resourcesWithGroups: Resource[] = [];

    resources.forEach((resource: Resource) => {
      if (resource.groups) {
        resourcesWithGroups.push(resource);
      }
    });

    return resourcesWithGroups;
  }

  private filterResourcesWithoutGroups(resources: Resource[]): Resource[] {

    let resourcesWithoutGroups: Resource[] = [];

    resources.forEach((resource: Resource) => {
      if (resource.identity && !resource.groups) {
        if (!resource.status)
        {
          resource.status = ResourceState.unknown;
        }

        resourcesWithoutGroups.push(resource);
      }
    });

    return resourcesWithoutGroups;
  }

  private updateCameraCount(): void {
    // Count cameras from the full modelStore
    const cameras = Array.from(this.modelStore.values())
      .filter(resource => resource.resourceType === ServerTypes.Core_RES_InputChannel);
    
    this.cameraCountService.updateCameraCountsFromResources(cameras);
  }
}
