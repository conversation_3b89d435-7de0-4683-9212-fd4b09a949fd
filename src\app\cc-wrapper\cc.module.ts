import { HTTP_INTERCEPTORS, HttpClient, HttpClientModule } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { StoreDevtoolsModule } from '@ngrx/store-devtools';
import { MissingTranslationHandler, TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { AppMapModule } from 'app/layout/app-map/app-map.module';
import { CustomerDashboardModule } from 'app/layout/customer-dashboard/customer-dashboard.module';
import { LogMissingTranslationHandler } from 'app/shared/helpers/missing-translation';
import { CoreModule } from 'app/shared/modules/core.module';
import { environment } from 'environments/environment';
import { SignalRConfiguration, SignalRModule } from 'ng2-signalr';
import { MessageService } from 'primeng/api';
import { AppSecurityInterceptor } from '../app-security.interceptor';
import { ReportsEffects } from '../layout/reports/store/reports.effects';
import { AuthEffects } from '../login/store/auth.effects';
import { HeaderEffects } from '../shared/components/header/store/header.effects';
import { DataLayerModule } from '../shared/modules/data-layer.module';
import { SharedModule } from '../shared/modules/shared.module';
import { reducers } from '../store/app.reducers';
import { ResourcesEffects } from '../store/resources.effects';
import { CCRoutingModule } from './cc-routing.module';
import { CCComponent } from './cc.component';

export function HttpLoaderFactory(httpClient: HttpClient) {
  return new TranslateHttpLoader(httpClient, '/assets/i18n/', '.json');
}
export function createConfig(): SignalRConfiguration {
  const config = new SignalRConfiguration();
  config.hubName = 'AppHub';
  config.url = environment.apiUrl;
  config.jsonp = !environment.production;
  return config;
}
@NgModule({
   imports: [
    BrowserModule,
    BrowserAnimationsModule,
    HttpClientModule,
    CCRoutingModule,
    SharedModule,
    CoreModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient]
      },
      missingTranslationHandler: { provide: MissingTranslationHandler, useClass: LogMissingTranslationHandler },
    }),
    StoreModule.forRoot(reducers),
    EffectsModule.forRoot([AuthEffects, HeaderEffects, ReportsEffects, ResourcesEffects]),
    !environment.production ? StoreDevtoolsModule.instrument() : [],
    SignalRModule.forRoot(createConfig),
    DataLayerModule,
    AppMapModule,
    CustomerDashboardModule    
  ],
  declarations: [
    CCComponent
  ],
  bootstrap: [CCComponent],
  providers: [
    MessageService,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AppSecurityInterceptor,
      multi: true
  }]

})
export class CCModule {
}
