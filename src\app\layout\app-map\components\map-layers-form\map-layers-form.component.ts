import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { FormGroup, FormControl, FormBuilder, Validators } from '@angular/forms';
import { MapUtilsService } from '../../services/map-utils.service';
import { MapForm } from '../../enums/map-form.enum';
import { Subscription, combineLatest } from 'rxjs';
import { startWith } from 'rxjs/operators';
import { LayerFormFilters } from '../../models/layer-form-filters.interface';
import { SelectItem } from 'primeng/api';
import { Resource } from 'app/shared/modules/data-layer/models/resource';
import { MapLayer } from 'app/shared/modules/data-layer/models/map-layer';
import { ServerTypes } from 'app/shared/modules/data-layer/enum/resource/resource-server-types.enum';

@Component({
  selector: 'app-map-layers-form',
  templateUrl: './map-layers-form.component.html',
  styleUrls: ['./map-layers-form.component.scss']
})
export class MapLayersFormComponent implements OnInit {
  data: { resources: Resource[], layer: MapLayer, isNew: boolean }
  public layerForm: FormGroup = null;
  private subscriptions: Subscription[] = [];

  public formFilters: LayerFormFilters = {
    groupList: [],
    resourceList: [],
    resourceTypeList: [],
    resourceStateList: []
  }

  @Output('onFormAction') onFormAction: EventEmitter<any> = new EventEmitter();

  constructor(
    private formBuilder: FormBuilder,
    private mapUtilsService: MapUtilsService
  ) { }

  ngOnInit() {
    this.generateForm(this.data.layer);
    this.formFilters = this.returnFormFilters(this.data.resources);
  }

  public generateForm(data?: MapLayer):void {

    this.layerForm = this.formBuilder.group({
      name: new FormControl(data ? data.name : '', Validators.required),
      selectedResourceGroups: new FormControl(data ? data.selectedResourceGroups : []),
      selectedResources: new FormControl({value: data ? data.selectedResources : [], disabled: !data || data.selectedResourceGroups.length === 0 ? true : false}),
      selectedStates: new FormControl(data ? data.selectedStates : []),
      selectedTypes: new FormControl(data ? data.selectedTypes : []),

    });

    this.mapUtilsService.setMapValidatorChange({[MapForm.LAYER]: this.layerForm.status === 'VALID' ? true : false});

    let formStatusChangedSubscription = this.layerForm.statusChanges.subscribe(status => {
      this.mapUtilsService.setMapValidatorChange({[MapForm.LAYER]: status === 'VALID' ? true : false});
    })
    this.subscriptions.push(formStatusChangedSubscription);

    let formDataChangedSubscription = combineLatest([
      this.layerForm.controls['selectedResourceGroups'].valueChanges.pipe(startWith(data ? data.selectedResourceGroups : [])),
      this.layerForm.controls['selectedStates'].valueChanges.pipe(startWith(data ? data.selectedStates : [])),
      this.layerForm.controls['selectedTypes'].valueChanges.pipe(startWith(data ? data.selectedTypes : []))
    ]).subscribe(res => {
      this.formFilters.resourceList = this.returnFilteredResourceList(res);
      const action: string = res[0].length === 0 ? 'disable' : 'enable';
      this.formActionControl('selectedResources', action);
    })
    this.subscriptions.push(formDataChangedSubscription);
  }

  private returnFormFilters(resources: Resource[]): LayerFormFilters {

    let filters: LayerFormFilters = {
      resourceList: [],
      groupList: [],
      resourceStateList: [],
      resourceTypeList:[]
    };

    resources.forEach(resource => {
      /**
       * Question for merge request code review.
       * Should i filter the devices/resources wich have lat lng properties here ?
       */

      resource.groups.forEach(group => {
        if(filters.groupList.findIndex(list => list.value === group.identity) === -1){
          filters.groupList.push({label: group.name, value: group.identity});
        }
      })

      if(filters.resourceList.findIndex(list => list.value === resource.identity) === -1){
        filters.resourceList.push({label: resource.name, value: resource.identity})
      }

      if(filters.resourceStateList.findIndex(list => list.value === resource.status) === -1){
        filters.resourceStateList.push({label: resource.status, value: resource.status})
      }

      if(filters.resourceTypeList.findIndex(list => list.value === resource.resourceType) === -1){
        filters.resourceTypeList.push({label: ServerTypes[resource.resourceType], value: resource.resourceType})
      }
    })
    return filters;
  }

  private returnFilteredResourceList(res: any): SelectItem[] {
    let resources: SelectItem[] = [];
    this.data.resources.filter(resource => {
      let groupsBool = resource.groups.some(group => res[0].includes(group.identity));
      let statusBool = res[1].length > 0 ? res[1].includes(resource.status) : true;
      let typeBool = res[2].length > 0 ? res[2].includes(resource.resourceType) : true;
      return (groupsBool &&  statusBool && typeBool)
    }).forEach(element => {
      resources.push({label: element.name, value: element.identity})
    })
    return resources;
  }

  private formActionControl(propertyName: string, action: string): void{
    switch(action){
      case 'enable':
        this.layerForm.controls[propertyName].enable();
        break;
      case 'disable':
        /**
         * Set timeout due to:
         * https://github.com/angular/angular/issues/22556
         */
        setTimeout(_ => {
          this.layerForm.controls[propertyName].disable()
        }, 200)
        break;
      default:
        this.layerForm.controls[propertyName].disable();
        break;
    }
  }

  onSubmit(){
    this.onFormAction.next(this.layerForm.value);
  }

  onCancel(){
    this.onFormAction.next(null);
  }

}
