import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { AssetsUploader } from 'app/layout/assets-management/assets-uploader/assetsUploader.component';
import { AppModal } from 'app/shared/components/app-modal/app-modal.component';
import { TableActionsComponent } from 'app/shared/components/ng-turbo-table/models/table-actions-component';
import { TableCell } from 'app/shared/components/ng-turbo-table/models/table-cell';
import { TableColumnProperties } from 'app/shared/components/ng-turbo-table/models/table-column';
import { TableData } from 'app/shared/components/ng-turbo-table/models/table-data';
import { ElementTableCell } from 'app/shared/components/ng-turbo-table/models/table-element';
import { TableFilterMatchMode } from 'app/shared/components/ng-turbo-table/models/table-filter-mode-enum';
import { ToastTypes } from 'app/shared/enum/toast-types';
import { EntityModel } from "app/shared/models/entity.model";
import {
    ChannelActionsComponent
} from 'app/shared/modules/inventory-table/components/actions/channel-actions/channel-actions.component';
import * as _ from "lodash";
import * as moment from 'moment';
import { MessageService, SelectItem } from 'primeng/api';
import { Message } from 'primeng/api/message';
import { Subscription } from 'rxjs/internal/Subscription';
import { environment } from '../../../environments/environment';
import { apiMap } from '../../shared/services/api.map';
import { EntitiesActionType } from '../entities/enums/entitiesActionType.enum';
import { Reservation } from './reservation';
import {Asset} from "app/shared/models/asset.model";
import {GeneralAssets,Icons} from "app/shared/enum/enum";

@Component({
    selector:'app-assets-management',
    templateUrl:'./assets-management.component.html',
    styleUrls:['./assets-management.component.scss']
})
export class AssestManagementComponent implements OnInit,OnDestroy {
    @ViewChild('dt',{static:false}) dataTable;
    @ViewChild('deleteModal',{static:false}) deleteModal: AppModal;
    @ViewChild('assetsUploader',{static:true}) private assetsUploader: AssetsUploader;
    @ViewChild('actionsChannel',{static:true}) actionsChannel: TemplateRef<ChannelActionsComponent>;
    @ViewChild('entityFormEdit',{static:false}) entityFormEdit: AppModal;
    showTableCaption: boolean = true;
    filteredGroups: SelectItem[] = [];
    reservations: Reservation[] = [];
    newReservation: Reservation = new Reservation();
    assets:Asset[] = [];
    entities: EntityModel[] = [];
    tableColumns: {
        fieldName: string;
        title: string;
        type?: string;
    } [] = [];
    displayReservationForm: boolean = false;
    formHeader: string = 'addReservation';
    index: number;

    footerButtons = [
        {icon:Icons.Add,title:"addReservation",type:"add"},
        {icon:Icons.Upload,title:"uploadAssetsFile",type:"uploadAssetsFile"}];
    availableEntities: EntityModel[];
    columns: TableColumnProperties[] = [];
    data: { [columnField: string]: string | TableCell; }[];
    newEntity: EntityModel;
    subscriptions: Subscription[] = [];

    constructor(
        private translateService: TranslateService,
        private messageService: MessageService,
        private httpClient: HttpClient,
        private changeDetectorRef: ChangeDetectorRef
    ) {
    }

    ngOnDestroy(): void {
        this.subscriptions.forEach(subscription=>subscription.unsubscribe());
    }

    public ngOnInit(): void {
        this.tableColumns = [
            {fieldName:'Asset',title:'asset'},
            {fieldName:'Entity',title:'entity'},
            {fieldName:'StartTime',title:'startDate'},
            {fieldName:'EndTime',title:'endDate'},
            {fieldName:'Comment',title:'comments'},
            {fieldName:'actions',title:'actions',type:"buttons[]"},
        ];

        this.getEntities();

    }

    onActionButtonClicked(evt): void {
        switch (evt.buttonType) {
            case 'delete':
                this.index = this.reservations.findIndex(reservation=>reservation.Identity === evt.Identity);
                this.onDeleteReservation(this.index);
                break;
            case 'edit':
                this.index = this.reservations.findIndex(reservation=>reservation.Identity === evt.Identity);
                this.newReservation = this.reservations[this.index];
                this.formHeader = 'editReservation';
                this.displayReservationForm = true;
                break;
        }
    }

    getFlattenObject(data: EntityModel[]): TableData {
        const results: { [columnField: string]: TableCell | string }[] = [];
        const fields: { [field: number]: boolean } = {};
        const actions = "actions";
        data.map((item: EntityModel)=>{
            let temp: { [columnField: string]: TableCell | string } = {};
            temp.Email = item.Email;
            fields['Email'] = true;
            temp.Identity = item.Identity;
            fields['Identity'] = true;
            temp.FirstName = item.FirstName;
            fields['FirstName'] = true;
            temp.LastName = item.LastName;
            fields['LastName'] = true;
            temp.MobilePhone = item.MobilePhone;
            fields['MobilePhone'] = true;
            temp.Username = item.Username;
            fields['Username'] = true;
            temp.UniqueID = item.UniqueID;
            fields['UniqueID'] = true;
            let actionCell: TableCell;
            let actionsTemplate: TemplateRef<TableActionsComponent> = null;
            actionsTemplate = this.actionsChannel;
            actionCell = new ElementTableCell(actionsTemplate);
            temp[actions] = actionCell;
            fields[actions] = true;
            results.push(temp);
        });
        return {
            items:results,
            fields:fields
        };
    }


    getEntities(): void {
        let getEntitiesSubscription = this.httpClient.get(environment.apiUrl + apiMap.getEntities.url).subscribe((res: EntityModel[])=>{
            this.entities = res;
            this.availableEntities = _.values(this.entities);
            const result = this.getFlattenObject(this.availableEntities);
            this.generateColumnsTable(result.fields);
            this.data = result.items;
            this.getReservations();
        },err=>{
            this.messageService.add({
                severity:'error',
                summary:this.translateService.instant(ToastTypes.error),
                detail:this.translateService.instant('unableToLoadData')
            });
        });
        this.subscriptions.push(getEntitiesSubscription);
    }


    saveEntity(entity: { entity: EntityModel,action: EntitiesActionType }): void {
        switch (entity.action) {
            case EntitiesActionType.addEntity:
                this.entities.push(entity.entity);
                break;

            case EntitiesActionType.editEntity:
                let index = this.entities.findIndex(item=>item.Identity == entity.entity.Identity);
                if (index < 0) {
                    throw 'Entity not found, something is terribly wrong';
                }
                this.entities.splice(index,1,entity.entity);
                break;
        }

        this.availableEntities = _.values(this.entities);
        const result = this.getFlattenObject(this.availableEntities);
        if (this.columns.length <= 0) {
            this.generateColumnsTable(result.fields);
        }

        this.data = result.items;
    }

    generateColumnsTable(fields: { [field: string]: boolean }): void {
        const keys = Object.keys(fields);
        keys.map((key: string)=>{
            let column = {
                field:key,
                header:key,
                visibile:key == "Identity" ? false : true,
                frozen:false,
                isFilterable:true,
                sortable:true,
                filterOptions:this.returnFilterOptions(key),
                filterMatchMode:TableFilterMatchMode.contains,
            };
            this.columns.push(new TableColumnProperties(column));
        });
    }

    onSelectedElements(event: { [columnField: string]: TableCell | string }[]): void {

    }

    returnFilterOptions(key: string): SelectItem[] {
        let filterOptions: SelectItem[] = [];
        switch (key) {
            case 'Email':
                filterOptions = this.filteredGroups;
                break;
            default:
                break;
        }
        return filterOptions;
    }


    closeForm(): void {
        this.entityFormEdit.closeModal();
        this.newEntity = null;
    }

    getReservations(): void {
        let startDate = new Date();
        startDate.setHours(0);
        startDate.setMinutes(0);
        let endDate = new Date();
        endDate.setHours(0);
        endDate.setMinutes(0);
        endDate.setFullYear(endDate.getFullYear() + 10);
        this.httpClient.post(environment.apiUrl + apiMap.getAssetsReservations.url,{
            StartTime:moment.utc(startDate).toDate().toISOString(),
            EndTime:moment.utc(endDate).toDate().toISOString()
        }).subscribe((res: Reservation[])=>{
            this.reservations = res;
            this.reservations.forEach(reservatoin=>{
                this.setReservationToTable(reservatoin);
            });
        },err=>{
            this.messageService.add({
                severity:'error',
                summary:this.translateService.instant(ToastTypes.error),
                detail:this.translateService.instant('unableToLoadData')
            });
        });
    }

    setReservationToTable(reservation: Reservation): void {
        reservation['StartTime'] = moment(reservation.StartTime).format("MMM DD YYYY, HH:mm");
        reservation['EndTime'] = moment(reservation.EndTime).format("MMM DD YYYY, HH:mm");
        reservation['actionButtons'] = [
            {name:'edit',icon:Icons.Edit,type:'edit'},
            {name:'delete',icon:Icons.Delete,type:'delete'}
        ];
        let entity = this.getEntityFromIdentity(reservation.EntityIdentity);
        reservation['Entity'] = entity.FirstName + " " + entity.LastName;
        reservation['Asset'] = this.getAssetNameFromIdentity(reservation.AssetIdentity);
    }

    getEntityFromIdentity(identity: string): EntityModel {//TODO strongly-typed
        if (identity) {
            return this.entities.find(entity=>entity.Identity === identity);
        } else {
            return null;
        }

    }


    editEntityForm(row?: EntityModel): void {
        if (row) {
            this.newEntity = this.getEntityFromIdentity(row.Identity);
        } else {
            this.newEntity = null;
        }

        this.changeDetectorRef.detectChanges();
        this.entityFormEdit.openModal();

    }

    getAssetNameFromIdentity(identity: string): string {
        return this.assets.find(asset=>asset.Identity === identity).Name;
    }

    confirmDeletion(): void {
        let entityDeleteSubscription = this.httpClient.get(environment.apiUrl + apiMap.removeReservation.url + this.reservations[this.index].Identity).subscribe(res=>{
            this.reservations.splice(this.index,1);

            this.displayReservationForm = false;
            this.deleteModal.closeModal();
        },err=>{
            this.messageService.add({
                severity:'error',
                summary:this.translateService.instant(ToastTypes.error),
                detail:this.translateService.instant('operationFailed')
            });
        });
        this.subscriptions.push(entityDeleteSubscription);
    }

    cancelDeletion(): void {
        this.index = -1;
        this.deleteModal.closeModal();
    }

    onCloseReservationForm(): void {
        this.displayReservationForm = false;
    }

    onDeleteReservation(event: number): void {
        this.deleteModal.openModal();
    }

    onSaveReservation(event: Reservation): void {
        let saveReservationSubscription = this.httpClient.post(environment.apiUrl + apiMap.setReservationToTable.url,{
            Identity:event.Identity,
            AssetIdentity:event.AssetIdentity,
            EntityIdentity:event.EntityIdentity,
            StartTime:moment.utc(event.StartTime).toDate().toISOString(),
            EndTime:moment.utc(event.EndTime).toDate().toISOString(),
            ReserveParking:event.ReserveParking,
            Comment:event.Comment
        }).subscribe((res: any)=>{
            if (res && res.parkingAssetId && event.AssetIdentity != GeneralAssets.generalParking.Identity) {
                let parkingReservation = new Reservation({
                    Identity:res.parkingReservationId,
                    Asset:this.getAssetNameFromIdentity(res.parkingAssetId),
                    AssetIdentity:res.parkingAssetId,
                    Entity:event.Entity,
                    EntityIdentity:event.EntityIdentity,
                    ReserveParking:false,
                    StartTime:event.StartTime,
                    EndTime:event.EndTime,
                    Comment:event.Comment
                });
                this.addSavedReservationToTable(parkingReservation);
            } else if (!res.parkingAssetId && event.ReserveParking) {
                this.messageService.add({
                    severity:'warn',
                    summary:this.translateService.instant(ToastTypes.info),
                    detail:this.translateService.instant('noAvailableParkingAtTheRequestedTime')
                });
            }

            if (event.AssetIdentity == GeneralAssets.GeneralDesk.Identity ||
                event.AssetIdentity == GeneralAssets.GeneralParking.Identity ||
                event.AssetIdentity == GeneralAssets.GeneralRoom.Identity) {

                let designatedAsset = this.assets.find(q=>q.Identity == res.reservationId);
                event.Asset = designatedAsset.Name;
                event.AssetIdentity = designatedAsset.Identity;
            }

            event.Identity = res.reservationId;
            this.addSavedReservationToTable(event);
        },err=>{
            let message: Message = {severity:'error',summary:this.translateService.instant(ToastTypes.error)};
            switch (err.status) {
                case 403: {
                    message.detail = this.translateService.instant('userIsNotAllowedToReserveAssets');
                    break;
                }
                case 405: {
                    message.detail = this.translateService.instant('selectedEntityAlreadyHasAReservationForTheRequestedTime');
                    break;
                }
                case 409: {
                    message.detail = this.translateService.instant('assetMaxTimeExceeded');
                    break;
                }
                case 502: {
                    message.detail = this.translateService.instant('thereAreNoAvailableAssetsAtTheRequestedTime');
                    break;
                }
                case 503: {
                    message.detail = this.translateService.instant('theAssetIsAlreadyReservedAtTheSelectedTimePleaseTryAnotherTime');
                    break;
                }
                default:
                    message.detail = this.translateService.instant('operationFailed');
                    break;

            }
            setTimeout(()=>{
                this.messageService.add(message);
            },100);
        });
        this.subscriptions.push(saveReservationSubscription);
    }

    addSavedReservationToTable(reservation: Reservation): void {
        if (this.index > -1) {
            this.reservations[this.index] = reservation;
        } else {
            this.index = this.reservations.length;
            this.reservations.push(reservation);
        }
        this.setReservationToTable(this.reservations[this.index]);

        this.displayReservationForm = false;
        this.index = -1;
    }

    onDelete(row: EntityModel): void {
        let onDeleteSubscription = this.httpClient.get(environment.apiUrl + apiMap.deleteEntity.url + row.Identity).subscribe(
            res=>{

            },err=>{

            });

        this.subscriptions.push(onDeleteSubscription);
    }

    deleteEntity(entity: { [columnField: string]: string | TableCell }): void {

        this.data = this.data.filter(entityFilterItem=>{
            return entityFilterItem.Identity !== entity.Identity;
        });
        let data = this.entities.filter(item=>{
            return item.Identity != entity.Identity;
        });
        let deleteEntitySubscription = this.httpClient.get(environment.apiUrl + apiMap.deleteEntity.url + entity.Identity).subscribe(
            res=>{
                this.subscriptions.push(deleteEntitySubscription);
            },err=>{
                console.log(err);
            });


    }
}
