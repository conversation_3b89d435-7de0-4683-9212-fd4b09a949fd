<div class="inventory-add-sidebar">
  <form role="form" class="form-format" [formGroup]="rtspForm">
    <div class="row">
      <div class="col col-md-12 form-group">
        <h2 class="h2-format">{{ 'inventory.addDevice' | translate }} </h2>
      </div>
    </div>

    <div class="row">
      <div class="col col-md-12 form-group">
        <label>{{ 'deviceName' | translate }}</label>
        <input type="text" formControlName="name" placeholder="{{ 'name' | translate }}">
        <div class="all-pattern-error"
          *ngIf="!rtspForm.controls.name.valid && rtspForm.controls.name.touched">
          <div class="input-error">
            <small *ngIf="rtspForm.controls['name'].hasError('required')" class="text-danger">
              {{ 'formValidation.field'  | translate}}
            </small>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col col-md-12 form-group">
        <label>{{ 'ipAddress' | translate }}</label>
        <input type="text" formControlName="ipAddress" placeholder="{{ 'ipAddress' | translate }}">
        <div class="all-pattern-error"
          *ngIf="!rtspForm.controls.ipAddress.valid && rtspForm.controls.ipAddress.touched">
          <div class="input-error">
            <small *ngIf="rtspForm.controls['ipAddress'].hasError('required')" class="text-danger">
              {{ 'formValidation.portField' | translate}}
            </small>
          </div>
          <div class="input-error">
            <small *ngIf="rtspForm.controls['ipAddress'].hasError('pattern')" class="text-danger">
              {{'formValidation.ipAddressField' | translate}}
            </small>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col col-md-12 form-group">
        <label>{{ 'inventory.httpPort' | translate }}</label>
        <input type="text" formControlName="httpPort" placeholder="{{ 'inventory.httpPort' | translate }}">
        <div class="all-pattern-error"
          *ngIf="!rtspForm.controls.httpPort.valid && rtspForm.controls.httpPort.touched">
          <div class="input-error">
            <small *ngIf="rtspForm.controls['httpPort'].hasError('required')" class="text-danger">
              {{ 'formValidation.field' | translate}}
            </small>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col col-md-12 form-group">
        <label>{{ 'inventory.rtspPort' | translate }}</label>
        <input type="text" formControlName="rtspPort" placeholder="{{ 'inventory.rtspPort' | translate }}">
        <div class="all-pattern-error"
          *ngIf="!rtspForm.controls.rtspPort.valid && rtspForm.controls.rtspPort.touched">
          <div class="input-error">
            <small *ngIf="rtspForm.controls['rtspPort'].hasError('required')" class="text-danger">
              {{ 'formValidation.field' | translate}}
            </small>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col col-md-12 form-group">
        <label>{{ 'inventory.rtspLink' | translate }}</label>
        <input type="text" formControlName="rtspLink" placeholder="{{ 'inventory.rtspLink' | translate }}">
        <div class="all-pattern-error"
          *ngIf="!rtspForm.controls.rtspLink.valid && rtspForm.controls.rtspLink.touched">
          <div class="input-error">
            <small *ngIf="rtspForm.controls['rtspLink'].hasError('required')" class="text-danger">
              {{ 'formValidation.field' | translate}}
            </small>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col col-md-12 form-group">
        <label class="col-form-label">{{ 'resourceGroups' | translate }}</label>
        <p-multiSelect defaultLabel="{{ 'choose' | translate }}" formControlName="resourceGroups" [options]="filteredResourceGroups"
         selectedItemsLabel="{{ 'nrItemsSelected' | translate }}" [maxSelectedLabels]="0">
          <ng-template let-event pTemplate="item">
             {{ event.label}}
          </ng-template>
        </p-multiSelect>
      </div>
    </div>

    <div class="row">
      <div class="col col-md-12 form-group">
        <label>{{ 'username' | translate }}</label>
        <input type="text" formControlName="username" placeholder="{{ 'username' | translate }}">
        <div class="all-pattern-error"
          *ngIf="!rtspForm.controls.username.valid && rtspForm.controls.username.touched">
          <div class="input-error">
            <small *ngIf="rtspForm.controls['username'].hasError('required')" class="text-danger">
              {{ 'formValidation.field' | translate}}
            </small>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col col-md-12 form-group">
        <label>{{ 'password' | translate }}</label>
        <input type="password" formControlName="password" placeholder="{{ 'password' | translate }}">
        <div class="all-pattern-error"
          *ngIf="!rtspForm.controls.password.valid && rtspForm.controls.password.touched">
          <div class="input-error">
            <small *ngIf="rtspForm.controls['password'].hasError('required')" class="text-danger">
              {{ 'formValidation.field' | translate}}
            </small>
          </div>
        </div>
      </div>
    </div>

    <div class="row mt-5">
      <div class="col col-md-12">
        <div class="btn-toolbar pull-right">
          <button type="button" class="btn btn-secondary" (click)="cancel()">{{ 'cancel' | translate }}</button>
          <button type="button" class="btn btn-primary btn-save" [disabled]="!rtspForm.valid || isLoading" (click)="addRtsp()">
            {{ 'add' | translate }}</button>
        </div>
      </div>
    </div>
  </form>
</div>