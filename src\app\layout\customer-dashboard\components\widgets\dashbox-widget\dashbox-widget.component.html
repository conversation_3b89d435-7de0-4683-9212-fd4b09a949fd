<div class="dashbox-container" (click)="initDashboxModal()">
    <div class="dashbox-no-data" *ngIf="!data?.widgetData?.selectedResourceType">
      {{ 'noData' | translate}}
    </div>

     <div class="dashbox-data {{'device-type-'+dashboxData?.resourceType}}" *ngIf="data?.widgetData?.selectedResourceType">
            <div class="dashbox-icon">
              <div class="data-icon" [style.color]="data.widgetData.selectedBackgroundColourCode"
               [appBackgroundColor]="data.widgetData.selectedBackgroundColourCode" addTransparency="0.3">
              </div>
            </div>
            <div class="dashbox-info" [ngClass]="{'no-resource-state': !data.widgetData.selectedResourceStates }">    
                <p class="dashbox-title"> {{ dashboxData?.deviceNumber }} {{ dashboxTitle }}</p>
                
                <ng-container *ngIf="dashboxData?.showStatus">
                  <ul> 
                    <ng-container *ngFor="let device of dashboxData.deviceStatuses">
                      <ng-container *ngFor="let item of device | keyvalue">
                        <li class="{{item.key}}"> 
                          <span class="status-counter">
                            {{item.value}}
                          </span>
                          <span  pTooltip="{{item.key | translate | lowercase}}" tooltipPosition="top">
                            {{item.key | translate | ellipsis: 15}}
                          </span>                         
                        </li>            
                      </ng-container>
                    </ng-container>
                  </ul>
                </ng-container>
            </div>
    </div>
</div>

<app-modal #dashboxModal [title]="'dashboxResourcesView'">
  <ng-container ngProjectAs="contentModal">
    
    <div class="view-switch-container">
      <p-inputSwitch styleClass="inputswitch-small" title="{{ 'viewType' | translate }}" [(ngModel)]="modalViewIsTable"></p-inputSwitch>
      <span class="view-title">{{(modalViewIsTable ? 'tableView' : 'mapView') | translate}}</span>
    </div>

    <div class="component-view-container">
      <inventory-table #inventoryTable 
        *ngIf="modalViewIsTable" 
        [resourceModelStore]="filteredResources"
        [showTableCaption]="false">
      </inventory-table>
      <app-cymbiot-map #cymbiotMap 
        *ngIf="!modalViewIsTable" 
        [selectedMapId]="selectedMapId" 
        [mapLayers]="{}" 
        [mapFilterOptions]="mapFilterOptions">
      </app-cymbiot-map>  
    </div>

  </ng-container>
</app-modal>