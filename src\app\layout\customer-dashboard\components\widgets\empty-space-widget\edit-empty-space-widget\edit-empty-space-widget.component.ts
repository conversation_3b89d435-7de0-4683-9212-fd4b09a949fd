import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { EmptySpaceWidget } from 'app/layout/customer-dashboard/models/empty-space-widget';
import { DashboardUtilsService } from 'app/layout/customer-dashboard/services/dashboard-utils.service';
import { SelectItem } from 'primeng/api';

@Component({
  selector: 'app-edit-empty-space-widget',
  templateUrl: './edit-empty-space-widget.component.html',
  styleUrls: ['./edit-empty-space-widget.component.scss']
})
export class EditEmptySpaceWidgetComponent implements OnInit {
  data: EmptySpaceWidget;
  public emptySpaceForm: FormGroup = null;
  gridColumnOptions: SelectItem[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private dashboardUtilsService: DashboardUtilsService) { }

  ngOnInit(): void{
    this.data = new EmptySpaceWidget(this.data);
    this.generateGridColumnOptions();
    this.generateForm(this.data);
  }

  generateForm(data: EmptySpaceWidget): void {
    this.emptySpaceForm = this.formBuilder.group({
      gridColumn: new FormControl(data.gridColumnSize ? data.gridColumnSize : ''),
    });
  }

  onGridColumnChange(event: SelectItem): void {
    this.data.gridColumnSize = event.value;
    this.dashboardUtilsService.setWidgetDataChange(this.data);
  }

  generateGridColumnOptions(): void {
    Array(9).fill(0).map((x, i) => {
      this.gridColumnOptions.push({ 'label': `${i + 1}`, 'value': i + 1 });
    });
  }

}
