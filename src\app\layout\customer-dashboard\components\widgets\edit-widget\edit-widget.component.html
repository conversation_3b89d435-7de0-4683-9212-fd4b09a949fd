<div class="form-item">
  <h2>{{ 'customerDashboard.editWidget' | translate }}</h2>
  <input type="text" name="title" [(ngModel)]="selectedWidget.title" [disabled]="!selectedWidget.hasTitle"
    placeholder="{{'customerDashboard.editWidgetTitle' | translate}}" (ngModelChange)="title$.next($event)" />
</div>

<!-- 
  Taken out due to widget sizing on size fits all.
-->

<!-- <div class="form-item">
  <h2>{{ 'customerDashboard.editWidgetSize' | translate }}</h2>  
  <p-dropdown 
    [options]="widgetSizeOption" 
    [(ngModel)]="selectedWidget.size" 
      
    [styleClass]="'input-element'" 
    (onChange)="onChangeWidgetSize($event)">
    
    <ng-template let-item pTemplate="selectedItem">
        <span>{{item.label| translate}}</span>
    </ng-template>
    <ng-template let-item pTemplate="item">
        <div class="ui-helper-clearfix">
            <div>{{item.label | translate}}</div>
        </div>
    </ng-template>
  </p-dropdown>
</div> -->