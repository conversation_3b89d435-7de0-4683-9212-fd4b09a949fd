import { Component, OnInit } from '@angular/core';
import { PlayerWidget } from 'app/layout/customer-dashboard/models/player-widget';
import { DefaultWidgetEditorComponent } from '../../default-widget/default-widget-editor.component';
import { DashboardUtilsService } from 'app/layout/customer-dashboard/services/dashboard-utils.service';
import { CameraOptions } from 'app/layout/customer-dashboard/enums/camera-options.enum';

@Component({
  selector: 'app-edit-player-widget',
  templateUrl: './edit-player-widget.component.html',
  styleUrls: ['./edit-player-widget.component.scss']
})
export class EditPlayerWidgetComponent extends DefaultWidgetEditorComponent implements OnInit {
  data: PlayerWidget;
  cameraOptions = CameraOptions;
  constructor(private dashboardUtilsService: DashboardUtilsService) {
    super();
  }

  ngOnInit() : void{
    this.data = new PlayerWidget(this.data);
  }

  setSelectedChannel(event:{playerId: string, channelId:string, widgetId: string}): void{
    if(this.data.id === event.widgetId){
      this.data.config.selectedChannelId = event.channelId;
      this.dashboardUtilsService.setWidgetDataChange(this.data);
    }
  }

  onCameraOptionsChange(event: boolean, option: CameraOptions): void{
      this.data.config[option] = event;
      this.dashboardUtilsService.setWidgetDataChange(this.data);
  }
}
