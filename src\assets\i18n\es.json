{"all": "todos", "rows": "filas", "dashboards": "Tableros", "newDashboard": "Nuevo tablero", "dashboard": "<PERSON><PERSON>", "tables": "Tablas", "forms": "Formularios", "component": "Componentes", "menu": "Menú", "submenu": "Submenú", "blankpage": "Página en blanco", "moretheme": "<PERSON><PERSON> temas", "lightTheme": "<PERSON><PERSON><PERSON>", "darkTheme": "Oscuro", "downloadNow": "<PERSON><PERSON><PERSON> ahora", "unassigned": "<PERSON>", "language": "Idioma", "viewAll": "<PERSON><PERSON>", "channels": "Canales", "maps": "Mapas", "apply": "Aplicar", "open": "<PERSON>bie<PERSON>o", "rec": "Rec", "closed": "<PERSON><PERSON><PERSON>", "selectStep": "Se<PERSON><PERSON><PERSON>r paso", "openChannelFail503": "Verifique: si el servicio webstreammer está configurado correctamente en el administrador y si el servicio webstreammer se inició en la dirección/puerto configurado", "StreamNotSupported": "El navegador no admite la transmisión de video", "FailedToConnect": "Se produjo un error de servidor interno inesperado. Verifique los registros del servidor", "in_progress": "En progreso", "waiting": "<PERSON><PERSON><PERSON><PERSON>", "errorCreatingProcedure": "Error al crear el procedimiento", "noProcedureTemplates": "No hay plantillas de procedimiento disponibles", "proceduresServiceIsNotRunning": "El servicio de procedimientos no está funcionando", "snoozed": "Snoozed", "archived": "Archivado", "created": "<PERSON><PERSON><PERSON>", "refreshLocation": "Actualizar ubicación", "updated": "Actualizado", "createProcedure": "<PERSON><PERSON><PERSON> proced<PERSON><PERSON>o", "commpleteStep": "Paso completo", "locationUpdated": "Ubicación actualizada", "locationUpdateError": "Error al actualizar la ubicación", "locationManagement": "Gestión de ubicación", "options": "Opciones", "comment": "Comentario", "actions:": "Acciones:", "handledBy": " manejado por", "completeStep": "Paso completo", "exportPDF": "Exportar PDF", "dashboardEditor": "Editor de tablero", "liveDashboard": "Tablero en vivo", "reports": "Reportes", "reportSelection": "Selección de informe", "dateFilter": "Filtro por fecha", "latestAlerts": "Últimas alertas", "period": "<PERSON><PERSON><PERSON>", "startDate": "Fecha de Inicio", "endDate": "Fecha final", "time": "<PERSON><PERSON><PERSON>", "selectEndDate": "Seleccionar fecha final", "reportType": "Tipo de informe", "alert": "<PERSON><PERSON><PERSON>", "trigger": "Disparador", "selectAlerts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectTriggers": "Seleccionar disparadores", "alertsSelected": "<PERSON>as selecciona<PERSON>", "triggersSelected": "disparadores seleccionados", "reportAlreadyGenerating": "Ya se está generando un informe", "reportGenerationStarted": "Generación de informe iniciada", "templateNotFound": "Plantilla no encontrada, puede haber sido eliminada", "templateRequiresName": "Una plantilla debe tener un nombre", "templateNameExists": "Ya existe una plantilla de búsqueda con este nombre", "templateRemoved": "Plantilla eliminada con éxito", "failedToRemoveTemplate": "Error al eliminar la plantilla", "updateSuccess": "Actualizado con éxito", "saveSuccess": "Guardado con éxito", "invalidDateRange": "La fecha inicial debe ser anterior a la fecha final", "selectValidDateRange": "Por favor, seleccione un rango de fechas válido", "invalidDateFilterType": "Tipo de filtro de fecha no válido", "reportCancelled": "Generación de informe cancelada", "noDataToExport": "No hay datos para exportar", "resources": "Recursos", "saveLocation": "Guardar ubicación", "lumenFactor": "Factor de lumen", "selectSearchTemplate": "Seleccionar Plantilla de Busqueda", "searchBy": "Buscar según", "addEditEntity": "Agregar / editar entidad", "textFilter": "Filtro textual", "resourceGroup": "Grupo de recursos", "resource": "Recursos", "selectDate": "<PERSON><PERSON><PERSON><PERSON><PERSON> fecha", "fromDate": "Fecha de inicio", "toDate": "Fecha final", "enableTrafficFactor": "Habilitar factor de tráfico", "enableWeatherFactor": "Habilitar el factor meteorológico", "addNewSearchTemplate": "Agrega una nueva plantilla de búsqueda", "triggerType": "<PERSON><PERSON><PERSON>/Gatillo", "exportToPDF": "Exportar a formato PDF", "exportRoute": "Ruta de exportación", "saveTemplate": "<PERSON><PERSON>", "generateReport": "Generar Reporte", "execute": "<PERSON><PERSON><PERSON><PERSON>", "clear": "Limpiar", "newTemplate": "Nueva Plantilla", "enterNewTemplateName": "Introducir el nuevo nombre de Plantilla", "selectOperation": "Seleccionar Operación", "events": "Eventos", "triggers": "Gatillos/Triggers", "heatMap": "Mapa termográfico", "date": "<PERSON><PERSON>", "last": "Última/o", "dateRange": "<PERSON><PERSON>", "latest": "Último", "seconds": "<PERSON><PERSON><PERSON>", "minutes": "<PERSON><PERSON><PERSON>", "days": "Días", "hours": "<PERSON><PERSON>", "months": "Meses", "weeks": "Semanas", "map": "Mapa", "table": "Tabla", "chart": "Gráfico", "Year": "<PERSON><PERSON>", "chartType": "Tipo de gráfico", "lightInformation": "Información", "bar": "Barras", "doughnut": "Gráfico radial ", "radar": "Gráfico radar", "pie": "Gráfico de porciones", "polarArea": "Grafica polar de áreas", "line": "Gráfico linear", "search...": "Buscar...", "systemIP": "IP de sistema", "ioCommands": "Comandos de entrada/salida", "changeStatus": "Cambiar estado", "password": "Contraseña", "username": "Nombre de Usuario", "login": "In<PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "badCredentialsMsg": "Nombre de usuario o contraseña incorrecta", "error": "Error", "yes": "Si", "no": "No", "confirm": "Confirmar", "delete": "Bo<PERSON>r", "ok": "Aceptar", "cancel": "<PERSON><PERSON><PERSON>", "DeviceConnection": "Conexión de dispositivo", "GenericSensorValue": "Valor de sensor genérico", "GenericSensorString": "Clave/string de definición para sensor genérico", "GenericSensorBoolTrue": "Sensor Genérico <PERSON> Real", "GenericSensorBoolFalse": "Sensor Genéric<PERSON>", "allTriggers": "Todos los gatillos/triggers", "noResponseFromServer": "Servidor sin respuesta", "loginFirst": "Por favor Inicie la Sesión", "removeTemplate": "Remover Plantilla", "updateTemplate": "Actualizar <PERSON>illa", "dashboardEditorText": "Editor de texto en tablero", "reportTransformation": "Transformación del reporte", "none": "<PERSON><PERSON><PERSON>", "id": "ID", "Id": "ID", "RuleDescription": "Descripción de regla", "TimeStamp": "Tiempo de sello Original", "ResourceName": "Nombre del Recurso", "TriggerType": "Tipo de gatillo/trigger", "longitude": "longitud", "latitude": "latitud", "Longitude": "<PERSON><PERSON><PERSON>", "Latitude": "Latitud", "battery_level": "<PERSON><PERSON> de <PERSON>", "sensor_id": "ID del Sensor", "RuleId": "ID de Regla", "event_id": "ID del Evento", "resource_name": "Nombre del Recurso", "trigger_type": "Tipo de gatillo/trigger", "heading": "Encabezado", "resource_id": "ID del Recurso", "time_stamp": "Tiempo de sello original", "foreign_id": "<PERSON><PERSON><PERSON>án<PERSON>", "last_moved": "<PERSON><PERSON><PERSON>", "NoMovement": "Sin Movimientos", "en": "Inglés", "he": "<PERSON><PERSON><PERSON>", "areYouSureDelete": "Esta seguro que desea borrar?", "searchTemplateWithThisNameAlredyExist": "Plantilla en existencia con el mismo nombre", "chooseDifferentName": "Seleccione un nombre diferente", "exportToCSV": "Exportar a formato CSV", "exportSuccess": "Exportación completada exitosamente", "exportFailed": "La exportación falló", "exportEndpointNotFound": "Endpoint de exportación no encontrado", "auditServiceIsNotRunning": "El servicio de auditoría no está funcionando", "pdfExportNotAvailable": "La exportación PDF no está disponible temporalmente. Por favor use la exportación CSV en su lugar.", "toggleSidebar": "<PERSON><PERSON>", "allResourcesInGroup": "Todos los recursos en grupo", "allResources": "Todos los Recursos", "fromDateShouldBeBeforeToDate": "El tiempo elegido para el campo: 'Desde la fecha' debe ser anterior al asignado para el campo: 'Hasta la fecha'", "Date": "<PERSON><PERSON>", "Name": "Nombre", "Sensor": "Sensor", "Start Time": "Hora de comienzo", "End Time": "Hora de finalización", "Work Hours": "Horas de trabajo", "Penalty Hours": "<PERSON><PERSON>", "Net Work Hours": "Horas trabajadas en la red", "Start Period Time": "Inicio del Periodo de tiempo", "End Period Time": "Finalización del Periodo de tiempo", "No Movement Period": "Períodos sin movimiento", "Penalty Period": "Períodos penalizable", "Distance": "Distancia", "DISTANCE": "Distancia", "distance": "Distancia", "Under Limit": "Bajo límite", "close": "<PERSON><PERSON><PERSON>", "now": "ahora", "Vmd": "VMD", "Relay": "Parada", "SignalLoss": "Señal perdida/desconectado", "HDFailure": "Falla en alta definición", "LocationChanged": "Cambio de ubicación", "BatteryLevel": "<PERSON><PERSON> de <PERSON>", "SensorActive": "Sensor activo", "SensorInactive": "Sensor Inactivo", "GarbageSensorDailyPickups": "Sensor para la recolección de basura diaria", "GarbageTruckLate": "Recolector de basura tardío", "GarbageTruckForbiddenArea": "Recolección de basura en áreas prohibidas", "ALPR": "Reconocimiento automático licencias de conducir numérica", "IdTag": "Tag ID", "CELLAV_BELOW_WORK_LIMITS_TRIGGERS": "Por debajo de los límites de trabajo (Gatillos/triggers)", "CELLAV_NO_WORK_TRIGGERS": "Sin trabajo (Gatillos/triggers)", "CELLAV_WORKING_HOURS_TRIGGERS": "<PERSON><PERSON> (Gatillos/triggers)", "CELLAV_NO_MOVEMENT_TRIGGERS": "Periodos sin movimiento (Gatillos/triggers)", "Last Comm": "Última Comunicación", "last_comm": "Última Comunicación", "Comm Error": "Error de Comunicación", "event_type": "Tipo de Evento", "reportGenerationFailed": "Generación errónea de reporte", "noData": "Sin Información", "entity_id": "ID de entidad", "entity_value": "Valor de Entidad", "extractVideo": "Extraer Video", "selectVideoFormat": "Se<PERSON><PERSON><PERSON><PERSON>", "maxRangeExceeded": "<PERSON>ngo máximo exedido", "extractionFail": "Falla en extracción", "openChannelFail": "Falla en la apertura de señal", "openingChannel": "Abriendo canal", "startExtraction": "Comenzar extracci<PERSON>", "generateReportFail": "Fallo en Reporte generado", "cantFindTemplate": "No se puede encontrar la plantilla", "cantFindSession": "Sesión desconectada, por favor inicie la sesión", "selectLang": "Selección de idioma", "selectFontSize": "Se<PERSON><PERSON><PERSON>r ta<PERSON> de fuente", "fontSizeSmall": "Pequeño", "fontSizeNormal": "Normal", "fontSizeLarge": "Grande", "startTimeNightTheme": "Hora de inicio del tema nocturno", "endTimeNightTheme": "Hora final para el tema de la noche", "dayNightSwitch": "Cambio automático de tema día / noche", "enableAutoLoginSwitch": "Habilitar inicio de sesión automático", "userLacksPermissions": "Usuario sin Permiso", "pleaseVerifyYourCredentials": "Por Favor verifique sus credenciales de usuario", "maxAllowedUsersAreCurrentlyLoggedIn": "Actualmente están conectados el máximo número de usuarios permitidos", "userAlreadyLoggedIn": "El usuario está actualmente conectado", "unableToConnectToSystemControl": "La Conexión con el sistema de control esta imposibilitada", "applicationErrorUnableToConnectTimeOut": "Error en la aplicación - Conexión imposibilitada debido a la caducidad en la sesión", "invalidLicense": "Licencia inválida", "operationFailed": "Operación fallida", "both_limits": "Distancia y Horas", "moveDown": "Bajar", "moveRight": "Mover a la derecha", "moveUp": "Subir", "moveLeft": "Mover a la izquierda", "playbackTime": "Tiempo de Reproducción", "playbackGo": "Comienzo de reproducción", "micOff": "Micró<PERSON><PERSON>", "micOn": "Micrófono encendido", "live": "Video en vivo", "rewindVideo": "Rebobinar video", "playVideo": "Reproducir video", "pauseVideo": "Pausar el video", "fastForward": "Avance rápido de video", "zoomIn": "Acercar", "zoomOut": "<PERSON><PERSON><PERSON>", "fullScreen": "Pantalla completa", "unknownError": "Error descon<PERSON>", "notSupportedYet": "Todavia sin soporte", "sessionTerminated": "Sesión finalizada", "systemIsDown": "El Sistema esta inhabilitado o apagado", "licenseDongleNotConnected": "El Dongle de licencia no esta conectado", "licenseExpired": "Licencia caducada", "closedBy_name": "Cerrar por: {{nombre}}", "failToLogout": "Falla al cerrar la sesión", "aTemplateMustHaveAName": "La plantilla necesita ser guardada bajo un NOMBRE", "noDataWasFound": "No hay datos encontrados", "savedSuccessfully": "Correctamente guradado", "updatedSuccessfully": "Exitosamente actualizado", "failedToSave": "Guarda fallida", "wasDeleted": "<PERSON><PERSON><PERSON>", "success": "Correcto", "extractionOf": "<PERSON><PERSON> de", "succeeded": "Exitosamente", "failure": "<PERSON>a", "failed": "Fallido", "extracting": "extrayendo", "noAvailablePlayers": "No hay reproductores disponibles", "freezeVideo": "Video Congelado", "vms": "VMS", "timeRangeOverflow": "Sobrepaso en el máximo de horario permitido", "identity": "Identidad", "generatingReport": "Generando reporte", "reportGeneratedSuccessfully": "Reporte generado exitosamente", "selectCol": "Seleccione de columnas para visualizar", "resultsAmount": "Resultado:", "of": "de", "fromTime": "<PERSON><PERSON>", "toTime": "hasta", "sunday": "Domingo", "monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "Viernes", "saturday": "Sábado", "today": "Hoy", "sun": "Dom", "mon": "<PERSON>n", "tue": "Mar", "wed": "<PERSON><PERSON>", "thu": "<PERSON><PERSON>", "fri": "Vie", "sat": "<PERSON><PERSON><PERSON>", "su": "Do", "mo": "<PERSON>", "tu": "Ma", "we": "<PERSON>", "th": "<PERSON>", "fr": "Vi", "sa": "Sá", "january": "<PERSON><PERSON>", "february": "<PERSON><PERSON><PERSON>", "march": "<PERSON><PERSON>", "april": "Abril", "may": "May", "june": "<PERSON><PERSON>", "july": "<PERSON>", "august": "Agosto", "september": "Septiembre", "october": "Octubre", "november": "Noviembre", "december": "Diciembre", "jan": "Ene", "feb": "Feb", "mar": "Mar", "apr": "Abr", "jun": "Jun", "jul": "Jul", "aug": "Ago", "sep": "Sep", "oct": "Oct", "nov": "Nov", "dec": "Dic", "maxAllowedRowsIs": "El número máximo de registros es", "entities": "Entidades", "selectedRangeCantBeInTheFuture": "El rango seleccionado no esta permitido para futuras fechas", "firstDayOfWeek": "<PERSON><PERSON>", "selectTime": "<PERSON><PERSON><PERSON><PERSON><PERSON> hora", "dateOverflow": "No se pueden seleccionar fechas a futuro", "colsSelected": "{0} Columnas selecciona<PERSON>", "noServerConnection": "Sin señal de <PERSON>, reconectando...", "description": "Descripción", "confidence": "<PERSON><PERSON> de confianza( % )", "noImagesFound": "No se encontraron imágenes", "carImage": "Imagen de vehículo", "plateImage": "Imagen de licencia", "entityImage": "Imagen de la entidad", "entity": "Entidad", "gallery": "Galería", "player": "Reproductor", "openPlaybackFailed": "Fallo en la apertura de archivo grabado", "noStreamerConnection": "Fallo en la conexión con el streamer ", "TwoHourMaxLimit": "Rango máximo expedido de 2 horas!", "searchTemplateParams": "Buscar parametros de plantillas", "From": "<PERSON><PERSON>", "To": "<PERSON><PERSON>", "ColumnDetails": "Detalle de Columnas", "ColumnName": "Nombre de la columna", "allValues": "Todos los valores", "addColumn": "Agregar <PERSON>", "removeColumn": "Remover Columna", "name": "Nombre", "type": "Tipo", "actions": "Acciones", "failedToAddColumn": "Falla al agregar columna", "colNameAlreadyExist": "Nombre de columna en existencia", "Core_RES_ALPRLane": "Canales de ALPR", "Core_RES_Dashboard": "Escritorio", "Core_RES_Device": "Dispositivo", "Core_RES_Input": "Entrada", "Core_RES_Output": "Salida", "Core_RES_Layout": "Diseño", "Core_RES_Map": "Mapa", "Core_RES_InputChannel": "Canal de entrada", "Core_RES_Entity": "Entidad", "Core_RES_Asset": "Activo", "Core_RES_LightGW": "Luz GW", "Core_RES_Light": "Luz", "Core_RES_Cabinet": "Gabi<PERSON><PERSON>", "Core_RES_Unknown": "Desconocido", "Core_RES_Node": "Nodo", "Core_RES_MapElement": "Elemento del mapa", "Core_RES_MapPreset": "Mapa preestablecido", "Core_RES_MapText": "Texto del mapa", "Core_RES_OutputChannel": "Canal de salida", "Core_RES_LayoutPreset": "Layout Preset", "Core_RES_Account": "C<PERSON><PERSON>", "Core_RES_AccountProfile": "Recorrido por el canal", "Core_RES_ChannelTour": "Channel Tour", "Core_RES_PTZPreset": "PTZ Preset", "Core_RES_ICN": "ICN", "Core_RES_VideoWall": "Pared de video", "Core_RES_ResourceGroup": "Grupo de recursos", "Core_RES_PTZPattern": "Patrón PTZ", "Core_RES_PTZAuxiliary": "PTZ auxiliar", "Core_RES_ProcedureTemplate": "Plantilla de procedimiento", "Core_RES_Sensor": "Sensor", "Core_RES_Sensor_Plural": "Sen<PERSON><PERSON>", "Core_RES_LightResourceGroup": "Grupo de recursos ligeros", "Core_RES_Geofence": "Geofence", "Core_RES_WebStreamer": "Streamer web", "Core_RES_PowerMeter": "Medidor de potencia", "toggleOutput": "Salida alternativa", "useInteractiveMode": "Usar modo Interactivo", "allowedFileTypes": "'Tipo de Archivo' permitido", "done": "Finalizado", "invalidFileType": "'Tipo de Archivo' inválido", "invalidFileSize": "Tamaño de archivo inválido", "maximumUploadSizeIs": "El tamaño máximo de carga permitido es", "chooseFiles": "Seleccionar Archivos", "failToUpdateServer": "Fallo en la actualización al servidor", "maxFileForInteractiveModeIs10": "El máximo de archivos permitidos para modo interactivo es de 10", "pleaseSelectAFile": "Por favor seleccione un archivo", "saved": "Guardado", "new": "Nuevo", "totalUpdated": "Todas las entidades fueron actualizadas", "totalAdded": "Todas las entidades fueron agregadas", "totalProcessedFiles": "Todos los archivos fueron procesados ", "next": "Siguient<PERSON>", "errorOnReadFiles": "Error en la lectura de archivos", "zone_name": "Nombre de zona", "rule_name": "Nombre de regla", "object_type": "Tipo de objeto", "truck_id": "Identificación de camión", "daily_pickups": "Recolección diaria", "addNewEntity": "Agregar nueva entidad", "addEntity": "Agregar entidad", "editEntity": "Editar En<PERSON>", "firstName": "Nombre", "lastName": "Apellido", "expirationDate": "<PERSON><PERSON> de caducidad", "startDateShouldBeBeforeExpirationDate": "La fecha de inicio tiene que ser anterior de la fecha de caducidad", "phone": "Teléfono", "email": "Correo electrónico", "deviceName": "Nombre de dispositivo", "deviceValue": "Valor del dispositivo", "associatedDevices": "Dispositivos asociados", "associatedResourceGroups": "Grupos de Recursos asociados", "fieldsMarkedWithAreMandatory": "Los campos marcados con * son obligatorios", "save": "Guardar", "systemDown": "Sistema inactivo", "primeryImage": "Imagen primaria", "secImage": "Imagen secundaria", "uploadFiles": "Cargar archivos", "choose": "Seleccione", "uploadConfigFile": "Cargar archivo de configuración", "fileUploadedSuccessfully": "Archivos cargados con éxito", "fileUploadFailed": "Fallo al cargar archivos", "upload": "<PERSON><PERSON>", "scheduler": "Planificar", "schedulerName": "Nombre del plan", "resourceGroups": "Grupo de recursos", "addSchedule": "Agregar un nuevo plan", "editSchedule": "Editar un plan existente", "duplicate": "Duplicar", "areYouSureYouWantToDeleteThisSchedule": "Estás seguro que desea eliminar esta planificación?", "priority": "Prioridad", "action": "Acción", "SpecificDay": "Diario", "entireMonth": "<PERSON><PERSON><PERSON>", "onceAYear": "<PERSON><PERSON>", "customDate": "<PERSON><PERSON><PERSON> fecha", "actionLevel": "Nivel de acción", "unableToLoadData": "Fallo en la carga de datos", "dateAndTime": "Día y Hora", "copy": "Copiar", "edit": "<PERSON><PERSON>", "rowUpdateFailed": "Fallo en la actualización de registro", "rowUpdatedSuccessfully": "Actualización registros con éxito", "inputchannel": "Canal de entrada", "state": "Entrada", "unknown": "Desconocido", "mapChanged": "Mapa actualizado", "mapRemoved": "Mapa Removido", "uploadAssetsFile": "Cargar archivo con lista de pertenencias", "assetsManagement": "Gestión de pertenencias", "procedures": "Procedimientos", "trafficDetections": "Detecciones de tráfico", "asset": "Pertenencia", "comments": "Comentarios", "reserveParking": "Reservar estacionamiento", "editReservation": "Editar Reservación", "addReservation": "Agregar reservación", "updatedResource": "Recurso actualizado", "startTimeShouldBeBeforeEndTime": "La hora de inicio debe ser anterior a la hora de de finalización", "areYouSureYouWantToDeleteThisReservation": "¿Esta seguro que desea eliminar la reservación?", "setLumenFactor": "Establecer factor de lumen", "setTrafficFactor": "Establecer factor de tráfico", "changeCompensationFactors": "Cambiar los factores de compensación", "ELECTRA_PARKING_USAGE": "Electra - Estacionamientos en uso", "ELECTRA_POINTGRAB_LAST_COUNT": "Electra - Pointgrab última actualización en recuento de presencia", "ELECTRA_POINTGRAB_USAGE": "Electra - Pointgrab recuento total de presencia", "uploadEntitiesFile": "Cargar archivos de entidades", "selectUniqueField": "Seleccionar archivo único", "undefined": "Indefinido", "dismissAll": "<PERSON><PERSON><PERSON> todo", "matchKeys": "Llave correlativas", "keys": "Llaves", "image": "Imagen", "mobilePhone": "Teléfono celular", "failedToParseInputFile": "Fallo en interpretación de archivo en carga", "CELLAV_BATTERY_LEVELS": "CELLAV - Nivel de baterías", "theAssetIsAlreadyReservedAtTheSelectedTimePleaseTryAnotherTime": "La selección está reservada con anterioridad a la misma hora, Por favor intente para otros tiempos.", "noAvailableParkingAtTheRequestedTime": "No hay estacionamiento disponible a la hora requerida.", "addNewResourceGroup": "Agregar nuevo grupo de recursos", "mandatoryFieldsCantBeUndefined": "Los campos obligatorios no pueden ser indefinidos", "totalIgnored": "El total es ignorado", "totalFailed": "El Total es fallido", "totalProcessedItems": "Total de artículos procesados", "StartRecording": "Comenzar la grabación", "ActivateRelay": "Activar paradas", "SetLightGroupStrength": "Establecer las potencias de Luz", "uniqueID": "unique ID", "allRequierdFieldsMustBeSelected": "Todos los archivos requeridos deberán ser seleccionados", "address": "Dirección", "status": "Estado", "symbol": "sí<PERSON>lo", "summary": "Resumen", "added": "<PERSON><PERSON><PERSON><PERSON>", "missingMandatoryFields": "Falta completar campos obligatorios", "alreadyExist": "En existencia", "downloadSummary": "<PERSON><PERSON><PERSON> resumen", "failureReason": "razón del fallo", "wrongFieldType": "Tipo de archivo erróneo", "reportGenerationCancelled": "Generación de reporte cancelado", "failedToOpenChannel": "Falla al abrir el canal", "generalDesk": "Mesa General", "generalRoom": "General Habitación", "generalParking": "Estacionamiento General", "dynamicAttributes": "Atributos dinámicos", "addNewEdFields": "Agregar nuevos campos editabbles", "displayLayout": "Visualizar capas de diseño", "cameras": "<PERSON><PERSON><PERSON><PERSON>", "videoWall": "Parede de vídeo", "saveLayout": "Guardar diseño", "layoutSelected": "<PERSON>seño selecci<PERSON>", "playerAdded": "<PERSON><PERSON><PERSON>", "makeFullScreen": "<PERSON><PERSON> pantalla completa", "showSideBar": "Mostrar barra lateral", "colNameCantContainDots": "La columna 'nombres' no puede contener puntos", "thereAreNoAvailableAssetsAtTheRequestedTime": "No hay pertenencias disponibles a la hora requerida", "userIsNotAllowedToReserveAssets": "El usuario no tiene permitido reservar bienes", "assetMaxTimeExceeded": "Máximo de tiempo excedido para planificar la toma de pertenencia", "selectedEntityAlreadyHasAReservationForTheRequestedTime": "La entidad ha sido seleccionada antes para la hora requerída", "marketPlace": "<PERSON><PERSON><PERSON>", "logoText": "CymbIoT ltd.", "startTime": "Hora de inicio", "endTime": "Hora final", "interval": "Intervalo", "on": "Abierta", "off": "<PERSON><PERSON><PERSON>", "OFF": "Abierta", "ON": "<PERSON><PERSON><PERSON>", "dayOfMonth": "Día del mes", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "<PERSON><PERSON>", "Custom": "Personalizar", "RepeatWeekly": "<PERSON><PERSON><PERSON>", "RepeatMonthly": "<PERSON><PERSON><PERSON> mensualmente ", "RepeatYearly": "<PERSON>etir anualmente", "Every": "Siempre", "Years": "<PERSON><PERSON><PERSON>", "selectCamera": "Selección de cámara", "addCol": "Agregar columna", "changeCol": "Cambiar columna", "deleteCol": "Bo<PERSON>r columna", "changedTo": "Cambiar a", "editColumns": "Editar columnas", "Core_RES_Subelement": "Subelementos", "selectAnOption": "Seleccionar una opción", "selectAGroup": "Seleccionar un grupo", "addToExistingResourceGroup": "Agrega a un grupo de recursos existentes", "createResourceGroup": "Crear un grupo de recursos", "deleteResourceGroup": "Borrar un grupo de recursos", "removeFromResourceGroup": "Remover de un grupo de recursos", "enterGroupName": "Agregar nombre de grupo", "resourceGroupManager": "Administrador del grupo de recursos", "openResourceGroupManager": "Abrir el administrador del grupo de recursos", "can'tDeleteAllResourcesFromAGroup": "No se pueden borrar todos los recursos pertenecientes a un grupo", "can'tDeleteResourcesFromUnassigned": "No se puede borrar recursos señalados como 'no asignados'", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "unselectAll": "<PERSON>elecci<PERSON><PERSON> todo", "createdSuccessfully": "Exitosamente creado", "resourcesAddedSuccessfully": "Los recursos fueron exitosamente agregados", "resourcesRemovedSuccessfully": "Los recursos fueron removidos exitosamente", "can'tAddResourcesOfDifferentTypeToAHomogeneousGroup": "No se pueden agregar recursos de diferentes tipos a un grupo homogéneo", "Granted Entry": "Enrtada autorizada", "Usage": "Utilización", "Hour": "<PERSON><PERSON>", "Free Zones": "Zonas des<PERSON>", "Used Zones": "Zona ocupadas", "Total Zones": "Total en zona", "device": "Dispositivo", "counter": "<PERSON><PERSON><PERSON>", "delta": "Delta", "id_counter": "Contador ID", "input": "Entrada", "connected": "Conectado", "addCamera": "<PERSON><PERSON><PERSON><PERSON>", "add": "Agregar", "profilesSelected": "{0} <PERSON><PERSON><PERSON>", "selectProfiles": "<PERSON><PERSON><PERSON><PERSON><PERSON> perfiles", "failedToChangeColumn": "Falla al cambiar el nombre de la columna", "failedToDeleteColumn": "falla al eliminar la columna", "failedToResetDevice": "Error al restablecer el dispositivo", "settings": "Ajustes y personalización", "openSettings": "Abrir ajustes personales", "ipAddress": "Direction IP", "httpPort": "Puerto HTTP", "rtspPort": "Puerto RTSP", "rtspLink": "Enlace RTSP", "deviceUnreachable": "Dispositivo sin respuesta", "formatException": "Formato erroneo", "insufficientLicense": "Licencia Insuficiente", "emptyValue": "Valor vacío", "labelAlreadyExists": "<PERSON><PERSON><PERSON><PERSON> existente", "resourceDown": "<PERSON><PERSON>rs<PERSON>", "resourceAlreadyAssigned": "Recurso previamente asignado", "invalidName": "Nombre inválido", "FailToAddRTSPDevice": "Falla al agregar dispositivo RTSP", "rtspUploader": "Cargador RTSP", "showVCAObjects": "Mostrar objetos VCA", "showVCAConfig": "Mostrar configuración VCA", "showPTZControls": "Mostrar controles PTZ", "languageOptions": "Opciones de lenguaje", "vcaOptions": "Opciones de analíticos en video", "groupedEvents": "Eventos agrupados", "openToggleTimeline": "Calendario de grabaciones", "closeToggleTimeline": "Cerrar escala cronológica", "entitySavedSuccessfully": "entidad guardada con éxito", "pleaseWait": "Por favor espere", "otherRequestsProgress": "Otro pedido está en proceso", "xAxis": "<PERSON><PERSON>", "yAxis": "<PERSON><PERSON>", "time_stamp_formatted": "Sello de tiempo formateado", "light_id": "Identificador de iluminación", "failure_msg": "<PERSON><PERSON><PERSON>", "line_counter_type": "Tipo de contador de linea", "LastUpdate": "Última Actualización", "rule_id": "Identificación de Regla", "zone_id": "Identificación de Zona", "old_counter": "<PERSON><PERSON><PERSON>", "extra_data": "Información extra", "active": "Activo", "message": "Men<PERSON><PERSON>", "tripwire": "Cable de enlace", "partial_view_change": "Cambio parcial de reproducción", "full_view_change": "Cambiar a reproduccióncion en pantalla completa", "output": "Salida", "signal": "Se<PERSON>l", "pir": "<PERSON><PERSON>", "audio_analytics_type": "Estilo de audio analítico", "input_channel": "Canal de entrada", "motion": "Movimiento", "vmd_area": "área <PERSON>", "user_logged": "Usuario conectado", "node": "Nodo", "hard_disk": "Disco Duro", "test": "Prueba", "geofence": "Geocerca", "geofence_id": "Identificación de Geocerca", "timer": "Cronómetro", "first_name": "Nombre", "last_name": "Apellido", "user_id": "Identificación de Usuario", "code": "Código", "concentrator_name": "Nombre del concentrador", "light_dim": "Atenuación de luz ", "light_power": "Potencia de luz", "light_strength": "Fuerza de la Luz", "light_energy": "Consumo energético", "light_power_factor": "Factor de potencia lumínico", "light_voltage": "Voltage de la Luz", "light_current": "Consumo de corriente de la luz", "light_working_hours": "Horas en uso de la Luz", "light_aux_power": "Potencia auxiliar de la luz", "light_aux_energy": "Energía auxiliar de la Luz", "sensor_type": "Tipo de sensor", "sensor_value": "Valor del sensor", "temperature": "Temperatura", "comm_quality": "Calidad de comunicación al dispositivo", "field_name": "Nombre del campo", "field_value": "Valor del campo", "vacancy": "Disponible", "empty": "Vacío", "entity_rule": "Regla para entidades", "backToLive": "Volver a video en vivo", "timeType": "Tipo de tiempo", "sunrise": "<PERSON><PERSON><PERSON>", "sunset": "<PERSON><PERSON><PERSON><PERSON>", "specificTime": "Hora específica", "beforeAfter": "después o antes (en minutos)", "nameInvalid": "Nombre inválido", "syncAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "unsync": "Asíncrono", "sync": "Sincronizar", "FireTrigger": "Disparar/Gatillar un trigger", "IoCommand": "Establecer estado de grupo", "guid": "GUID", "copyToClipboard": "Copiar al portapapeles", "copiedToClipboard": "Copiado al portapapeles", "LightWorkingHours": "Horas de trabajo de la luz", "LightId": "Identificador de la luz", "AlarmConfigurationText": "Configuración de alarma", "AlarmSeverityValue": "Alarma de severidad", "AlarmStatusValue": "Estado de alarma", "LightTemperature": "Temperatura de la luz", "IsAuxOn": "Está encendido el auxiliar de la luz", "AuxEnergy": "Energía auxiliar de la luz", "AuxPower": "Potencia auxiliar de la luz", "LightNominalPower": "Potencia nominal de la luz", "PhotoSensorPresence": "Presencia del sensor de foto", "vmsPageTitle": "<PERSON><PERSON><PERSON><PERSON>", "help": "<PERSON><PERSON><PERSON>", "searchBox": "Buscar", "changeTheme": "<PERSON><PERSON><PERSON> de te<PERSON>", "closeAll": "<PERSON><PERSON><PERSON>", "dayTheme": "Tema del día", "nightTheme": "Tema de la noche", "Temperature": "Temperatura", "LightStrength": "Fuerza de la luz", "LightPower": "Potencia de la luz", "LightEnergy": "Energia luminosa", "LightCommQuality": "Calidad de comunicación ligera", "LightFailure": "Falla de luz", "LightVoltage": "Voltaje de luz", "LightCurrent": "Corriente de luz", "LightPowerFactor": "Factor de potencia de luz", "customerDashboard": {"editDashboard": "Editar Dash<PERSON>", "addNewDashboard": "Agregar nuevo panel de control", "saveTemplateDashboard": "Guardar Dashboard como plantilla", "saveDashboard": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "addDashboardTitle": "Agregar nuevo panel de control", "editDashboardTitle": "Editar Dash<PERSON>", "addWidgetsTitle": "Agregar widgets", "selectHierarchyLevel": "Seleccione el nivel de jerarquía", "noDashboardParent": "Sin padre", "delete": "Eliminar Dashboard", "deleteConfirmationMessage": "Quieres borrar este panel ?", "deleteConfirmationHeader": "Borrar la confirmación del cuadro de mando ?", "noWidgetsAvailable": "No hay widgets disponibles.", "isOldDashboard": "Este es un tablero antiguo.", "noDashboardsAvailable": "No hay tableros disponibles.", "errorGettingDashboard": "Error al obtener el tablero", "editWidget": "<PERSON><PERSON>", "deleteWidget": "Eli<PERSON><PERSON> Widget", "widgetSize": {"map": {"xXBig": "Grande", "xBig": "Medio", "big": "Pequeña"}, "player": {"big": "Grande", "medium": "Medio"}, "dashbox": {"small": "Pequeña", "xMedium": "Medio"}, "gauge": {"xSmall": "Pequeña", "medium": "Medio", "big": "Grande"}, "notification": {"big": "Grande", "medium": "Medio", "tall": "Alta"}, "pieChart": {"big": "Grande", "medium": "Medio", "tall": "Alta"}, "lineChart": {"big": "Small", "wide": "Medio", "xWide": "Grande"}, "sensorStatus": {"big": "Grande"}, "emptySpace": {"xXBig": "Grande (5x5)", "xBig": "Grande (4x4)", "big": "Grande (3x3)", "medium": "Medio (2x2)", "xMedium": "Medio (1x3)", "small": "Pequeña (1x2)", "xSmall": "Pequeña (2x1)", "xXSmall": "Pequeña (1x1)", "tall": "Alta (4x2)", "xTall": "Alta (4x1)", "wide": "amplio (3x4)", "xWide": "amplio (3x5)"}, "embeddedFile": {"xXSmall": "Extra pequeño"}, "xXBig": "Grande (5x5)", "xBig": "Grande (4x4)", "big": "Grande (3x3)", "medium": "Medio (2x2)", "xMedium": "Medio (1x3)", "small": "Pequeña (1x2)", "xSmall": "Pequeña (2x1)", "xXSmall": "Pequeña (1x1)", "tall": "Alta (4x2)", "xTall": "Alta (4x1)", "wide": "amplio (3x4)", "xWide": "amplio (3x5)"}, "editWidgetTitle": "Editar widget", "editWidgetSize": "<PERSON><PERSON> ta<PERSON> del widget", "selectMap": "Seleccionar mapa", "selectResourceGroup": "Seleccionar del grupo de recursos", "itemsFound": "<PERSON><PERSON><PERSON><PERSON> encontrados", "Core_RES_InputChannel": "<PERSON><PERSON><PERSON><PERSON>", "Core_RES_Light": "<PERSON><PERSON>", "Core_RES_ALPRLane": "Canales de ALPR", "Core_RES_Dashboard": "Escritorio", "Core_RES_Device": "Dispositivo", "Core_RES_Input": "Entrada", "Core_RES_Output": "Salida", "Core_RES_Layout": "Diseño", "Core_RES_Map": "Mapa", "Core_RES_Entity": "Entidad", "Core_RES_Asset": "Activo", "Core_RES_LightGW": "Luz GW", "Core_RES_System": "Sistema", "selectGroup": "Selecciona grupo", "selectResource": "Sele<PERSON><PERSON>r recurso", "selectTrigger": "Seleccionar disparador", "showAllGroups": "Mostrar todos los grupos", "defaultMap": "Mapa predeterminado", "widgetType": {"status": "Estado", "map": "Mapa", "player": "Vídeo", "dashbox": "Cuadro de instrumentos", "timeline": "Cronograma", "chart": "Gráfico", "gauge": "Manómetro", "notification": "Panel de notificación", "pieChart": "Gráfico circular", "lineChart": "Gráfico de linea", "sensorStatus": "Estado del sensor", "embeddedFile": "Archivo incrustado", "urlShortcut": "Atajo de URL"}, "thresholdPoints": "Puntos de umbral", "minMaxMeasurmentValues": "Min y Max y valores de medición", "dataNotSet": "Datos no establecidos", "thresholdStart": "comienzo", "thresholdEnd": "fin", "thresholdEventName": "nombre del evento", "selectNotificationNumber": "Número de notificación", "chartDataType": "Tipo de <PERSON>", "selectDataType": "Seleccionar tipo de datos", "siteReadiness": "Estado del recurso", "tasks": "<PERSON><PERSON><PERSON>", "tasksUrgency": "<PERSON><PERSON><PERSON>", "dataIsZero": "No hay datos para mostrar", "pieChartLabels": {"open": "<PERSON>bie<PERSON>o", "closed": "<PERSON><PERSON><PERSON>", "inprogress": "En progreso", "important": "Importante", "low": "<PERSON><PERSON>", "normal": "Normal", "none": "Ninguna", "online": "En línea", "offline": "Desconectado", "free": "<PERSON><PERSON><PERSON>", "occupied": "Ocupado", "alarmed": "<PERSON><PERSON><PERSON>", "error": "Error", "saved": "Sal<PERSON><PERSON>", "on": "En", "off": "<PERSON><PERSON><PERSON>", "unknown": "Desconocido", "offbyradio": "Apagado por radio", "dim": "Oscuro", "offbypower": "<PERSON><PERSON><PERSON> por energía", "temperror": "Error temporal", "notspecified": "No especificado", "withoutcommunication": "Sin comunicacion"}, "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deselect": "Deseleccionar", "selectResourceState": "Seleccionar estado del recurso", "selectTimePeriod": "Seleccionar per<PERSON>do de tiempo", "Core_RES_Unknown": "Core RES Desconocido", "selectResourceType": "Seleccionar tipo de recurso", "selectColorClass": "Seleccionar color", "selectBakgroundColorClass": "Seleccionar color de fondo", "selectTextColorClass": "Seleccionar color de texto", "saveAsDefault": "Guardar por defecto", "displayMethod": "Método de visualización", "countResults": "Contar resultados", "resultValue": "Valor de resultado", "groupedValue": "Valor agrupado", "dashboardPriority": "Prioridad del panel", "displayInInventory": "Mostrar en inventario"}, "dim": "Oscuro", "offByRadio": "Apagado por radio", "checkLight": "Comprobar luz", "checklight": "Comprobar luz", "Checklight": "Comprobar luz", "NO COMM": "NO (Comprobar)", "offMaintenanceCheck": "DESACTIVADO (Comprobación de mantenimiento)", "OFF MAINTENANCE CHECK": "DESACTIVADO (Comprobación de mantenimiento)", "offParamsFluctuation": "DESACTIVADO (Fluctuación de parámetros)", "OFF PARAMS FLUCTUATION": "DESACTIVADO (Fluctuación de parámetros)", "onParamsFluctuation": "ACTIVADO (Fluctuación de parámetros)", "ON PARAMS FLUCTUATION": "ACTIVADO (Fluctuación de parámetros)", "SIM ISSUE": "Gateway sim no conectado a la red", "simIssue": "Gateway sim no conectado a la red", "sim issue": "Gateway sim no conectado a la red", "off maintenance check": "DESACTIVADO (Comprobación de mantenimiento)", "maintenance check": "ON (Comprobación de mantenimiento)", "maintenanceCheck": "ON (Comprobación de mantenimiento)", "MAINTENANCE CHECK": "ON (Comprobación de mantenimiento)", "CHECK LIGHT": "Comprobar luz", "offByPower": "<PERSON><PERSON><PERSON> por poder", "tempError": "Error temporal", "notSpecified": "No especificado", "withoutCommunication": "Sin comunicacion", "projectNameHe": "Nombre del proyecto He", "area": "Zona", "streetName": "Nombre de la calle", "poleId": "ID de poste", "poleHeight": "Altura del poste", "lampPower": "Poder de la lámpara", "lampModel": "<PERSON><PERSON> de la lámpara", "projectNameEn": "Nombre del proyecto En", "lampDriver": "Conductor <PERSON> la lámpara", "powerBoxId": "ID de la caja de alimentación", "pBoxNameHe": "P Nombre de la caja Él", "pBoxLatitude": "P Box Latitude", "pBoxLongitude": "Longitud de la caja P", "areaId": "ID del área", "areaHeb": "Area Heb", "projectLampId": "ID de la lámpara del proyecto", "streetId": "ID de la calle", "arm": "Brazo", "nrItemsSelected": "{0} <PERSON><PERSON><PERSON><PERSON>", "min": "Min", "max": "Max", "measurementUnit": "Unidad de medida", "formErrors": {"required": "Necesario", "startEndRequired": "Campos obligatorios de inicio y fin", "pattern": "El código no satisface el patrón"}, "dashboardSavedAsDefault": "Panel guardado como predeterminado", "deletedDefaultDashboard": "Panel predeterminado eliminado", "noDataWaitingForEvents": "Sin datos, esperando eventos", "micError": "No se pudo comenzar a grabar tu micrófono", "appMap": {"noMapsAvailable": "No hay mapas disponibles", "addMap": "Agregar mapa", "editMap": "<PERSON>ar mapa", "deleteMap": "Eliminar mapa", "saveAsDefault": "Guardar por defecto", "deletedDefaultMap": "Mapa predeterminado eliminado", "editLayers": "Capas", "tileSource": {"OSM": "<PERSON><PERSON><PERSON>", "SW": "Color del agua del estambre", "raster": "Trama"}, "saveMap": "Guardar mapa", "mapSaved": "Mapa guardado", "failedToSaveMap": "Error al guardar el mapa", "deleteConfirmationMessage": "Quieres borrar este mapa?", "deleteConfirmationHeader": "Eliminar confirmación de mapa?", "mapSavedAsDefault": "Mapa guardado como predeterminado", "selectTileSource": "Seleccionar fuente de Tyle", "mapName": "Nombre del mapa", "layers": "Capas", "manage": "Gestionar", "addNewLayer": "Agregar nueva capa", "layerName": "Nombre de capa", "selectResourceGroup": "Seleccionar grupo de recursos", "selectResource": "Sele<PERSON><PERSON>r recurso", "selectResourceState": "Seleccionar estado del recurso", "selectResourceType": "Seleccionar tipo de recurso", "availableDevices": "Recursos disponibles", "updateMapElement": "Actualizar elemento del mapa", "mapElementSaved": "Elemento del mapa guardado", "failedToUpdateMapElement": "Error al actualizar el elemento del mapa", "saveMapElement": "Guardar elemento del mapa", "deleteMapElement": "Eliminar elemento del mapa", "mapElementDeleted": "Elemento del mapa eliminado", "failedToDeleteMapElement": "Error al eliminar el elemento del mapa", "filter": "Filtrar", "groupActions": "Acciones grupales", "search": "Búsqueda de mapas", "searchSuccessful": "La búsqueda es exitosa", "searchHasResultsNumber": "Tienes {{resultsNumber}} resultados.", "noResultsSummary": "No aplicar filtros de búsqueda.", "resetSearch": "Restable<PERSON> b<PERSON>"}, "outdatedBrowser": "Actualiza a la última versión!", "outdatedBrowserTitle": "Tu navegador está desactualizado", "free": "Libre", "occupied": "Occupied", "alarmed": "Alarmed", "takeSnapshot": "Toma una foto", "finish": "Terminar", "update": "Actualizar", "setLightIntensity": "Establecer intensidad de luz", "setWeatherFactor": "Establecer el factor meteorológico", "editFactors": "Editar factores", "enableDisableGroups": "Activar / desactivar grupos", "compensatedValue": "Valor compensado", "setFactorsSucces": "Factores establecidos con éxito", "setLightIntensitySucces": "Establecer la intensidad de la luz se estableció con éxito", "setLightIntensityError": "No se puede establecer la intensidad de la luz.", "setLightIntensityFailure": "No se pudo establecer la intensidad de la luz", "openCamera": "<PERSON><PERSON><PERSON> abie<PERSON>", "addDevice": "<PERSON><PERSON>dir dispositivo", "inventory": {"inventory": "Inventario", "addDevice": "Agregar dispositivo", "editDevice": "Editar dispositivo", "results": "Resul<PERSON><PERSON>", "selectManufacturer": "Seleccionar fabricante", "selectModel": "Seleccionar modelo", "credentials": "Cartas credenciales", "httpPort": "Puerto HTTP", "rtspPort": "Puerto RTSP", "storageDrive": "Unidad de almacenamiento", "rtspLink": "Enlace RTSP", "deleteConfirmationMessage": "Quieres eliminar estos recursos?", "deleteConfirmationHeader": "Eliminar confirmación de recursos?"}, "resourceGroupManagerNew": {"resourceGroup": "Gerente de grupo de recursos", "newResourceGroup": "Crear un nuevo grupo de recursos", "editResourceGroup": "Editar grupo de recursos existente", "selectGroup": "Selecciona un grupo", "selectProfiles": "<PERSON><PERSON><PERSON><PERSON><PERSON> perfiles"}, "formValidation": {"field": "Se requiere campo!", "numberAndSymbolsNotAllowed": "Números y símbolos no están permitidos!", "portField": "El campo es obligatorio (ej .: 127.0.0.1)!", "ipAddressField": "no es una dirección IP válida!", "minFourCharacters": "Debe tener al menos 4 caracteres!"}, "swalMessages": {"selectEntity": "Seleccionar entidad", "selectEntityMessage": "El administrador de recursos requiere la selección de al menos una entidad. Haga una selección para continuar con su acción."}, "resetTable": "Restablecer tabla y filtros", "location": "Ubicación", "propertiesNoDataWasFound": "No se encontraron datos adicionales", "viewInfo": "Ver información", "refreshLightData": "Actualizar da<PERSON> de luz", "refreshLightDataSuccess": "Actualizar éxitos de datos ligeros", "refreshLightDataError": "Actualizar error de datos de luz", "refreshLightDataFailure": "No se pudieron actualizar los datos de la luz", "lat": "Latitud", "lng": "<PERSON><PERSON><PERSON>", "resourceType": "Tipo de recurso", "groups": "Grupos", "lampGUID": "GUID de la lámpara", "refreshData": "<PERSON><PERSON><PERSON><PERSON>", "importCSV": "Importar CSV", "jumpToMap": "Saltar al mapa", "jumpToInventory": "Saltar al inventario", "VCA5_LINECOUNTER_A": "VCA Línea que cruza A", "VCA5_DWELL": "VCA Habitar", "VCA5_PRESENCE": "VCA Presencia", "VCA5_ENTER": "VCA Entrar", "VCA5_EXIT": "VCA Salida", "VCA5_APPEAR": "VCA Aparecer", "VCA5_DISAPPEAR": "VCA Desaparecer", "VCA5_STOP": "VCA Detener", "VCA5_DIRECTION": "VCA Dirección", "VCA5_SPEED": "VCA Velocidad", "VCA5_TAILGATING": "VCA Chupar rueda", "VCA5_LINECOUNTER_B": "VCA Línea que cruza B", "VCA5_ABOBJ": "VCA Abobj", "VCA5_SMOKE": "VCA Fumar", "VCA5_FIRE": "VCA Fuego", "VCA5_COLSIG": "VCA ColSig", "VCA5_UNKNOWN": "VCA Desconocido", "VCA5_PRESENCE_End": "VCA Fin de presencia", "VCA5_ENTER_End": "VCA Ingrese Fin", "VCA5_EXIT_End": "VCA Fin de salida", "VCA5_APPEAR_End": "VCA Aparecer Fin", "VCA5_DISAPPEAR_End": "VCA Desaparecer Fin", "VCA5_STOP_End": "VCA Stop End", "VCA5_DWELL_End": "VCA Fin de morada", "VCA5_DIRECTION_End": "VCA Fin de la dirección", "VCA5_SPEED_End": "VCA Fin de velocidad", "VCA5_TAILGATING_End": "VCA Final de chupar rueda", "VCA5_LINECOUNTER_A_End": "VCA Contador de línea A Fin", "VCA5_LINECOUNTER_B_End": "VCA Contador de línea B Fin", "VCA5_ABOBJ_End": "VCA Fin de Abobj", "VCA5_RMOBJ_End": "VCA Rmobj End", "VCA5_SMOKE_End": "VCA Fin de humo", "VCA5_FIRE_End": "VCA Fuego final", "VCA5_COLSIG_End": "VCA Final de colsing", "VCA5_UNKNOWN_End": "VCA Fin desconocido", "selectFieldName": "Seleccionar nombre de campo", "selectOperand": "Seleccionar operando", "freeText": "Texto libre", "reset": "Reiniciar", "operand": {"equals": "Igual", "notEquals": "No es igual", "contains": "<PERSON><PERSON><PERSON>", "notContains": "No contiene"}, "selectedRows": "filas selecci<PERSON>", "selectedRow": "fila seleccionada", "deviceNotSelected": "Sensor no seleccionado", "eventName": "Nombre del evento", "total": "Total", "allEvents": "Todos los eventos", "selectEvent": "Seleccionar evento", "resetResource": "Restablecer recurso", "resetResourceSuccess": "Restablecer recurso con éxito", "resetResourceError": "Restablecer error de recurso", "successfullyDownloaded": "<PERSON><PERSON><PERSON> con éxito!", "downloadError": "No se puede descargar!", "openNewTab": "Abrir una nueva pestaña", "generatingReportCancelled": "Generando informe cancelado", "MinVoltage": "<PERSON><PERSON><PERSON>", "MaxVoltage": "Voltaje m<PERSON>xi<PERSON>", "MinTotalPower": "Potencia total mínima", "eventId": "ID del evento", "MaxTotalPower": "Potencia total máxima", "MinVoltageBetweenPhases": "Tensión mínima entre fases", "MaxVoltageBetweenPhases": "Tensión máxima entre fases", "MinPowerFactor": "Factor de potencia mínimo", "MaxPowerFactor": "Factor de potencia máximo", "Line1ActiveWatts": "Línea 1 vatios activos", "Line2ActiveWatts": "Línea 2 vatios activos", "Line3ActiveWatts": "Línea 3 vatios activos", "ActiveWattHours": "Horas de vatios activos", "Line1ApparentVoltsAmps": "Línea 1 Amperios de voltios aparentes", "Line2ApparentVoltsAmps": "Línea 2 Amperios de voltios aparentes", "Line3ApparentVoltsAmps": "Línea 3 Amperios de voltios aparentes", "ApparentVoltAmpHours": "Horas aparentes de amperios de voltios", "CombinedActiveWatts123": "Vatios activos combinados 123", "CombinedApparentWatts123": "Vatios aparentes combinados 123", "CombinedPowerFactor123": "Factor de potencia combinado 123", "CombinedReactiveWatts123": "Vatios reactivos combinados 123", "AMC_RESOURCE_STATUS": "Estado de recurso AMC", "AMC_ENVIRONMENT_DUST_PM1": "Concentración de polvo PM1", "AMC_ENVIRONMENT_DUST_PM25": "Concentración de polvo  PM 2.5", "AMC_ENVIRONMENT_DUST_PM10": "Concentración de polvo  PM 10", "AMC_ENVIRONMENT_SO2_LEVEL": "Nivel de SO2", "AMC_ENVIRONMENT_NO2_LEVEL": "Nivel de NO2", "AMC_ENVIRONMENT_O3_LEVEL": "Nivel de O3", "AMC_PRESENCE_STATS": "Estado de presencia", "AMC_PROFILE": "Perfil", "AMC_STATE": "Estado", "AMC_AREA_ID": "ID de área", "AMC_RULE_NAME": "Nombre de la regla", "AMC_VIDEO_SOURCE": "Fuente de vídeo", "AMC_SOURCE": "Fuente", "AMC_MOTION_TYPE": "Tipo de movimiento", "AMC_VIDEO_ANALYTICS": "Análisis de video", "AMC_DOOR_STATE": "Estado de la puerta", "AMC_ENTITY_FIRST_NAME": "Nombre", "AMC_ENTITY_LAST_NAME": "Apellido", "AMC_ENTITY_MIN_AGE": "<PERSON><PERSON>", "AMC_ENTITY_MAX_AGE": "<PERSON><PERSON>", "AMC_ENTITY_GENDER": "<PERSON><PERSON><PERSON>", "AMC_ENTITY_USER_ID": "ID de usuario", "AMC_ENTITY_CONFIDENCE": "Confianza", "AMC_DETECTION_TIME": "Tiempo de detección", "AMC_SOURCE_DESCRIPTION": "Descripción de la fuente", "AMC_SHOCK": "Choque", "PEOPLE_COUNT": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>s", "AMC_SENSOR_CO": "Nivel de CO", "PERSON_STATS": "Estadísticas de persona", "VEHICLE_STATS": "Estadísticas de vehículo", "FACE_STATS": "Estadísticas de rostro", "AMC_IO_ALARM_OUTPUT": "Salida de alarma de IO AMC", "AMC_IO_ANALOG_VALUE": "Valor analógico de IO AMC", "AMC_IO_VALUE_0_VOLT": "Valor de IO 0 Volt AMC", "AMC_IO_VALUE_10_VOLT": "Valor de IO 10 Volt AMC", "AMC_IO_COMMUNICATION_STATUS": "Estado de comunicación de IO AMC", "AMC_IO_COMMUNICATION_MODE": "Modo de comunicación de IO AMC", "AMC_IO_ALARM_TEXT": "Texto de alarma de IO AMC", "AMC_IO_ALARM_ID": "ID de alarma de IO AMC", "AMC_IO_ALARM_SEVERITY": "Severidad de alarma de IO AMC", "Line1Current": "Línea 1 Corriente", "Line2Current": "Línea 2 Corriente", "Line3Current": "Línea 3 Corriente", "NeutralLineCurrent": "Corriente de línea neutra", "Line1PowerFactor": "Factor de potencia de línea 1", "Line2PowerFactor": "Factor de potencia de línea 2", "Line3PowerFactor": "Factor de potencia de Line 3", "Line1VoltsAmpsReactive": "Línea 1 Voltios Amperios Reactivo", "Line2VoltsAmpsReactive": "Línea 2 Voltios Amperios Reactivo", "Line3VoltsAmpsReactive": "Línea 3 Voltios Amperios Reactivo", "Line3ToLine1Voltage": "Voltaje de línea 3 a línea 1", "Line1ToLine2Voltage": "Voltaje de línea 1 a línea 2", "Line2ToLine3Voltage": "Voltaje de línea 2 a línea 3", "Line1Voltage": "Voltaje de línea 1", "Line2Voltage": "Voltaje de línea 2", "Line3Voltage": "Voltaje de línea 3", "NeutralCurrent": "Corriente neutral", "p1": "P1", "p2": "P2", "p3": "P3", "p4": "P4", "p5": "P5", "p6": "P6", "p7": "P7", "p8": "P8", "p9": "P9", "p10": "P10", "p11": "P11", "p12": "P12", "p13": "P13", "p14": "P14", "p15": "P15", "p16": "P16", "p17": "P17", "p18": "P18", "p19": "P19", "p20": "P20", "p21": "P21", "p22": "P22", "p23": "P23", "p24": "P24", "p25": "P25", "p26": "P26", "p27": "P27", "p28": "P28", "p29": "P29", "p30": "P30", "lamptype": "Tipo de lámpara", "dimmingRequest": "Solicitud de atenuación", "refreshPowerMeterData": "Actualizar datos del medidor de potencia", "refreshPowerMeterDataSuccess": "Actualice los datos del medidor de energía correctamente", "refreshPowerMeterDataError": "Actualizar error de datos del medidor de potencia", "refreshPowerMeterDataFailure": "No se pudieron actualizar los datos del medidor de potencia", "addedScheduler": "Program<PERSON> a<PERSON>", "cannotAddSchedule": "No se puede agregar el horario", "deletedScheduler": "Planificador eliminado", "updatedScheduler": "Programador actualizado", "cannotUpdateSchedule": "No se puede actualizar el planificador", "onDeletedScheduler": "Eliminar planificador?", "addedRtsp": "RTSP agregado", "cannotAddRtsp": "No se puede agregar RTSP", "selectTypeOfTheFile": "Seleccione el tipo de archivo", "CSVUpload": "Subir CSV", "rtspChannels": "Canales RTSP", "dropFileOrClickToUpload": "Suelte el archivo o haga clic para cargar", "PowerMeter_MinVoltage": "PM <PERSON>", "PowerMeter_MaxVoltage": "PM <PERSON>", "PowerMeter_MinTotalPower": "PM <PERSON>", "PowerMeter_MaxTotalPower": "PM <PERSON> TP", "PowerMeter_MinVoltageBetweenPhases": "PM Min V BP", "PowerMeter_MaxVoltageBetweenPhases": "PM Max V BP", "PowerMeter_MinPowerFactor": "PM <PERSON>", "PowerMeter_MaxPowerFactor": "PM <PERSON>", "PowerMeter_Line1ActiveWatts": "PM L1 AW", "PowerMeter_Line2ActiveWatts": "PM L2 AW", "PowerMeter_Line3ActiveWatts": "PM L3 Aw", "PowerMeter_ActiveWattHours": "PM Active WH", "PowerMeter_Line1ApparentVoltsAmps": "PM L1 AVA", "PowerMeter_Line2ApparentVoltsAmps": "PM L2 AVA", "PowerMeter_Line3ApparentVoltsAmps": "PM L3 AVA", "PowerMeter_ApparentVoltAmpHours": "PM AVAH", "PowerMeter_CombinedActiveWatts_123": "PM CActW 123", "PowerMeter_CombinedApparentWatts_123": "PM CAppW 123", "PowerMeter_CombinedPowerFactor_123": "PM CPowF 123", "PowerMeter_CombinedReactiveWatts_123": "PM CReaW 123", "PowerMeter_Line1Current": "PM L1C", "PowerMeter_Line2Current": "PM L2C", "PowerMeter_Line3Current": "PM L3C", "PowerMeter_NeutralLineCurrent": "PM NLC", "PowerMeter_Line1PowerFactor": "PM L1PF", "PowerMeter_Line2PowerFactor": "PM L2PF", "PowerMeter_Line3PowerFactor": "PM L3PF", "PowerMeter_Line1VoltsAmpsReactive": "PM L1VAR", "PowerMeter_Line2VoltsAmpsReactive": "PM L2VAR", "PowerMeter_Line3VoltsAmpsReactive": "PM L3VAR", "PowerMeter_Line3ToLine1Voltage": "PM L3-L1-V", "PowerMeter_Line1ToLine2Voltage": "PM L2-Li-V", "PowerMeter_Line1ToLine3Voltage": "PM L1-L3-V", "PowerMeter_Line1Voltage": "PM L1 V", "PowerMeter_Line2Voltage": "PM L2 V", "PowerMeter_Line3Voltage": "PM L3 V", "PowerMeter_NeutralCurrent": "PM <PERSON>", "PowerMeter_Failure": "PM F", "showResourcesFromGroup": "Mostrar recursos del grupo de recursos", "acknowledgeClick": "Haga clic para reconocer", "dismiss": "<PERSON><PERSON><PERSON>", "acknowledge": "Acknowledge", "enableAutoAcknowledgeEvents": "Habilitar reconocimiento automático", "enableAutoClearEvents": "Habilitar borrado automático", "noElementsForSnapshotFound": "No se encontraron elementos para la instantánea", "deleteResource": "El recurso fue eliminado", "cannotDeleteResources": "Los recursos no se pueden eliminar", "StatusChange": "Cambio de estado", "resourceStatus": "Estado del recurso", "LightColor": "Color claro", "LeditechStatusCode": "Código de estado de Leditech", "ackEventSuccess": "Reconocer el éxito del evento", "ackEventError": "Confirmar error de evento", "widgetOutOfBounds": "Widget eliminado porque no cabe en el tablero", "icon-view-1": "Presentar para 1 pantalla", "icon-view-4": "Presente para 4 pantallas", "icon-view-9": "Presente para 9 pantallas", "icon-view-10": "Presente para 10 pantallas", "showMore": "Mostrar más", "showLess": "<PERSON><PERSON><PERSON> men<PERSON>", "default": "Defecto", "low": "<PERSON><PERSON>", "normal": "Normal", "important": "Importante", "urgent": "Urgente", "userName": "Nombre de usuario", "enabled": "Habilitado", "remove": "Eliminar", "selectCSVColumn": "Seleccionar columna CSV", "REPORT_COUNT": "Recuento de informes", "active_watt_hours": "Horas de vatios activos", "apparent_volt_amp_hours": "Voltios amperios aparentes", "combined_active_watts_123": "Vatios activos combinados 123", "combined_apparent_watts_123": "Vatios aparentes combinados 123", "combined_power_factor_123": "Factor de potencia combinado 123", "combined_reactive_watts_123": "Vatios reactivos combinados 123", "line1_active_watts": "Line1 vatios activos", "line1_apparent_volt_amps": "Amperios de voltios aparentes Line1", "line1_current": "Corriente Line1", "line1_power_factor": "Factor de potencia Line1", "line1_to_line2_voltage": "Voltaje de línea 1 a línea 2", "line1_voltage": "Voltaje Line1", "line1_volts_amps_reactive": "Línea 1 voltios amperios reactiva", "line2_active_watts": "Line2 vatios activos", "line2_apparent_volt_amps": "Line2 voltios aparentes amperios", "line2_current": "Corriente Line2", "line2_power_factor": "Factor de potencia Line2", "line2_to_line3_voltage": "Line2 a line3 voltaje", "line2_voltage": "Voltaje Line2", "line2_volts_amps_reactive": "Línea 2 voltios amperios reactiva", "line3_active_watts": "Line3 vatios activos", "line3_apparent_volt_amps": "Line3 voltios aparentes amperios", "line3_current": "Corriente Line3", "line3_power_factor": "Factor de potencia Line3", "line3_to_line1_voltage": "Line3 a line1 voltaje", "line3_voltage": "Voltaje Line3", "line3_volts_amps_reactive": "Línea 3 voltios amperios reactiva", "max_power_factor": "Factor de potencia máximo", "max_total_power": "Potencia total máxima", "max_voltage": "Voltaje m<PERSON>xi<PERSON>", "max_voltage_between_phases": "Voltaje máximo entre fases", "min_power_factor": "Factor de potencia mínimo", "min_total_power": "Potencia total mínima", "min_voltage": "Voltaje min", "min_voltage_between_phases": "Voltaje m<PERSON> entre fases", "neutral_current": "<PERSON><PERSON><PERSON> neutra", "neutral_line_current": "Corriente de línea neutra", "LEDITECH_CONSUMPTION": "El consumo de energía", "number_items": "Número de artículos", "select_item": "<PERSON><PERSON><PERSON><PERSON><PERSON> artic<PERSON>", "generateChart": "<PERSON><PERSON>", "data_values": "Valores de datos", "PowerMeter_AverageVoltage": "PM Avg V", "PowerMeter_AveragePowerFactor": "PM Avg PF", "PowerMeter_TotalPower": "PM Total P", "PowerMeter_Line2ToLine3Voltage": "PM L2-L3-V", "Core_RES_Light_Plural": "<PERSON><PERSON>", "Core_RES_Input_Plural": "entradas", "Core_RES_Output_Plural": "Producción", "Core_RES_PowerMeter_Plural": "Medidores de potencia", "Core_RES_InputChannel_Plural": "Canales de entrada", "link": "Enlace", "invalidDomain": "La URL debe contener el dominio local", "reportResults": "resultados del informe", "gridColumnSize": "Tamaño de columna de cuadrícula", "dashboardHasDifferentResolution": "Este panel fue creado para {{width}} x {{height}} tama<PERSON> de pantalla", "unableToSaveDashboardDueToScrollInfo": "El panel no se guardará si supera el tamaño de la pantalla vertical", "unableToSaveDashboardDueToScrollError": "El panel no se puede guardar porque supera el tamaño de la pantalla vertical", "cameraAlreadyInUse": "La cámara ya está en uso", "dateIsRequired": "Debe seleccionar una fecha", "tableView": "Vista de tabla", "mapView": "Vista del mapa", "viewType": "Tipo de vista", "dashboxResourcesView": "Vista de recursos del cuadro de mando", "dismissEventSuccess": "Descartar el éxito del evento", "dismissEventError": "Ignorar error de evento", "alreadyAckNotification": "Ya reconoces este evento", "AMC_LPR_DATE_TIME": "hora de fecha LPR", "AMC_LPR_PLATE_NO": "LPR plate number", "AMC_LPR_PLATE_COUNTRY": "País de placa LPR", "AMC_LPR_PLATE_CONFIDENCE": "Confianza de la placa LPR", "AMC_LPR_MOVE_DIRECTION": "Dirección de movimiento LPR", "AMC_LPR_MS_IMAGE_PROCESSING": "LPR image processing", "AMC_LPR_VEHICLE_BRAND": "Procesamiento de imágenes LPR", "AMC_LPR_VEHICLE_MODEL": "Modelo de vehículo LPR", "AMC_LPR_VEHICLE_TYPE": "Tipo de vehículo LPR", "AMC_LPR_VEHICLE_COLOR": "Color del vehículo LPR", "AMC_LPR_VEHICLE_CONFIDENCE": "Confianza del vehículo LPR", "AMC_LPR_SOURCE_CAMERA_NAME": "Nombre de la cámara de origen LPR", "AMC_LPR_SOURCE_CAMERA_ADDRESS": "dirección de la cámara LPR", "AMC_PERSON_STATS": "estadísticas de persona", "AMC_VEHICLE_STATS": "Estadísticas de vehículos", "AMC_PEOPLE_COUNTING": "conteo de personas", "detectionList": {"detectionList": "Detecciones de tráfico", "camera": "<PERSON><PERSON><PERSON>", "brand": "<PERSON><PERSON>", "color": "Color", "country": "<PERSON><PERSON>", "licensePlate": "Placa", "vehicleModel": "<PERSON><PERSON> de ve<PERSON>í<PERSON>lo", "vehicleType": "Tipo de vehiculo", "cameraTime": "Tiempo de cámara", "plateConfidence": "Confianza en el plato", "vehicleConfidence": "Confianza del vehículo", "platePhoto": "Foto de placa", "downloadPhotos": "Descargar fotos", "downloadFailed": "Error al descargar fotos", "platePhotoDeleted": "Se eliminó la foto de la placa", "platePhotoFailedToDownload": "No se pudo descargar la foto de la placa", "detectionPhotoDeleted": "Se eliminó la foto de detección", "detectionPhotoFailedToDownload": "No se pudo descargar la foto de detección", "actions": "Comportamiento", "viewOnMap": "Ver en el mapa", "missingDetectionPhoto": "Falta la foto de detección", "searchSpecific": "¿Estás buscando algo en particular?", "reset": "Restablecer filtros", "noTrafficDetectionsFound": "No se encontraron detecciones de tráfico"}, "follow": {"follow": "<PERSON><PERSON><PERSON>", "items": "Elementos", "location": "Ubicación GPS", "missingPlateImage": "Falta la imagen de la placa", "newDetectionHasOccurred": "Se ha producido una nueva detección"}, "vehicleTraffic": {"back": "Atrás", "pollutionStandardBroken": "Este vehículo ha roto el estándar de contaminación permitido de la zona.", "accessFeeNotPayed": "Este vehículo ha entrado en esta área sin pagar la tarifa de acceso.", "licenseExpiredOrSuspended": "La licencia de conducir del conductor titular de este vehículo está vencida o suspendida", "vignetteNotPayed": "Este vehículo no tiene una viñeta activa", "technicalInspectionExpired": "La inspección técnica de este vehículo ha expirado", "vehicleTaxNotPayed": "Los impuestos de este vehículo no se pagan hasta la fecha"}, "detectionsChart": {"notEnoughData": "No hay suficientes datos para mostrar un gráfico.", "detectionsInInterval": "detecciones en intervalo"}, "parkingDetections": {"parkingDetections": "Detecciones de estacionamiento", "paidAccess": "El acceso ha sido pagado", "status": "Estado"}, "playerBackgroundImage": "Imagen de fondo de los jugadores", "resetBackgroundImage": "Restablecer la imagen de fondo de los jugadoress", "signalingServerDown": "El servidor de señalización no funciona !", "procedureStep": "Procedimiento", "jumpToProcedures": "Saltar a procedimientos", "generalDetections": {"generalDetections": "Detecciones generales", "detectionId": "Número de ID", "description": "Descripción", "timeStamp": "Marca de tiempo", "inputChannel": "Canal de entrada", "startDate": "Fecha de inicio", "endDate": "Fecha de finalización", "entityId": "ID de entidad", "plateId": "ID de placa", "downloadArchive": "Descargar archivo", "jumpToTrafficDetections": "Saltar a detecciones de tráfico", "followOnTrafficDetections": "Seguimiento de detecciones de tráfico", "jumpToDossier": "Saltar al dossier", "generalDetection": "Detección general", "reset": "Restablecer filtros", "actions": "Acciones", "noGeneralDetectionsFound": "No se encontraron detecciones generales"}, "welcomeBack": "¡Bienvenido de nuevo!", "enterLoginDetails": "Por favor ingrese los detalles de inicio de sesión a continuación", "enterSystemIP": "Ingrese la IP del sistema", "enterUsername": "Ingrese el nombre de usuario", "enterPassword": "Ingrese la contraseña", "signIn": "In<PERSON><PERSON>", "version": "Versión", "notificationsPage": {"totalRecordsFormat": "{totalRecords} Alertas totales", "value": "Valor"}, "car": "Auto", "truck": "Camión", "suv": "Suv", "van": "Furgoneta", "lcv": "Furgoneta", "lastOpenedCameras": "Abierto por última vez", "layouts": "Diseños", "systemStructure": "Estructura del Sistema", "online": "En línea", "offline": "Desconectado", "cameraStatusHistory": "Historial de Estado de Cámaras", "clearHistory": "<PERSON><PERSON><PERSON>", "noCommunication": "Sin Comunicación", "actualizationIn": "Actualización automática en", "sec": "seg", "hello": "<PERSON><PERSON>", "alerts": "<PERSON><PERSON><PERSON>", "alarms": "<PERSON><PERSON><PERSON>", "warnings": "Advertencias", "dispatch": "<PERSON><PERSON><PERSON>", "camera": "<PERSON><PERSON><PERSON>", "detections": "Detecciones", "loading": "Cargando...", "playback": "Reproducir", "downloadArchive": "Descargar Archivo", "sumarAlarmeUltimele72Ore": "Resumen de alarmas de las últimas 72 horas", "unresolvedAlarms": "<PERSON><PERSON><PERSON> sin resolver", "nonconformingAlarms": "Alarmas no conformes", "alarmsVerified": "<PERSON><PERSON><PERSON> verifica<PERSON>", "pm": "PM", "searchButton": "Buscar", "searchByCamera": "Buscar por cámara", "dossiers": "Expedientes", "dossierTable": {"title": "Dosier", "searchByName": "Buscar por nombre", "readOnly": "Solo lectura", "all": "Todos", "onlyDossiers": "Solo expedientes", "onlyArchives": "Solo archivos", "startTime": "Hora de inicio", "endTime": "Hora de fin", "autoDelete": "<PERSON><PERSON><PERSON>", "name": "Nombre", "timestamp": "Marca de tiempo", "actions": "Acciones", "viewDetections": "Ver detecciones", "toggleAutoDelete": "Alternar borrado <PERSON>má<PERSON>", "uploadDossier": "Subir archivo", "viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "noDossiersFound": "No se encontraron expedientes", "showingEntries": "Mostrando {first} a {last} de {totalRecords} expedientes", "autoDeleteTrue": "Eliminación automática habilitada (verdadero)", "autoDeleteFalse": "Eliminación automática habilitada (falso)", "reset": "Restablecer filtros", "alreadyDeleted": "Expediente ya eliminado: {{timestamp}}", "deletionTime": "Hora de eliminación"}, "dossierHistory": {"title": "Historial", "search": "Buscar...", "startTime": "Hora de inicio", "endTime": "Hora de fin", "details": "Detalles", "timestamp": "Marca de tiempo", "actions": "Acciones", "download": "<PERSON><PERSON><PERSON>", "noHistoryFound": "No se encontró historial", "showingEntries": "Mostrando {first} a {last} de {totalRecords} entradas", "downloadError": "Error al descargar el expediente", "loadError": "Error al cargar el historial del expediente", "noDossierId": "No se encontró ningún ID de expediente para eliminar", "dossierDeletedSuccessfully": "<PERSON><PERSON><PERSON> deleted successfully", "failedDeleteDossier": "Dossier eliminado con éxito", "noEventIdNavigation": "No se encontró ningún ID de evento para la navegación", "noEventIdDownload": "No se encontró ningún ID de evento para descargar", "failedToLoadDossiers": "No se pudieron cargar los expedientes", "failedToLoadDossierDetails": "No se pudieron cargar los detalles del expediente", "failedToggleReadOnly": "No se pudo alternar entre solo lectura", "failedToggleAutoDelete": "No se pudo activar la eliminación automática"}, "dossierDetails": {"title": "Detalles del expediente", "basicInformation": "Información básica", "id": "ID", "eventId": "ID del evento", "name": "Nombre", "creationTime": "Fecha de creación", "status": "Estado", "autoDelete": "<PERSON><PERSON><PERSON>", "enabled": "Habilitado", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "readOnly": "Solo lectura", "yes": "Sí", "no": "No", "liveDuration": "Duración en vivo", "archiveDuration": "Duración del archivo", "hours": "horas", "storage": "Almacenamiento", "path": "<PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "details": "Detalles", "timeStamp": "Marca de tiempo", "fileList": "Lista de archivos"}, "dossierUpload": {"noFileSelected": "No hay ningún archivo seleccionado", "fileUploadSuccessfully": "¡Archivo cargado exitosamente!", "invalidFileType": "Tipo de archivo no válido. Seleccione un archivo compatible: {{fileTypes}}"}, "dossier": {"autoDelete": "Eliminación automática {{status}}", "readOnly": "S<PERSON>lo lectura {{status}}", "statusOptions": {"enabled": "activado", "disabled": "desactivado"}}, "audit": {"title": "REGISTRO/Auditoría"}, "auditFilter": {"searchSpecific": "¿Estás buscando algo en particular?", "searchMessage": "Buscar por mensaje", "searchUser": "Buscar por usuario", "searchIP": "Buscar por dirección IP", "startDate": "Fecha de inicio", "endDate": "<PERSON><PERSON> de fin", "reset": "Restablecer"}, "auditTable": {"name": "Usuario", "action": "Acción", "message": "Men<PERSON><PERSON>", "timeStamp": "Marca de tiempo", "ipAddress": "Dirección IP", "noAuditsFound": "No se encontraron auditorías"}, "generalDetectionsDetails": {"type": "Tipo", "timestamp": "Marca de tiempo", "actions": "Acciones", "ack": "Reconocer", "closeDetails": "<PERSON><PERSON><PERSON>", "noDetectionsDetailsFound": "No se encontraron detalles de las detecciones"}, "alertsAlarms": {"plateNumber": "Número de placa", "eventName": "Nombre del evento", "noData": "Sin datos"}, "totalAlerts": "Total de alertas", "totalAlarms": "Total de alarmas", "totalWarnings": "Total de advertencias", "switchPosition": "Posición del interruptor", "watermark": {"user": "Usuario", "date": "<PERSON><PERSON>"}, "infoNote": "Nota informativa", "infoNoteDescription": "Adaugat in portal nota de informare cu caracter personal", "viewInfoPage": "Ver página de información", "accept": "Aceptar", "ai": {"title": "IA", "content": "Contenido de IA", "loading": "Cargando contenido de IA...", "error": "Error al cargar contenido de IA. Por favor, inténtelo de nuevo más tarde."}, "noValue": "No disponible", "search": "Buscar", "selectFromResourceGroup": "Seleccionar del grupo de recursos", "selectChannelTour": "Seleccionar tour de canal", "tour": "Tour", "videoWallSettings": "Configuración de videowall", "layout": "Diseño", "export": "Exportar", "exporting": "Exportando", "exportToHTML": "Exportar a HTML", "weatherInfo": {"humidity": "Humedad", "pressure": "Presión", "title": "Información del clima", "loadingWeatherData": "Cargando datos del clima.", "loadingWeatherDataError": "No se pudieron cargar los datos meteorológicos."}}