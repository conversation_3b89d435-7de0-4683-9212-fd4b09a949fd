import { Component, OnInit, ViewChild, HostListener } from '@angular/core';
import { VideoWallService } from '../../shared/services/video-wall.service';
import { NavigationService, Pages } from '../../shared/services/navigation.service';
import { Router } from '@angular/router';
import { AuthService } from '../../shared/services/auth.service';
import { SettingsComponent } from './../../shared/components/settings-page/settings.component';
import { LocalStorageService } from '../../shared/services/local-storage.service';
import { MainLayoutService } from '../../shared/services/main-layout.service';
import { GlobalConstants } from '../../shared/modules/global-constants';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent implements OnInit {
  @ViewChild("settingsModal", { static: false }) private settingsModal: SettingsComponent;
  videoWallCount: number = 0;
  pages = Pages;
  videoWallsCountStorage = "VIDEO_WALL_COUNT";
  aiLinkExists: boolean = false;
  isSmallScreen: boolean = false;
  isSidebarExpanded: boolean = false;
  screenWidth: number;
  windowSize = GlobalConstants[1280];
  

  @HostListener('window:resize', ['$event'])
  onResize(_event: any): void {
    this.checkScreenSize();
  }

  constructor(
    private videoWallService: VideoWallService,
    private navigationService: NavigationService,
    private router: Router,
    private authService: AuthService,
    private localStorageService: LocalStorageService,
    private mainLayoutService: MainLayoutService) { 
    this.screenWidth = window.innerWidth;
    this.checkScreenSize();
  }

  ngOnInit(): void {
    this.videoWallService.getVideoWalls().subscribe(videoWalls => {
      if (videoWalls && videoWalls.length) {
        this.videoWallCount = videoWalls.length;
        localStorage.setItem(this.videoWallsCountStorage, this.videoWallCount.toString());
      }
    });
    this.aiLinkExists = this.isAiLinkExist();

    // Subscribe to sidebar expanded state
    this.mainLayoutService.sidebarExpanded$.subscribe((expanded: boolean) => {
      this.isSidebarExpanded = expanded;
    });
  }

  checkScreenSize(): void {
    this.screenWidth = window.innerWidth;
    this.isSmallScreen = this.screenWidth < this.windowSize;
  }

  toggleSidebar(): void {
    this.mainLayoutService.toggleSidebar();
  }

  isAllowedPage(page:string): boolean
  {
    return this.authService.isAllowedPage(page);
  }

  isAiLinkExist(): boolean {
    const getLink = this.localStorageService.get('aiLink');
    const link = getLink !== null && getLink.trim() !== ''
    return link ;
  }

  navigate(page: string): void {
    this.navigationService.navigate(page);
  }

  navigateToVideoWall(): void {
    const count = this.videoWallCount;
    if (count > 0) {
      for (let i = 0; i < count; i++) {
        const url = this.router.createUrlTree(['/video-wall'], { queryParams: { layout: i + 1 } }).toString();
        window.open(url, '_blank');
      }
    }
  }

  openSettingsModal(): void {
    this.settingsModal.show();
  }
}
