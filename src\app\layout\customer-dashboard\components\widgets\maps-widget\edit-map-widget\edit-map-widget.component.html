<div class="dashboardSettings">

    <app-edit-widget [selectedWidget]="data"></app-edit-widget>

    <div class="form-item">
        <h2>{{ 'customerDashboard.selectMap' | translate }}</h2>
        <p-dropdown [options]="mapList" [(ngModel)]="selectedMapId" [styleClass]="'input-element'"
            (onChange)="selectMap($event)">
            <ng-template let-item pTemplate="selectedItem">
                <span>{{item.label| translate}}</span>
            </ng-template>
            <ng-template let-item pTemplate="item">
                <div class="ui-helper-clearfix">
                    <div>{{item.label | translate}}</div>
                </div>
            </ng-template>
        </p-dropdown>
    </div>
    <div class="form-item">
        <h2>{{'appMap.layers' | translate}}</h2>
        <ul class="elements">
            <li *ngFor="let element of data.mapLayers; let index = index">
                <span class="element-title">{{element?.name}}</span>
                <span class="actions">
                    <button class="btn visibility" (click)="toggleMapLayerVisibility(index)">
                        <i class="fa {{element.hidden ? 'fa-eye-slash': 'fa-eye'}}" aria-hidden="true"></i>
                    </button>
                </span>
            </li>
        </ul>
    </div>
</div>