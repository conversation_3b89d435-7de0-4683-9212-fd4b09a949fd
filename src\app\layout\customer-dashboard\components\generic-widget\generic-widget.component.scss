$infoWrapperHeight: 55px;

:host ::ng-deep {

    .default-widget-wrapper,
    .map-wrapper,
    .player-wrapper,
    .gauge-wrapper,
    .notification-wrapper,
    .pie-chart-wrapper,
    .line-chart-wrapper,
    .sensor-status-wrapper,
    .embedded-file-wrapper,
    .url-shortcut-wrapper {
        height: calc(100% - 40px);
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
        border-width: 1px 1px 1px 1px;
        border-color: var(--secondary-highlight-5);
        border-style: solid;
        overflow: hidden;

    }

    .wrapper-dashbox {
        height: 100%;
    }

    .wrapper-dashbox::-webkit-scrollbar-track {
        background-color: transparent;
    }
}

.widget-wrapper {
    position: relative;
    height: 100%;
    width: 100%;

    &.edit-mode {
        cursor: move;
        pointer-events: none;

        .info-wrapper {
            pointer-events: visible;
        }
    }

    .widget-actions {
        position: absolute;
        right: -4px;
        top: 0;
        pointer-events: all;
        z-index: 1;

        :host-context(.rtl) & {
            left: -4px;
            right: unset;
        }
    }

    h3 {
        font-size: 0.9375rem;
        font-weight: 300;
        display: inline-block;
        padding: 0 15px;
    }

    .info-wrapper {
        height: $infoWrapperHeight;
        padding: 10px;
        line-height: 1rem;
        background-color: var(--secondary-5);
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        border-width: 1px 1px 0 1px;
        border-color: var(--secondary-highlight-5);
        border-style: solid;
        display: flex;
        justify-content: space-between;
        align-items: center;

    }

    .widget-icon {
        font-size: 1rem;

        &.dashbox,
        &.player,
        &.map,
        &.gauge,
        &.notification,
        &.pieChart,
        &.lineChart,
        &.chart,
        &.sensorStatus {
            &::before {
                display: inline-block;
            }
        }
    }
}

ul {
    padding: 0;
    margin: 0;

    li {
        list-style-type: none;
    }
}
