import { Component, OnInit, On<PERSON><PERSON>roy, HostListener } from '@angular/core';
import { VideoWallService } from '../../shared/services/video-wall.service';
import { AuthService } from '../../shared/services/auth.service';
import { Router } from '@angular/router';
import { WebSocketManagerService } from '../../shared/services/websocket-manager.service';
import {ResourcePort} from "app/shared/modules/data-layer/services/resource/resource-port";
import {ResourceCacheService} from "app/shared/modules/data-layer/services/resource/resource.cache.service";
import { MainLayoutService } from '../../shared/services/main-layout.service';

@Component({
  selector: 'app-main-layout',
  templateUrl: './main-layout.component.html',
  styleUrls: ['./main-layout.component.scss']
})
export class MainLayoutComponent implements OnInit, OnDestroy {
  videoWallCount: number = 0;
  currentUser: any;
  userName: string = '';
  sidebarExpanded: boolean = false;
  screenWidth: number;

  constructor(
    private videoWallService: VideoWallService,
    private authService: AuthService,
    private router: Router,
    private webSocketManager: WebSocketManagerService,
    private resourcePort: ResourcePort,
    private resourceCacheService: ResourceCacheService,
    private mainLayoutService: MainLayoutService) {
    this.screenWidth = window.innerWidth;
  }

  @HostListener('window:resize', ['$event'])
  onResize(_event: any): void {
    this.screenWidth = window.innerWidth;
  }

  ngOnInit() {
    // Initialize WebSocket connections
    this.userName = this.authService.getCurrentUser().Username;
    this.webSocketManager.initializeConnections();

    // Get video wall count for conditional display
    this.videoWallService.getVideoWalls().subscribe(videoWalls => {
      this.videoWallCount = videoWalls.length;
    });

    // Get current user from private property
    this.currentUser = this.authService['_user'];

    // Subscribe to sidebar expanded state
    this.mainLayoutService.sidebarExpanded$.subscribe((expanded: boolean) => {
      this.sidebarExpanded = expanded;
    });
  }

  toggleSidebar(): void {
    this.mainLayoutService.toggleSidebar();
  }

  logout(): void {
    this.authService.logoutFlow().subscribe(() => {
        this.resourcePort.clearCache();
        this.resourceCacheService.clearCache();
      this.router.navigate(['/login']);
    });
  }

  ngOnDestroy(): void {
    // Cleanup will be handled by the WebSocketManagerService
  }
}
