import { SignalRService } from 'app/shared/services/signalR.service';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { GeneralDetection } from '../../shared/modules/data-layer/models/general-detections/general-detection';
import { DetectionTypeEnum } from '../../shared/enum/detection-types.enum';
import { GeneralDetectionService } from '../../services/generalDetections/general-detection.service';
import { Subscription } from 'rxjs';
import { NavigationService, Pages } from '../../shared/services/navigation.service';
import { INotificationData } from '../../shared/components/header/notifications/INotificationData';
import { PageResult } from '../../shared/modules/data-layer/models/paging/page-result';
import { TriggerTypes } from '../../shared/enum/trigger-types';
import { NotificationsService } from '../../shared/components/header/notifications/notifications.service';
import ObjectAnalysisWarning from '../../shared/modules/data-layer/models/object-analysis/object-analysis-warning';
import { ObjectAnalysisType } from '../../shared/modules/data-layer/models/object-analysis/object-analysis-type';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit, OnDestroy {
  alarms: GeneralDetection[] = [];
  alerts: GeneralDetection[] = [];
  totalAlarmRecords = 0;
  totalAlertRecords = 0;
  totalWarningRecords = 0;
  loading: boolean = false;
  alertLoading: boolean = false;
  alarmPageIndex = 0;
  alertPageIndex = 0;
  pageSize: number = 10;
  startDate: Date = new Date(1970, 1);
  endDate: Date = new Date(2100, 1);

  triggerTypes: TriggerTypes[] = [TriggerTypes.Tampering, TriggerTypes.AMC_SHOCK, TriggerTypes.Gunshot, TriggerTypes.AMC_VIDEO_ANALYTICS, TriggerTypes.ACFail, TriggerTypes.CC1Open, TriggerTypes.CC2Open, TriggerTypes.BatteryLow, TriggerTypes.ChargerFail];

  notifications: INotificationData[] = [];
  totalNotificationRecords = 0;
  notificationPageIndex = 0;
  notificationLoading: boolean = false;

  isWarningFirst = false;
  private subscriptions: Subscription[] = [];
  private subscribedToServerEvents : boolean = false;

  constructor(
    private navigationService: NavigationService,
    private signalrService: SignalRService,
    private notificationService: NotificationsService,
    private generalDetectionService: GeneralDetectionService) { }


  ngOnInit(): void {
    this.fetchAlarms();
    this.fetchAlerts();
    this.fetchNotifications();

    if (!this.subscribedToServerEvents){
        this.subscribeToServerEvents();
    }
  }


  private fetchAlarms(): void {
    this.loading = true;
    this.subscriptions.push(this.generalDetectionService
      .getDetectionByState(this.alarmPageIndex, this.pageSize,
        this.startDate,
        this.endDate,
        DetectionTypeEnum.Alarm
      )
      .subscribe(page => {
        if (this.alarmPageIndex === 0) {
          this.alarms = page.Items;
        } else {
          this.alarms = [...this.alarms, ...page.Items];
        }

        this.totalAlarmRecords = page.TotalCount;
        this.loading = false;
      }, error => {
        this.loading = false;
      })
    );
  }

  private fetchAlerts(): void {
    this.alertLoading = true;
    this.subscriptions.push(
      this.generalDetectionService
        .getDetectionByState(
          this.alertPageIndex, this.pageSize,
          this.startDate, this.endDate,
          DetectionTypeEnum.Alert
        )
        .subscribe(page => {
          if (this.alertPageIndex === 0) {
            this.alerts = page.Items;
          } else {
            this.alerts = [...this.alerts, ...page.Items];
          }

          this.totalAlertRecords = page.TotalCount;
          this.alertLoading = false;
        },
        error => {
          this.alertLoading = false;
        })
    );
  }

  private fetchNotifications(): void {
    if (this.notifications.length >= this.totalNotificationRecords && this.totalNotificationRecords > 0) {
      return;
    }

    this.notificationLoading = true;
    this.subscriptions.push(this.notificationService
      .getNotificationsPage(
        this.notificationPageIndex,
        this.pageSize,
        this.triggerTypes
      )
      .subscribe((page: PageResult<INotificationData>) => {
        page.Items.map(warning =>{
          if(warning.EventData && warning.EventData[0] && warning.EventData[0].TriggerCode === TriggerTypes.AMC_VIDEO_ANALYTICS) {
            this.resolveWarningName(warning);
          }
        });

        this.notifications = [...this.notifications,...page.Items];
        this.totalNotificationRecords = page.TotalCount;
        this.notificationLoading = false;
      }));
  }

  private subscribeToServerEvents(): void {
    var signalRConnection = this.signalrService.GetConnection("VideoHub");
    const signalRSubscription = signalRConnection.subscribe((res) => {
        res.invoke("subscribeServerEvents", "webServerEvents").then(
            () => {}
        );
        res.listenFor<any>("webServerEvents").subscribe((res) => {
            res = JSON.parse(res);
            switch (res.changeType) {
                case "newSurpriseInfo": {
                    let notification = <INotificationData>(
                        JSON.parse(res.data)
                    );
                    if (!notification.EventData || !notification.EventData[0] || 
                      this.triggerTypes.indexOf(notification.EventData[0].TriggerCode) < 0
                    )
                    {
                      return;
                    }

                    if (notification.EventData[0].TriggerCode === TriggerTypes.AMC_VIDEO_ANALYTICS)
                    {
                      this.resolveWarningName(notification);
                    }

                    this.notifications = [notification, ...this.notifications];
                    this.totalNotificationRecords++; // Manually increased the number of warning items.
                    break;
                }

                default: {
                    break;
                }
            }
        });
    });
    this.subscriptions.push(signalRSubscription);
    this.subscribedToServerEvents = true;
  }

  onNotificationLazyLoad(event: number): void {
    this.notificationPageIndex = event;
    this.fetchNotifications();
  }

  onAlarmsLazyLoad(event: number): void {
    this.alarmPageIndex = event;
    this.fetchAlarms();
  }

  onAlertsLazyLoad(event: number): void {
    this.alertPageIndex = event;
    this.fetchAlerts();
  }

  onRowSelect(detection: GeneralDetection): void {
    const eventId = detection.id;
    this.navigationService.navigate(Pages.generalDetections, { eventId });
  }

  onNotificationSelect(notification: INotificationData):void  {
    const eventId = notification.SurpriseId;
    this.navigationService.navigate(Pages.generalDetections, { eventId });
  }

  switchPosition(): void {
    this.isWarningFirst = !this.isWarningFirst;
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private resolveWarningName( notification: INotificationData): void {
    if (!notification.EventData || !notification.EventData[0]) {
      return;
    }
    const objectAnalysis: ObjectAnalysisWarning = JSON.parse(notification.EventData[0].JsonData);
    const type = ObjectAnalysisType[objectAnalysis.description.object_type];
    notification.Name = `${notification.Name} ${objectAnalysis.description.rule_name} ${type}`;
  }

}
