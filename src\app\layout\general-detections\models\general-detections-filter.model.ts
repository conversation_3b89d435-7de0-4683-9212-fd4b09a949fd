export interface GeneralDetectionsFilter {
    searchValue1: string | null;
    searchValue2: string | null;
    resourceGroupId: string;
    startDate: Date | null;
    endDate: Date | null;
    eventId: string | null;
  }
  

  export interface GeneralDetectionExport {
    StartTimestamp?: Date;
    EndTimestamp?: Date;
    ResourceGroupId?: string;
    FilterValue?: string;
    SecondFilterValue?: string;
    EventId?: string;
    State?: string;
    FileType?: string;
  }