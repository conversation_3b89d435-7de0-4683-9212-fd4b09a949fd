import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges, ViewChild, OnInit } from '@angular/core';
import { Table } from 'primeng/table';
import { OverlayPanel } from 'primeng/overlaypanel';
import { TableColumnProperties } from 'app/shared/components/ng-turbo-table/models/table.models';
import { ReportDataChunkPayload } from 'app/shared/modules/data-layer/models/report-data-chunk-payload';
import { EventService, EventType } from 'app/shared/services/event.service';
import { ReportsTurboTableService } from 'app/shared/services/reports-turbo-table.service';
import { SelectItem } from 'primeng/api';

@Component({
  selector: 'app-report-table',
  templateUrl: './report-table.component.html',
  styleUrls: ['./report-table.component.scss']
})
export class ReportTableComponent implements OnChanges, OnInit {
  @Input() tableData: any[] = [];
  @Input() tableColumns: TableColumnProperties[] = [];
  @Input() loading: boolean = false;
  @Input() totalRecords: number = 0;
  @Input() reportDataChunkPayload: ReportDataChunkPayload;
  @Input() exportFileName: string = 'reportExport';

  @Output() filterEvent = new EventEmitter<any>();

  @ViewChild('dt') table: Table;
  @ViewChild('filterOverlay') filterOverlay: OverlayPanel;

  pageSize: number = 10;
  pageIndex: number = 0;
  visibleColumns: TableColumnProperties[] = [];
  currentFilterColumn: TableColumnProperties;
  filterValues: { [key: string]: any } = {};
  activeFilters: { [key: string]: boolean } = {};

  constructor(
    private eventService: EventService,
    private reportsTurboTableService: ReportsTurboTableService
  ) {}

  ngOnInit(): void {
    // Initialize filter values object
    if (this.tableColumns) {
      this.tableColumns.forEach(col => {
        this.filterValues[col.field] = null;
      });
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.tableColumns && changes.tableColumns.currentValue) {
      // Set visible columns based on the visibile property
      this.visibleColumns = this.tableColumns.filter(col => col.visibile);

      // Initialize filter values for new columns
      this.tableColumns.forEach(col => {
        if (!(col.field in this.filterValues)) {
          this.filterValues[col.field] = null;
        }

        // Set isFilterable property if not already set
        if (col.isFilterable === undefined) {
          col.isFilterable = true;
        }
      });
    }
  }

  onPageChange(event: any): void {
    this.pageIndex = event.first / event.rows;
    // You can emit an event here if needed
  }

  onColumnToggle(): void {
    // Make sure visibleColumns is always sorted in the same order as tableColumns
    this.visibleColumns = this.visibleColumns.sort((a, b) => {
      return this.tableColumns.findIndex(col => col.field === a.field) -
             this.tableColumns.findIndex(col => col.field === b.field);
    });
  }

  resetColumnSelection(event: Event): void {
    event.stopPropagation();
    // Reset to default visible columns (those marked as visible in the original tableColumns)
    this.visibleColumns = this.tableColumns.filter(col => col.visibile);
  }

  selectAllColumns(event: Event): void {
    event.stopPropagation();
    // Select all columns
    this.visibleColumns = [...this.tableColumns];
  }

  openFilter(event: MouseEvent, column: TableColumnProperties, overlay: OverlayPanel): void {
    this.currentFilterColumn = column;

    // If the column has filterOptions but they're empty, try to generate them from the data
    if (column.isFilterable && (!column.filterOptions || column.filterOptions.length === 0)) {
      this.generateFilterOptions(column);
    }

    overlay.toggle(event, event.target);
    event.stopPropagation();
  }

  generateFilterOptions(column: TableColumnProperties): void {
    if (!this.tableData || this.tableData.length === 0) return;

    // Get unique values for the column
    const uniqueValues = new Set<string>();

    this.tableData.forEach(row => {
      if (row[column.field] !== undefined && row[column.field] !== null) {
        uniqueValues.add(row[column.field].toString());
      }
    });

    // Convert to SelectItem array
    const options: SelectItem[] = Array.from(uniqueValues).map(value => ({
      label: value,
      value: value
    }));

    // Sort options alphabetically
    options.sort((a, b) => a.label.localeCompare(b.label));

    // Set the filter options
    column.filterOptions = options;
  }

  onFilter(event: any): void {
    // Update active filters
    this.activeFilters = {};

    if (event.filters) {
      Object.keys(event.filters).forEach(field => {
        if (event.filters[field].value) {
          this.activeFilters[field] = true;
        }
      });
    }

    // Emit filter event
    this.filterEvent.emit(event.filteredValue);
  }

  clearFilters(): void {
    if (this.table) {
      this.table.clear();
    }

    // Reset filter values
    Object.keys(this.filterValues).forEach(key => {
      this.filterValues[key] = null;
    });

    // Reset active filters
    this.activeFilters = {};
  }

  exportToCSV(): void {
    this.reportsTurboTableService.exportToCSV(this.tableData, this.exportFileName);
  }

  exportToPDF(): void {
    this.reportsTurboTableService.exportToPDF(this.tableData);
  }

  getColumnHeaders(): string[] {
    return this.visibleColumns.map(col => col.header || col.field);
  }

  getColumnFields(): string[] {
    return this.visibleColumns.map(col => col.field);
  }
}
