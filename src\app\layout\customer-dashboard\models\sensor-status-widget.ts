import { Widget } from "./widget";
import { DashboardWidget } from "./dashboard-widget.interface";
import { WidgetSize } from "../enums/widget-size.enum";
import { Guid } from "app/shared/enum/guid";

export class SensorStatusWidget extends Widget {
    
    selectedGroupId: string;
    resourceStates: string[];
    
    constructor(obj?: DashboardWidget){
        super(obj);
        if(!this.selectedGroupId){
            this.selectedGroupId = Guid.EMPTY;
        }
    }

    getWidgetSize(): WidgetSize[]{
        return [WidgetSize.big]
    }

}