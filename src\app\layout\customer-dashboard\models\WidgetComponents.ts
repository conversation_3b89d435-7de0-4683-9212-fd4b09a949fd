import { Type } from "@angular/core";
import { ChartWidgetComponent } from "../components/widgets/chart-widget/chart-widget.component";
import { DashboxWidgetComponent } from "../components/widgets/dashbox-widget/dashbox-widget.component";
import { DefaultWidgetComponent } from '../components/widgets/default-widget/default-widget.component';
import { EmbeddedFileComponent } from "../components/widgets/embedded-file/embedded-file.component";
import { EmptySpaceWidgetComponent } from "../components/widgets/empty-space-widget/empty-space-widget.component";
import { GaugeWidgetComponent } from "../components/widgets/gauge-widget/gauge-widget.component";
import { MapWidgetComponent } from "../components/widgets/maps-widget/map-widget.component";
import { NotificationWidgetComponent } from "../components/widgets/notification-widget/notification-widget.component";
import { PieChartWidgetComponent } from "../components/widgets/pie-chart-widget/pie-chart-widget.component";
import { PlayerWidgetComponent } from "../components/widgets/player-widget/player-widget.component";
import { SensorStatusWidgetComponent } from "../components/widgets/sensor-status-widget/sensor-status-widget.component";
import { UrlShortcutComponent } from '../components/widgets/url-shortcut/url-shortcut.component';
import { WidgetType } from "../enums/widget-type.enum";

export const WidgetComponents: { [id in WidgetType] : Type<DefaultWidgetComponent>; } = {
    player: PlayerWidgetComponent,
    dashbox: DashboxWidgetComponent,
    timeline: null,
    map: MapWidgetComponent,
    pieChart: PieChartWidgetComponent,
    sensorStatus: SensorStatusWidgetComponent,
    gauge: GaugeWidgetComponent,
    notification: NotificationWidgetComponent,
    lineChart: ChartWidgetComponent,
    chart: ChartWidgetComponent,
    emptySpace: EmptySpaceWidgetComponent,
    embeddedFile: EmbeddedFileComponent,
    urlShortcut: UrlShortcutComponent
};