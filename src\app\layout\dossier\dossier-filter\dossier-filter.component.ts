import { TranslateService } from '@ngx-translate/core';
import { Component, Output, EventEmitter, OnInit, OnDestroy, Input } from '@angular/core';
import { Subject, Subscription } from 'rxjs';
import { DossierPageRequest } from '../models/dosierPageRequest.model';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';


@Component({
  selector: 'app-dossier-filter',
  templateUrl: './dossier-filter.component.html',
  styleUrls: ['./dossier-filter.component.scss']
})

export class DossierFilterComponent implements OnInit, OnDestroy {
  @Input() routeEventId = '';
  @Output() dossierFilter = new EventEmitter<DossierPageRequest>();
  searchQuery: string = '';
  startDate: Date | null = null;
  endDate: Date | null = null;
  autoDeleteOptions: { label: string; value: null | boolean }[] = [];
  selectedAutoDelete: boolean | null = null;
  readOnlyOptions: { label: string; value: null | boolean }[] = [];
  selectedReadOnly: boolean | null  = null;
  subscriptions: Subscription[] = [];
  searchSubject: Subject<string> = new Subject<string>();
  tooltipOptions = {
    showDelay: 150,
    autoHide: false,
    tooltipEvent: 'hover',
    tooltipPosition: 'top'
  };

  constructor(private translateService: TranslateService) {
    this.subscriptions.push(this.translateService.onLangChange.subscribe(() => {
      this.initializeAutoDeleteOptions();
      this.initializeReadOnlyOptions();
    }));
  }

  get isResetDisabled(): boolean {
    return (
      !this.searchQuery &&
      !this.startDate &&
      !this.endDate &&
      !this.routeEventId &&
      this.selectedAutoDelete === null &&
      this.selectedReadOnly === null
    );
  }

  ngOnInit(): void {
    this.initializeAutoDeleteOptions();
    this.initializeReadOnlyOptions();
    this.subscriptions.push(
      this.searchSubject.pipe(debounceTime(700), distinctUntilChanged()).subscribe(() => {
        this.updateFilter();
      }));
  }

   onSearch(): void {
    this.searchSubject.next(this.searchQuery);
  }

  onFilterChange(): void {
    this.updateFilter();
  }

  onDateSelect(): void {
    this.updateFilter();
  }

  resetFilterTable(): void {
    this.resetData();
    this.updateFilter();
  }

  resetData(): void {
    this.searchQuery = '';
    this.startDate = null;
    this.endDate = null;
    this.selectedAutoDelete = null;
    this.selectedReadOnly = null;
    this.routeEventId = '';
  }


  ngOnDestroy(): void {
    this.subscriptions.forEach((i) => i.unsubscribe());
  }

  private updateFilter(overrides: Partial<DossierPageRequest> = {}): void {
    const filter: DossierPageRequest = {
      PageIndex: 0,
      PageSize: 12,
      Name: this.searchQuery || null,
      StartTimestamp: this.startDate ? this.startDate.toISOString() : undefined,
      EndTimestamp: this.endDate ? this.endDate.toISOString() : undefined,
      IsReadonly: this.selectedReadOnly, 
      IsDeletable: this.selectedAutoDelete,
      ...overrides,
    };
    this.dossierFilter.emit(filter);
  }

  private initializeAutoDeleteOptions(): void {
    this.autoDeleteOptions = [
      { label: this.translateService.instant('all'), value: null },
      { label: this.translateService.instant('dossierTable.autoDeleteTrue'), value: true },
      { label: this.translateService.instant('dossierTable.autoDeleteFalse'), value: false }
    ];
  }

  private initializeReadOnlyOptions(): void {
    this.readOnlyOptions = [
      { label: this.translateService.instant('all'), value: null },
      { label: this.translateService.instant('dossierTable.onlyDossiers'), value: false },
      { label: this.translateService.instant('dossierTable.onlyArchives'), value: true }
    ];
  }
}