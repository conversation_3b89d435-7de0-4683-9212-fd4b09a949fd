import {<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,OnInit} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";

import {MessageService} from "primeng/api";

import {TranslateService} from "@ngx-translate/core";
import {Guid} from "app/shared/enum/guid";
import {ProcedureActionTypes} from "app/shared/enum/procedureactiontypes.enum";
import {ProcedureStatus} from "app/shared/enum/procedures-status";
import {ToastTypes} from "app/shared/enum/toast-types";
import {IoStatusChange} from "app/shared/models/ioStatusChange.model";
import {Action} from "app/shared/models/procedures/action.model";
import {ProcedureModel} from "app/shared/models/procedures/procedure.model";
import {ProcTemplate} from "app/shared/models/procedures/proctemplate.model";
import {Step} from "app/shared/models/procedures/step.model";
import {StepsList} from "app/shared/models/procedures/steplist.model";
import {ioStatusEnum} from "app/shared/modules/data-layer/enum/iostatus.enum";
import {NotificationCategory} from "app/shared/modules/data-layer/enum/reports/notification-category.enum";
import {
    Header,
    IoResourceModel,
} from "app/shared/modules/data-layer/models/IoResource.model";
import {IocommandService} from "app/shared/modules/data-layer/services/io-commands/iocommand.service";
import {MenolinxioService} from "app/shared/modules/data-layer/services/menolinx-io/menolinxio.service";
import {AuthService} from "app/shared/services/auth.service";
import {EmailSmsService} from "app/shared/services/emailSms.service";
import {
    NavigationService,
    Pages,
} from "app/shared/services/navigation.service";
import {PlayersService} from "app/shared/services/players.service";
import {SignalRService} from "app/shared/services/signalR.service";
import {ISignalRConnection} from "ng2-signalr";
import {Subscription} from "rxjs";
import {v4 as uuidv4} from "uuid";
import { ProceduresPageExportRequest, ProceduresPageRequest } from "../../../shared/models/procedures/procedures-page-request.model";
import { HttpClient, HttpResponse } from "@angular/common/http";
import { ExportTypes } from "../../../shared/enum/export-types.enum";
import { ProcedureService } from '../../../shared/services/procedure.service';

@Component({
    selector:"app-procedurestable",
    templateUrl:"./procedurestable.component.html",
    styleUrls:["./procedurestable.component.scss"],
})
export class ProcedurestableComponent implements OnInit,OnDestroy {
    procedures: ProcedureModel[];
    rowsOnPage = 10;
    totalProcedures = 0;
    pagedProcedures: ProcedureModel[];
    currentPage = 0;
    eventId: string = '';

    statuses = [
        {
            label: this.i18n.instant("none"),
            value: ProcedureStatus.None,
            icon: 'assets/icons/pending.png'
        },
        {
            label: this.i18n.instant("open"),
            value: ProcedureStatus.Open,
            icon: 'assets/icons/open.png'
        },
        {
            label: this.i18n.instant("closed"),
            value: ProcedureStatus.Close,
            icon: 'assets/icons/closed.png'
        },
        {
            label: this.i18n.instant("in_progress"),
            value: ProcedureStatus.InProgress,
            icon: 'assets/icons/pending.png'
        },
        { label: this.i18n.instant("waiting"), value: ProcedureStatus.Waiting },
        { label: this.i18n.instant("snoozed"), value: ProcedureStatus.Snoozed },
        { label: this.i18n.instant("archived"), value: ProcedureStatus.Archive }
    ];
    name = "";
    procedureDoneSteps: Map<string,Map<number,StepsList>> = new Map<
        string,
        Map<number,StepsList>
    >();
    procedureProcTempSteps: Map<string,Map<number,Step>> = new Map<
        string,
        Map<number,Step>
    >();
    procedureMap: Map<string,Step[]>;
    procedureActionTypes = ProcedureActionTypes;
    selectedValues: Map<string, Guid> = new Map<string, Guid>();
    status;
    procedureTemplates: ProcTemplate[];
    commentForAction: string;
    selectedTemplate: Guid;
    selectedTemplateDropdown: ProcTemplate;
    userIdentity: Guid;
    ngSignalRConnection: ISignalRConnection;
    subscriptions:Subscription[]=[]
    currentStepComment: string = '';
    procedureTemplatesArray:any[]=[];
    expandedProcedures: Set<string> = new Set<string>();
    bufferedUpdates: boolean = false;
    pendingProcedureUpdates: boolean = false;

    // Export state
    exportLoading = false;
    exportType: ExportTypes | null = null;
    exportTypes = ExportTypes;

    ProcedureStatus = ProcedureStatus;
    stepDescriptionDropDown: Step[];
    stepDescriptionFilter: string;
    selectedTemplateFilter:string;
    selectedStep: Step;

    constructor(
        private procedureService: ProcedureService,
        private iocommandService: IocommandService,
        private menolinxIoService: MenolinxioService,
        private playersService: PlayersService,
        private navigationService: NavigationService,
        private emailSmsService: EmailSmsService,
        private messageService: MessageService,
        private i18n: TranslateService,
        private authService: AuthService,
        private route: ActivatedRoute,
        private router: Router,
        private httpClient: HttpClient) {

        this.userIdentity = Guid.parse(this.authService.user.Identity);
        this.procedureService.getTemplates().subscribe((procedureTemplates: ProcTemplate[])=>{
            this.procedureTemplates = procedureTemplates;
           // procedureTemplates.map((item)=>{
           //     this.procedureTemplatesArray.push(item);
           // })
        },(error)=>{
            if (error.status === 503) {
                console.error('Error getting ProcedureTemplates: ProceduresService is not running.');

                this.messageService.add({
                    severity:"error",
                    summary:this.i18n.instant(ToastTypes.error),
                    detail:this.i18n.instant("proceduresServiceIsNotRunning"),
                });
            }
            else {
                console.error('Error getting ProcedureTemplates:', error.status);

                this.messageService.add({
                    severity:"error",
                    summary:this.i18n.instant(ToastTypes.error),
                    detail:this.i18n.instant("noProcedureTemplates"),
                });
            }
        });


        this.subscriptions.push(
            this.route.queryParams.subscribe(params => {
                this.eventId = params['eventId'] || '';
                this.name = params['name'] || '';
                this.status = params['status'] || 0;
                this.currentPage = parseInt(params['page']) || 0;
                this.loadProceduresPage();
            })
        );
    }

    getStatusClass(status: ProcedureStatus): string {
        return ProcedureStatus[status];
    }

    changeProceduresStatus(status: ProcedureStatus,id: Guid): void {
        this.pagedProcedures.map((item,index)=>{
            let procedureIdentity = item.ProcedureId;
            if (procedureIdentity === id) {
                item.Status = status;
            }
        });
    }

    ngOnInit() {
        let procSubscriptions=this.procedureService.procedureMessageSubject.subscribe((data)=>{
            switch (data.category) {
                case NotificationCategory.NewProcedureInstance:
                    let newProcedureInstance: {
                        Header: Header;
                        Payload: ProcedureModel;
                    } = JSON.parse(data.message);
                    if (this.expandedProcedures.size > 0) {
                        this.pendingProcedureUpdates = true;
                    } else {
                        this.loadProceduresPage();
                    }
                    break;
                case NotificationCategory.ProcedureStep:
                    let procedureStep: {
                        Header: Header;
                        Payload: { Status: number;Step: StepsList };
                    } = JSON.parse(data.message);
                    let procedureId = procedureStep.Payload.Step.ProcedureId;
                    if (!this.expandedProcedures.has(procedureId)) {
                        this.pagedProcedures.map((item,index)=>{
                            let procedureIdentity = item.ProcedureId.toString();
                            if (procedureIdentity === procedureId) {
                                item.StepsList.push(procedureStep.Payload.Step);
                                this.generateProceduresMap();
                            }
                        });
                    } else {
                        this.pendingProcedureUpdates = true;
                    }
                    break;
                case NotificationCategory.ProcedureStatus:
                    let message: {
                        Header: Header;
                        Payload: { Status: ProcedureStatus;Id: Guid };
                    } = JSON.parse(data.message);

                    if (message.Payload.Id && message.Payload.Id != null) {
                        const procedureId = message.Payload.Id.toString();
                        if (!this.expandedProcedures.has(procedureId)) {
                            this.changeProceduresStatus(
                                message.Payload.Status,
                                message.Payload.Id
                            );
                        } else {
                            this.pendingProcedureUpdates = true;
                        }
                    }
                    break;
                default:
                    throw "no procedure action";
            }
        });
        this.subscriptions.push(procSubscriptions);
        this.loadProceduresPage();
    }

    loadProceduresPage():void {
        const request: ProceduresPageRequest = {
            PageIndex: this.currentPage,
            PageSize: this.rowsOnPage,
            StatusFilter: this.status ? this.status : 0,
            NameFilter: this.name || undefined,
            EventIdFilter: this.eventId || undefined,
            ProcedureTemplateFilter: this.selectedTemplateFilter || undefined,
            StepDescriptionFilter: this.stepDescriptionFilter || undefined
        };

        this.procedureService.getProceduresPage(request).subscribe(
            (response: any) => {

                const items = response.Items || response.items || [];
                const totalCount = response.TotalCount || response.totalCount || 0;

                const mappedItems = items.map((item) => {
                    if (item.StepsList) {
                        item.StepsList.sort((a, b) => a.StepIndex - b.StepIndex);
                    }
                    if (item.ProcTemplate && item.ProcTemplate.Steps) {
                        item.ProcTemplate.Steps.sort((a, b) => a.StepIndex - b.StepIndex);
                    }
                    return item;
                });

                this.procedures = mappedItems;
                this.pagedProcedures = mappedItems;
                this.totalProcedures = totalCount;
                this.generateProceduresMap();
            },
            (error) => {
                if (error.status === 503) {
                    console.error('Error getting Procedures: ProceduresService is not running.');
                    this.messageService.add({
                        severity:"error",
                        summary:this.i18n.instant(ToastTypes.error),
                        detail:this.i18n.instant("proceduresServiceIsNotRunning"),
                    });
                } else {
                    console.error('Error getting Procedures:', error.status);
                    this.messageService.add({
                        severity:"error",
                        summary:this.i18n.instant(ToastTypes.error),
                        detail:this.i18n.instant("errorGettingProcedures"),
                    });
                }
            }
        );
    }

    paginate(event: { page: number, first: number, rows: number, pageCount: number }): void {
        this.currentPage = event.page;
        this.rowsOnPage = event.rows;
        this.selectedValues.clear();
        this.updateFilters();
    }

    generateProceduresMap(): void {
        if (!this.pagedProcedures) return;

        this.pagedProcedures.forEach((item) => {
            if (!item.Identity) return;

            let itemIdentity = item.Identity.toString();
            let stepsDone = new Map<number,StepsList>();
            let procTemplateSteps = new Map<number,Step>();

            if (item.StepsList && item.StepsList.length > 0) {
                this.getNextIndex(
                    itemIdentity,
                    item.StepsList[item.StepsList.length - 1]
                );

                item.StepsList.forEach((steplistItem) => {
                    if (steplistItem && steplistItem.StepIndex) {
                        stepsDone.set(steplistItem.StepIndex, steplistItem);
                    }
                });
            }

            this.procedureDoneSteps.set(itemIdentity, stepsDone);

            if (item.ProcTemplate && item.ProcTemplate.Steps) {
                item.ProcTemplate.Steps.forEach((step) => {
                    if (step && step.StepIndex) {
                        procTemplateSteps.set(step.StepIndex, step);
                    }
                });
            }

            this.procedureProcTempSteps.set(itemIdentity, procTemplateSteps);
        });
    }

    getNextIndex(procedureId: string, stepIndex: any): void {
        if (!stepIndex || !this.pagedProcedures) return;

        const procedure = this.pagedProcedures.find(item => item.Identity?.toString() === procedureId);
        if (!procedure || !procedure.ProcTemplate?.Steps) return;

        const nextStep = procedure.ProcTemplate.Steps.find(step =>
            step.StepIndex === stepIndex.StepIndex + 1 && stepIndex.NextIndex !== -1
        );

        if (nextStep) {
            procedure.notDone = [nextStep];
        }
    }

    getNextStepsForProcedure(procedureId: Guid) {
        if (!procedureId) return null;

        const procedureIdentity = procedureId.toString();
        const doneSteps = this.procedureDoneSteps.get(procedureIdentity);
        const procTemplateSteps = this.procedureProcTempSteps.get(procedureIdentity);

        if (!doneSteps || !procTemplateSteps) return null;

        if (doneSteps.size === 0) {
            return procTemplateSteps.get(1);
        }

        const lastKeyInMap = Array.from(doneSteps.keys()).pop();
        if (!lastKeyInMap) return null;

        const lastDoneStep = doneSteps.get(lastKeyInMap);
        if (!lastDoneStep || lastDoneStep.NextIndex === -1) return null;

        return procTemplateSteps.get(lastDoneStep.NextIndex);
    }

    getProcedureActions(procedureId: Guid, stepIndex: number): Step | null {
        if (!procedureId) return null;

        const item = this.procedureProcTempSteps.get(procedureId.toString());
        if (!item) return null;

        return item.get(stepIndex) || null;
    }

    makeAction(
        action: Action,
        procedureId?: Guid,
        procedureName?: string
    ): void {
        switch (action.Type) {
            case ProcedureActionTypes.Activate_Output:
                this.activateOutput(action);
                break;
            case ProcedureActionTypes.Send_Email:
                this.executeAction(action,procedureId,procedureName);
                break;
            case ProcedureActionTypes.Open_Channel:
                this.openChannel(action);
                break;
            case ProcedureActionTypes.Send_SMS:
                this.executeAction(action,procedureId,procedureName);
                break;
            case ProcedureActionTypes.Open_Map:
                this.openMap(action);
                break;
            default:
                throw "action not found";
        }
    }

    activateOutput(action: Action): void {
        this.menolinxIoService
            .getMenolinxIoOutputItem(action.Resource.toString())
            .subscribe((response: IoResourceModel)=>{
                let metadata = JSON.parse(response.metadata);
                let data: IoStatusChange = {
                    Status:ioStatusEnum.On,
                    ConcentratorId:metadata.ConcentratorId,
                    IoIndex:metadata.IoIndex,
                    DeviceIdentity:metadata.DeviceIdentity,
                };
                this.iocommandService
                    .ioChangeStatus(data)
                    .subscribe((response)=>{
                    });
            });
    }

    openChannel(action: Action): void {
        this.playersService
            .openChannelPlayback(action.Resource.toString(),1,null,null)
            .subscribe();
    }

    executeAction(action: Action,procedureId: Guid,procedureName: string) {
        let data = {
            ProcedureName:procedureName,
            ProcedureId:procedureId.toString(),
            Action:action,
            Message:this.commentForAction,
        };
        this.emailSmsService.executeAction(data).subscribe(
            (response)=>{
                this.messageService.add({
                    severity:"success",
                    summary:this.i18n.instant(ToastTypes.success),
                    detail:this.i18n.instant("actionExecutedSuccessfully"),
                });
                this.commentForAction = "";
            },
            (err)=>{
                this.commentForAction = "";
                throw err;
            }
        );
    }

    openMap(action: Action): void {
        this.navigationService.navigate(Pages.mapNew,{
            map:action.Resource,
            zoom:19,
            identity:action.Resource,
        });
    }

    addStep(step: Step, procedureId: Guid): void {
        let procedure = this.pagedProcedures.find((item) =>
            item.Identity === procedureId
        );

        if (!procedure) {
            console.error('Procedure not found');
            return;
        }

        const selectedValue = this.selectedValues.get(procedureId.toString());
        let selectedOption = step.Options.find((item) =>
            item.ProcOptionId === selectedValue
        );

        if (!selectedOption) {
            console.error('Selected option not found');
            return;
        }

        // Check if this is the last step in the procedure template
        const isLastStep = !procedure.ProcTemplate?.Steps.some(s => s.StepIndex > step.StepIndex);

        // Only set nextIndex to -1 if it's explicitly the last step and no target step is specified
        const nextIndex = isLastStep && !selectedOption.TargetStepId ? -1 : (selectedOption.TargetStepId || step.StepIndex + 1);

        let data = {
            ProcStepId: uuidv4(),
            ProcedureId: procedureId,
            StepIndex: step.StepIndex,
            ProcOptionId: selectedOption.ProcOptionId,
            SelectedOptionVal: selectedOption.Value,
            Time: new Date().toISOString(),
            Description: step.Question,
            Comment: step.Comment,
            NextIndex: nextIndex,
            HandledBy: this.userIdentity["Identity"],
        };

        // Only close if it's explicitly the last step and nextIndex is -1
        if (nextIndex === -1 && isLastStep) {
            const procedureToUpdate = this.procedures.find(item => item.Identity === procedureId);
            if (procedureToUpdate) {
                procedureToUpdate.Status = ProcedureStatus.Close;
            }
        }

        this.procedureService.completeStep(data).subscribe(
            (response) => {
                // Clear only this procedure's selected value
                this.selectedValues.delete(procedureId.toString());

                this.messageService.add({
                    severity: "success",
                    summary: this.i18n.instant(ToastTypes.success),
                    detail: this.i18n.instant("stepCompleted"),
                });
            },
            (err) => {
                console.error('Error completing step:', err);
                this.messageService.add({
                    severity: "error",
                    summary: this.i18n.instant(ToastTypes.error),
                    detail: this.i18n.instant("errorCompletingStep"),
                });
            }
        );
    }

    getSelectedValue(procedureId: Guid): Guid | undefined {
        return this.selectedValues.get(procedureId.toString());
    }

    setSelectedValue(procedureId: Guid, value: Guid): void {
        this.selectedValues.set(procedureId.toString(), value);
    }

    updateSelectedValue(procedureId: Guid, value: Guid): void {
        this.setSelectedValue(procedureId, value);
    }

    isValidStepOptionSelected(procedureId: Guid): boolean {
        const selectedValue = this.selectedValues.get(procedureId.toString());
        return selectedValue !== undefined && selectedValue !== null;
    }

    createProcedure(): void {
        if (this.selectedTemplate == null) {
            this.messageService.add({
                severity:"error",
                summary:this.i18n.instant(ToastTypes.error),
                detail:this.i18n.instant("noProcedureTemplates"),
            });
            return;
        }
        this.procedureService
            .createProcedure(this.selectedTemplate)
            .subscribe((response)=>{
                this.messageService.add({
                    severity:"success",
                    summary:this.i18n.instant(ToastTypes.success),
                    detail:this.i18n.instant("procedureCreated"),
                });
            },(err)=>{
                this.messageService.add({
                    severity:"error",
                    summary:this.i18n.instant(ToastTypes.error),
                    detail:this.i18n.instant("errorCreatingProcedure"),
                });
            });
    }

    /** Get status label for a given status */
    getStatusLabel(status: ProcedureStatus): string {
        const statusConfig = this.statuses.find(s => s.value === status);
        return statusConfig?.label || '';
    }

    /** Get the icon for a given status */
    getStatusIcon(status: ProcedureStatus): string {
        const statusConfig = this.statuses.find(s => s.value === status);
        return statusConfig?.icon || 'assets/icons/pending.png';
    }

    getStepComment(procedureId: Guid): string {
        const nextStep = this.getNextStepsForProcedure(procedureId);
        if (!nextStep) return '';
        return nextStep.Comment || '';
    }

    setStepComment(procedureId: Guid, comment: string):void {
        const nextStep = this.getNextStepsForProcedure(procedureId);
        if (nextStep) {
            nextStep.Comment = comment;
        }
    }

    /** Get completed steps with their colors for a procedure */
    getCompletedSteps(procedure: ProcedureModel): { stepIndex: number, color: number, nextIndex: number }[] {
        if (!procedure || !procedure.StepsList || !procedure.ProcTemplate?.Steps) {
            return [];
        }

        const sortedSteps = [...procedure.StepsList].sort((a, b) => {
            const timeA = new Date(a.Time).getTime();
            const timeB = new Date(b.Time).getTime();
            return timeA - timeB;
        });

        return sortedSteps.map(step => {
            const templateStep = procedure.ProcTemplate.Steps.find(s => s.StepIndex === step.StepIndex);
            let x=this.procedureProcTempSteps.get(procedure.ProcedureId.toString());
            return {
                stepIndex: step.StepIndex,
                color: templateStep?.Color ?? step.StepIndex % 10,
                nextIndex: step.NextIndex ?? -1
            };
        });
    }

    /**
     * Converts an integer to ARGB components
     * @param colorInt The integer representation of the color
     * @returns Object containing alpha, red, green, and blue components
     */
    int32ToARGB(colorInt: number): { alpha: number, red: number, green: number, blue: number } {
        return {
            alpha: (colorInt >> 24) & 0xFF,
            red: (colorInt >> 16) & 0xFF,
            green: (colorInt >> 8) & 0xFF,
            blue: colorInt & 0xFF
        };
    }

    /**
     * Converts an integer to a hexadecimal ARGB string
     * @param colorInt The integer representation of the color
     * @returns Hexadecimal string in #AARRGGBB format
     */
    int32ToHexARGB(colorInt: number): string {
        const { alpha, red, green, blue } = this.int32ToARGB(colorInt);
        return `#${red.toString(16).padStart(2, '0')}${green.toString(16).padStart(2, '0')}${blue.toString(16).padStart(2, '0')}`;
    }

    getColorFromNumber(colorNumber: number): string {
        // If colorNumber is a full int32 color value, convert it to hex
        if (colorNumber < 0) {
            return this.int32ToHexARGB(colorNumber);
        }

        return '#000000';
    }

    ngOnDestroy(): void {
        this.subscriptions.forEach(sub=>{
            sub.unsubscribe();
        })
    }

    updateFilters():void {
        this.router.navigate([], {
            relativeTo: this.route,
            queryParams: {
                eventId: this.eventId || null,
                name: this.name || null,
                status: this.status || null,
                page: this.currentPage || null
            },
            queryParamsHandling: 'merge'
        });
    }

    onEventIdChange(value: string):void {
        this.eventId = value;
        this.currentPage = 0;
        this.updateFilters();
    }

    onNameChange(value: string):void {
        this.name = value;
        this.currentPage = 0;
        this.updateFilters();
        this.loadProceduresPage();
    }


    onStatusChange(value: any):void {
        this.status = value;
        this.currentPage = 0;
        this.updateFilters();
    }

    onProcedureTemplateChange(value: any): void {
        if (value) {
            this.selectedTemplateFilter = value.Identity;
            this.selectedTemplate=value;
            this.selectedTemplateDropdown = this.procedureTemplates.find(item =>
                item.Identity === value.Identity
            );
            this.stepDescriptionDropDown = this.selectedTemplateDropdown?.Steps || [];

            this.currentPage = 0;
            this.updateFilters();
            this.loadProceduresPage();
        }
    }

    onProcedureStepChange(value: Step):void {
        this.stepDescriptionFilter = value.Question;
        this.currentPage=0;
        this.updateFilters();
        this.loadProceduresPage();
    }

    getStepActions(procedure: ProcedureModel, stepIndex: number):Action[] | [] {
        if (!procedure?.Identity) {
            return [];
        }

        const procedureActions = this.getProcedureActions(procedure.Identity, stepIndex);
        return procedureActions?.Actions || [];
    }

    navigateToGeneralDetection(procedure: ProcedureModel):void {
        this.navigationService.navigate(Pages.generalDetections, { eventId: procedure.Source });
    }

    /**
     * Handles the panel before toggle event
     * @param event The toggle event
     * @param procedure The procedure being toggled
     */
    onPanelBeforeToggle(event: any, procedure: ProcedureModel): void {
        if (event.collapsed) {
            const procedureId = procedure.ProcedureId.toString();
            this.expandedProcedures.add(procedureId);
        }
    }

    /**
     * Handles the panel after toggle event
     * @param event The toggle event
     * @param procedure The procedure being toggled
     */
    onPanelAfterToggle(event: any, procedure: ProcedureModel): void {
        if (event.collapsed) {
            const procedureId = procedure.ProcedureId.toString();
            this.expandedProcedures.delete(procedureId);
            if (this.expandedProcedures.size === 0 && this.pendingProcedureUpdates) {
                this.pendingProcedureUpdates = false;
                this.loadProceduresPage();
            }
        }
    }

  export(type: ExportTypes): void {
    if (this.exportLoading){
      return;
    } 
    this.exportLoading = true;
    this.exportType = type;
    const exportRequest = this.buildExportRequest(type);
    this.callExportEndpoint(exportRequest);
  }


   private buildExportRequest(fileType: ExportTypes): any {
        const request: ProceduresPageExportRequest = {} as ProceduresPageExportRequest;
        if(fileType) {
            request.FileType = fileType.toUpperCase();
        }
        if (this.status && this.status > 0) {
            request.Status = this.status;
        }
        if (this.name && this.name.trim()) {
            request.Name = this.name.trim();
        }
        if (this.eventId && this.eventId.trim()) {
            request.EventId = this.eventId.trim();
        }
        if (this.selectedTemplateFilter && this.selectedTemplateFilter.trim()) {
            request.ProcedureTemplate = this.selectedTemplateFilter.trim();
        }
        if (this.stepDescriptionFilter && this.stepDescriptionFilter.trim()) {
            request.StepDescription = this.stepDescriptionFilter.trim();
        }
        return request;
    }

    private callExportEndpoint(request: ProceduresPageExportRequest): void {
        this.subscriptions.push(
            this.procedureService.exportProcedures(request).subscribe({
                next: (response) => {
                    this.handleExportSuccess(response);
                },
                error: () => {
                    this.handleExportError();
                }
            })
        );
    }

    private handleExportSuccess(response: HttpResponse<Blob>): void {
        if (!response.body) {
                this.messageService.add({
                severity: ToastTypes.error,
                detail: this.i18n.instant('noDataToExport')
            });
            this.resetExportState();
            return;
        }
        
        const blob = response.body;

        const mimeType = blob.type;
        const extension = mimeType ? `.${mimeType.split('/')[1]}` : '';

        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const fileName = `Procedures-export-${timestamp}${extension}`;

        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        this.messageService.add({
            severity: ToastTypes.success,
            detail: this.i18n.instant('exportSuccess')
        });
        this.resetExportState();
    }

    private handleExportError(): void {
        let errorMessage = this.i18n.instant('exportFailed');
        this.messageService.add({
            severity: ToastTypes.error,
            detail: errorMessage
        });

        this.resetExportState();
    }

    private resetExportState(): void {
        this.exportLoading = false;
        this.exportType = null;
    }
}
