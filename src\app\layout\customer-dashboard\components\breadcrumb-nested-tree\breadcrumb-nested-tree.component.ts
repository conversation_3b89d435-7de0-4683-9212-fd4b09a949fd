import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { NavigationService, Pages } from 'app/shared/services/navigation.service';
import { OverlayPanel } from 'primeng/overlaypanel';

import { Guid } from 'app/shared/enum/guid';
import { Dashboard } from 'app/shared/modules/data-layer/models/dashboard';
import { DashboardPath } from '../../models/dashboard-path';
import { DashboardUtilsService } from '../../services/dashboard-utils.service';
import { AuthService } from 'app/shared/services/auth.service';

@Component({
  selector: 'app-breadcrumb-nested-tree',
  templateUrl: './breadcrumb-nested-tree.component.html',
  styleUrls: ['./breadcrumb-nested-tree.component.scss']
})
export class BreadcrumbNestedTreeComponent implements OnInit, OnChanges {
  @Input() breadcrumbTree: DashboardPath;
  @Input() breadcrumbTreePath: DashboardPath[];
  @Input() hasParent: boolean = false;
  @Input() selectedDashboardId: string;
  @Output('onSelectDashboard') onSelectDashboard: EventEmitter<Guid> = new EventEmitter<Guid>();
  @ViewChild('siblingsOp', {static: false}) siblingsOp: OverlayPanel;
  
  public displayedChildTree: Dashboard;
  public overlaySiblings: Dashboard[];
  
  constructor(
    private navigationService: NavigationService,
    private dashboardUtilsService: DashboardUtilsService,
    private authService: AuthService
  ) { }

  ngOnInit() {
    this.selectCurrentChild();
  }

  ngOnChanges(changes: SimpleChanges){
    this.selectCurrentChild();
  }

  selectCurrentChild(){
    let index = this.dashboardUtilsService.findTreeIndex(this.breadcrumbTree.children, this.selectedDashboardId, null);
    this.displayedChildTree = this.breadcrumbTree.children.length > 0 ? (index > -1 ? this.breadcrumbTree.children[index] : this.breadcrumbTree.children[0]) : null;
  }

  selectItem(item: DashboardPath){
    this.breadcrumbTree = item;
    this.selectCurrentChild();
    let dashboardId = Guid.parse(item.identity);
    this.onSelectDashboard.next(dashboardId);
    this.siblingsOp.hide();
  }

  navigateTo(item: Dashboard){
    if (this.authService.isAllowedPage(Pages.dashboard))
    {
      this.navigationService.navigate(Pages.dashboard, {dashboard: item.identity});
      this.siblingsOp.hide();
    }
    else{
        throw new Error("Dashboard feature is disabled");
    }
  }

  startNavigationPanel(event:MouseEvent, treePath:DashboardPath[]){
    if(this.hasParent && treePath.length === 1){
      this.selectItem(treePath[0]);
    }
    else if(!this.hasParent && treePath.length === 2){
      this.selectItem(treePath[1]);
    }
    else {
      this.siblingsOp.show(event);
    }
  }

  showSibling(sibling:Dashboard): boolean {
    if(sibling.identity !== this.selectedDashboardId && Guid.isGuid(sibling.identity) && sibling.identity !==Guid.EMPTY)
      return true
    return false
  }
}
