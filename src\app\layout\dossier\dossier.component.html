
<app-dossier-filter [routeEventId]="routeEventId" (dossierFilter)="onDossierFilter($event)">           
</app-dossier-filter>
<app-dossier-table 
    [dossiers]="dossiers"
    [loading]="loading"
    [showActions]="true"
    [totalRecords]="totalRecords"
    [pageIndex]="currentPageIndex"
    (toggleAutoDelete)="onToggleAutoDelete($event)"
    (toggleReadOnly)="onToggleReadOnly($event)"
    (uploadDossier)="onUploadDossier($event)"
    (downloadDossier)="onDownloadDossier($event)"
    (viewHistory)="onViewHistory($event)"
    (viewDetails)="onViewDetails($event)"
    (pageChange)="onPageChange($event)">
</app-dossier-table>


<app-dossier-history #historyDialog></app-dossier-history>
<app-dossier-details #detailsDialog></app-dossier-details>
