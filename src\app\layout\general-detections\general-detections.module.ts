import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { SharedModule } from 'app/shared/modules/shared.module';
import { ConfirmDialogModule } from 'primeng/confirmdialog';

import { GeneralDetectionsRoutingModule } from "./general-detections-routing.module";
import { GeneralDetectionsComponent } from "./general-detections.component";
import { GeneralDetectionsTableComponent } from "./general-detections-table.component";
import { GeneralDetectionActionsComponent } from './general-detection-actions/general-detection-actions.component';
import { GeneralDetectionFilterComponent } from './general-detection-filter/general-detection-filter.component';
import { GeneralDetectionsDetailsComponent } from './general-detections-details/general-detections-details.component';


@NgModule({
  imports: [
    CommonModule,
    GeneralDetectionsRoutingModule,
    SharedModule,
    ConfirmDialogModule,
  ],
  providers: [

  ],
  declarations: [ GeneralDetectionsComponent, GeneralDetectionsTableComponent, GeneralDetectionActionsComponent, GeneralDetectionFilterComponent, GeneralDetectionsDetailsComponent],
  entryComponents: [GeneralDetectionsComponent]
})
export class GeneralDetectionsModule { }
