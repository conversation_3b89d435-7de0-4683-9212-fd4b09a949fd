import { Component, Input } from '@angular/core';
import { DossierDetailsResponse } from '../models/dossierDetailsResponse.model';


@Component({
  selector: 'app-dossier-details',
  templateUrl: './dossier-details.component.html',
  styleUrls: ['./dossier-details.component.scss']
})
export class DossierDetailsComponent {
  @Input() dossiers: DossierDetailsResponse[] = [];
  @Input() dossierName: string = ''
  visible = false;

  tooltipOptions = {
    showDelay: 150,
    autoHide: false,
    tooltipEvent: 'hover',
    tooltipPosition: 'top'
};

  show(): void {
    this.visible = true;
  }

  hide(): void {
    this.visible = false;
    this.dossiers = [];
    this.dossierName = '';
  }
}