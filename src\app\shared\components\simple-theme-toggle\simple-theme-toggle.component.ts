import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { SimpleThemeService } from '../../services/simple-theme.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-simple-theme-toggle',
  templateUrl: './simple-theme-toggle.component.html',
  styleUrls: ['./simple-theme-toggle.component.scss']
})
export class SimpleThemeToggleComponent implements OnInit, OnChanges {
  isDarkMode = false;
  lightTheme: string;
  darkTheme: string;
  
  @Input() nightThemeActive = false;
  @Output() themeToggled = new EventEmitter<boolean>();

  constructor(private themeService: SimpleThemeService, private i18n: TranslateService) { }

  ngOnInit(): void {
    // Initialize from SimpleThemeService
    this.themeService.isDarkMode().subscribe(isDark => {
      this.isDarkMode = isDark;
    });

    this.lightTheme = this.i18n.instant("lightTheme");
    this.darkTheme = this.i18n.instant("darkTheme");
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Update isDarkMode when nightThemeActive changes
    if (changes.nightThemeActive && changes.nightThemeActive.currentValue !== undefined) {
      // If nightThemeActive is true, we'll use the current theme from the settings
      // If it's false, we'll use the SimpleThemeService's current state
    }
  }

  toggleTheme(): void {
    // Toggle the theme in SimpleThemeService
    this.themeService.toggleTheme();

    // Get the updated dark mode state
    const isDarkMode = this.themeService.getCurrentTheme() === 'dark';

    // Toggle the night theme active state if needed
    if (isDarkMode && !this.nightThemeActive) {
      // If switching to dark mode, activate night theme
      this.nightThemeActive = true;
    } else if (!isDarkMode && this.nightThemeActive) {
      // If switching to light mode, deactivate night theme
      this.nightThemeActive = false;
    }

    // Emit the event to notify the parent component
    this.themeToggled.emit(isDarkMode);
  }
}
