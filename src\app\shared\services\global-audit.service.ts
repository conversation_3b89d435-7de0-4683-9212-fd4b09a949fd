import { Injectable, NgZone } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';
import { AuditLoggerService } from './audit-logger.service';
import { AuthService } from './auth.service';
import { SettingsService } from './settings.service';

@Injectable({
    providedIn: 'root'
})
export class GlobalAuditService {
    private currentRoute: string;

    constructor(
        private zone: NgZone,
        private router: Router,
        private auditLogger: AuditLoggerService,
        private auth: AuthService,
        private translateService: TranslateService,
        private settingsService: SettingsService
    ) {
        this.initializeClickTracking();
        this.trackRouteChanges();
    }

    /**
     * Safely gets a translation with a fallback value
     * @param key The translation key
     * @param fallback The fallback value if translation fails
     * @returns The translated string or fallback
     */
    private getTranslation(key: string, fallback: string): string {
        try {
            const translated = this.translateService.instant(key);
            return (translated !== key) ? translated : fallback;
        } catch (error) {
            return fallback;
        }
    }

    /**
     * Extracts tooltip text from a tooltip attribute that might contain translation keys
     */
    private extractTooltipText(tooltipAttr: string): string {
        if (!tooltipAttr) return '';

        // Check if it's a translation template with {{ 'key' | translate }}
        if (tooltipAttr.includes('{{') && tooltipAttr.includes('}}') && tooltipAttr.includes('translate')) {
            const match = tooltipAttr.match(/{{\s*'([^']+)'\s*\|\s*translate\s*}}/i);
            if (match && match[1]) {
                return this.getTranslation(match[1], match[1]);
            }
        }

        // Check if it's a direct translation key with 'key' | translate
        if (tooltipAttr.includes(' | translate')) {
            const match = tooltipAttr.match(/'([^']+)'\s*\|\s*translate/i);
            if (match && match[1]) {
                return this.getTranslation(match[1], match[1]);
            }
        }

        return tooltipAttr;
    }

    private initializeClickTracking(): void {
        this.zone.runOutsideAngular(() => {
            // Track click events
            document.addEventListener('click', (event: MouseEvent) => {
                const target = event.target as HTMLElement;

                if (!this.isInteractiveElement(target)) {
                    return;
                }

                const auditData = this.extractElementData(target, event);
                this.logAction('CLICK', auditData);
            }, true);

            // Track input field changes
            document.addEventListener('change', (event: Event) => {
                const target = event.target as HTMLElement;

                // Only track input, textarea and select elements
                if (target.tagName.toLowerCase() !== 'input' &&
                    target.tagName.toLowerCase() !== 'textarea' &&
                    target.tagName.toLowerCase() !== 'select') {
                    return;
                }

                // Don't track password inputs for security reasons
                if (target.tagName.toLowerCase() === 'input' &&
                    (target as HTMLInputElement).type === 'password') {
                    return;
                }

                const auditData = this.extractInputData(target);
                this.logAction('INPUT', auditData);
            }, true);

            // Track form submissions
            document.addEventListener('submit', (event: Event) => {
                const form = event.target as HTMLFormElement;
                if (!form || form.tagName.toLowerCase() !== 'form') {
                    return;
                }

                const auditData = this.extractFormData(form);
                this.logAction('FORM_SUBMIT', auditData);
            }, true);
        });
    }

    private isInteractiveElement(element: HTMLElement): boolean {
        const interactiveElements = [
            'BUTTON', 'A', 'INPUT', 'SELECT', 'TEXTAREA',
            'DETAILS', 'SUMMARY', '[role="button"]',
            // Add more interactive elements
            'P-BUTTON', 'P-TOGGLEBUTTON', 'P-CHECKBOX', 'P-RADIOBUTTON',
            'P-DROPDOWN', 'P-MULTISELECT', 'P-CALENDAR', 'P-INPUTSWITCH',
            'LI.ITEM', '[class*="btn"]', '[class*="button"]', '[pTooltip]'
        ];
        let current = element;
        while (current && current !== document.body) {
            if (interactiveElements.some(selector =>
                current.matches(selector) ||
                current.closest(selector)
            )) {
                return true;
            }
            current = current.parentElement;
        }
        return false;
    }

    private extractElementData(element: HTMLElement, event: MouseEvent): any {
        // Find the closest interactive element if the clicked element itself isn't interactive
        const interactiveElement = this.findClosestInteractiveElement(element);
        const targetElement = interactiveElement || element;

        const elementData: any = {
            elementType: targetElement.tagName.toLowerCase(),
            elementId: targetElement.id || undefined,
            elementClasses: targetElement.className,
            elementText: targetElement.textContent?.trim(),
            route: this.currentRoute,
            coordinates: {
                x: event.clientX,
                y: event.clientY
            }
        };

        // Extract component name from the route
        const routeParts = this.currentRoute?.split('/');
        if (routeParts && routeParts.length > 0) {
            const lastPart = routeParts[routeParts.length - 1];
            if (lastPart) {
                elementData.componentName = lastPart;
            }
        }

        // Check for tooltip content - first try to get the actual tooltip value after translation
        let tooltipElement = null;

        // Look for PrimeNG tooltip elements in the DOM
        const tooltips = document.querySelectorAll('.p-tooltip-text');
        if (tooltips.length > 0) {
            // Get the most recently shown tooltip
            tooltipElement = tooltips[tooltips.length - 1];
            if (tooltipElement && tooltipElement.textContent) {
                elementData.tooltipText = tooltipElement.textContent.trim();
            }
        }

        // If no active tooltip found, try to get the tooltip attribute
        if (!elementData.tooltipText) {
            // First check for pTooltip directive
            const tooltipAttr = targetElement.getAttribute('pTooltip');
            if (tooltipAttr) {
                // Try to extract the actual tooltip text from translation keys
                const extractedTooltip = this.extractTooltipText(tooltipAttr);
                if (extractedTooltip) {
                    elementData.tooltipText = extractedTooltip;
                } else {
                    // Store the raw attribute if extraction failed
                    elementData.tooltipAttr = tooltipAttr;
                }

                // Also check if it's a translation key for debugging
                if (tooltipAttr.includes('{{') && tooltipAttr.includes('}}')) {
                    // It's likely a translation template, store for reference
                    elementData.tooltipTemplate = tooltipAttr;
                } else if (tooltipAttr.includes(' | translate')) {
                    // It's a translation pipe, extract the key for reference
                    const match = tooltipAttr.match(/'([^']+)'/);
                    if (match && match[1]) {
                        elementData.tooltipTranslationKey = match[1];
                    }
                }
            }
        }

        // Try to get tooltip from other attributes if PrimeNG tooltip not found
        if (!elementData.tooltipText && !elementData.tooltipAttr) {
            const title = targetElement.getAttribute('title');
            const ariaLabel = targetElement.getAttribute('aria-label');
            if (title) {
                elementData.title = title;
            }
            if (ariaLabel) {
                elementData.ariaLabel = ariaLabel;
            }
        }

        // Check for icon - often indicates button purpose
        const iconElement = targetElement.querySelector('i.fa, i.pi, i.icon');
        if (iconElement) {
            const iconClasses = Array.from(iconElement.classList);
            const iconClass = iconClasses.find(cls => cls.startsWith('fa-') || cls.startsWith('pi-') || cls.startsWith('icon-'));
            if (iconClass) {
                elementData.iconClass = iconClass;
            }
        }

        // For buttons without text content but with icon, try to determine purpose from icon
        if (targetElement.tagName.toLowerCase() === 'button' && !elementData.elementText && elementData.iconClass) {
            const iconMap = {
                'fa-upload': 'Upload',
                'fa-download': 'Download',
                'fa-history': 'History',
                'fa-info-circle': 'Details',
                'fa-search': 'Search',
                'fa-bell': 'Notifications',
                'fa-lock': 'Lock',
                'fa-unlock': 'Unlock',
                'fa-refresh': 'Refresh',
                'fa-trash': 'Delete',
                'fa-edit': 'Edit',
                'fa-plus': 'Add',
                'fa-minus': 'Remove',
                'fa-save': 'Save',
                'fa-times': 'Cancel',
                'fa-check': 'Confirm',
                'fa-map-o': 'Map',
                'fa-car': 'Traffic',
                'fa-folder-open': 'Open Folder'
            };

            if (iconMap[elementData.iconClass]) {
                elementData.buttonPurpose = iconMap[elementData.iconClass];
            }
        }

        // Check for placeholder in inputs
        const placeholder = targetElement.getAttribute('placeholder');
        if (placeholder) {
            elementData.placeholder = placeholder;
        }

        // Get more attributes
        const relevantAttributes = ['name', 'value', 'href', 'type', 'role', 'aria-label', 'data-action'];
        relevantAttributes.forEach(attr => {
            const value = targetElement.getAttribute(attr);
            if (value) {
                elementData[attr] = value;
            }
        });

        // Check for PrimeNG components
        if (targetElement.tagName.toLowerCase().startsWith('p-') ||
            targetElement.className.includes('p-') ||
            targetElement.closest('[class*="p-"]')) {
            elementData.isPrimeNG = true;

            // Try to determine PrimeNG component type
            const primeNgClass = Array.from(targetElement.classList).find(cls => cls.startsWith('p-')) ||
                               (targetElement.closest('[class*="p-"]')?.className.match(/p-[a-z-]+/) || [])[0];
            if (primeNgClass) {
                elementData.primeNgComponent = primeNgClass;
            }
        }

        // Get form context if available
        if (targetElement.closest('form')) {
            elementData.formContext = {
                formId: targetElement.closest('form').id,
                formAction: targetElement.closest('form').action
            };
        }

        // Special handling for dossier component buttons
        if (this.currentRoute?.includes('dossier')) {
            // Check if this is an action button in the dossier table
            const isActionButton = targetElement.closest('.action-buttons') !== null;
            if (isActionButton) {
                elementData.isDossierActionButton = true;

                // Try to determine which action button it is
                if (elementData.iconClass) {
                    const dossierActionMap = {
                        'fa-bell': 'View Detections',
                        'fa-lock': 'Toggle Auto-Delete',
                        'fa-unlock': 'Toggle Auto-Delete',
                        'fa-upload': 'Upload Dossier',
                        'fa-download': 'Download Dossier',
                        'fa-history': 'View History',
                        'fa-info-circle': 'View Details'
                    };

                    if (dossierActionMap[elementData.iconClass]) {
                        elementData.dossierAction = dossierActionMap[elementData.iconClass];
                    }
                }
            }
        }

        // Special handling for general detections component buttons
        if (this.currentRoute?.includes('general-detections')) {
            // Check if this is an action button in the general detections table
            const isActionButton = targetElement.closest('.action-buttons') !== null;
            if (isActionButton) {
                elementData.isGeneralDetectionActionButton = true;

                // Try to determine which action button it is
                if (elementData.iconClass) {
                    const generalDetectionActionMap = {
                        'icon-widget-camera': 'Playback',
                        'fa-map-o': 'Jump to Map',
                        'fa-car': 'Follow on Traffic Detections',
                        'fa-download': 'Download Archive',
                        'icon-maps': 'Jump to Traffic Detections',
                        'fa-folder-open': 'Jump to Dossier'
                    };

                    if (generalDetectionActionMap[elementData.iconClass]) {
                        elementData.generalDetectionAction = generalDetectionActionMap[elementData.iconClass];
                    }
                }
            }
        }

        return elementData;
    }

    private trackRouteChanges(): void {

        this.currentRoute = window.location.pathname;

        this.router.events.pipe(
            filter(event => event instanceof NavigationEnd)
        ).subscribe((event: NavigationEnd) => {

            if (event.url && event.url !== '[object Object]') {
                this.currentRoute = event.url;
            } else {

                this.currentRoute = window.location.pathname;
            }
        });
    }

    /**
     * Extracts data from an input field
     */
    private extractInputData(element: HTMLElement): any {
        const elementData: any = {
            elementType: element.tagName.toLowerCase(),
            elementId: element.id || undefined,
            elementClasses: element.className,
            route: this.currentRoute,
            componentName: this.getComponentNameFromRoute(this.currentRoute)
        };

        // Get input value based on element type
        if (element.tagName.toLowerCase() === 'input') {
            const inputElement = element as HTMLInputElement;
            const inputType = inputElement.type.toLowerCase();

            elementData.inputType = inputType;

            // Handle different input types
            if (inputType === 'checkbox' || inputType === 'radio') {
                elementData.checked = inputElement.checked;
                elementData.value = inputElement.value;
            } else if (inputType !== 'password') { // Skip password fields
                elementData.value = inputElement.value;
            }
        } else if (element.tagName.toLowerCase() === 'textarea') {
            const textareaElement = element as HTMLTextAreaElement;
            elementData.value = textareaElement.value;
        } else if (element.tagName.toLowerCase() === 'select') {
            const selectElement = element as HTMLSelectElement;
            elementData.value = selectElement.value;

            // Get selected option text if available
            if (selectElement.selectedIndex >= 0) {
                const selectedOption = selectElement.options[selectElement.selectedIndex];
                elementData.selectedText = selectedOption.text;
            }
        }

        // Get placeholder, name, and label
        const placeholder = element.getAttribute('placeholder');
        if (placeholder) {
            elementData.placeholder = placeholder;
        }

        const name = element.getAttribute('name');
        if (name) {
            elementData.name = name;
        }

        // Try to find associated label
        if (element.id) {
            const label = document.querySelector(`label[for="${element.id}"]`);
            if (label) {
                elementData.label = label.textContent?.trim();
            }
        }

        // Check for tooltip
        const tooltipAttr = element.getAttribute('pTooltip');
        if (tooltipAttr) {
            const extractedTooltip = this.extractTooltipText(tooltipAttr);
            if (extractedTooltip) {
                elementData.tooltipText = extractedTooltip;
            }
        }

        // Get form context if available
        if (element.closest('form')) {
            elementData.formContext = {
                formId: element.closest('form').id,
                formAction: element.closest('form').action
            };
        }

        return elementData;
    }

    /**
     * Extracts data from a form submission
     */
    private extractFormData(form: HTMLFormElement): any {
        const formData: any = {
            elementType: 'form',
            elementId: form.id || undefined,
            elementClasses: form.className,
            route: this.currentRoute,
            componentName: this.getComponentNameFromRoute(this.currentRoute),
            formAction: form.action,
            formMethod: form.method,
            formInputs: []
        };

        // Get all form inputs (excluding password fields)
        const inputs = form.querySelectorAll('input:not([type="password"]), textarea, select');
        inputs.forEach((input: HTMLElement) => {
            if (input.tagName.toLowerCase() === 'input') {
                const inputElement = input as HTMLInputElement;
                const inputType = inputElement.type.toLowerCase();

                // Skip password fields
                if (inputType === 'password') {
                    return;
                }

                const inputData: any = {
                    type: inputType,
                    name: inputElement.name || undefined,
                    id: inputElement.id || undefined
                };

                // Handle different input types
                if (inputType === 'checkbox' || inputType === 'radio') {
                    inputData.checked = inputElement.checked;
                    inputData.value = inputElement.value;
                } else {
                    inputData.value = inputElement.value;
                }

                formData.formInputs.push(inputData);
            } else if (input.tagName.toLowerCase() === 'textarea') {
                const textareaElement = input as HTMLTextAreaElement;
                formData.formInputs.push({
                    type: 'textarea',
                    name: textareaElement.name || undefined,
                    id: textareaElement.id || undefined,
                    value: textareaElement.value
                });
            } else if (input.tagName.toLowerCase() === 'select') {
                const selectElement = input as HTMLSelectElement;
                const selectedOption = selectElement.options[selectElement.selectedIndex];
                formData.formInputs.push({
                    type: 'select',
                    name: selectElement.name || undefined,
                    id: selectElement.id || undefined,
                    value: selectElement.value,
                    selectedText: selectedOption ? selectedOption.text : undefined
                });
            }
        });

        return formData;
    }

    /**
     * Extract component name from route
     */
    private getComponentNameFromRoute(route: string): string {
        if (!route) return '';

        const routeParts = route.split('/');
        if (routeParts && routeParts.length > 0) {
            const lastPart = routeParts[routeParts.length - 1];
            if (lastPart) {
                // Remove query parameters if any
                return lastPart.split('?')[0];
            }
        }
        return '';
    }

    private logAction(operation: string, data: any): void {
        // Generate appropriate message based on operation type
        let message = '';

        if (operation === 'CLICK') {
            message = this.generateActionMessage(operation, data);
        } else if (operation === 'INPUT') {
            message = this.generateInputMessage(data);
        } else if (operation === 'FORM_SUBMIT') {
            message = this.generateFormSubmitMessage(data);
        } else {
            return; // Skip unknown operations
        }

        const auditMessage = {
            Action: operation,
            Route: this.currentRoute || '',
            SourceElement: data?.elementType || 'Page',
            Data: JSON.stringify(data),
            Message: message
        };

        this.auditLogger.logAuditData(auditMessage);
    }

    /**
     * Find the closest interactive element from the clicked element
     */
    private findClosestInteractiveElement(element: HTMLElement): HTMLElement | null {
        const interactiveSelectors = [
            'button', 'a', 'input', 'select', 'textarea',
            '[role="button"]', '[pTooltip]', 'p-button', 'p-togglebutton',
            'li.item', '.btn', '.button'
        ];

        let current = element;
        while (current && current !== document.body) {
            // Check if the current element matches any of the selectors
            for (const selector of interactiveSelectors) {
                if (current.matches(selector)) {
                    return current;
                }
            }

            // Check if any parent matches the selectors
            const closestInteractive = interactiveSelectors
                .map(selector => current.closest(selector))
                .filter(Boolean)[0] as HTMLElement;

            if (closestInteractive) {
                return closestInteractive;
            }

            current = current.parentElement;
        }

        return null;
    }

    /**
     * Generates a message for input field changes
     */
    private generateInputMessage(data: any): string {
        try {
            const username = this.auth.getUser()?.Username || 'Utilizatorul';
            const userId = this.auth.getUser()?.Identity || '';
            const userLabel = this.getTranslation('auditTable.name', 'User');

            // Determine input type for translation
            let inputTypeKey = 'audit.inputField';
            if (data.elementType === 'textarea') {
                inputTypeKey = 'audit.textArea';
            } else if (data.elementType === 'select') {
                inputTypeKey = 'audit.dropdown';
            } else if (data.inputType) {
                if (data.inputType === 'text') {
                    inputTypeKey = 'audit.textField';
                } else if (data.inputType === 'checkbox') {
                    inputTypeKey = 'audit.checkbox';
                } else if (data.inputType === 'radio') {
                    inputTypeKey = 'audit.radioButton';
                }
            }

            const inputTypeTranslated = this.getTranslation(inputTypeKey, data.elementType);

            // Get the most descriptive name for the input
            let inputName = '';
            if (data.label) {
                inputName = data.label;
            } else if (data.placeholder) {
                inputName = data.placeholder;
            } else if (data.name) {
                inputName = data.name;
            } else if (data.id) {
                inputName = data.id;
            } else if (data.tooltipText) {
                inputName = data.tooltipText;
            }

            // Get page name
            let pageName = this.getPageNameFromRoute(this.currentRoute);
            if (!pageName || pageName === '[object Object]' || typeof pageName === 'object') {
                pageName = data.componentName || this.getTranslation('audit.currentPage', 'current page');
            }

            // Format the value based on input type
            let valueText = '';
            if (data.elementType === 'select' && data.selectedText) {
                valueText = data.selectedText;
            } else if (data.inputType === 'checkbox' || data.inputType === 'radio') {
                valueText = data.checked ? 'checked' : 'unchecked';
            } else if (data.value) {
                // Truncate long values
                valueText = data.value.length > 50 ? data.value.substring(0, 47) + '...' : data.value;
            }

            // Build the message in Romanian format: "userul x a introdus valoarea y in campul z din pagina w"
            const enteredAction = this.getTranslation('audit.entered', 'a introdus');
            const valueLabel = this.getTranslation('audit.value', 'valoarea');
            const inFieldText = this.getTranslation('audit.inField', 'in campul');
            const fromPageText = this.getTranslation('audit.fromPage', 'din pagina');

            let message = '';
            if (username === 'Utilizatorul' && userId) {
                message = `${userLabel} ${userId} ${enteredAction} `;
            } else {
                message = `${username} ${enteredAction} `;
            }

            if (valueText) {
                message += `${valueLabel} "${valueText}" `;
            }

            message += `${inFieldText} ${inputTypeTranslated}`;

            if (inputName) {
                message += ` "${inputName}"`;
            }

            if (pageName) {
                message += ` ${fromPageText} "${pageName}"`;
            }

            return message;
        } catch (error) {
            return `User entered value in input field (error: ${error.message})`;
        }
    }

    /**
     * Generates a message for form submissions
     */
    private generateFormSubmitMessage(data: any): string {
        try {
            const username = this.auth.getUser()?.Username || 'Utilizatorul';
            const userId = this.auth.getUser()?.Identity || '';
            const userLabel = this.getTranslation('auditTable.name', 'User');

            // Get form name
            let formName = '';
            if (data.elementId) {
                formName = data.elementId;
            } else if (data.formAction) {
                // Extract the last part of the form action URL
                const actionParts = data.formAction.split('/');
                formName = actionParts[actionParts.length - 1];
            }

            // Get page name
            let pageName = this.getPageNameFromRoute(this.currentRoute);
            if (!pageName || pageName === '[object Object]' || typeof pageName === 'object') {
                pageName = data.componentName || this.getTranslation('audit.currentPage', 'current page');
            }

            // Build the message in Romanian format: "userul x a trimis formularul y din pagina z"
            const submittedAction = this.getTranslation('audit.submitted', 'a trimis');
            const formText = this.getTranslation('audit.form', 'formularul');
            const fromPageText = this.getTranslation('audit.fromPage', 'din pagina');

            let message = '';
            if (username === 'Utilizatorul' && userId) {
                message = `${userLabel} ${userId} ${submittedAction} `;
            } else {
                message = `${username} ${submittedAction} `;
            }

            message += formText;

            if (formName) {
                message += ` "${formName}"`;
            }

            if (pageName) {
                message += ` ${fromPageText} "${pageName}"`;
            }

            // Add information about form inputs if available
            if (data.formInputs && data.formInputs.length > 0) {
                const withValuesText = this.getTranslation('audit.withValues', 'cu valorile');
                message += ` ${withValuesText}: `;

                const inputSummaries = data.formInputs.map((input: any) => {
                    const inputName = input.name || input.id || input.type;
                    const inputValue = input.selectedText || input.value || (input.checked ? 'checked' : 'unchecked');
                    return `${inputName}: ${inputValue}`;
                }).join(', ');

                // Truncate if too long
                if (inputSummaries.length > 100) {
                    message += inputSummaries.substring(0, 97) + '...';
                } else {
                    message += inputSummaries;
                }
            }

            return message;
        } catch (error) {
            return `User submitted form (error: ${error.message})`;
        }
    }

    private generateActionMessage(operation: string, data: any): string {
        if (operation === 'CLICK') {
            try {
                const username = this.auth.getUser()?.Username || 'Utilizatorul';
                const elementText = data?.elementText ? data.elementText.substring(0, 20) : '';
                const elementId = data?.elementId || '';
                const elementClasses = data?.elementClasses || '';
                const elementType = data?.elementType || 'element';

                // Get the most descriptive tooltip information available
                let tooltipInfo = data?.tooltipText || data?.tooltipAttr || data?.title || data?.ariaLabel || '';
                const placeholder = data?.placeholder || '';
                const componentName = data?.componentName || '';

                // Determine element type with more precision
                let elementTypeKey = 'audit.element';
                let elementSubtype = '';
                let elementAction = '';
                let buttonName = '';

                // Special handling for dossier action buttons
                if (data?.isDossierActionButton && data?.dossierAction) {
                    buttonName = data.dossierAction;
                    elementAction = data.dossierAction;
                }
                // Special handling for general detections action buttons
                else if (data?.isGeneralDetectionActionButton && data?.generalDetectionAction) {
                    buttonName = data.generalDetectionAction;
                    elementAction = data.generalDetectionAction;
                }
                // Use button purpose determined from icon if available
                else if (data?.buttonPurpose) {
                    buttonName = data.buttonPurpose;
                    elementAction = data.buttonPurpose;
                }
                // Check for tooltip first as it often describes the action
                else if (tooltipInfo) {
                    elementAction = tooltipInfo;
                    buttonName = tooltipInfo;
                }

                // Determine element type
                if (elementType.toLowerCase() === 'button' || elementType.toLowerCase() === 'a' || data?.role === 'button') {
                    elementTypeKey = 'audit.button';
                } else if (elementType.toLowerCase() === 'input') {
                    elementTypeKey = 'audit.inputField';

                    if (data?.type) {
                        elementSubtype = data.type.toLowerCase();
                        if (elementSubtype === 'text') {
                            elementTypeKey = 'audit.textField';
                        } else if (elementSubtype === 'checkbox') {
                            elementTypeKey = 'audit.checkbox';
                        } else if (elementSubtype === 'radio') {
                            elementTypeKey = 'audit.radioButton';
                        } else if (elementSubtype === 'password') {
                            elementTypeKey = 'audit.passwordField';
                        } else if (elementSubtype === 'submit') {
                            elementTypeKey = 'audit.submitButton';
                        }
                    }

                    // Use placeholder as action description if available
                    if (placeholder && !elementAction) {
                        elementAction = placeholder;
                    }
                } else if (elementType.toLowerCase() === 'select') {
                    elementTypeKey = 'audit.dropdown';
                } else if (elementType.toLowerCase() === 'textarea') {
                    elementTypeKey = 'audit.textArea';
                } else if (data?.isPrimeNG) {
                    // Handle PrimeNG components
                    if (data.primeNgComponent) {
                        if (data.primeNgComponent.includes('button')) {
                            elementTypeKey = 'audit.button';
                        } else if (data.primeNgComponent.includes('dropdown')) {
                            elementTypeKey = 'audit.dropdown';
                        } else if (data.primeNgComponent.includes('checkbox')) {
                            elementTypeKey = 'audit.checkbox';
                        } else if (data.primeNgComponent.includes('calendar')) {
                            elementTypeKey = 'audit.calendar';
                        } else if (data.primeNgComponent.includes('input')) {
                            elementTypeKey = 'audit.inputField';
                        } else {
                            elementTypeKey = 'audit.primengComponent';
                        }
                    }
                } else if (elementType.toLowerCase() === 'span' || elementType.toLowerCase() === 'div' || elementType.toLowerCase() === 'li') {
                    if (data?.role) {
                        if (data.role === 'button') {
                            elementTypeKey = 'audit.button';
                        } else if (data.role === 'checkbox') {
                            elementTypeKey = 'audit.checkbox';
                        } else if (data.role === 'menuitem') {
                            elementTypeKey = 'audit.menuItem';
                        } else {
                            elementTypeKey = `audit.${data.role}`;
                        }
                    } else if (elementClasses && (elementClasses.includes('btn') || elementClasses.includes('button'))) {
                        elementTypeKey = 'audit.button';
                    } else {
                        elementTypeKey = 'audit.element';
                    }
                }

                const elementTypeTranslated = this.getTranslation(elementTypeKey, elementType);

                // Build element description
                let elementDesc = '';

                // First try to use button name if available (for dossier actions)
                if (buttonName) {
                    elementDesc = `"${buttonName}"`;
                }
                // Then try tooltip or placeholder as they're most descriptive
                else if (tooltipInfo) {
                    elementDesc = `"${tooltipInfo}"`;
                } else if (data?.name) {
                    const withNameText = this.getTranslation('audit.withName', 'named');
                    elementDesc = `${withNameText} "${data.name}"`;
                } else if (elementText) {
                    elementDesc = `"${elementText}${elementText.length > 20 ? '...' : ''}"`;
                } else if (placeholder) {
                    elementDesc = `"${placeholder}"`;
                } else if (data?.value) {
                    const withValueText = this.getTranslation('audit.withValue', 'with value');
                    elementDesc = `${withValueText} "${data.value}"`;
                } else if (elementId) {
                    const withIdText = this.getTranslation('audit.withId', 'with ID');
                    elementDesc = `${withIdText} "${elementId}"`;
                } else if (elementClasses) {
                    const classes = elementClasses.split(' ').filter(cls =>
                        !cls.startsWith('ng-') &&
                        !cls.startsWith('p-') &&
                        cls !== 'cdk-overlay-pane' &&
                        cls !== 'cdk-overlay-container'
                    );

                    if (classes.length > 0) {
                        const mainClass = classes[0];
                        const withClassText = this.getTranslation('audit.withClass', 'with class');
                        elementDesc = `${withClassText} "${mainClass}"`;
                    } else if (elementClasses.includes('p-')) {
                        elementDesc = this.getTranslation('audit.primengComponent', 'component');
                    }
                }

                // Get page name
                let pageName = this.getPageNameFromRoute(this.currentRoute);
                if (!pageName || pageName === '[object Object]' || typeof pageName === 'object') {
                    pageName = componentName || this.getTranslation('audit.currentPage', 'current page');
                }

                const fromPageText = this.getTranslation('audit.fromPage', 'from page');
                const pageContext = pageName ? `${fromPageText} "${pageName}"` : '';

                // Build the action description
                let actionDescription = '';
                if (elementAction && !buttonName) { // Only add if not already included in button name
                    actionDescription = ` care ${elementAction}`;
                }

                // Build the complete message in Romanian format: "userul x a apasat butonul din pagina y care face z"
                let message = '';
                const userId = this.auth.getUser()?.Identity || '';
                const userLabel = this.getTranslation('auditTable.name', 'User');
                const clickedAction = this.getTranslation('audit.clicked', 'a apasat');

                if (username === 'Utilizatorul' && userId) {
                    message = `${userLabel} ${userId} ${clickedAction} `;
                } else {
                    message = `${username} ${clickedAction} `;
                }

                message += elementTypeTranslated;
                if (elementDesc) {
                    message += ` ${elementDesc}`;
                }

                if (pageContext) {
                    message += ` ${pageContext}`;
                }

                if (actionDescription) {
                    message += actionDescription;
                }

                return message;
            } catch (error) {
                return `User clicked element (error: ${error.message})`;
            }
        }

        return operation;
    }

    private getPageNameFromRoute(route: string): string {
        if (!route) return '';

        try {

            if (route === '[object Object]' || typeof route === 'object') {

                route = window.location.pathname;
            }

            const parts = route.split('/');
            const lastPart = parts[parts.length - 1] || parts[parts.length - 2] || '';


            const cleanLastPart = lastPart.split('?')[0];


            if (!cleanLastPart || cleanLastPart === '[object Object]') {

                const previousPart = parts[parts.length - 2] || '';
                if (previousPart && previousPart !== '[object Object]') {
                    return this.getPageNameFromPart(previousPart);
                }


                return this.getTranslation('audit.currentPage', 'current page');
            }

            return this.getPageNameFromPart(cleanLastPart);
        } catch (error) {
            return this.getTranslation('audit.currentPage', 'current page');
        }
    }

    /**
     * Gets the page name from a route part
     * @param part The route part
     * @returns The translated page name
     */
    private getPageNameFromPart(part: string): string {

        const pageNameMap = {
            'dashboard': 'dashboard',
            'map': 'maps',
            'inventory': 'inventory',
            'reports': 'reports',
            'audit': 'audit.title',
            'settings': 'settings',
            'users': 'users',
            'cameras': 'cameras',
            'events': 'events',
            'alarms': 'alerts',
            'analytics': 'analytics',
            'detection': 'detections',
            'vms': 'vms',
            'login': 'login'
        };

        const translationKey = pageNameMap[part];

        if (translationKey) {

            return this.getTranslation(translationKey, part);
        }

        return part;
    }
}