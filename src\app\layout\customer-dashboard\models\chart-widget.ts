import { Widget } from "./widget";
import { DashboardWidget } from "./dashboard-widget.interface";
import { WidgetSize } from "../enums/widget-size.enum";
import { LineChartDataType } from "../enums/line-chart-data.enum";
import { Guid } from "app/shared/enum/guid";
import { ResourceState } from 'app/shared/modules/data-layer/enum/resource/resource-state.enum';
import { ReportDataType } from 'app/shared/modules/data-layer/enum/reports/report-data-type.enum';
import { LineChartDisplayResults } from "../enums/line-chart-display-results.enum";
import { ChartType } from '../enums/chart-type.enum';
import { DashboardAction } from './dashboard-action.interface';
import { DashboardActionType } from '../enums/dashboard-action-type.enum';

export class ChartWidget extends Widget {
    selectedGroupId: string;
    selectedResourceIds: string[];
    lineChartDataType: LineChartDataType;
    resourceStates: ResourceState[];
    selectedEventsIds: string[];
    selectedTriggers: number[];
    selectedDateRangeType: number;
    selectedDateUnitLast: number;
    fromDate: Date;
    toDate: Date;
    currentDate: Date;
    unitLastValue: ReportDataType;
    displayedDataTimeSettings: number;
    selectedResourceStates: string[];
    displayResultsMethod: LineChartDisplayResults;
    chartType: ChartType;

    constructor(obj?: DashboardWidget){
        super(obj);
        if(!this.selectedGroupId){
            this.selectedGroupId = Guid.EMPTY;
        }
        if(!this.selectedResourceIds){
            this.selectedResourceIds = [];
        }
        if(!this.selectedTriggers){
            this.selectedTriggers = [];
        }
        if(!this.unitLastValue){
            this.unitLastValue = 10;
        }
        if(!this.selectedDateRangeType && this.selectedDateRangeType !== 0){
            this.selectedDateRangeType = 2;
        }
        if(!this.displayedDataTimeSettings){
            this.displayedDataTimeSettings = 3;
        }
        if(!this.displayResultsMethod){
            this.displayResultsMethod = LineChartDisplayResults.countResults;
        }
        if(!this.chartType){
            this.chartType = ChartType.line;
        }
    }

    getWidgetSize(): WidgetSize[]{
        return [WidgetSize.big, WidgetSize.wide, WidgetSize.xWide];
    }

    getWidgetExtraActions(): DashboardAction[]{
        return [
            {name: 'customerDashboard.displayInInventory', type: DashboardActionType.viewInTable, isVisible: true}
        ];
    }

}