import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';
import { CymsidebarService } from 'app/shared/components/cym-sidebar/cym-sidebar.service';
import { MessageService, SelectItem } from 'primeng/api';
import { TranslateService } from '@ngx-translate/core';
import { ToastTypes } from 'app/shared/enum/toast-types';
import { ResourceGroupService } from 'app/shared/modules/data-layer/services/resource-group/resource-group.service';
import { Subscription } from 'rxjs';
import { RtspModel } from 'app/shared/modules/data-layer/models/rtsp.model';
import { ResourceGroup } from 'app/shared/modules/data-layer/models/resource-group';
import { ResourceService } from "../../../services/resource/resource.service";
@Component({
  selector: 'app-add-device',
  templateUrl: './add-device.component.html',
  styleUrls: ['./add-device.component.scss']
})
export class AddDeviceComponent implements On<PERSON>ni<PERSON>, OnDestroy {
  public rtspForm: FormGroup = null;
  rtsp: RtspModel = new RtspModel();
  isLoading: boolean = false;
  filteredResourceGroups: SelectItem[] = [];
  subscriptions: Subscription[] = [];
  resourceGroupModelStore: { [id: string]: ResourceGroup }
  constructor(private formBuilder: FormBuilder,
      private resourceService: ResourceService,
      private cymSidebarService: CymsidebarService,
      private messageService: MessageService,
      private _i18n: TranslateService,
      private resourceGroupService: ResourceGroupService,
      ) { }

  ngOnInit(): void {
    this.generateAddDeviceForm(this.rtsp);
    this.getResourceGroups();
   }

  generateAddDeviceForm(rtsp: RtspModel): void{
    const ipAddressPattern =
      "(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)";
    this.rtspForm = this.formBuilder.group({
      name: new FormControl(rtsp.name ? rtsp.name : '', Validators.compose([Validators.required])),
      resourceGroups: new FormControl(rtsp.resourceGroups ? rtsp.resourceGroups : []),
      ipAddress: new FormControl(rtsp.ipAddress ? rtsp.ipAddress : '', Validators.compose([Validators.required, Validators.pattern(ipAddressPattern)])),
      httpPort: new FormControl(rtsp.httpPort ? rtsp.httpPort : '', Validators.compose([Validators.required])),
      rtspPort: new FormControl(rtsp.rtspPort ? rtsp.rtspPort : '', Validators.compose([Validators.required])),
      rtspLink: new FormControl(rtsp.rtspLink ? rtsp.rtspLink : '', Validators.compose([Validators.required])),
      username: new FormControl(rtsp.username ? rtsp.username : '', Validators.compose([Validators.required])),
      password: new FormControl(rtsp.password ? rtsp.password : '', Validators.compose([Validators.required])),
    });
  }


  getResourceGroups(): void {
   let resourceGroups = this.resourceGroupService.getAll().subscribe((response) => {
    this.resourceGroupModelStore = response;
    for(let key in this.resourceGroupModelStore){
      if(this.resourceGroupModelStore[key].isHomogeneous){
        this.filteredResourceGroups.push({ label: this.resourceGroupModelStore[key].name, value: this.resourceGroupModelStore[key].identity });
      }
     }
    });
    this.subscriptions.push(resourceGroups);
  }

  addRtsp(): void {
    this.isLoading = true;
    this.resourceService.createRTSP([this.createRtspModel(this.rtspForm)]).subscribe((response) => {
      if(response[0].status == 'success'){
      this.messageService.add({severity: 'success', summary: this._i18n.instant(ToastTypes.success), detail:  this._i18n.instant(response[0].status)});
      }else{
        this.messageService.add({severity: 'error', summary: this._i18n.instant(ToastTypes.error), detail: this._i18n.instant(response[0].status)});
      }
      this.cancel();
      this.isLoading = false;
    }, (err) => {
      console.error('Cannot add RTSP device', err);
      this.messageService.add({severity: 'error', summary: this._i18n.instant(ToastTypes.error), detail: this._i18n.instant('cannotAddRtsp')});
      this.isLoading = false;
    });
  }

  createRtspModel(form: FormGroup): RtspModel{
    return new RtspModel({
      name: form.get('name').value,
      resourceGroups:form.get('resourceGroups').value && form.get('resourceGroups').value != null ? form.get('resourceGroups').value.map((item) => {return this.resourceGroupModelStore[item];}) : null,
      ipAddress: form.get('ipAddress').value,
      rtspPort: form.get('rtspPort').value,
      httpPort: form.get('httpPort').value,
      rtspLink: form.get('rtspLink').value,
      username: form.get('username').value,
      password: form.get('password').value,
    });
  }

  cancel(): void {

    this.isLoading = false;
    this.rtsp = new RtspModel();
    this.rtspForm.reset();
    this.rtspForm.setErrors= null;
    this.cymSidebarService.closeSidebar();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(subscription => {return subscription.unsubscribe();});
  }
}
