import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { dataTabel, columnsTabel } from '../data/data';

@Component({
  selector: 'resource-group-manager',
  templateUrl: './resource-group-manager.component.html',
  styleUrls: ['./resource-group-manager.component.scss']
})
export class ResourceGroupManagerComponent implements OnInit {
  @Output('selectedElements') selectedElements: EventEmitter<any[]> = new EventEmitter();
  columns: Array<{}> = [];
  data: Array<{}> = [];
   
  constructor() { }

  ngOnInit() {
    this.data = dataTabel;
    this.columns = columnsTabel;    
  }


}
