import { AfterViewInit, ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Inject, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { Guid } from 'app/shared/enum/guid';
import { Feature } from 'ol';
import Map from 'ol/Map';
import Overlay from 'ol/Overlay';


import View from 'ol/View';
import { Coordinate } from 'ol/coordinate';
import { Extent, boundingExtent } from 'ol/extent';
import LineString from 'ol/geom/LineString';
import Point from 'ol/geom/Point';
import {
  DragRotateAndZoom,
  defaults as defaultInteractions
} from 'ol/interaction';
import { Tile as TileLayer } from 'ol/layer';
import ImageLayer from 'ol/layer/Image';
import VectorLayer from 'ol/layer/Vector';
import Projection from 'ol/proj/Projection';
import { fromCode } from 'ol/proj/Units';
import { Cluster, XYZ } from 'ol/source';
import Static from 'ol/source/ImageStatic';
import VectorSource from 'ol/source/Vector';
import CircleStyle from 'ol/style/Circle';
import Fill from 'ol/style/Fill';
import Icon from 'ol/style/Icon';
import Stroke from 'ol/style/Stroke';
import Style from 'ol/style/Style';
import Text from 'ol/style/Text';
import { Subject, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { Arrow } from '../../models/arrow';
import { Base64TileSource, Button, Point as CYPoint, Circle, DrawMode, ExtentObj, GisTileSource, Marker, Polygon, ProjectionEnum, TemplateMarker, TileLayerSource, TileSource } from '../../models/map.models';
import { BaseMapImplComponent } from '../base-map-impl.component';
import { IMapProjectionTransformService } from '../base-map-transform.service';


@Component({
  selector: 'app-open-layers-map',
  templateUrl: './open-layers-map.component.html',
  styleUrls: ['./open-layers-map.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class OpenLayersMapComponent extends BaseMapImplComponent implements OnInit, OnDestroy, AfterViewInit {

  @ViewChild('mapContainer', {static: false}) mapContainer: ElementRef;
  @Output('extentChanged') extentChanged: EventEmitter<Extent> = new EventEmitter();
  private extentDebouncer: Subject<Extent> =  new Subject<Extent>();
  public renderCompleted: Subject<boolean> = new Subject<boolean>();
  private clusterDistance: number = 40;

  private tileLayer:any =  new TileLayer({
    preload: Infinity
  });

  public mapid: Guid = Guid.create();

  private view: View = new View({
    projection: new Projection({
      code: ProjectionEnum.EPSG3857,
      units:fromCode(9001),
    }),
    center: [0, 0],
    zoom: 2
  });

  private source: VectorSource = new VectorSource({
    features: null
  })

  private clusterSource: Cluster = new Cluster({
    distance: this.clusterDistance,
    source: this.source,
  })


  private arrowsSource = new VectorSource();
  private styleFunction (feature) {
    const geometry = feature.getGeometry();
    const styles = [
      // linestring
      new Style({
        stroke: new Stroke({
          color: '#198cd5',
          width: 2
        }),
      }),
    ];

    geometry.forEachSegment(function (start, end) {
      const dx = end[0] - start[0];
      const dy = end[1] - start[1];
      const rotation = Math.atan2(dy, dx);
      // arrows
      styles.push(
        new Style({
          geometry: new Point(end),
          image: new Icon({
            src: '/assets/images/arrow.png',
            anchor: [0.75, 0.5],
            rotateWithView: true,
            rotation: -rotation,
          }),
        })
      );
    });

    return styles;
  }
  private arrowsLayer = new VectorLayer({
    source: this.arrowsSource,
    style: this.styleFunction,
  });




  styleCache = {};

  private clusters: any = new VectorLayer({

    source: this.clusterSource,
    style: (feature) => {
      let size = feature.get('features').length;
      let style = this.styleCache[size];
      if (!style) {
        if(size  !== 1  && this.clusterDistance > 0) {
          style = new Style({
            image: new Icon({
              src: '/assets/images/cluster.png',
              opacity: 1,
              size: [68, 68],
              imgSize: [68, 68]
            }),
            text: new Text({
              text: size.toString(),
              fill: new Fill({
                color: '#FFFFFF'
              })
            })
          });
        } else {
            style = new Style({
              image: new CircleStyle({
                radius: 0
              })
            });
        }
        this.styleCache[size] = style;
      }
      return style;
    }
  })

  public vectorMap: Map = new Map({
    interactions: defaultInteractions().extend([new DragRotateAndZoom()]),
    layers: [this.tileLayer, this.clusters, this.arrowsLayer],
    view: this.view,
  });

  private imageTileLayer = null;


  subscriptions: Subscription[] = [];
  overlay: Overlay;
  mouseHoveredFeature: Feature = null;

  constructor(
    @Inject('IMapProjectionTransformService') private projectionTransform: IMapProjectionTransformService
  ) {
    super();

    if(!this.mapTileSource){
      this.setTileSource(this.defaultTileSource);
    }

    this.view.on("change:resolution", () => {
      /**
       * All view changed outputs will generate from here
       */
      let zoom = this.vectorMap.getView().getZoom();
      if(zoom >= 18){
        this.setClusterDistance(0);
      }
      else if(zoom < 18 && this.clusterDistance === 0) {
        this.setClusterDistance(40);
      }
    });

    this.vectorMap.on('moveend', () => {
      this.extentDebouncer.next(this.vectorMap.getView().calculateExtent());
    });

    this.vectorMap.on('rendercomplete', () => {
      this.extentDebouncer.next(this.view.calculateExtent());
      this.renderCompleted.next(true);
    });

    this.vectorMap.on('click', (event) => {
      let coordinates: Coordinate[] = [];
      this.vectorMap.forEachFeatureAtPixel(event.pixel, (feature) => {
        let features: Feature[] = feature.get('features');
        if(!features || features.length <= 1){
          return;
        }
        coordinates = features.map(item => {
          return item.get('geometry').getCoordinates();
        });
        this.view.fit(boundingExtent(coordinates), {duration: 600});
      });
    });

    this.vectorMap.on('pointermove', (event) => {
      this.vectorMap.getTargetElement().style.cursor = 'default';
      if(this.mouseHoveredFeature){
        this.mouseHoveredFeature.setStyle(undefined);
        this.mouseHoveredFeature = null;
      }
      this.vectorMap.forEachFeatureAtPixel(event.pixel, (feature) => {
        this.vectorMap.getTargetElement().style.cursor = 'pointer';
        let features = feature.get('features');
        if (!features)
        {
          return true;
        }
        let size: number = features.length;
        this.mouseHoveredFeature = feature as Feature;
        this.mouseHoveredFeature.setStyle( new Style({
          image: new Icon({
            src: '/assets/images/cluster-hover.png',
            opacity: 1,
            size: [68, 68],
            imgSize: [68, 68]
          }),
          text: new Text({
            text: size.toString(),
            fill: new Fill({
              color: '#3B86FF'
            })
          })
        }));
        return true;
      });
    });

    let extentChangeSubscription = this.extentDebouncer.pipe(
      debounceTime(500),
      distinctUntilChanged((prev, curr) => {return JSON.stringify(prev) === JSON.stringify(curr);})
    ).subscribe(extent => {
      this.extentChanged.next(extent);
      this.addOverlays();
    });
    this.subscriptions.push(extentChangeSubscription);

  }

  ngOnInit(): void {
    super.ngOnInit();
  }

  ngAfterViewInit(): void {
    this.vectorMap.setTarget('map-' + this.mapid);
  }

  ngOnDestroy(): void {
    this.vectorMap.dispose();
    this.subscriptions.forEach(subscription => {return subscription.unsubscribe();});
  }

  setClusterDistance(value: number):void {
    this.clusterDistance = value;
    this.clusterSource.setDistance(value);
  }

  setZoom(zoom: number): void {
    this.view.animate({zoom: zoom});
  }

  getZoom(): number
  {
    return this.view.getZoom();
  }

  setExtent(extent: ExtentObj): void {
    setTimeout(() => {
      this.view.fit([extent.minX, extent.minY, extent.maxX, extent.maxY], {duration: 600});
    }, 500);
  }

  setCenterDeffered(center: CYPoint, zoom?: number): void {
    let subscription = this.renderCompleted.subscribe(res=>{
      this.view.animate({center: [center.x, center.y]});
      zoom ? this.setZoom(zoom) : null;

      subscription.unsubscribe();
    })

  }

  setCenter(center: CYPoint, zoom?: number, animate: boolean = true): void {
    if (animate)
    {
      this.view.animate({center: [center.x, center.y]});
    }
    else{
      this.view.setCenter([center.x, center.y]);
    }
    if(zoom){
      this.setZoom(zoom);
    }
  }

    renderMarkers(markers: globalThis.Map<string, Marker> | Record<string, Marker>): void {
        let markerMap: globalThis.Map<string, Marker>;
        if (markers instanceof globalThis.Map) {
            markerMap = markers;
        } else {
            markerMap = new globalThis.Map(Object.entries(markers));
        }
        this.source.clear();
        let features: Feature[] = [];

        markerMap.forEach((marker: Marker, id: string) => {
            const point = this.projectionTransform.fromLatLng(marker.x, marker.y);
            let feature: Feature = new Feature({
                geometry: new Point([point.x, point.y])
            });
            feature.set('marker', marker);
            feature.setId(marker.id);
            features.push(feature);
        });

        this.source.addFeatures(features);
        this.renderingCompleted.next();
        this.addOverlays();
    }


    renderArrows(arrows: {[id:string]: Arrow}): void{
    this.arrowsSource.clear();
    let features = [];
    Object.keys(arrows).forEach(id => {
      let arrow: Arrow = arrows[id];

      const startPoint = this.projectionTransform.fromLatLng(arrow.startPoint.y, arrow.startPoint.x);
      let startCoordinate : Coordinate = [startPoint.x, startPoint.y];

      const endPoint = this.projectionTransform.fromLatLng(arrow.endPoint.y, arrow.endPoint.x);
      let endCoordinate: Coordinate = [endPoint.x, endPoint.y];
      let coordinates : Coordinate[] = [startCoordinate, endCoordinate];

      let feature: Feature = new Feature({
        geometry: new LineString(coordinates)
      });

      feature.setId(id);
      features.push(feature);
    });
    this.arrowsSource.addFeatures(features);
  }

  addOverlays(): void {
    this.vectorMap.getOverlays().clear();
    let extent = this.vectorMap.getView().calculateExtent(this.vectorMap.getSize());
    this.clusterSource.forEachFeatureInExtent(extent, (feature) => {
      if(feature.get('features').length  === 1 || this.clusterDistance === 0){
        feature.get('features').forEach(el => {
          let position = el.get('geometry').getCoordinates();
          let element: TemplateMarker = el.get('marker');
          let markers: Marker[] = feature.get('features').map(feature => {
            return feature.get('marker');
          });
          let marker = new Overlay({
            id: element.id,
            position: position,
            element: element.generateTemplateRef(markers) ? element.generateTemplateRef(markers).nativeElement: null,
            stopEvent: true,
            positioning: 'bottom-center' // Position the marker at the bottom center of the overlay
          });
          this.vectorMap.addOverlay(marker);
        });
      }
    });
  }

  renderCircles(circles: Circle[]): void {
    console.error(circles);
  }

  renderPolygons(polygons: Polygon[]): void {
    console.error(polygons);
  }

  setTileSource(source: TileSource): void {

    this.removeLayers();

    switch(source.getType()){
      case TileLayerSource.GIS: {
        let tileSource: GisTileSource = source as GisTileSource;
        let newTileSource = new XYZ({
          url: tileSource.tileUrl,
          attributions: tileSource.attribution,
          projection: tileSource.projection.code || ProjectionEnum.EPSG3857,
          crossOrigin: "Anonymous"
        });
        this.tileLayer.setSource(newTileSource);
        this.tileLayer.setVisible(true);
        break;
      }
      case TileLayerSource.RASTER_BASE64: {
        let tileSource: Base64TileSource = source as Base64TileSource;

        this.imageTileLayer = new ImageLayer({
          source: new Static({
            attributions: tileSource.attribution,
            url: "data:image/png;base64," + tileSource.base64Image,
            projection: new Projection({
              code: tileSource.projection.code as any,
              units: tileSource.projection.units as any,
              extent: [this.extent.minX, this.extent.minY, this.extent.maxX, this.extent.maxY],
            }),
            imageExtent: [-17811118.5, -7361866.1, 17811118.5, 14368684.2]
          })
        });

        this.vectorMap.getLayers().insertAt(0, this.imageTileLayer);
        break;
      }
      default: {
        console.error("Unsuported tile layer source", source.getType());
        break;
      }
    }
  }

  private removeLayers(): void {
    if(this.imageTileLayer) {
      this.vectorMap.removeLayer(this.imageTileLayer);
      this.imageTileLayer = null;
    }

    this.tileLayer.setVisible(false);
  }

  maybeEnableCorrectDrawMode(enabled: DrawMode): void {
    console.error(enabled);
  }

  toggleClustering(enabled: boolean): void {
    console.error(enabled);
  }

  toggleHeatmap(enabled: boolean): void {
    console.error(enabled);
  }

  toggleHistoryTimeline(enabled: boolean): void {
    console.error(enabled);
  }

  toggleTraffic(enabled: boolean): void {
    console.error(enabled);
  }

  renderButtons(buttons: Button[]): void {
    console.error(buttons);
  }

  toggleSearching(enabled: boolean): void {
    console.error(enabled);
  }

  zoomToFit(): void {

    if(this.source.getFeatures().length > 0) {
      this.view.fit(this.source.getExtent(), {duration: 600});
      this.view.setZoom(Math.min(this.view.getZoom(), this.view.getMaxZoom()));
    }
  }

  updateSize(): void {
    let interval = setInterval(() => {
      this.vectorMap.updateSize();
    }, 20);
    setTimeout(() => { clearInterval(interval); }, 1000);
  }
}
