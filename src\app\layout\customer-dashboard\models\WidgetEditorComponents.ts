import { WidgetType } from "../enums/widget-type.enum";
import { DefaultWidgetEditorComponent } from "../components/widgets/default-widget/default-widget-editor.component";
import { EditMapWidgetComponent } from "../components/widgets/maps-widget/edit-map-widget/edit-map-widget.component";
import { Type } from "@angular/core";
import { EditPlayerWidgetComponent } from "../components/widgets/player-widget/edit-player-widget/edit-player-widget.component";
import { EditDashboxWidgetComponent } from "../components/widgets/dashbox-widget/edit-dashbox-widget/edit-dashbox-widget.component";
import { EditGaugeWidgetComponent } from "../components/widgets/gauge-widget/edit-gauge-widget/edit-gauge-widget.component";
import { EditNotificationWidgetComponent } from "../components/widgets/notification-widget/edit-notification-widget/edit-notification-widget.component";
import { EditPieChartWidgetComponent } from "../components/widgets/pie-chart-widget/edit-pie-chart-widget/edit-pie-chart-widget.component";
import { EditChartWidgetComponent } from "../components/widgets/chart-widget/edit-chart-widget/edit-chart-widget.component";
import { EditSensorStatusWidgetComponent } from "../components/widgets/sensor-status-widget/edit-sensor-status-widget/edit-sensor-status-widget.component";
import { EditEmptySpaceWidgetComponent } from "../components/widgets/empty-space-widget/edit-empty-space-widget/edit-empty-space-widget.component";
import { EditEmbeddedFileComponent } from "../components/widgets/embedded-file/edit-embedded-file/edit-embedded-file.component";
import { EditUrlShortcutComponent } from "../components/widgets/url-shortcut/edit-url-shortcut/edit-url-shortcut.component";

export const WidgetEditorsComponents: { [id in WidgetType] : Type<DefaultWidgetEditorComponent>; } = {
    player: EditPlayerWidgetComponent,
    dashbox: EditDashboxWidgetComponent,
    timeline: null,
    map: EditMapWidgetComponent,
    pieChart: EditPieChartWidgetComponent,
    sensorStatus: EditSensorStatusWidgetComponent,
    gauge: EditGaugeWidgetComponent,
    notification: EditNotificationWidgetComponent,
    chart: EditChartWidgetComponent,
    lineChart: EditChartWidgetComponent,
    emptySpace: EditEmptySpaceWidgetComponent,
    embeddedFile: EditEmbeddedFileComponent,
    urlShortcut: EditUrlShortcutComponent

};