
.title-wrapper, .elements li {
    border-bottom: 1px solid var(--secondary-highlight-2);
    flex-wrap: wrap;
    padding: 10px 0;
    color: var(--primary-highlight-1);
}

.elements {
    margin: 0;
    padding: 0;
    
    li {
        font-size: 1rem;
        list-style-type: none;
        display: flex;
        align-items: center;
    }
    .actions {
        margin-left: auto;
        :host-context(.rtl) &{
            margin-right: auto;
            margin-left: 0;
        }    
        .edit {
            font-family: 'icomoon';
            margin-left: 10px;
            outline: none;
        }
        .edit {
            background-color: var(--secondary-2);
            color: var(--primary-1);
            border-color: transparent;
            &:hover, &:focus {
                background-color: var(--secondary-3);
                color:var(--light);
                outline: none;

            }
        }
    }
}
