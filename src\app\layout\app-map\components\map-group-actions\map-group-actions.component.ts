import { Component, OnInit, Output, EventE<PERSON>ter, ComponentRef, <PERSON><PERSON><PERSON>dren, ViewContainerRef, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { DefaultMapEditorComponent } from '../default-map-editor/default-map-editor.component';
import { ResourceGroupService } from 'app/shared/modules/data-layer/services/resource-group/resource-group.service';
import { ResourceGroup } from 'app/shared/modules/data-layer/models/resource-group';
import * as _ from 'lodash';
import { ResourceGroupWithActions } from 'app/shared/models/resource-group-with-actions';
import { ResourceActionType } from 'app/shared/enum/resource-action-type.enum';
import { ResourceActionsService } from '../../services/resource-actions.service';
import { Subscription } from 'rxjs/internal/Subscription';

@Component({
  selector: 'app-map-group-actions',
  templateUrl: './map-group-actions.component.html',
  styleUrls: ['./map-group-actions.component.scss']
})
export class MapGroupActionsComponent extends DefaultMapEditorComponent implements <PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>roy {
  @Output('onGroupAction') onGroupAction: EventEmitter<any> = new EventEmitter();
  resourceGroupModelStore: { [id: string]: ResourceGroup }
  resourceGroups: ResourceGroupWithActions[] = null;
  @ViewChildren ('dynamicAction', {read: ViewContainerRef}) public dynamicActionTargets: QueryList<ViewContainerRef>; 
  componentRef: ComponentRef<any>;
  searchText: string;
  subscriptions: Subscription[] = [];
  constructor(
    private resourceGroupService: ResourceGroupService,
    private resourceActionsService: ResourceActionsService
  ) {
    super();
  }
  ngOnDestroy(): void {
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
  }

  ngOnInit() {
    this.getResourceData();
  }

  getResourceData(): void {
    let resourceGroupGetAllSubscription=this.resourceGroupService.getAll().subscribe((response) => {
      this.resourceGroupModelStore = response;
      this.resourceGroups = _.values(response).filter(item => item.isHomogeneous === true).map(element => new ResourceGroupWithActions(element));
    });
    this.subscriptions.push(resourceGroupGetAllSubscription);
  }

  executeAction(actionType: ResourceActionType, identity: string, name: string, index: number): void{
    this.resourceActionsService.executeAction(actionType, identity, name, index, this.dynamicActionTargets);
   
 }
}
