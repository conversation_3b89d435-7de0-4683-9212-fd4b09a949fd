<app-config-uploader #configUploader Title="entityUploader" [ServerKeys]="serverKeysConfig" (JsonData)="onDataUploaded($event)"></app-config-uploader>

<app-modal #summaryModal [title]="'summary'">
    <ng-container ngProjectAs="contentModal">
        <div class="container-fluid">
            <table class="table table-striped">
                <tr>
                    <td>{{ 'totalProcessedItems' | translate }}</td>
                    <td colspan="2">{{totalEntitiesAdded+totalEntitiesUpdated+totalEntitiesFailed+totalEntitiesMissingMandatoryFields}}</td>
                </tr>
                <tr>
                    <td>{{ 'totalAdded' | translate }}</td>
                    <td colspan="2">{{totalEntitiesAdded}}</td>
    
                </tr>
                <tr>
                    <td>{{ 'totalUpdated' | translate }}</td>
                    <td colspan="2">{{totalEntitiesUpdated}}</td>
    
                </tr>
                <tr>
                    <td>{{ 'totalMissingMandatoryFields' | translate }}</td>
                    <td colspan="2">{{totalEntitiesMissingMandatoryFields}}</td>
    
                </tr>
                <tr>
                    <td>{{ 'totalFailed' | translate }}</td>
                    <td colspan="2">{{totalEntitiesFailed}}</td>
                </tr>
            </table>    
            <a class="fa fa-download" (click)="downloadSummaryFile()" href="javascript:void(0);" title='{{ "downloadSummary" | translate }}'></a>
        </div>
    </ng-container>
    <ng-container ngProjectAs="footerModal">
        <button type="button" class="btn btn-primary" (click)="onSummaryConfirm()">{{ "ok" | translate }}</button>
    </ng-container>
</app-modal>
