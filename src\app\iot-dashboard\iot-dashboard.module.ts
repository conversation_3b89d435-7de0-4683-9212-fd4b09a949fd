import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MainLayoutComponent } from './main-layout/main-layout.component';
import {IotDashboardRouting} from "app/iot-dashboard/iot-dashboard-routing";
import { DashboardComponent } from './dashboard/dashboard.component';
import { CamerasComponent } from './dashboard/cameras/cameras.component';
import { SystemStructureComponent } from './dashboard/system-structure/system-structure.component';
import { AlarmSummaryComponent } from './dashboard/alarm-summary/alarm-summary.component';
import { MapComponent } from './dashboard/map/map.component';
import { NotificationListComponent } from './dashboard/notification-list/notification-list.component';
import { GenericMapModule } from '../shared/modules/generic-map/generic-map.module';
import { CymbiotMapModule } from '../shared/modules/cymbiot-map/cymbiot-map.module';
import { SharedModule } from '../shared/modules/shared.module';
import { WarningListComponent } from './dashboard/warning-list/warning-list.component';
import { SidebarComponent } from './sidebar/sidebar.component';
import { SimpleThemeService } from '../shared/services/simple-theme.service';
import { WeatherInfoComponent } from './dashboard/weather-info/weather-info.component';

@NgModule({
  declarations: [
    MainLayoutComponent,
    DashboardComponent,
    CamerasComponent,
    SystemStructureComponent,
    AlarmSummaryComponent,
    MapComponent,
    NotificationListComponent,
    WarningListComponent,
    SidebarComponent,
    WeatherInfoComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    IotDashboardRouting,
    SharedModule,
    GenericMapModule,
    CymbiotMapModule
  ],
  providers: [
    SimpleThemeService
  ]
})
export class IotDashboardModule { }
