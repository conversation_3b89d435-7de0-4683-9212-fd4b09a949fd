<div>
    <div class="range-time row">
        <div class="col-6">
            <div> {{ 'fromTime' | translate }} </div>
            <app-datetime-picker icon="fa fa-clock-o" [(date)]="fromTime" dataType="fromTime" showSeconds="true" [showDateInForm]="true"
                [timeOnly]="false" [inline]="false" (valueChange)="onFromTimeChange($event)">
            </app-datetime-picker>
        </div>

        <div class="col-6">
            <div> {{ 'toTime' | translate }} </div>
            <app-datetime-picker icon="fa fa-clock-o" [(date)]="toTime" [showDateInForm]="true" showSeconds="true" [inline]="false"
                [timeOnly]="false" (valueChange)="onToTimeChange($event)">
            </app-datetime-picker>
        </div>
    </div>
</div>