import { NgModule } from '@angular/core';
import { InventoryTableModule } from 'app/shared/modules/inventory-table/inventory-table.module';
import { SharedModule } from 'app/shared/modules/shared.module';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { AddDeviceComponent } from './add-device/add-device.component';
import { AddInventoryComponent } from './add-inventory/add-inventory.component';
import { InventoryNewRoutingModule } from './inventory-new-routing.module';
import { InventoryNewComponent } from './inventory-new.component';
import { AddResourceGroupComponent } from './resource-group-manager/add-resource-group/add-resource-group.component';
import { ResourceGroupManagerComponent } from './resource-group-manager/resource-group-manager.component';


@NgModule({
  imports: [
    SharedModule,
    InventoryTableModule,
    InventoryNewRoutingModule,
    ConfirmDialogModule
  ],
  declarations: [
    InventoryNewComponent,
    ResourceGroupManagerComponent,
    AddInventoryComponent,
    AddResourceGroupComponent,
    AddDeviceComponent,
  ],
  exports: [
  ],
  entryComponents: [
    AddInventoryComponent,
    AddDeviceComponent
  ]
})
export class InventoryNewModule { }
