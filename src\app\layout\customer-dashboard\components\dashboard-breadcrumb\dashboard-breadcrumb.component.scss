.breadcrumb-wrapper {
    display: flex;
    flex-direction: row;
    
    .selected-item-title {
        margin-right: 20px;
        margin-bottom: 0;
    }
    a {
        color: var(--primary-1);
        &:hover, &:focus {
            text-decoration: none;
            outline: none;
        }
    }
}
.selected-item-children {
    ::ng-deep {
        ul {
            padding: 0;
            margin: 0;
        }
        ul, ul li {
            display: inline;
        }
        a {
            color: var(--primary-1);
            &:hover, &:focus {
                text-decoration: none;
                outline: none;
            }
        }
    }
}