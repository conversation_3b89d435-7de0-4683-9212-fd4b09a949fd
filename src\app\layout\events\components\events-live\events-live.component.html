<div class="live-events-action">
    <span class="status-text">{{ ('eventsPage.liveView' | translate) + ' ' + ((liveEventsEnabled ? 'eventsPage.liveViewOn': 'eventsPage.liveViewOff') | translate) }}</span>
    <p-inputSwitch styleClass="inputswitch-small" title="{{ 'changeTheme' | translate }}" [(ngModel)]="liveEventsEnabled"  (onChange)="toggleLiveEvents($event)"></p-inputSwitch>
</div>

<ng-turbo-table [data]="data" [columns]="columns" [showResultsCountTop]="true" [exportFileName]="'inventoryExport'"
    [showToggleColumns]="false" [numOfRows]="5" [enableRowClick]="true" [scrollHeight]="'600px'">


<ng-template #actionsDefault let-rowData>
    <app-default-actions [row]='rowData' (selectedItem)="onSelectedItem($event)" ></app-default-actions>
</ng-template>

<ng-template #imageCell let-rowData>
    <app-display-image [data]='rowData.image'></app-display-image>
</ng-template>