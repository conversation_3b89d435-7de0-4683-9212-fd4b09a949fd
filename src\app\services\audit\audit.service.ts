import { Injectable } from '@angular/core';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Audit } from '../../layout/audit/models/audit.model';
import { environment } from '../../../environments/environment';
import { apiMap } from '../../shared/services/api.map';

export interface AuditPageRequest {
  PageIndex: number;
  PageSize: number;
  StartTimestamp: string;
  EndTimestamp: string;
  Message?: string;
  IP?: string;
  User?: string;
  FileType?: string;
}

export interface AuditPageResponse {
  items: Audit[];
  totalCount: number;
}

// This interface represents the raw response from the API
export interface RawAuditResponse {
  items: {
    id: string;
    user: string;
    message: string;
    timestamp: string;
    ip: string;
  }[];
  totalCount: number;
}

@Injectable({
  providedIn: 'root'
})
export class AuditService {
  private readonly API_URL = environment.apiUrl + '/token/audit/page';

  constructor(private http: HttpClient) { }

  /**
   * Get audits with pagination and filtering
   * @param request The request parameters for pagination and filtering
   */
  getAudits(request: AuditPageRequest): Observable<AuditPageResponse> {
    return this.http.post<any>(this.API_URL, request).pipe(
      map(response => {
        console.log('Raw API response:', response);

        // Ensure we have a valid response structure
        if (!response) {
          console.error('Empty response from API');
          return { items: [], totalCount: 0 };
        }

        // Check if the response has the expected structure
        if (!Array.isArray(response.items)) {
          console.error('Invalid response format - items is not an array:', response);
          // Try to handle different response formats
          if (Array.isArray(response)) {
            return { items: response, totalCount: response.length };
          } else {
            return { items: [], totalCount: 0 };
          }
        }

        return response;
      })
    );
  }


  exportAudit(request: AuditPageRequest): Observable<HttpResponse<Blob>> {
    const url = environment.apiUrl + apiMap.auditExport.url;
    return this.http.post(url, request, {
      responseType: 'blob',
      observe: 'response'
    })
  }

  /**
   * Log a custom audit message
   * @param message The message to log
   */
  logCustomAudit(message: string): Observable<any> {
    const auditLog = [
      {
        Message: message,
        Timestamp: new Date().toISOString()
      }
    ];

    return this.http.post(environment.apiUrl + '/token/audit/upload', auditLog);
  }
}
