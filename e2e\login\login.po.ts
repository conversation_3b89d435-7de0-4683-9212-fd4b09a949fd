import { browser, by, element } from 'protractor';

export class LoginPage {
  navigateTo() {
    return browser.get('/login') as Promise<any>;
  }

  setUserName(userName) {
    return element(by.id('float-input-user')).sendKeys(userName);
  }

  setPassword(password) {
    return element(by.id('float-input-pass')).sendKeys(password);    
  }

  doLogin() {
    return element(by.css('app-login form a.btn')).click();    
  }

  getInputPasswordValue() {
     return element(by.css('#float-input-pass')).getAttribute('value');        
  }
}