import { Injectable } from '@angular/core';
import { Map } from 'app/shared/modules/data-layer/models/map';
import { Subject, Observable } from 'rxjs';
import { MapActionType } from '../enums/map-action-type.enum';
import { Guid } from 'app/shared/enum/guid';
import { MapLayer } from 'app/shared/modules/data-layer/models/map-layer';

@Injectable({
  providedIn: 'root'
})
export class MapUtilsService {

  private mapDataChange = new Subject<Map>();
  private filterMapChange = new Subject<{[mapId: string]: string[]}>();
  private mapValidatorChange = new Subject<{ [formName: string]: boolean }>();
  private appliedFiltersByMapInstanceId:{[mapId: string]: string[]} = {};
  constructor() { }

  setMapDataChange(item: Map): void {
    this.mapDataChange.next(item)
  }

  setFilterDataChange(filters: string[], mapInstanceId: string): void {
    this.appliedFiltersByMapInstanceId[mapInstanceId] = filters;
    this.filterMapChange.next(this.appliedFiltersByMapInstanceId);
  }

  getFilterDataChange(): Observable<{[mapInstanceId: string]: string[]}> {
    return this.filterMapChange.asObservable();
  }

  returnFilterForMapWithInstanceId(mapInstanceId:string):string[] {
    return this.appliedFiltersByMapInstanceId[mapInstanceId];
  }

  getMapDataChange(): Observable<Map> {
    return this.mapDataChange.asObservable();
  }

  setMapValidatorChange(item: { [formName: string]: boolean }): void {
    this.mapValidatorChange.next(item);
  }

  getMapValidatorChange(): Observable<{ [formName: string]: boolean }> {
    return this.mapValidatorChange.asObservable();
  }

  returnNewMapLayer(data: MapLayer, mapAction: MapActionType, selectedMapId: string): MapLayer {
    let newMapLayer: MapLayer = null
    switch (mapAction) {
      case MapActionType.addLayer:
        newMapLayer = new MapLayer({
          identity: Guid.create().toString(),
          mapIdentity: selectedMapId,
          hidden: data.hidden,
          name: data.name,
          selectedResourceGroups: data.selectedResourceGroups,
          selectedResources: data.selectedResources,
          selectedStates: data.selectedStates,
          selectedTypes: data.selectedTypes
        })
        break;
      default:
        throw "map action not found";
    }
    return newMapLayer;
  }

  filterResourceGroupOnMapWithInstanceId(groupId:string, mapInstanceId:string): void {
    let filters: string[] = this.appliedFiltersByMapInstanceId[mapInstanceId] ? this.appliedFiltersByMapInstanceId[mapInstanceId] : []; 
    let groupIndex = filters.length > 0 ? this.appliedFiltersByMapInstanceId[mapInstanceId].findIndex(group => group === groupId) : -1;
    if(groupIndex > -1){
      filters.splice(groupIndex, 1);
    }
    else {
      filters.push(groupId);
    }
    this.setFilterDataChange(filters, mapInstanceId);
  }
}
