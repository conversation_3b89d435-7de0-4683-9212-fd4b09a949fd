import { TableColumnProperties } from 'app/shared/components/ng-turbo-table/models/table-column';

export const entitiesActiveColumns: TableColumnProperties[] = [
    {
        field: "visibleDescription",
        header: "description",
        frozen: false,
        visibile: true,
        sortable: true
    },
    {
        field: "entity",
        header: "entity",
        frozen: false,
        visibile: true,
        sortable: true
    },
    {
        field: "timestamp",
        header: "time_stamp",
        frozen: false,
        visibile: true,
        sortable: true
    },
    {
        field: "entityValue",
        header: "entity_value",
        frozen: false,
        visibile: true,
        sortable: true
    },
    {
        field: "confidence",
        header: "confidence",
        frozen: false,
        visibile: true,
        sortable: true
    },
    {
        field: "actions",
        header: "actions",
        frozen: false,
        visibile: true,
        sortable: true
    }
]