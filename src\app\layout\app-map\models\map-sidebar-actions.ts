
import { GlobalAction } from "../../../shared/models/global-action.interface";
import { MapActionType } from "../enums/map-action-type.enum";

export const mapSidebarActions: GlobalAction[] = [
    {name: 'cancel', type: MapActionType.cancel, importance: 'secondary', disabled: false},
    {name: 'save', type: MapActionType.save, importance: 'primary', disabled: false},
    {name: 'finish', type: MapActionType.finish, importance: 'primary', disabled: false}
]