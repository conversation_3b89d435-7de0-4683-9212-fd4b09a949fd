:host {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    .dashboard-tree {
        border-width: 0 1px 1px 1px;
        border-style: solid;
        border-color:var(--secondary-highlight-2);
        background-color: var(--secondary-1);
    }
    .btn-secondary {
        border-color:var(--secondary-highlight-2);
        background: var(--secondary-1)
    }
}
.widgetWrapper {
    display:grid;
    grid-gap: 10px;
    grid-auto-rows: 78px;
    grid-template-columns: repeat(2, 1fr);
}
.widget {
    background: var(--secondary-highlight-5);
    padding: 15px;
    font-size: 0.875rem;
    text-align: center;
    cursor: pointer;
}