import { Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, QueryList, Renderer2, ViewChild, ViewChildren } from '@angular/core';
import { Subscription } from 'rxjs';
import { DashboardWidget } from '../../models/dashboard-widget.interface';
import { Widget } from '../../models/widget';
import { DashboardUtilsService } from '../../services/dashboard-utils.service';


@Component({
  selector: 'app-dashboard-layout',
  templateUrl: './dashboard-layout.component.html',
  styleUrls: ['./dashboard-layout.component.scss']
})
export class DashboardLayoutComponent implements OnInit, OnDestroy {
  @ViewChild('widgetWrapper', {static: false}) widgetWrapper: ElementRef;
  @ViewChild('layoutContainer', {static: false}) layoutContainer: ElementRef;
  @ViewChildren('widgetElement') widgetElements: QueryList<ElementRef>;
  @Input() widgets: DashboardWidget[] = [];
  @Input() editMode: boolean = false;
  @Input() selectedWidget: DashboardWidget;
  @Output() onAddWidget: EventEmitter<DashboardWidget> = new EventEmitter()
  @Output() onChangeWidgetOrdering: EventEmitter<{dragElCurrentIndex:number, dragOverElCurrentIndex: number}> = new EventEmitter();

  public draggedWidget: DashboardWidget = null;
  private subscriptions: Subscription[] = [];

  public dragEl = null;
  public dragElCurrentIndex = null;
  public dragOverElCurrentIndex = null;
  public nextEl = null;
  public newPos = null;
  public currentPos: DOMRect[] = [];


  constructor(
    private dashboardUtilsService: DashboardUtilsService,
    private renderer: Renderer2
  ) { }
 

  ngOnInit(): void {

    let draggedWidgetSubscription = this.dashboardUtilsService.draggedWidgetData.subscribe(widgetData => {
      this.draggedWidget = widgetData;
    });
    this.subscriptions.push(draggedWidgetSubscription);

    let widgetDataChangeSubscription = this.dashboardUtilsService.getWidgetDataChange().subscribe(widgetData => {
      this.changeWidgetSize(widgetData.widget);
    });
    this.subscriptions.push(widgetDataChangeSubscription);
  }

  onEditMode():void {
    this.currentPos = this.getCurrentElementPositions();
  }

  private changeWidgetSize(widget: Widget):void {

    let index = this.widgets.findIndex(el => {return el.id === widget.id;});
    if(index > -1){
      this.widgets[index].size = widget.size;
    }
  }

  onDropAddWidget(): void{
    if(this.draggedWidget){
      this.onAddWidget.emit(this.draggedWidget);
      this.draggedWidget = null;
      setTimeout(() => {
        this.currentPos = this.getCurrentElementPositions();
      }, 200);
    }
  }

  getCurrentElementPositions(): DOMRect[]{
    let currentPos: DOMRect[] = [];
    this.widgetElements.forEach(element => {
      element.nativeElement.draggable = true;
      currentPos.push(element.nativeElement.getBoundingClientRect());
    });
    return currentPos;
  }

  onDragStart(event:DragEvent, currentIndex:number): void {
    if(!this.editMode){
      return;
    }

    this.dragEl = event.target;
    this.dragElCurrentIndex = currentIndex;
    this.nextEl = this.dragEl.nextSibling;

    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('Text', this.dragEl.textContent);

    setTimeout(() => {
      this.renderer.addClass(this.dragEl, 'ghost');
    }, 0);
  }

  onDragOver(event:DragEvent, currentIndex: number): void {
    if(!this.editMode){
      return;
    }

    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';

    let elementTarget = event.target as Element;
    let target = elementTarget.parentElement as HTMLElement;

    if( target && target !== this.dragEl && this.dragEl){
      if(!target.classList.contains('widget')) {
        event.stopPropagation();
      }
      else {
        let targetPos = target.getBoundingClientRect();
        let next = (event.clientY - targetPos.top) / (targetPos.bottom - targetPos.top) > .5 || (event.clientX - targetPos.left) / (targetPos.right - targetPos.left) > .5;
        this.renderer.insertBefore(this.widgetWrapper.nativeElement, this.dragEl, next && target.nextSibling || target);
        this.dragOverElCurrentIndex = currentIndex;
      }
    }
  }

  onDragEnd(event:DragEvent):void {
    if(!this.editMode){
      return;
    }

    event.preventDefault();
    this.newPos = this.getCurrentElementPositions();
    this.renderer.removeClass(this.dragEl, 'ghost');

    if(this.dragElCurrentIndex !== null && this.dragOverElCurrentIndex !== null){
      this.onChangeWidgetOrdering.emit({dragElCurrentIndex: this.dragElCurrentIndex, dragOverElCurrentIndex: this.dragOverElCurrentIndex});
    }
    this.resetState();
  }

  ngOnDestroy(): void{
    this.subscriptions.forEach(item => {
      item.unsubscribe();
    });
  }

  private resetState(){
    this.dragEl = null;
    this.dragElCurrentIndex = null;
    this.dragOverElCurrentIndex = null;
  }

  public restoreWidgetPositionAndLayout(oldWidgetList: Widget[], widgetsToBeAdded:{index: number, widget: DashboardWidget}[]): void {

    if(oldWidgetList && this.widgets && oldWidgetList.length !== this.widgets.length){
      let widgetsToBeRemoved: Widget[] = this.widgets.filter(widget => {return !oldWidgetList.find(el => {return el.id === widget.id;});});
      widgetsToBeRemoved.forEach((widget) => {
        let index = this.widgets.findIndex(element => {return element.id === widget.id;});
        this.widgets.splice(index, 1);
      });
      widgetsToBeAdded.forEach(widgetObject => {
        this.widgets.splice(widgetObject.index, 0, widgetObject.widget);
      });
    }
    if (oldWidgetList && this.widgets && this.widgets.length > 0)
    {
      oldWidgetList.forEach((widget, index) => {
        if(widget.id !== this.widgets[index].id){
          this.widgets = this.dashboardUtilsService.arrayMove(this.widgets, 
          this.widgets.findIndex(el => {return el.id === widget.id;}), index);
        }
      });
    }
  }
}
