


@media (min-width: 1920px) {
  .content-wrapper {
    padding: 0 15px 15px 0px;
  }
}



  @media (max-width: 1600px) {
    .container-dashboard .column:nth-child(2) {
      flex: 1.5;
    }
  }


@media (max-width: 1200px) {
  /* Ensure viewport-container takes full width */
  .viewport-container {
    width: 100% !important;
    max-width: 100% !important;
  }

  .container-dashboard {
    flex-direction: column !important;
    height: auto !important;
    width: 100% !important;
    max-width: 100% !important;
    max-height: 100%;
  }

  .container-dashboard .column {
    flex: 1 1 100% !important;
    max-width: 100% !important;
    width: 100% !important;
    margin-bottom: 16px !important;
    box-sizing: border-box !important;
    gap: 16px !important; /* Equal gap between panels in responsive mode */
  }

  .container-dashboard .dash-panel {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  .container-dashboard .panel-1 {
    height: 300px !important;
    max-height: 300px !important;
  }

  .container-dashboard .panel-2 {
    height: 500px !important;
    max-height: 500px !important;
  }

  .logs-container {
    height: 350px !important;
    max-height: 350px !important;
    overflow-y: scroll !important;
  }

  .container-dashboard .panel-3 {
    height: 150px !important;
    max-height: 150px !important;
  }

  .container-dashboard .panel-4 {
    height: 500px !important;
    max-height: 500px !important;
  }

  .container-dashboard .panel-5 {
    height: 600px !important;
    max-height: 600px !important;
  }

  .viewport-container {
    height: auto !important;
    overflow: visible !important;
  }

  .viewport-content {
    height: auto !important;
    max-height: none !important;
    overflow: visible !important;
  }

   .iot-modal-search {
    width: 90% !important;
   }

  .iot-modal-search, 
  .filters-content,
  .right-filter{
      flex-direction: column !important;
}

.iot-modal-header h3 {
  width: 100% !important;
}

.filters-content {
    display: grid;
    margin-top: 10px;
}

:host ::ng-deep {
    .p-dialog {
        .p-dialog-header {
            .iot-modal-header {
                    h3 {
                        width: 100%;
                        .dossier-name {
                            flex: 1;
                            overflow: hidden;
                            text-overflow: unset;
                            white-space: normal;
                        }
                    }  

            }
        }
    }
}
}

@media (max-width: 768px) {
  .sidebar-container {
    position: absolute;
    top: 0;
    left: 0;
    height: 100vh;
    transform: translateX(-100%);
  }

  .sidebar-container.expanded {
    transform: translateX(0);
  }

  .container-dashboard {
    padding: 8px !important;
    gap: 8px !important;
  }

  .container-dashboard .dash-panel {
    width: 100% !important;
    box-sizing: border-box !important;
  }

  .container-dashboard .panel-1 {
    height: 250px !important;
    max-height: 250px !important;
  }

  .container-dashboard .panel-2 {
    height: 400px !important;
    max-height: 400px !important;
  }

  .container-dashboard .panel-3 {
    height: 120px !important;
    max-height: 120px !important;
  }

  .container-dashboard .panel-4 {
    height: 400px !important;
    max-height: 400px !important;
  }

  .container-dashboard .panel-5 {
    height: 500px !important;
    max-height: 500px !important;
  }

   .camera-status-summary {
    flex-direction: column;
  }

  .log-details {
    grid-template-columns: repeat(2, 1fr) !important;
  }
  
}

@media (max-width: 480px) {
  .container-dashboard {
    padding: 6px !important;
    gap: 6px !important;
  }

  .container-dashboard .dash-panel {
    width: 100% !important;
    box-sizing: border-box !important;
  }

  .container-dashboard .panel-1 {
    height: 200px !important;
    max-height: 200px !important;
  }

  .container-dashboard .panel-2 {
    height: 350px !important;
    max-height: 350px !important;
  }

  .container-dashboard .panel-3 {
    height: 100px !important;
    max-height: 100px !important;
  }

  .container-dashboard .panel-4 {
    height: 350px !important;
    max-height: 350px !important;
  }

  .container-dashboard .panel-5 {
    height: 450px !important;
    max-height: 450px !important;
  }

  .dashboard-title {
    font-size: 18px !important;
  }
}

