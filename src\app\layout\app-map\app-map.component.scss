@import '~styles/app';

:host {
    width: 100%;
    display: block;
}

.page-format {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 0px;
    position: absolute;
}

.content-wrapper {
    flex: 1;
    overflow: auto;
    width: 100%;
    margin: 0 auto;
}

.iot-map {
    height: 100%;
}

:host {
    .map-actions-wrapper {
        z-index: 1;
        position: absolute;
        width: calc(100% - 46px);
        top: 23px;
        left: 23px;
        margin-right: 23px;
        border-radius: 4px;
        background: rgba(var(--primary-skin), var(--primary-opacity-1));
        @include box-shadow(1px, 1px, 14px, 2px, var(--shadow));
        min-height: 44px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
        backdrop-filter: saturate(180%) blur(4px);
    }
    
    .map-layout-content, .map-wrapper {
        width: 100%;
        height: 100%;
    }

    ::ng-deep{
        #results-div {
            margin-top: 0;
        }
        .sidebar-div {
            padding: 0;
        }
        .sidebar-actions {
            margin-top: auto;
            margin-right: 25px;
            display: flex;
            justify-content: flex-end;
            button {
                margin-left: 10px;
            }
        }
        .input-element {
            width: 100%;    
        }
        .form-item {
            position: relative;
            &.multiple {
                display: flex;
                justify-content: space-between;
                flex-flow: wrap;

                h2 {
                    width: 100%;
                }
            }
            margin: 20px 0;
        }

        .form-item {
            .groups-active {
                background: var(--light);
                border-bottom: none !important;
                border-bottom-left-radius: 0;
                border-bottom-right-radius: 0;
            }
        }

        .dynamic-edit-component {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }
       
        .ui-calendar {
            &.full-width {
                min-width: 100%;
            }
            > .ui-inputtext {
                padding: 12px 20px;
                width: 100%;
            }
        }

        .input-error {
            position: absolute;
            bottom: -18px;
            left: 0;
            color: var(--error);
            font-size: 0.8rem;
        }

        input::placeholder {
            opacity: 0.5;
        }
        
        .icon-outline-fullscreen {
            font-size: 2.4rem;
            color: var(--primary-1);
            &:hover {
                color: var(--secondary-3);
            }
        }
    }
}
.side-container {
    ::ng-deep {
        h1 {
            font-size: 1.375rem;
        }
        h2 {
            font-size: 1.125rem;
        }    
        h2, h1 {
           color: var(--secondary-3);
           margin-bottom: 10px;
        }
        .dynamic-edit-component {
            overflow-y: scroll;
            padding: 25px;
            height: calc(100vh - 70px);
        }
    }
}
