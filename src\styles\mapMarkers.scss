@import '~styles/colors';
@import '~styles/animate';

.marker-wrapper {
    position: relative;
    display: inline-block;
    width: 32px;
    left: -50%;
    z-index: 1;

    .marker {
        position: relative;
        font-family: 'icomoon';
        font-size: 40.6px;
        line-height: 40.6px;

        &::before {
            content: "\e91f";
        }

        &.online,
        &.on,
        &.ON,
        &.onParamsFluctuation,
        &.ON.PARAMS.FLUCTUATION {
            color: $online;

            .core_res_light {
                &::before {
                    color: $dim;
                }
            }

            .core_res_inputchannel,
            .core_res_cabinet,
            .core_res_powermeter {
                &::before {
                    color: $online;
                }
            }
        }

        &.off,
        &.OFF,
        &.OFF.MAINTENANCE.CHECK,
        &.offMaintenanceCheck,
        &.OFF.PARAMS.FLUCTUATION,
        &.offParamsFluctuation {
            color: $off;
        }

        &.offline,
        &.offByRadio,
        &.OFFLINE {
            color: $offline;

            .core_res_light,
            .core_res_inputchannel,
            .core_res_cabinet,
            .core_res_powermeter {
                &::before {
                    color: $offline;
                }
            }
        }

        &.unknown,
        &.checkLight,
        &.Checklight,
        &.CHECK.LIGHT {
            color: $unknown;

            .core_res_light,
            .core_res_inputchannel,
            .core_res_cabinet,
            .core_res_powermeter {
                &::before {
                    color: $unknown;
                }
            }
        }

        &.dim,
        &.MAINTENANCE.CHECK,
        &.maintenanceCheck {
            color: $dim;
        }

        &.alarmed,
        &.simIssue,
        &.SIM.ISSUE {
            color: $error;

            .core_res_light,
            .core_res_inputchannel,
            .core_res_cabinet {
                &::before {
                    color: $error;
                }
            }
        }

        &.offByPower {
            color: $offByPower;

        }

        &.tempError {
            color: $tempError;
        }

        &.error {
            color: $error;
        }

        &.notSpecified {
            color: $notSpecified;
        }

        &.withoutCommunication,
        &.NO.COMM {
            color: $withoutCommunication;
        }

        &.resource-group {
            color: var(--info-2);
        }

        .device {
            position: absolute;
            top: 1px;
            left: 3px;
            text-align: center;
            width: 26px;
            height: 27px;
            border-radius: 50%;
            background: $white;
            font-size: 1rem;
            line-height: 1.6875rem;
            cursor: pointer;
        }

        .core_res_inputchannel {
            &::before {
                content: "\e902";
            }
        }

        .core_res_input {
            font-family: 'FontAwesome';

            &::before {
                content: "\ea4b";
            }
        }

        .core_res_output {
            font-family: 'FontAwesome';

            &::before {
                content: "\e9b7";
            }
        }

        .core_res_light {
            &::before {
                content: "\e900";
            }
        }

        .core_res_cabinet {
            font-family: 'FontAwesome';
        }

        .core_res_cabinet {
            &::before {
                content: "\f1ad";
            }
        }

        .core_res_sensor {
            &::before {
                content: "\e912";
            }
        }

        .core_res_map {
            &::before {
                content: "\e90b";
            }
        }

        .core_res_powermeter {
            font-family: 'FontAwesome';

            &::before {
                content: "\f0e7";
            }
        }

        .core_res_dashboard {
            &::before {
                content: "\e907";
            }
        }

        &.addEvent {
            color: $addEvent;
            animation: wobble 1s infinite;

            .device {
                background: $addEvent;
                font-family: "FontAwesome";

                &::before {
                    content: "\f12a";
                    color: $white;
                }
            }
        }
    }

    &.in-view {
        z-index: 2; /* Ensure marker with popup is above other markers */
    }
}
