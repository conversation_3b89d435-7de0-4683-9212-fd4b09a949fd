import { Type } from "@angular/core";
import { DefaultMapEditorComponent } from "../components/default-map-editor/default-map-editor.component";
import { MapEditComponent } from "../components/map-edit/map-edit.component";
import { MapLayersEditComponent } from "../components/map-layers-edit/map-layers-edit.component";
import { MapFiltersComponent } from "../components/map-filters/map-filters.component";
import { MapActionType } from "../enums/map-action-type.enum";
import { MapGroupActionsComponent } from "../components/map-group-actions/map-group-actions.component";
import { MapSearchComponent } from "../components/map-search/map-search.component";

export const MapEditorComponents: {[id in MapActionType]: Type<DefaultMapEditorComponent>} = {
    addNew: MapEditComponent,
    edit: MapEditComponent,
    editLayers: MapLayersEditComponent,
    filter: MapFiltersComponent,
    save: null,
    cancel: null,
    saveAsDefault: null,
    delete: null,
    fullScreen: null,
    search: MapSearchComponent,
    groupAction: MapGroupActionsComponent,
    addLayer: null,
    updateLayer: null,
    finish: null,
    resetSearch: null
};