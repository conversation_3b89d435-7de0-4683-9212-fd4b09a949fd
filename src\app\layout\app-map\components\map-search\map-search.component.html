<div class="map-group-search-wrapper">
  <div class="title-wrapper">
    <h2>{{ 'searchBox' | translate}}</h2>
  </div>

  <form [formGroup]="searchForm" (ngSubmit)="onSubmit()">

    <div class="form-item">
      <p-dropdown formControlName="fieldName" [options]="formFilters.fieldName" [styleClass]="'input-element'"
        [placeholder]="'selectFieldName' | translate">
        <ng-template let-item pTemplate="selectedItem">
          <span>{{item.label| translate}}</span>
        </ng-template>
        <ng-template let-item pTemplate="item">
          <div class="ui-helper-clearfix">
            <div>{{item.label | translate}}</div>
          </div>
        </ng-template>
      </p-dropdown>
      <div *ngIf="fieldName.invalid && (fieldName.dirty || fieldName.touched)" class="input-error">
        <span>{{'formErrors.required' | translate}}</span>
      </div>
    </div>
    <div class="form-item">
      <p-dropdown formControlName="operand" [options]="formFilters.operand" [styleClass]="'input-element'"
        [placeholder]="'selectOperand' | translate">
        <ng-template let-item pTemplate="selectedItem">
          <span>{{item.label| translate}}</span>
        </ng-template>
        <ng-template let-item pTemplate="item">
          <div class="ui-helper-clearfix">
            <div>{{item.label | translate}}</div>
          </div>
        </ng-template>
      </p-dropdown>
      <div *ngIf="operand.invalid && (operand.dirty || operand.touched)" class="input-error">
        <span>{{'formErrors.required' | translate}}</span>
      </div>
    </div>
    <div class="form-item">
      <input type="text" name="freeText" class="name-control" formControlName="freeText"
        placeholder="{{'freeText' | translate}}" />
      <div *ngIf="freeText.invalid && (freeText.dirty || freeText.touched)" class="input-error">
        <span>{{'formErrors.required' | translate}}</span>
      </div>
    </div>
    <div class="sidebar-actions">
      <button class="btn btn-secondary" type="submit" (click)="onReset()" [disabled]="!searchForm.valid">{{ 'reset' |
        translate}}</button>
      <button class="btn btn-primary" type="submit" [disabled]="!searchForm.valid">{{ 'execute' | translate}}</button>
    </div>
  </form>
</div>