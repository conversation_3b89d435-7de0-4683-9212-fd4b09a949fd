import { Component, ComponentFactoryResolver, ComponentRef, OnDestroy, OnInit, Renderer2, Type, ViewChild, ViewContainerRef } from "@angular/core";
import { CymsidebarService } from "app/shared/components/cym-sidebar/cym-sidebar.service";
import { Dashboard } from "app/shared/modules/data-layer/models/dashboard";
import { DashboardAction } from "./models/dashboard-action.interface";
import { DashboardObject } from "./models/dashboard-object.interface";
import { DashboardState } from "./models/dashboard-state.interface";
import { DashboardWidget } from "./models/dashboard-widget.interface";
import { SwapArrayElementsEvent } from "./models/swap-array-elements-event.interface";
import { DashboardUtilsService } from "./services/dashboard-utils.service";

import { TranslateService } from "@ngx-translate/core";
import { Guid } from "app/shared/enum/guid";
import { NavigationService, Pages } from "app/shared/services/navigation.service";
import { ConfirmationService, MessageService } from "primeng/api";
import { Subscription } from "rxjs";
import { DashboardLayoutComponent } from "./components/dashboard-layout/dashboard-layout.component";
import { DashboardActionType } from "./enums/dashboard-action-type.enum";


import { DashboardService } from "app/services/dashboard/dashboard.service";
import { ToastTypes } from 'app/shared/enum/toast-types';
import { DashboardPriority } from 'app/shared/modules/data-layer/enum/dashboard/dashboard-priority.enum';
import { ResourceGroupService } from "app/shared/modules/data-layer/services/resource-group/resource-group.service";
import { SettingsService } from "app/shared/services/settings.service";
import * as _ from 'lodash';
import { switchMap, take } from "rxjs/operators";
import { EditDashboardComponent } from "./components/edit-dashboard/edit-dashboard.component";
import { DefaultWidgetEditorComponent } from "./components/widgets/default-widget/default-widget-editor.component";
import { WidgetEditorsComponents } from "./models/WidgetEditorComponents";
import { DashboardPath } from "./models/dashboard-path";
import { DefaultDashboardData } from './models/default-dashboard-data.interface';
import { Widget } from "./models/widget";
import { WidgetSettingsService } from "./services/widget-settings.service";
import { WidgetType } from "./enums/widget-type.enum";
import { AuthService } from "app/shared/services/auth.service";

@Component({
    selector: "app-customer-dashboard",
    templateUrl: "./customer-dashboard.component.html",
    styleUrls: ["./customer-dashboard.component.scss"],
    providers: [CymsidebarService, ConfirmationService]
})
export class CustomerDashboardComponent implements OnInit, OnDestroy {
    public opened: boolean = false;
    public dockedSize: string = "0px";
    public dashboardsTree: Dashboard[] = null;
    public dashboardsObject: { [id: string] : Dashboard; } = null;
    public selectedDashboard: DashboardObject = {
        id: null,
        item: null,
        widgets: null
    }
    public state: DashboardState = {
        edit: false,
        addNew: false,
        editWidget: false,
        selectedWidget: null
    }
    private subscriptions: Subscription[] = [];
    private navParName: string = 'dashboard';

    public parentDashboardTree: Dashboard;
    public selectedWidgetDataChange: Widget;
    @ViewChild(DashboardLayoutComponent, {static: false}) dashboardLayoutComponent: DashboardLayoutComponent;
    public sidebarActions: DashboardAction[] = [
      {name: 'customerDashboard.cancel', type: DashboardActionType.cancel, importance: 'secondary', disabled: false},
      {name: 'customerDashboard.saveDashboard', type: DashboardActionType.save, importance: 'primary', disabled: false}
    ]

    @ViewChild('editComponentFactory', {read: ViewContainerRef, static: false}) editComponent: ViewContainerRef;
    componentRef: ComponentRef<DefaultWidgetEditorComponent | EditDashboardComponent>;
    public deletedWidgets: {index: number, widget: DashboardWidget}[] = [];

    constructor(
        private cymSidebarService: CymsidebarService,
        private dashboardUtilsService: DashboardUtilsService,
        private navigationService: NavigationService,
        private translateService: TranslateService,
        private confirmationService: ConfirmationService,
        private dashboardService: DashboardService,
        private componentFactoryResolver: ComponentFactoryResolver,
        private renderer2: Renderer2,
        private settingsService: SettingsService,
        private messageService: MessageService,
        private resourceGroupService: ResourceGroupService,
        private widgetSettingsService: WidgetSettingsService,
        private authService: AuthService
    ) {}

    ngOnInit(): void {  
       let navAndDashSubscription= this.navigationService.getParams().pipe(
        switchMap(params => {
            return  this.dashboardService.getDashboarsdNoData()
        }),
      
        ).subscribe((res: any) => {
          this.dashboardsObject = res;
            this.buildTree(res);

            if (res.length == 0)//no dashboards
            {
                return;
            }

            let defaultDashboardId = this.settingsService.get('defaultDashboard');

            if (!defaultDashboardId)
            {
                let dashboardIds = this.dashboardsTree.filter(dashboard => dashboard.identity != Guid.EMPTY);
                defaultDashboardId = dashboardIds.length > 0 ? dashboardIds[0].identity : null;
            }

            if(defaultDashboardId){
                this.selectDashboard(defaultDashboardId);
                if (this.authService.isAllowedPage(Pages.dashboard))
                {
                    this.navigationService.navigate(Pages.dashboard, { dashboard: defaultDashboardId });
                }
                 else{
                    throw new Error("Dashboard feature is disabled");
                }
                return;
            }
        });

        let widgetActionsSubscription = this.dashboardUtilsService.getWidgetAction().subscribe(res => {
            if(this.selectedDashboard.item && this.selectedDashboard.widgets.findIndex(widget => {return widget.id === res.widget.id;}) > -1){
                this.state.selectedWidget = res.widget;
                this.onDashboardAction(res.actionType);
            }
        });
        this.subscriptions.push(widgetActionsSubscription);

        let widgetDataChangeSubscription = this.dashboardUtilsService.getWidgetDataChange().subscribe(res => {
            this.selectedWidgetDataChange = res.widget;

            if (res.widget.type === WidgetType.map) {
                this.updateWidgetData(_.cloneDeep(res.widget))
            }
        });
        this.subscriptions.push(widgetDataChangeSubscription);

        let sidebarActionsStateSubscription = this.dashboardUtilsService.getSidebarActionState().subscribe(res => {
            this.sidebarActions.map(action => {
                if(action.type === res.actionType){
                    action.disabled = res.disabled;
                }
            });
        });
        this.subscriptions.push(sidebarActionsStateSubscription);

        let resourceGroupSubscription = this.resourceGroupService.resourceChanged().subscribe(() => {
            this.dashboardService.getAll().pipe(take(1)).subscribe((response:any) => {
                this.buildTree(response);
            });
        });
        this.subscriptions.push(resourceGroupSubscription);
        this.subscriptions.push(navAndDashSubscription);
      
    }

    ngOnDestroy(): void {
        this.subscriptions.forEach(item => { return item.unsubscribe();});
    }

    onDashboardAction(event: DashboardActionType): void {
        let index: number;
        switch(event) {
            case DashboardActionType.edit :
                this.setStateProperties({edit: true, addNew: false, editWidget: false});
                this.dashboardLayoutComponent.onEditMode();
                this.detectScrollOnWidgetslayoutContainer() ? this.messageService.add({severity: 'info', summary: this.translateService.instant(ToastTypes.info), detail: this.translateService.instant('unableToSaveDashboardDueToScrollInfo')}) : null;
                this.createEditComponent(EditDashboardComponent, {state: this.state, selectedDashboard: this.selectedDashboard, dashboardsTree: this.dashboardsTree, parentDashboardTree: this.parentDashboardTree, dashboardsObject: this.dashboardsObject }, 'onAddWidget');
                this.openSidebar();
                break;
            case DashboardActionType.addNew :
                this.setStateProperties({edit: false, addNew: true});
                this.selectedDashboard.item = new Dashboard({
                    identity: Guid.create().toString(),
                    parent: new Dashboard({
                        identity: Guid.EMPTY
                    }),
                    isNewDashboard: true,
                    widgets: [],
                    priority: DashboardPriority.Default
                });
                this.selectedDashboard.widgets = [];
                this.dashboardLayoutComponent.onEditMode();
                this.createEditComponent(EditDashboardComponent, {state: this.state, selectedDashboard: this.selectedDashboard, dashboardsTree: this.dashboardsTree, parentDashboardTree: this.parentDashboardTree, dashboardsObject: this.dashboardsObject}, 'onAddWidget');
                this.openSidebar();
                break;
            case DashboardActionType.save :
                if(this.detectScrollOnWidgetslayoutContainer()){
                    this.messageService.add({severity: 'error', summary: this.translateService.instant(ToastTypes.error), detail: this.translateService.instant('unableToSaveDashboardDueToScrollError')});
                    break;
                }
                this.selectedDashboard.item.widgets = _.cloneDeep(this.selectedDashboard.widgets);
                this.state.editWidget &&  this.selectedWidgetDataChange ? this.updateWidgetData(this.selectedWidgetDataChange) : null;
                this.state.addNew ? this.createDashboard(this.selectedDashboard.item) : this.updateDashboard(this.selectedDashboard.item);
                this.widgetSettingsService.deleteWidgetSettings(this.deletedWidgets.map(item => {return item.widget.id;}));
                this.setStateProperties({selectedWidget: null});
                this.closeSidebar();
                break;
            case DashboardActionType.saveTemplate:
                break;
            case DashboardActionType.cancel :
                this.setStateProperties({edit: false, editWidget: false, selectedWidget: null});
                if (this.selectedDashboard.item)
                {
                    this.dashboardLayoutComponent.restoreWidgetPositionAndLayout(
                        this.selectedDashboard.item.widgets, this.deletedWidgets);
                }
                this.deletedWidgets = [];
                this.closeSidebar();
                break;
            case DashboardActionType.delete :
                this.deleteDashboard(this.selectedDashboard.item);
                this.closeSidebar();
                break;
            case DashboardActionType.editWidget :
                this.setStateProperties({editWidget: true, edit: false});
                this.createEditComponent(WidgetEditorsComponents[this.state.selectedWidget.type], this.state.selectedWidget);
                this.openSidebar();
                break;
            case DashboardActionType.deleteWidget :
                index = this.selectedDashboard.widgets.findIndex(element => {return element.id === this.state.selectedWidget.id;});
                this.deletedWidgets.push({ index: index, widget: this.selectedDashboard.widgets[index]});
                this.selectedDashboard.widgets.splice(index, 1);
                this.createEditComponent(EditDashboardComponent, {state: this.state, selectedDashboard: this.selectedDashboard, dashboardsTree: this.dashboardsTree, parentDashboardTree: this.parentDashboardTree, dashboardsObject: this.dashboardsObject }, 'onAddWidget');
                this.openSidebar();
                break;
            case DashboardActionType.saveAsDefault:
                this.setDefaultDashboard(this.selectedDashboard.id);
                break;
            default :
                break;
        }
    }

    selectDashboard(dashboardId: Guid): void
    {
        this.getDashboard(dashboardId.toString());
    }

    getDashboard(id: string): void {
       this.dashboardService.get(id).subscribe(res => {
        if(!res){
            return;
        }   
          const clonedDashboard = _.cloneDeep(new Dashboard(res));
          const clonedWidgets = _.isUndefined(res.widgets) ? [] : _.cloneDeep(res.widgets);
          this.selectedDashboard = {
            id: res.identity,
            item: clonedDashboard,
            widgets: clonedWidgets
            };
            if((this.selectedDashboard.item.resolution && 
            this.selectedDashboard.item.resolution.width && this.selectedDashboard.item.resolution.height) && (window.innerWidth !== this.selectedDashboard.item.resolution.width || window.innerHeight !== this.selectedDashboard.item.resolution.height)){
                this.messageService.add({severity: 'info', summary: this.translateService.instant(ToastTypes.info), detail: this.translateService.instant('dashboardHasDifferentResolution', {width: this.selectedDashboard.item.resolution.width, height: this.selectedDashboard.item.resolution.height})});
            }
            this.parentDashboardTree = this.returnParentDashboard();
            this.onDashboardAction(DashboardActionType.cancel);
       });
    }

    updateDashboard(dashboard: Dashboard): void {
        this.dashboardService.update(this.updateResolutionAndReturnDashboard(dashboard))
            .subscribe(() => {
                this.state.edit = false;
                this.state.editWidget = false;
                this.dashboardsObject[dashboard.identity] = dashboard;
                this.buildTree(this.dashboardsObject);
                this.messageService.add({severity: 'success', summary: this.translateService.instant(ToastTypes.success), detail: this.translateService.instant('dashboardSaved')});
            },
            () => {
                this.messageService.add({severity: 'error', summary: this.translateService.instant(ToastTypes.error), detail: this.translateService.instant('failedToSaveDashboard')});
                this.state.edit = true;
            });
    }

    updateWidgetData(widget: Widget): void {
        // Update data in both dashboard widgets object and widgets array
        let widgetIndex = this.selectedDashboard.item.widgets.findIndex(element => {return element.id === widget.id;});
        if(widgetIndex > -1){
            this.selectedDashboard.item.widgets[widgetIndex] = widget;
            this.selectedWidgetDataChange = null;
        }

        widgetIndex = this.selectedDashboard.widgets.findIndex(element => {return element.id === widget.id;});
        if(widgetIndex > -1){
            this.selectedDashboard.widgets[widgetIndex] = widget;
            this.selectedWidgetDataChange = null;
        }
    }

    createDashboard(dashboard: Dashboard): void {
        this.dashboardService.create(this.updateResolutionAndReturnDashboard(dashboard))
            .subscribe(() => {
                if (this.authService.isAllowedPage(Pages.dashboard))
                {
                    this.navigationService.navigate(Pages.dashboard, { dashboard: this.selectedDashboard.item.identity });
                }
                else{
                    throw new Error("Dashboard feature is disabled");
                }


                this.state.addNew = false;
                this.state.edit = false;
                this.dashboardsObject[dashboard.identity] = dashboard;
                this.buildTree(this.dashboardsObject);
                this.selectedDashboard = {id:dashboard.identity, widgets: dashboard.widgets, item:dashboard};
                this.messageService.add({severity: 'success', summary: this.translateService.instant(ToastTypes.success), detail: this.translateService.instant('dashboardSaved')});
            },
            () => {
                this.messageService.add({severity: 'error', summary: this.translateService.instant(ToastTypes.error), detail: this.translateService.instant('failedToSaveDashboard')});
                this.state.edit = true;
            });


    }

    addWidget(widgetData: DashboardWidget): void {
        let widget = this.dashboardUtilsService.returnWidgetClass(widgetData) || widgetData;
        this.selectedDashboard.widgets.push(widget);
        this.handleFixedHeightLayout();
    }

    handleFixedHeightLayout(): void {
        // Wait for the widget to be rendered. 
        // This is not optimal, we should extend all widget components from a base widget component and use one of it's hooks to determine when the component has rendered
        setTimeout(() => {
            if(this.detectScrollOnWidgetslayoutContainer()){
                this.selectedDashboard.widgets.splice(-1, 1);
                this.messageService.add({severity: 'info', summary: this.translateService.instant(ToastTypes.info), detail: this.translateService.instant('widgetOutOfBounds')});
            }
        }, 100);
    }

    detectScrollOnWidgetslayoutContainer() : boolean {
      
        if(this.dashboardLayoutComponent.layoutContainer.nativeElement.offsetHeight > this.dashboardLayoutComponent.layoutContainer.nativeElement.clientHeight){
            return true;
        }
        return false;
    }

    openSidebar(): void {
        this.cymSidebarService.toggleDockSidebar("push");
        this.cymSidebarService.openSidebar();
    }

    closeSidebar(): void {
        this.cymSidebarService.closeSidebar();
    }

    onDashboardAddWidget(event: DashboardWidget): void {
        this.addWidget(event);
    }

    onDashboardChangeWidgetOrdering(event: SwapArrayElementsEvent): void {
        this.selectedDashboard.widgets = this.dashboardUtilsService.arrayMove(this.selectedDashboard.widgets, event.dragElCurrentIndex, event.dragOverElCurrentIndex);
    }

    deleteDashboard(dashboard: Dashboard): void {
        this.confirmationService.confirm({
            message: this.translateService.instant('customerDashboard.deleteConfirmationMessage'),
            header: this.translateService.instant('customerDashboard.deleteConfirmationHeader'),
            icon: 'fa fa-trash',
            acceptLabel: this.translateService.instant('yes'),
            rejectLabel: this.translateService.instant('no'),
            accept: () => {
                this.dashboardService.delete(dashboard).subscribe(() => {
                    
                    let defaultDashboardId = this.settingsService.get('defaultDashboard');
                    if(defaultDashboardId === dashboard.identity){
                        this.setDefaultDashboard(null, 'deletedDefaultDashboard');
                    }
                    let redirectToDashboardId = this.returnAvailableDashboardId();
                    this.selectedDashboard = {widgets:[], id:null, item:null};
                    delete this.dashboardsObject[dashboard.identity];
                    this.buildTree(this.dashboardsObject);
                    this.widgetSettingsService.deleteWidgetSettings(dashboard.widgets.map(widget => {return widget.id;}));

                    if (this.authService.isAllowedPage(Pages.dashboard))
                    {
                        this.navigationService.navigate(Pages.dashboard, {dashboard: redirectToDashboardId});
                    }
                    else{
                        throw new Error("Dashboard feature is disabled");
                    }

                });
            }
        });
    }

    returnTreeOptions(tree: Dashboard[]): Dashboard[] {
        let index = tree.findIndex( item => {return item.identity === Guid.EMPTY;});
        if(index === -1){
            tree.unshift({name: this.translateService.instant('customerDashboard.noDashboardParent'), identity: Guid.EMPTY, parent: null, children: [], isNewDashboard: true});
        }
        return tree;
    }

    returnAvailableDashboardId(): string {
        let id:string;
        let parentId = this.selectedDashboard.item.parent.identity;
        switch(Guid.isGuid(parentId) && parentId !== Guid.EMPTY){
            case true:
                id = parentId;
                break;
            case false:
                id = this.dashboardsObject[1] ? this.dashboardsObject[1].identity : null;
                break;
            default:
                break;
        }
        return id;
    }

    returnParentDashboard(): Dashboard {
        let id: string;
        switch(this.selectedDashboard.item){
            case null:
                id = this.selectedDashboard.id;
                break;
            default:
                id = this.selectedDashboard.item.parent.identity;
                break;
        }
        return  this.dashboardsTree ? this.dashboardUtilsService.findInTree(this.dashboardsTree.map(el => {return new DashboardPath(el);}), id) || this.dashboardsTree[0] : null;
    }

    buildTree(data: {[id: string] : Dashboard}): void {
        let tree = this.dashboardUtilsService.buildDashboardsTree(data);
        this.dashboardsTree = this.returnTreeOptions(tree);
        this.parentDashboardTree = this.returnParentDashboard();
       
    }

    createEditComponent(editComponent: Type<DefaultWidgetEditorComponent | EditDashboardComponent>, data: DefaultDashboardData | Widget, output?: string): void {
        if(!editComponent){
            return;
        }

        if(this.componentRef){
            this.componentRef.destroy();
        }
        const factory = this.componentFactoryResolver.resolveComponentFactory(editComponent as Type<DefaultWidgetEditorComponent>);
        this.componentRef = this.editComponent.createComponent(factory);
        this.renderer2.addClass(this.componentRef.location.nativeElement, 'dynamic-edit-component');
        this.componentRef.instance.data = data;
        if(output){
            this.componentRef.instance[output].subscribe(event => {return this.addWidget(event);});
        }
    }

    setStateProperties(stateObject: DashboardState): void {
        for(let i in stateObject){
            this.state[i] = stateObject[i];
        }
    }

    private setDefaultDashboard(dashboardId: string, messageString? ): void {

        this.settingsService.set('defaultDashboard', dashboardId);

        let message = messageString || 'dashboardSavedAsDefault';

        let saveSettingsSubscription =  this.settingsService.saveSettings().subscribe(() => {
            this.messageService.add({severity: 'success', summary: this.translateService.instant(ToastTypes.success), detail: this.translateService.instant(message)});
        });
        this.subscriptions.push(saveSettingsSubscription);
    }

    updateResolutionAndReturnDashboard(dashboard: Dashboard): Dashboard {
        dashboard.resolution = {
            width: window.innerWidth,
            height: window.innerHeight
        };
        return dashboard;
    }
}
