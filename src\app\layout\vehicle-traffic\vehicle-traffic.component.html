<div class="page-format">
  <div class="content-wrapper">
    <app-page-title title="vehicleTraffic.vehicleTraffic"></app-page-title>
    <div class="iot-map">

    <div class="map-layout-content" content>
      <button type="button" title="{{'vehicleTraffic.back' | translate}}" (click)="back()" class="back">
        <i class="fa fa-angle-left"></i>
      </button>

      <div class="map-actions-wrapper">
        <timeline [detections]="detections" *ngIf="detections" class="timeline"></timeline>
      </div>
      <div class="follow-container">
        <follow [detections]="detectionsReversed" *ngIf="showFollow" (passTurboTable)="getTurboTable($event)"></follow>
      </div>
      <div class="map-wrapper" #mapWrapper>
        <app-generic-map [mapTileSource]="mapTileSource" [markers]="markersDictionary" [arrows]="arrows"
          (extentChanged)="onMapExtentChanged($event)" #map>
          <ng-container #customMarkersFactory></ng-container>
        </app-generic-map>

      </div>
    </div>
  </div>
  </div>
</div>