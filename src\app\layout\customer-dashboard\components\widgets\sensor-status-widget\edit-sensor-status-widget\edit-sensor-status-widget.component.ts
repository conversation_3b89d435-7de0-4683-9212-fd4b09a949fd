import { Component, OnInit } from '@angular/core';
import { SensorStatusWidget } from 'app/layout/customer-dashboard/models/sensor-status-widget';
import { DashboardUtilsService } from 'app/layout/customer-dashboard/services/dashboard-utils.service';
import { Guid } from 'app/shared/enum/guid';
import { ResourceState } from 'app/shared/modules/data-layer/enum/resource/resource-state.enum';
import {
    DeviceStatusService
} from "app/shared/modules/data-layer/services/device-status/device-status.service";
import { ResourceGroupService } from 'app/shared/modules/data-layer/services/resource-group/resource-group.service';
import { SelectItem } from 'primeng/api';
import { DefaultWidgetEditorComponent } from '../../default-widget/default-widget-editor.component';

@Component({
  selector: 'app-edit-sensor-status-widget',
  templateUrl: './edit-sensor-status-widget.component.html',
  styleUrls: ['./edit-sensor-status-widget.component.scss']
})
export class EditSensorStatusWidgetComponent extends DefaultWidgetEditorComponent implements OnInit {
  data: SensorStatusWidget;
  groupList: SelectItem[] = [{label: "customerDashboard.showAllGroups", value: Guid.EMPTY}];
  statusList: SelectItem[] = [];

  constructor(
    private resourceGroupService: ResourceGroupService,
    private dashboardUtilsService: DashboardUtilsService,
    private deviceStatusService: DeviceStatusService
  ) {
    super();
  }

  ngOnInit() {
    this.data = new SensorStatusWidget(this.data);

    this.resourceGroupService.getAll().subscribe(res => {
      for (const key in res) {
        if(res[key].isHomogeneous){
          this.groupList.push({label: res[key].name, value: res[key].identity})
        }
      }
    })

    this.deviceStatusService.getAll().subscribe(res => {
      for(const key in res) {
        if(this.statusList.findIndex(el => el.value === res[key].status) === -1){
          this.statusList.push({label: res[key].status, value: res[key].status})
        }
      }
      for(let state in ResourceState){
        if(this.statusList.findIndex(el => el.value === state) === -1){
          this.statusList.push({label: state, value: state})
        }
      }
    })
  }

  onWidgetDataChange(){
    this.dashboardUtilsService.setWidgetDataChange(this.data);
  }

}
