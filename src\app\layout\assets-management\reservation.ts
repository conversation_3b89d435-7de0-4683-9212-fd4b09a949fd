interface IReservation {
    Identity: string;
    Asset: string;
    AssetIdentity: string;
    Entity: string;
    EntityIdentity: string;
    ReserveParking: boolean;
    StartTime: Date | string;
    EndTime: Date | string;
    Comment: string;
}

export class Reservation implements IReservation {
    Identity: string;
    Asset: string;
    AssetIdentity: string;
    Entity: string;
    EntityIdentity: string;
    ReserveParking: boolean;
    StartTime: Date | string;
    EndTime: Date | string;
    Comment: string;

    constructor(reservation?: IReservation) {
        if (reservation) {
            this.Identity = reservation.Identity;
            this.Asset = reservation.Asset;
            this.AssetIdentity = reservation.AssetIdentity;
            this.Entity = reservation.Entity;
            this.EntityIdentity = reservation.EntityIdentity;
            this.ReserveParking = reservation.ReserveParking || false;
            this.StartTime = reservation.StartTime;
            this.EndTime = reservation.EndTime;
            this.Comment = reservation.Comment;
        }
    }
}
