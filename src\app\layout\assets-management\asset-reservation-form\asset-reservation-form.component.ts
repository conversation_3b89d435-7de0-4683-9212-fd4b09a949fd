import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Store } from '@ngrx/store';
import * as _ from 'lodash';
import * as moment from 'moment';
import * as fromAppReducers from '../../../store/app.reducers';

import { HttpClient } from '@angular/common/http';
import { TranslateService } from '@ngx-translate/core';
import { ToastTypes } from 'app/shared/enum/toast-types';
import { Entity } from 'app/shared/modules/data-layer/models/entity';
import { apiMap } from 'app/shared/services/api.map';
import { environment } from 'environments/environment';
import { MessageService } from 'primeng/api';
import { HeaderStoreService } from '../../../services/store_servcies/header-store.service';

import { Reservation } from './../reservation';
import {AssetsTypes,GeneralAssets,GuidUtils} from "app/shared/enum/enum";

@Component({
    selector: 'app-asset-reservation-form',
    templateUrl: './asset-reservation-form.component.html',
    styleUrls: ['./asset-reservation-form.component.scss']
})
export class AssetReservationFormComponent implements OnInit {
    _reservation: Reservation;
    @Input() set reservation(val) {
        this._reservation = val;
        this.getAssets();
        this.initValues();
    }
    @Output('closeAssetReservationForm') closeAssetReservationForm = new EventEmitter<null>();
    @Output('deleteReservation') deleteReservation = new EventEmitter<Reservation>();
    @Output('saveReservation') saveReservation = new EventEmitter<Reservation>();
    assetTypes = AssetsTypes;
    assets = [];
    entities = [];
    selectedAsset;
    selectedEntity;
    reserveParking: boolean = false;
    comments;
    minDate = new Date();
    startDate = new Date();
    endDate = moment(this.startDate).add(30, 'm').toDate();
    maxDate;
    isRtl = false;
    invalidDates = false;
    invalidForm = true;

    constructor(
        private httpClient: HttpClient,
        private store: Store<fromAppReducers.AppState>,
        private _i18n: TranslateService,
        private messageService: MessageService,private headerStoreService:HeaderStoreService) { }

    ngOnInit() {

        this.headerStoreService.getState().subscribe(settings => {
            this.isRtl = settings.direction === 'rtl' ? true : false;
        })
        this.getAssets();
        this.getEntities();
    }

    getAssets() {
        this.httpClient.get(environment.apiUrl + apiMap.getAssets.url).subscribe(res => {
            this.setAssetsToForm(res);
        }, err => {
            this.messageService.add({severity: 'error', summary: this._i18n.instant(ToastTypes.error), detail: this._i18n.instant('unableToLoadData')});
        })

    }

    setAssetsToForm(assets) {
        this.assets = [
            {
                name: this._i18n.instant(GeneralAssets.generalRoom.Name),
                value: GeneralAssets.generalRoom,
                type: GeneralAssets.generalRoom.Type
            },
            {
                name: this._i18n.instant(GeneralAssets.generalDesk.Name),
                value: GeneralAssets.generalDesk,
                type: GeneralAssets.generalDesk.Type
            },
            {
                name: this._i18n.instant(GeneralAssets.generalParking.Name),
                value: GeneralAssets.generalParking,
                type: GeneralAssets.generalParking.Type
            }];


        assets.forEach(asset => {
            this.assets.push({
                name: asset.Name,
                value: asset,
                type: asset.Type
            });
        });
    }

    getEntities() {
        this.httpClient.get(environment.apiUrl + apiMap.getEntities.url).subscribe((res:Entity[]) => {
            this.setEntitiesToForm(res);
        }, err => {
            this.messageService.add({severity: 'error', summary: this._i18n.instant(ToastTypes.error), detail: this._i18n.instant('unableToLoadData')});
        })

    }

    setEntitiesToForm(entities) {
        entities.forEach(entity => {
            this.entities.push({
                name: entity.FirstName + " " + entity.LastName,
                value: entity
            })
        });
        this.entities = [...this.entities];
    }

    initValues() {
        if (!_.isEmpty(this._reservation)) {
            this.selectedAsset = this.findAsset(this._reservation.AssetIdentity);
            this.selectedEntity = this.findEntity(this._reservation.EntityIdentity);
            this.reserveParking = this._reservation.ReserveParking;
            this.comments = this._reservation.Comment;
            this.startDate = new Date(this._reservation.StartTime);
            this.endDate = new Date(this._reservation.EndTime);
            this.validateDates();
            this.validateForm();
        } else {
            this.selectedAsset = "";
            this.selectedEntity = "";
            this.reserveParking = false;
            this.comments = "";
            this.invalidDates = false;
            this.startDate = new Date();
            this.endDate = moment(this.startDate).add(30, 'm').toDate();
        }
    }

    findAsset(assetIdentity) {
        return this.assets.find(asset => asset.value.Identity === assetIdentity);
    }

    findEntity(entityIdentity) {
        return this.entities.find(entity => entity.value.Identity === entityIdentity);
    }

    onSelectAsset(event) {
        this.selectedAsset = event.value;
        this.validateForm();
    }

    onSelectEntity(event) {
        this.selectedEntity = event.value;
        this.validateForm();
    }

    onCheckboxClicked(event) {
        this.reserveParking = event;
    }

    onStartDateChange(event) {
        this.startDate = event;
        if (this.validateDates()) {
            this.endDate = moment(this.startDate).add(30, 'm').toDate();
            this.validateDates();
        }
        this.validateForm();
    }

    onEndDateChange(event) {
        this.endDate = event;
        this.validateDates();
        this.validateForm();
    }

    validateDates() {
        return this.invalidDates = (this.startDate > this.endDate) ? true : false;
    }

    validateForm() {
        this.invalidForm = !(this.selectedAsset && this.selectedEntity && !this.invalidDates);
    }

    setToSubmit() {
        return new Reservation({
            Identity: this._reservation.Identity ? this._reservation.Identity : GuidUtils.emptyGuid(),
            Asset: this.selectedAsset.value.Name,
            AssetIdentity: this.selectedAsset.value.Identity,
            Entity: this.selectedEntity.name,
            EntityIdentity: this.selectedEntity.value.Identity,
            StartTime: this.startDate,
            EndTime: this.endDate,
            ReserveParking: this._reservation.Identity ? false : this.reserveParking,
            Comment: this.comments
        })
    }

    onDelete() {
        this.deleteReservation.emit(this._reservation);
    }

    onCancel() {
        this.closeAssetReservationForm.emit();
    }

    onSubmit() {
        this.saveReservation.emit(this.setToSubmit());
    }
}
