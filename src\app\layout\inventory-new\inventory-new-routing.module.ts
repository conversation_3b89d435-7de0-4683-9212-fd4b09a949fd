
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { InventoryNewComponent } from './inventory-new.component';
import { ResourceGroupManagerComponent } from './resource-group-manager/resource-group-manager.component';

const routes: Routes = [

  { path: '',  component: InventoryNewComponent }, 
  { path: 'resource-group-manager', component: ResourceGroupManagerComponent }

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class InventoryNewRoutingModule { }
