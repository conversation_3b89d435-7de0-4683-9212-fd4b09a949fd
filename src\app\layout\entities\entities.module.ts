import { ReactiveFormsModule } from '@angular/forms';
import { NgModule } from '@angular/core';
import { EntitiesRoutingModule } from 'app/layout/entities/entities-routing.module';
import { EntityFormComponent } from './entity-form/entity-form.component';
import { EntitiesComponent } from 'app/layout/entities/entities.component';
import { SharedModule } from 'app/shared/modules/shared.module';
import { DefaultActionsComponent } from './actions/default-actions/default-actions.component';
import {DataViewModule} from 'primeng/dataview';
import {EntityItemComponent} from "app/layout/entities/enityItem.component";


@NgModule({
  imports: [
    SharedModule,
    EntitiesRoutingModule,
    ReactiveFormsModule,
    DataViewModule
  ],
  declarations: [
    EntitiesComponent,
      EntityItemComponent,
    DefaultActionsComponent
  ]
})
export class EntitiesModule { }
