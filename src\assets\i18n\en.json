{"all": "All", "rows": "Rows", "dashboards": "Dashboards", "newDashboard": "New Dashboard", "dashboard": "Dashboard", "tables": "Tables", "forms": "Forms", "component": "Component", "menu": "<PERSON><PERSON>", "submenu": "Submenu", "blankpage": "<PERSON><PERSON> <PERSON>", "moretheme": "More Themes", "lightTheme": "Light", "darkTheme": "Dark", "downloadNow": "Download Now", "language": "Language", "viewAll": "View All", "channels": "Channels", "maps": "Maps", "exportPDF": "Export PDF", "dashboardEditor": "Dashboard Editor", "liveDashboard": "Live Dashboard", "reports": "Reports", "selectSearchTemplate": "Select Report", "searchBy": "Search By", "textFilter": "Text Filter", "resourceGroup": "Resource Group", "resource": "Resource", "dateFilter": "Date Filter", "selectDate": "Select Date", "fromDate": "From Date", "toDate": "To Date", "addNewSearchTemplate": "Add New Search Template", "triggerType": "Trigger Type", "exportToPDF": "Export To PDF", "exportRoute": "Export Route", "saveTemplate": "Save Template", "generateReport": "Generate Report", "execute": "Execute", "clear": "Clear", "newTemplate": "New Template", "enterNewTemplateName": "Enter New Template Name", "selectOperation": "Select Operation", "events": "Events", "triggers": "Triggers", "heatMap": "Heat Map", "date": "Date", "last": "Last", "dateRange": "Date Range", "latest": "Latest", "seconds": "Seconds", "minutes": "Minutes", "days": "Days", "hours": "Hours", "months": "Months", "weeks": "Weeks", "map": "Map", "table": "Table", "Year": "Year", "chart": "Chart", "chartType": "Chart Type", "bar": "Bar Chart", "doughnut": "Doughnut Chart", "radar": "Radar Chart", "pie": "Pie Chart", "polarArea": "Polar Area Chart", "line": "Line Chart", "changeStatus": "Change Status", "ioCommands": "I/O Commands", "search...": "Search...", "systemIP": "System IP", "password": "Password", "username": "User name", "login": "<PERSON><PERSON>", "logout": "Logout", "hello": "Hello", "search": "Search", "badCredentialsMsg": "Bad Credentials", "error": "Error", "yes": "Yes", "no": "No", "confirm": "Confirm", "delete": "Delete", "ok": "OK", "cancel": "Cancel", "DeviceConnection": "Device Connection", "GenericSensorValue": "Generic Sensor Value", "GenericSensorString": "Generic Sensor String", "GenericSensorBoolTrue": "Generic Sensor True", "GenericSensorBoolFalse": "Generic Sen<PERSON>e", "allTriggers": "All Triggers", "noResponseFromServer": "No Response From Server", "loginFirst": "Please Login", "removeTemplate": "Remove Template", "updateTemplate": "Update Template", "dashboardEditorText": "Dashboard Editor", "reportTransformation": "Transformation", "none": "None", "id": "ID", "Id": "ID", "RuleDescription": "Description", "TimeStamp": "Time Stamp", "ResourceName": "Resource Name", "TriggerType": "Trigger Type", "longitude": "Longitude", "latitude": "Latitude", "Longitude": "Longitude", "Latitude": "Latitude", "battery_level": "Battery Level", "sensor_id": "Sensor Id", "RuleId": "Rule ID", "event_id": "Event ID", "resource_name": "Resource Name", "trigger_type": "Trigger Type", "heading": "Heading", "resource_id": "Resource Id", "time_stamp": "Time Stamp", "foreign_id": "Foreign Id", "last_moved": "Last Moved", "NoMovement": "No Movement", "en": "עברית", "he": "English", "areYouSureDelete": "Are You Sure?", "searchTemplateWithThisNameAlredyExist": "A Search Template with This Name<br/> Already Exists", "chooseDifferentName": "Choose a Different Name", "exportToCSV": "Export To CSV", "importCSV": "Import CSV", "toggleSidebar": "Toggle Sidebar", "allResourcesInGroup": "All Resources in Group", "updatedResource": "Updated Resource", "allResources": "All Resources", "fromDateShouldBeBeforeToDate": "'From Date' Should Be Before 'To Date'", "Date": "Date", "Name": "Name", "Sensor": "Sensor", "Start Time": "Start Time", "End Time": "End Time", "Work Hours": "Work Hours", "Penalty Hours": "Penalty Hours", "Net Work Hours": "Net Work Hours", "Start Period Time": "Start Period Time", "End Period Time": "End Period Time", "No Movement Period": "No Movement Period", "resources": "Resources", "saveLocation": "Save Location", "Penalty Period": "Penalty Period", "Distance": "Distance", "DISTANCE": "Distance", "distance": "Distance", "Under Limit": "Under Limit", "close": "Close", "now": "Now", "Vmd": "VMD", "Relay": "<PERSON><PERSON>", "SignalLoss": "Signal Loss", "HDFailure": "HD Failure", "LocationChanged": "Location Changed", "BatteryLevel": "Battery Level", "SensorActive": "Sensor Active", "SensorInactive": "Sensor Inactive", "GarbageSensorDailyPickups": "Garbage Sensor Daily Pickups", "GarbageTruckLate": "Garbage Truck Late", "GarbageTruckForbiddenArea": "Garbage Truck Forbidden Area", "ALPR": "ALPR", "IdTag": "Id Tag", "CELLAV_BELOW_WORK_LIMITS_TRIGGERS": "Below Work Limits (Triggers)", "CELLAV_NO_WORK_TRIGGERS": "No Work (<PERSON><PERSON><PERSON>)", "CELLAV_WORKING_HOURS_TRIGGERS": "Working Hours (Triggers)", "CELLAV_NO_MOVEMENT_TRIGGERS": "No Movement Periods (Triggers)", "Last Comm": "Last Comm", "last_comm": "Last Comm", "Comm Error": "Communication Error", "event_type": "Event Type", "reportGenerationFailed": "Report Generation Failed", "noData": "No Data", "entity_id": "Entity Id", "entity_value": "Entity Value", "extractVideo": "Extract Video", "selectVideoFormat": "Select Format", "maxRangeExceeded": "Max Range Exceeded", "extractionFail": "Extraction Fail", "openChannelFail": "Open Channel Fail", "openChannelFail503": "Please check:  if webstreamer service is properly configured in manager and if webstreammer service started on configured address/port", "openingChannel": "Opening Channel", "StreamNotSupported": "Stream Not Supported", "FailedToConnect": "An unexpected internal server error occurred. Check server logs", "startExtraction": "Start Extraction", "generateReportFail": "Generate Report Fail", "cantFindTemplate": "Can't <PERSON>late", "cantFindSession": "Can't Find Session, Please login", "selectLang": "Select Language", "selectFontSize": "Select font size", "fontSizeSmall": "Small", "fontSizeNormal": "Normal", "fontSizeLarge": "Large", "startTimeNightTheme": "Start time for Night theme", "endTimeNightTheme": "End time for Night theme", "dayNightSwitch": "Day / Night theme auto switch", "enableAutoLoginSwitch": "Enable auto login", "userLacksPermissions": "User lacks permissions", "pleaseVerifyYourCredentials": "Please verify your credentials", "maxAllowedUsersAreCurrentlyLoggedIn": "Max allowed users are currently logged in", "userAlreadyLoggedIn": "User already logged in", "unableToConnectToSystemControl": "Unable to connect to System Control", "applicationErrorUnableToConnectTimeOut": "Application Error - Unable to connect - Time Out", "indLicense": "Invalid License", "operationFailed": "Operation Failed", "both_limits": "Distance and Hours", "moveDown": "Move Down", "moveRight": "Move Right", "moveUp": "Move Up", "moveLeft": "Move Left", "playbackTime": "Playback Time", "playbackGo": "<PERSON><PERSON><PERSON>", "micOff": "Microphone Off", "micOn": "Microphone On", "live": "Live", "rewindVideo": "Rewind Video", "playVideo": "Play Video", "pauseVideo": "Pause Video", "fastForward": "Fast Forward", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "fullScreen": "Full Screen", "unknownError": "Unknown Error", "notSupportedYet": "Not Supported Yet", "sessionTerminated": "Session Terminated", "systemIsDown": "System Is Down", "licenseDongleNotConnected": "License Dongle Not Connected", "licenseExpired": "License Expired", "closedBy_name": "Closed By {{name}}", "failToLogout": "Fail To Logout", "aTemplateMustHaveAName": "A Template Must Have a Name", "noDataWasFound": "No data was found", "savedSuccessfully": "Saved successfully", "updatedSuccessfully": "Updated successfully", "failedToSave": "Failed to save", "wasDeleted": "Was Deleted", "success": "Success", "extractionOf": "Extraction Of", "succeeded": "Succeeded", "failure": "Failure", "failed": "Failed", "extracting": "Extracting", "noAvailablePlayers": "No Available Players", "freezeVideo": "Freeze Video", "vms": "VMS", "timeRangeOverflow": "Make Sure Start Time is Greater than End Time", "generatingReport": "Generating Report", "reportGeneratedSuccessfully": "Report Generated Successfully", "selectCol": "Select Columns to Display", "resultsAmount": "Results Count:", "of": "of", "fromTime": "From", "toTime": "To", "sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "today": "Today", "sun": "Sun", "mon": "Mon", "tue": "<PERSON><PERSON>", "wed": "Wed", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat", "su": "Su", "mo": "Mo", "tu": "Tu", "we": "We", "th": "Th", "fr": "Fr", "sa": "Sa", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "jan": "Jan", "feb": "Feb", "mar": "Mar", "apr": "Apr", "jun": "Jun", "jul": "Jul", "aug": "Aug", "sep": "Sep", "oct": "Oct", "nov": "Nov", "dec": "Dec", "maxAllowedRowsIs": "Max Allowed Rows Is", "entities": "Entities", "selectedRangeCantBeInTheFuture": "Selected Range Can't be in The Future", "firstDayOfWeek": "monday", "selectTime": "Select Time", "dateOverflow": "Can't Select Dates In The Future", "colsSelected": "{0} columns selected", "noServerConnection": "Camera Connection Lost, Reconnecting...", "description": "Description", "confidence": "Confidence (%)", "noImagesFound": "No Images Found", "carImage": "Car Image", "plateImage": "Plate Image", "entityImage": "Entity Image", "entity": "Entity", "gallery": "Gallery", "player": "Player", "openPlaybackFailed": "Open Playback Failed", "noStreamerConnection": "Camera Connection Lost", "TwoHourMaxLimit": "2 Hours Max Range Exceeded!", "searchTemplateParams": "Search Template Params", "From": "from", "To": "to", "ColumnDetails": "Column Details", "ColumnName": "Column Name", "allValues": "All Values", "addColumn": "Add Column", "removeColumn": "Remove <PERSON>n", "name": "Name", "type": "Type", "failedToAddColumn": "Failed To Add Column", "colNameAlreadyExist": "Column Name Already Exist", "ServerTypes": {"Core_RES_System": "System", "Core_RES_ALPRLane": "ALPRLane", "Core_RES_Dashboard": "Dashboard", "Core_RES_Device": "<PERSON><PERSON>", "Core_RES_Input": "Input", "Core_RES_Output": "Output", "Core_RES_Layout": "Layout", "Core_RES_Map": "Map", "Core_RES_InputChannel": "InputChannel", "Core_RES_Entity": "Entity", "Core_RES_Asset": "<PERSON><PERSON>", "Core_RES_LightGW": "Light GW", "Core_RES_Light": "Light", "Core_RES_Cabinet": "Cabinet", "Core_RES_PowerMeter": "Power Meter"}, "Core_RES_ALPRLane": "ALPRLane", "Core_RES_Dashboard": "Dashboard", "Core_RES_Device": "<PERSON><PERSON>", "Core_RES_Input": "Input", "Core_RES_Output": "Output", "Core_RES_Layout": "Layout", "Core_RES_System": "System", "Core_RES_Map": "Map", "Core_RES_InputChannel": "Input Channel", "Core_RES_Entity": "Entity", "Core_RES_Asset": "<PERSON><PERSON>", "Core_RES_LightGW": "Light GW", "Core_RES_Light": "Light", "Core_RES_Cabinet": "Cabinet", "Core_RES_Unknown": "Unknown", "Core_RES_Node": "Node", "Core_RES_MapElement": "Map Element", "Core_RES_MapPreset": "Map Preset", "Core_RES_MapText": "Map Text", "Core_RES_OutputChannel": "Output Channel", "Core_RES_LayoutPreset": "Layout Preset", "Core_RES_Account": "Account", "Core_RES_AccountProfile": "Account Profile", "Core_RES_ChannelTour": "Channel Tour", "Core_RES_PTZPreset": "PTZ Preset", "Core_RES_ICN": "ICN", "Core_RES_VideoWall": "Video Wall", "Core_RES_ResourceGroup": "Resource Group", "Core_RES_PTZPattern": "PTZ Pattern", "Core_RES_PTZAuxiliary": "PTZ Auxiliary", "Core_RES_ProcedureTemplate": "Procedure Template", "Core_RES_Sensor": "Sensor", "Core_RES_Sensor_Plural": "Sensors", "Core_RES_LightResourceGroup": "Light Resource Group", "Core_RES_Geofence": "Geofence", "Core_RES_WebStreamer": "Web Streamer", "Core_RES_PowerMeter": "Power Meter", "toggleOutput": "Toggle Output", "useInteractiveMode": "Use Interactive Mode", "allowedFileTypes": "Allowed File Types", "done": "Done", "invalidFileType": "Invalid File Type", "invalidFileSize": "Invalid File Size", "maximumUploadSizeIs": "Maximum Upload Size Is", "chooseFiles": "<PERSON><PERSON>", "failToUpdateServer": "Fail To Update Server", "maxFileForInteractiveModeIs10": "Maximum Files For Interactive Mode Is 10", "pleaseSelectAFile": "Please Select a File", "saved": "Saved", "new": "New", "totalUpdated": "Total Entities Updated", "totalAdded": "Total Entities Added", "totalProcessedFiles": "Total Files Processed", "next": "Next", "errorOnReadFiles": "Error On Reading Files", "zone_name": "Zone Name", "rule_name": "Rule Name", "object_type": "Object Type", "truck_id": "Truck ID", "daily_pickups": "Daily Pickups", "addNewEntity": "Add New Entity", "addEntity": "Add Entity", "editEntity": "Edit En<PERSON>", "firstName": "First Name", "lastName": "Last Name", "startDate": "Start Date", "expirationDate": "Expiration Date", "startDateShouldBeBeforeExpirationDate": "Start date should be before expiration date", "phone": "Phone", "email": "Email", "deviceName": "Device Name", "deviceValue": "Device Value", "associatedDevices": "Associated Devices", "associatedResourceGroups": "Associated Resource Groups", "fieldsMarkedWithAreMandatory": "Fields marked with * are mandatory", "save": "Save", "systemDown": "System is down", "primeryImage": "Primary Image", "secImage": "Secondary Image", "uploadFiles": "Upload Files", "choose": "<PERSON><PERSON>", "uploadConfigFile": "Upload Configuration File", "fileUploadedSuccessfully": "File Uploaded Successfully", "fileUploadFailed": "File Upload Failed", "upload": "Upload ", "lumenFactor": "Lumen factor", "scheduler": "Scheduler", "schedulerName": "Scheduler Name", "resourceGroups": "Resource Groups", "addSchedule": "Add Scheduler", "editSchedule": "Edit Scheduler", "duplicate": "Duplicate", "areYouSureYouWantToDeleteThisSchedule": "Are you sure you want to delete this schedule?", "priority": "Priority", "action": "Action", "actionExecutedSuccessfully": "Action Executed Successfully", "stepCompleted": "Step Completed", "procedureCreated": "Procedure Created", "errorCreatingProcedure": "Error Creating Procedure", "noProcedureTemplates": "No Procedure Templates Found", "proceduresServiceIsNotRunning": "Procedures service is not running", "jumpToProcedures": "Jump To Procedures", "SpecificDay": "Specific Day(s)", "entireMonth": "Entire Month(s)", "onceAYear": "Once a Year", "customDate": "Custom Date", "actionLevel": "Action Level", "unableToLoadData": "Unable to load data", "time": "Time", "dateAndTime": "Date and Time", "copy": "Copy", "edit": "Edit", "rowUpdateFailed": "Row Update Failed", "rowUpdatedSuccessfully": "Row Updated Successfully", "inputchannel": "Input Channel", "state": "State", "unknown": "Unknown", "mapChanged": "Map Changed", "mapRemoved": "Map Removed", "uploadAssetsFile": "Upload Assets File", "assetsManagement": "Assets Management", "procedures": "Procedures", "marketPlace": "Market Place", "locationUpdated": "Location Updated", "locationUpdateError": "Location Update Error", "trafficDetections": "Traffic Detections", "locationManagement": "Location Management", "asset": "<PERSON><PERSON>", "endDate": "End Date", "comments": "Comments", "reserveParking": "Reserve Parking", "editReservation": "Edit Reservation", "addReservation": "Add Reservation", "startTimeShouldBeBeforeEndTime": "Start time should be before end time", "areYouSureYouWantToDeleteThisReservation": "Are you sure you want to delete this reservation?", "setLumenFactor": "Set lumen factor", "addEditEntity": "Add / Edit Entity", "changeCompensationFactors": "Change conpensation factors", "ELECTRA_PARKING_USAGE": "Electra - Parking Usage", "ELECTRA_POINTGRAB_LAST_COUNT": "Electra - Pointgrab Latest Count", "ELECTRA_POINTGRAB_USAGE": "Electra - Pointgrab Usage", "uploadEntities": "Upload Entities", "selectUniqueField": "Select Unique Field", "undefined": "Undefined", "matchKeys": "Match Keys", "keys": "Keys", "image": "Image", "mobilePhone": "Mobile Phone", "failedToParseInputFile": "Failed To Parse Input File", "CELLAV_BATTERY_LEVELS": "Battery Levels", "theAssetIsAlreadyReservedAtTheSelectedTimePleaseTryAnotherTime": "The selected asset is already reserved at the selected time. Please try another time.", "noAvailableParkingAtTheRequestedTime": "There's no available parking at the requested time.", "addNewResourceGroup": "Add New Resource Group", "mandatoryFieldsCantBeUndefined": "Mandatory fields can't be Undefined", "totalIgnored": "Total Ignored", "totalFailed": "Total Failed", "totalProcessedItems": "Total Processed Items", "StartRecording": "Start Recording", "ActivateRelay": "Activate Relay", "SetLightGroupStrength": "Set Light Group Strength", "uniqueID": "Unique ID", "allRequierdFieldsMustBeSelected": "All requierd fields must be selected", "address": "Address", "status": "Status", "symbol": "Symbol", "summary": "Summary", "added": "Added", "missingMandatoryFields": "Missing Manda<PERSON> Fields", "alreadyExist": "Already Exist", "downloadSummary": "Download Summary", "failureReason": "Failure Reason", "wrongFieldType": "Wrong Field Type", "reportGenerationCancelled": "Report Generation Cancelled", "failedToOpenChannel": "Failed To Open Channel", "generalDesk": "General <PERSON>", "generalRoom": "General Room", "generalParking": "<PERSON>", "dynamicAttributes": "Dynamic Attributes", "addNewEdFields": "Add <PERSON> EdFields", "displayLayout": "Display Layout", "cameras": "Cameras", "videoWall": "Video Wall", "makeFullScreen": "Make Full Screen", "showSideBar": "Show Side Bar", "colNameCantContainDots": "Column Name Can't Contain Dots", "thereAreNoAvailableAssetsAtTheRequestedTime": "There Are No Available Assets At The Requested Time", "userIsNotAllowedToReserveAssets": "User Is Not Allowed To Reserve Assets", "assetMaxTimeExceeded": "Asset Max Time Exceeded", "selectedEntityAlreadyHasAReservationForTheRequestedTime": "Selected Entity Already Has A Reservation For The Requested Time", "logoText": "CymbIoT", "startTime": "Start Time", "endTime": "End Time", "interval": "Interval", "on": "On", "OFF": "off", "ON": "on", "off": "Off", "dayOfMonth": "Day Of Month", "monthly": "Monthly", "yearly": "Yearly", "Custom": "Custom Date", "RepeatWeekly": "Repeat weekly", "RepeatMonthly": "Repeat monthly", "RepeatYearly": "Repeat yearly", "Every": "Every", "Years": "Years", "selectCamera": "Select a Camera", "addCol": "Add Column", "changeCol": "Change Column", "deleteCol": "Delete Column", "changedTo": "Changed To", "editColumns": "<PERSON>", "Core_RES_Subelement": "Sub Element", "selectAnOption": "Select An Option", "selectAGroup": "Select A Group", "addToExistingResourceGroup": "Add To Existing Resource Group", "createResourceGroup": "Create Resource Group", "deleteResourceGroup": "Delete Resource Group", "removeFromResourceGroup": "Remove From Resource Group", "enterGroupName": "Enter Group Name", "resourceGroupManager": "Resource Group Manager", "openResourceGroupManager": "Open Resource Group Manager", "can'tDeleteAllResourcesFromAGroup": "Can't Delete All Resources From A Group", "can'tDeleteResourcesFromUnassigned": "Can't Delete Resources From Unassigned", "unassigned": "Unassigned", "selectAll": "Select All", "unselectAll": "Unselect All", "createdSuccessfully": "Created Successfully", "created": "Created", "updated": "Updated", "createProcedure": "Create Procedure", "options": "Options", "comment": "Comment", "actions:": "Actions:", "handledBy": "Handled By", "completeStep": "Complete Step", "resourcesAddedSuccessfully": "Added Resources Successfully", "resourcesRemovedSuccessfully": "Removed Resources Successfully", "can'tAddResourcesOfDifferentTypeToAHomogeneousGroup": "Can't Add Resources Of Different Type To A Homogeneous Group", "Granted Entry": "Granted Entry", "Usage": "Usage", "Hour": "Hour", "Free Zones": "Free Zones", "Used Zones": "Used Zones", "Total Zones": "Total Zones", "device": "<PERSON><PERSON>", "counter": "Counter", "delta": "Delta", "id_counter": "Counter Id", "input": "Input", "connected": "Connected", "addCamera": "Add Camera", "add": "Add", "profilesSelected": "{0} profiles selected", "selectProfiles": "Select Profiles", "failedToChangeColumn": "Failed To Change Column Name", "failedToDeleteColumn": "Failed To Delete Column", "failedToResetDevice": "Failed To Reset Device", "settings": "Settings", "openSettings": "Open Settings", "ipAddress": "IP Address", "httpPort": "HTTP Port", "rtspPort": "RTSP Port", "rtspLink": "RTSP Link", "deviceUnreachable": "<PERSON>ce Unreachable", "formatException": "Wrong Format", "insufficientLicense": "Insufficient License", "emptyValue": "Empty Value", "labelAlreadyExists": "Label Already Exists", "resourceDown": "Resource Down", "resourceAlreadyAssigned": "Resource Already Assigned", "invalidName": "Invalid Name", "FailToAddRTSPDevice": "Fail To Add RTSP Device", "uploadRTSP": "Upload RTSP", "showVCAObjects": "Show VCA Objects", "showVCAConfig": "Show VCA Config", "showPTZControls": "Show PTZ Controls", "togglePTZControls": "Toggle PTZ Controls", "showVCAConfigTooltip": "Show or hide VCA configuration", "showVCAObjectsTooltip": "Show or hide VCA detected objects", "saveSettings": "Save settings", "cancelChanges": "Cancel changes", "closeLayouts": "Close layouts panel", "closeCameraSelection": "Close camera selection", "languageOptions": "Language Options", "vcaOptions": "Video Analytics Options", "groupedEvents": "Grouped Events", "openToggleTimeline": "Recordings Calendar", "closeToggleTimeline": "Close Timeline", "entitySavedSuccessfully": "Entity Saved Successfully", "pleaseWait": "Please Wait", "otherRequestsProgress": "Other Requests Are In Progress", "xAxis": "X Axis", "yAxis": "Y Axis", "time_stamp_formatted": "Time Stamp", "light_id": "Light Id", "failure_msg": "Failure Message", "line_counter_type": "Line Counter Type", "LastUpdate": "Last Update", "rule_id": "Rule Id", "zone_id": "Zone Id", "old_counter": "Old Counter", "extra_data": "extra_data", "active": "Active", "message": "Message", "tripwire": "<PERSON>", "partial_view_change": "Partial View Change", "full_view_change": "Full View Change", "output": "Output", "signal": "Signal", "pir": "<PERSON><PERSON>", "audio_analytics_type": "Audio Analytics Type", "input_channel": "Input Channel", "motion": "Motion", "vmd_area": "VMD Area", "user_logged": "User Logged", "node": "Node", "hard_disk": "Hard Disk", "test": "Test", "geofence": "Geofence", "geofence_id": "Geofence Id", "timer": "Timer", "first_name": "First Name", "last_name": "Last Name", "user_id": "User Id", "code": "Code", "concentrator_name": "Concentrator Name", "light_dim": "Light Dim", "light_power": "Light Power", "light_strength": "Light Strength", "light_energy": "Light Energy", "light_power_factor": "Light Power Factor", "light_voltage": "Light Voltage", "light_current": "Light Current", "light_working_hours": "Light Working Hours", "light_aux_power": "Light Aux Power", "light_aux_energy": "Light Aux Energy", "sensor_type": "Sensor Type", "sensor_value": "Sensor Value", "temperature": "Temperature", "comm_quality": "Comm Quality", "field_name": "Field Name", "field_value": "Field Value", "vacancy": "vacancy", "empty": "empty", "entity_rule": "Entity Rule", "backToLive": "Back To Live", "timeType": "Time Type", "sunrise": "Sunrise", "sunset": "Sunset", "specificTime": "Specific time", "beforeAfter": "Before or After (minutes)", "nameInvalid": "Name invalid", "syncAll": "Sync All", "LightWorkingHours": "Light Working Hours", "AlarmConfigurationText": "Alarm Configuration", "AlarmSeverityValue": "Alarm Severity Value", "AlarmStatusValue": "Alarm Status Value", "LightId": "Light Id", "LightTemperature": "Light Temperature", "IsAuxOn": "Is Aux On", "AuxEnergy": "Aux Energy", "AuxPower": "Aux Power", "LightNominalPower": "Light Nominal Power", "PhotoSensorPresence": "Photo Sensor Presence", "unsync": "Unsync", "FireTrigger": "<PERSON> As Trigger", "IoCommand": "Set Group Status", "authenticationError": "The user is blocked, Try later", "captchaNotValid": "If error persists please contact customer support", "authenticationErrorTitle": "Authentication error", "uploadManager": "Upload Manager", "downloadEntitiesTemplate": "Download Entities Template", "uploadFile": "Upload File", "uploadingEntities": "Uploading Entities", "entitiesUploadedSuccessfully": "Entities Uploaded Successfully", "noItemWasSelected": "No Item Was Selected", "openUploadManager": "Open Upload Manager", "rtspUploader": "RTSP Uploader", "identity": "Identity", "totalMissingMandatoryFields": "Total Missing Man<PERSON><PERSON> Fields", "entityUploader": "Entity Uploader", "transformation": "Transformation", "deleteRules": "The schedule has associated rules. Continue anyway?", "noRecordsFound": "No Records Found", "wrongGuidFormat": "Wrong Guid Format", "addNewDashboard": "Add New Dashboard", "timeline": "Timeline", "static": "Static", "saveLayout": "Save Layout", "layoutSelected": "Layout Selected", "playerAdded": "Player Added", "dashboardSaved": "Dashboard Saved", "failedToSaveDashboard": "Failed To Save Dashboard", "savingDashboard": "Saving Dashboard", "areYouSureYouWantToDeleteThisDashboard": "Are You Sure You Want To Delete This Dashboard?", "dashboardDeletedSuccessfully": "Dashboard Deleted Successfully", "failedToDeleteDashboard": "Failed To Delete Dashboard", "createDashboard": "Create Dashboard", "dashboardName": "Dashboard Name", "unsavedChangesWillBeRemovedProceed": "Unsaved Changes Will Be Removed. Proceed?", "create": "Create", "editDashboardName": "Edit Dashboard Name", "editName": "Edit Name", "saveDashboard": "Save Dashboard", "removeDashboard": "Remove Dashboard", "refreshDashboard": "Refresh Dashboard", "itemsSelected": "items Selected", "selectDataSource": "Select Data Source", "header": "Header", "downloadLightsTemplate": "Download Lights Template", "uploadLights": "Upload Lights", "lightsUploader": "Lights Uploader", "deviceID": "Device ID", "lightID": "Light ID", "lightIdStr": "Light Id Str", "gatewayID": "Gateway ID", "totalInvalidLocation": "Total Invalid Location", "extraData": "Extra Data", "General": "General", "MissingData": "Missing Data", "FailedToParse": "Failed To Parse", "AlreadyExist": "Already Exist", "InvalidName": "Invalid Name", "DeviceDoesNotExist": "<PERSON><PERSON> Does Not Exist", "InvalidLocation": "Invalid Location", "FailedToAddLocation": "Failed To Add Location", "FailedToAddED": "Failed To Add ED", "NotAuthorized": "Not Authorized", "notifications": "Notifications", "createReport": "Create Report", "resetCounter": "Reset Counter", "baseRasterMap": "Base Raster Map", "online": "Online", "offline": "Offline", "details": "Details", "unavailable": "Unavailable", "total": "Total", "population": "Population", "occupancy": "Occupancy", "reopenChannels": "Do you what to Reopen last viewed Channels?", "guid": "GUID", "open": "Open", "closed": "Closed", "in_progress": "In Progress", "waiting": "Waiting", "snoozed": "Snoozed", "archived": "Archived", "copyToClipboard": "Copy to Clipboard", "copiedToClipboard": "Copied to Clipboard", "vmsPageTitle": "Add Cameras", "help": "Help", "searchBox": "Search", "changeTheme": "Change Theme", "closeAll": "Close All", "dayTheme": "Day Theme", "nightTheme": "Night Theme", "themeToggle": "Theme Toggle", "Temperature": "Temperature", "LightStrength": "Light Strenght", "LightPower": "Light Power", "LightEnergy": "Light Energy", "LightCommQuality": "Light Comm Quality", "LightFailure": "Light Failure", "LightVoltage": "Light Voltage", "LightCurrent": "Light Current", "LightPowerFactor": "Light Power Factor", "customerDashboard": {"editDashboard": "Edit Dashboard", "addNewDashboard": "Add new Dashboard", "saveTemplateDashboard": "Save Dashboard as Template", "saveDashboard": "Save", "cancel": "Cancel", "addDashboardTitle": "Add new Dashboard", "editDashboardTitle": "Edit Dashboard", "addWidgetsTitle": "Add widgets", "selectHierarchyLevel": "Select hierarchy level", "noDashboardParent": "Without parent", "delete": "Delete Dashboard", "deleteConfirmationMessage": "Do you want to delete this dashboard ?", "deleteConfirmationHeader": "Delete Dashboard Confirmation ?", "noWidgetsAvailable": "There are no widgets available.", "isOldDashboard": "This is an old dashboard.", "noDashboardsAvailable": "There are no dashboards available.", "errorGettingDashboard": "Error when getting dashboard", "editWidget": "Edit Widget", "deleteWidget": "<PERSON><PERSON><PERSON>t", "widgetSize": {"map": {"xXBig": "Big", "xBig": "Medium", "big": "Small"}, "player": {"big": "Big", "medium": "Medium"}, "dashbox": {"small": "Small", "xMedium": "Medium"}, "gauge": {"xSmall": "Small", "medium": "Medium", "big": "Big"}, "notification": {"big": "Big", "medium": "Medium", "tall": "Tall"}, "pieChart": {"big": "Big", "medium": "Medium", "tall": "Tall"}, "lineChart": {"big": "Small", "wide": "Medium", "xWide": "Big"}, "sensorStatus": {"big": "Big"}, "emptySpace": {"xXBig": "Big (5x5)", "xBig": "Big (4x4)", "big": "Big (3x3)", "medium": "Medium (2x2)", "xMedium": "Medium (1x3)", "small": "Small (1x2)", "xSmall": "Small (2x1)", "xXSmall": "Small (1x1)", "tall": "Tall (4x2)", "xTall": "Tall (4x1)", "wide": "Wide (3x4)", "xWide": "Wide (3x5)"}, "embeddedFile": {"xXSmall": "Extra small"}, "xXBig": "Big (5x5)", "xBig": "Big (4x4)", "big": "Big (3x3)", "medium": "Medium (2x2)", "xMedium": "Medium (1x3)", "small": "Small (1x2)", "xSmall": "Small (2x1)", "xXSmall": "Small (1x1)", "tall": "Tall (4x2)", "xTall": "Tall (4x1)", "wide": "Wide (3x4)", "xWide": "Wide (3x5)"}, "editWidgetTitle": "Edit Widget Title", "editWidgetSize": "Edit Widget Size", "selectMap": "Select Map", "selectResourceGroup": "Select From Resource Group", "itemsFound": "items found", "Core_RES_InputChannel": "Cameras", "Core_RES_Light": "Lights", "Core_RES_ALPRLane": "ALPRLane", "Core_RES_Dashboard": "Dashboard", "Core_RES_Device": "<PERSON><PERSON>", "Core_RES_Input": "Input", "Core_RES_Output": "Output", "Core_RES_Layout": "Layout", "Core_RES_Map": "Map", "Core_RES_Entity": "Entity", "Core_RES_Asset": "<PERSON><PERSON>", "Core_RES_LightGW": "Light GW", "Core_RES_System": "System", "selectGroup": "Select Group", "selectResource": "Select Resource", "selectTrigger": "Select Trigger", "showAllGroups": "Show All Groups", "defaultMap": "Default Map", "widgetType": {"status": "Status", "map": "Map", "player": "Player", "dashbox": "Dashbox", "timeline": "Timeline", "chart": "Chart", "gauge": "Gauge", "notification": "Notification panel", "pieChart": "Pie Chart", "lineChart": "Line Chart", "sensorStatus": "Sensor Status", "emptySpace": "Empty Space", "embeddedFile": "Embedded File", "urlShortcut": "Url Shortcut"}, "thresholdPoints": "Threshold Points", "minMaxMeasurmentValues": "Min & Max & Measurment Values", "dataNotSet": "Data not set", "thresholdStart": "start", "thresholdEnd": "end", "thresholdEventName": "event name", "selectNotificationNumber": "Notification number", "chartDataType": "Data Type", "selectDataType": "Select Data Type", "siteReadiness": "Resource Status", "tasks": "Tasks", "tasksUrgency": "Tasks Urgency", "dataIsZero": "No data to show", "pieChartLabels": {"open": "Open", "closed": "Closed", "inprogress": "In progress", "important": "Important", "low": "Low", "normal": "Normal", "none": "None", "online": "Online", "offline": "Offline", "free": "Free", "occupied": "Occupied", "alarmed": "Alarmed", "error": "Error", "saved": "Saved", "on": "On", "off": "Off", "unknown": "Unknown", "offbyradio": "Off By Radio", "dim": "<PERSON><PERSON>", "offbypower": "Off By Power", "temperror": "Temporary Error", "notspecified": "Not specified", "withoutcommunication": "No communication"}, "selectResourceState": "Select Resource State", "selectTimePeriod": "Select Time Period", "Core_RES_Unknown": "Core RES Unknown", "selectResourceType": "Select Resource Type", "selectColorClass": "Select Colour", "selectBakgroundColorClass": "Select Background Colour", "selectTextColorClass": "Select Text Colour", "saveAsDefault": "Save as <PERSON><PERSON><PERSON>", "displayMethod": "Display Method", "countResults": "Count Results", "resultValue": "Result Value", "groupedValue": "Grouped Value", "dashboardPriority": "Dashboard Priority", "displayInInventory": "Display in inventory"}, "dim": "<PERSON><PERSON>", "offByRadio": "Off By Radio", "checkLight": "Check status", "resourceStatus": "Resource Status", "checklight": "Check status", "offMaintenanceCheck": "OFF (Maintenance Check) !", "offParamsFluctuation": "OFF (Params Fluctuation) !", "OFF PARAMS FLUCTUATION": "OFF (Params Fluctuation) !", "onParamsFluctuation": "ON (Params Fluctuation) !", "ON PARAMS FLUCTUATION": "ON (Params Fluctuation) !", "OFF MAINTENANCE CHECK": "OFF (Maintenance Check) !", "off maintenance check": "OFF (Maintenance Check) !", "SIM ISSUE": "Gateway sim not connected to network ", "simIssue": "Gateway sim not connected to network ", "sim issue": "Gateway sim not connected to network ", "maintenanceCheck": "ON (Maintenance Check) !", "maintenance check": "ON (Maintenance Check) !", "MAINTENANCE CHECK": "ON (Maintenance Check) !", "Checklight": "Check status", "NO COMM": "No communication", "CHECK LIGHT": "ON (check status)", "offByPower": "Off By Power", "tempError": "Temporary Error", "notSpecified": "Not specified", "withoutCommunication": "No communication", "projectNameHe": "Project Name He", "area": "Area", "streetName": "Street Name", "poleId": "Pole Id", "poleHeight": "Pole Height", "lampPower": "Lamp Power", "lampModel": "Lamp Model", "projectNameEn": "Project Name En", "lampDriver": "<PERSON><PERSON>", "powerBoxId": "Power Box Id", "pBoxNameHe": "P Box Name He", "pBoxLatitude": "P Box Latitude", "pBoxLongitude": "P Box Longitude", "areaId": "Area Id", "areaHeb": "Area Heb", "projectLampId": "Project Lamp Id", "streetId": "Street Id", "arm": "Arm", "nrItemsSelected": "{0} items selected", "min": "Min", "max": "Max", "measurementUnit": "Measurement Unit", "formErrors": {"required": "Required", "startEndRequired": "Start and end fields required", "pattern": "Code does not satisfy pattern"}, "dashboardSavedAsDefault": "Dashboard saved as default", "deletedDefaultDashboard": "Deleted default dashboard", "noDataWaitingForEvents": "No Data, Waiting for Events", "micError": "Failed to start recording your microphone", "appMap": {"noMapsAvailable": "There are no maps available", "addMap": "Add Map", "editMap": "Edit Map", "deleteMap": "Delete Map", "saveAsDefault": "Save as default", "deletedDefaultMap": "Deleted default map", "editLayers": "Layers", "tileSource": {"OSM": "Open Street Map", "SW": "Stamen Water Color", "raster": "<PERSON><PERSON>"}, "saveMap": "Saving Map", "mapSaved": "Map Saved", "failedToSaveMap": "Failed to save map", "deleteConfirmationMessage": "Do you want to delete this map ?", "deleteConfirmationHeader": "Delete Map Confirmation ?", "mapSavedAsDefault": "Map Saved as <PERSON><PERSON><PERSON>", "selectTileSource": "Select Tile Source", "mapName": "Map Name", "layers": "Layers", "manage": "Manage", "addNewLayer": "Add new layer", "layerName": "Layer Name", "selectResourceGroup": "Select Resource Group", "selectResource": "Select Resource", "selectResourceState": "Select Resource State", "selectResourceType": "Select Resource Type", "availableDevices": "Available Resources", "updateMapElement": "Update map element", "mapElementSaved": "Map element saved", "failedToUpdateMapElement": "Failed to update map element", "saveMapElement": "Save map element", "deleteMapElement": "Delete Map Element", "mapElementDeleted": "Map element deleted", "failedToDeleteMapElement": "Failed to delete map element", "filter": "Filter", "groupActions": "Group actions", "search": "Map Search", "searchSuccessful": "Search is successful", "searchHasResultsNumber": "You have {{resultsNumber}} results.", "noResultsSummary": "Not applying search filters.", "resetSearch": "Reset search"}, "apply": "Apply", "successfullyDownloaded": "Successfully downloaded!", "downloadError": "Cannot be downloaded!", "outdatedBrowser": "You are using an outdated browser version. Please update to the latest version!", "outdatedBrowserTitle": "Your browser is outdated", "free": "Free", "occupied": "Occupied", "alarmed": "Alarmed", "takeSnapshot": "Take a snapshot", "finish": "Finish", "update": "Update", "setLightIntensity": "Set light intensity", "editFactors": "Edit Factors", "enableTrafficFactor": "Enable Traffic Factor", "enableWeatherFactor": "Enable Weather Factor", "enableDisableGroups": "Enable/Disable Groups", "compensatedValue": "Compensated Value", "setWeatherFactor": "Set weather factor", "setTrafficFactor": "Set traffic factor", "setLightIntensitySucces": "Set light intensity was successfully set", "setFactorsSucces": "Factors set successfully", "setLightIntensityError": "<PERSON><PERSON> set light intensity", "setLightIntensityFailure": "Failed to set light intensity", "openCamera": "Open camera", "inventory": {"inventory": "Inventory", "addDevice": "Add device", "editDevice": "Edit device", "results": "Results", "selectManufacturer": "Select manufacturer", "selectModel": "Select model", "credentials": "Credentials", "httpPort": "HTTP Port", "rtspPort": "RTSP Port", "storageDrive": "Storage drive", "rtspLink": "RTSP link", "deleteConfirmationMessage": "Do you want to delete this resources?", "deleteConfirmationHeader": "Delete resource confirmation?"}, "resourceGroupManagerNew": {"resourceGroup": "Resource group mananager", "newResourceGroup": "Create a new Resource Group", "editResourceGroup": "Edit existing Resource Group", "selectGroup": "Select a group", "selectProfiles": "Select profiles"}, "formValidation": {"field": " Field is required!", "numberAndSymbolsNotAllowed": "Numbers and symbols are not allowed!", "portField": "Field is required (ex: 127.0.0.1)!", "ipAddressField": "is not a valid IP Address!", "minFourCharacters": "Must be at least 4 characters!"}, "swalMessages": {"selectEntity": "Select entity", "selectEntityMessage": "Resource manager requires selection of at least one entity. Please make a selection to continue with your action."}, "resetTable": "Reset table and filters", "properties": "Properties", "propertiesNoDataWasFound": "No extra data was found", "viewInfo": "View Info", "refreshLightData": "Refresh light data", "refreshLightDataSuccess": "Refresh light data successs", "refreshLightDataError": "Refresh light data error", "refreshLightDataFailure": "Refresh light data failed", "lat": "Latitude", "lng": "Longitude", "groups": "Groups", "lampGUID": "<PERSON><PERSON>", "resourceType": "Resource Type", "jumpToMap": "Jump to map", "jumpToInventory": "Jump to inventory", "VCA5_LINECOUNTER_A": "VCA Line crossing A", "VCA5_DWELL": "VCA Dwell", "VCA5_PRESENCE": "VCA Presence", "VCA5_ENTER": "VCA Enter", "VCA5_EXIT": "VCA Exit", "VCA5_APPEAR": "VCA Appear", "VCA5_DISAPPEAR": "VCA Disappear", "VCA5_STOP": "VCA Stop", "VCA5_DIRECTION": "VCA Direction", "VCA5_SPEED": "VCA Speed", "VCA5_TAILGATING": "VCA Tailgating", "VCA5_LINECOUNTER_B": "VCA Line Crossing B", "VCA5_ABOBJ": "VCA Abobj", "VCA5_SMOKE": "VCA Smoke", "VCA5_FIRE": "VCA Fire", "VCA5_COLSIG": "VCA ColSig", "VCA5_UNKNOWN": "VCA Unknown", "VCA5_PRESENCE_End": "VCA Presence End", "VCA5_ENTER_End": "VCA Enter End", "VCA5_EXIT_End": "VCA Exit End", "VCA5_APPEAR_End": "VCA Appear End", "VCA5_DISAPPEAR_End": "VCA Disappear End", "VCA5_STOP_End": "VCA Stop End", "VCA5_DWELL_End": "VCA Dwell End", "VCA5_DIRECTION_End": "VCA Direction End", "VCA5_SPEED_End": "VCA Speed End", "VCA5_TAILGATING_End": "VCA Tailgating End", "VCA5_LINECOUNTER_A_End": "VCA Line Counter A End", "VCA5_LINECOUNTER_B_End": "VCA Line Counter B End", "VCA5_ABOBJ_End": "VCA Abobj End", "VCA5_RMOBJ_End": "VCA Rmobj End", "VCA5_SMOKE_End": "VCA Smoke End", "VCA5_FIRE_End": "VCA Fire End", "VCA5_COLSIG_End": "VCA Colsing End", "VCA5_UNKNOWN_End": "VCA Unknown End", "selectFieldName": "Select field name", "selectOperand": "Select operand", "freeText": "Free text", "reset": "Reset", "operand": {"equals": "Equals", "notEquals": "Not Equals", "contains": "Contains", "notContains": "Not Contains"}, "selectedRows": "rows selected", "selectedRow": "row selected", "select": "Select", "deselect": "Deselect", "deviceNotSelected": "<PERSON><PERSON> not selected", "eventName": "Event Name", "allEvents": "All Events", "selectEvent": "Select Event", "resetResource": "Reset resource", "resetResourceSuccess": "Reset resource successfully", "resetResourceError": "Reset resource error", "openNewTab": "Open a new tab", "generatingReportCancelled": "Generating Report Cancelled", "MinVoltage": "Min Voltage", "MaxVoltage": "Max Voltage", "MinTotalPower": "Min Total Power", "MaxTotalPower": "Max Total Power", "MinVoltageBetweenPhases": "Min Voltage Between Phases", "MaxVoltageBetweenPhases": "Max Voltage Between Phases", "MinPowerFactor": "Min Power Factor", "MaxPowerFactor": "Max Power Factor", "Line1ActiveWatts": "Power R", "Line2ActiveWatts": "Power S", "Line3ActiveWatts": "Power T", "ActiveWattHours": "Active Total Energy", "Line1ApparentVoltsAmps": "Apparent Power R", "Line2ApparentVoltsAmps": "Apparent Power S", "Line3ApparentVoltsAmps": "Apparent Power T", "ApparentVoltAmpHours": "Apparent Volt Amp Hours", "CombinedActiveWatts123": "Combined Active Watts 123", "CombinedApparentWatts123": "Combined Apparent Watts 123", "CombinedPowerFactor123": "Combined Power Factor 123", "CombinedReactiveWatts123": "Combined Reactive Watts 123", "AMC_ENVIRONMENT_DUST_PM1": "Dust Concentration PM1", "AMC_ENVIRONMENT_DUST_PM25": "Dust Concentration PM 2.5", "AMC_ENVIRONMENT_DUST_PM10": "Dust Concentration PM 10", "AMC_ENVIRONMENT_SO2_LEVEL": "SO2 Level", "AMC_ENVIRONMENT_NO2_LEVEL": "NO2 Level", "AMC_ENVIRONMENT_O3_LEVEL": "O3 Level", "AMC_SENSOR_CO": "CO Level", "PEOPLE_COUNT": "PEOPLE COUNT", "AMC_RESOURCE_STATUS": "AMC RESOURCE STATUS", "PERSON_STATS": "PERSON STATS", "VEHICLE_STATS": "VEHICLE STATS", "FACE_STATS": "FACE STATS", "AMC_IO_ALARM_OUTPUT": "AMC IO ALARM OUTPUT", "AMC_IO_ANALOG_VALUE": "AMC IO ANALOG VALUE", "AMC_IO_VALUE_0_VOLT": "AMC IO VALUE 0 VOLT", "AMC_IO_VALUE_10_VOLT": "AMC IO VALUE 10 VOLT", "AMC_IO_COMMUNICATION_STATUS": "AMC IO COMMUNICATION STATUS", "AMC_IO_COMMUNICATION_MODE": "AMC IO COMMUNICATION MODE", "AMC_IO_ALARM_TEXT": "AMC IO ALARM TEXT", "AMC_IO_ALARM_ID": "AMC IO ALARM ID", "AMC_IO_ALARM_SEVERITY": "AMC IO ALARM SEVERITY", "Line1Current": "Current R", "Line2Current": "Current S", "Line3Current": "Current T", "NeutralLineCurrent": "Neutral Line Current", "Line1PowerFactor": "PF R", "Line2PowerFactor": "PF S", "Line3PowerFactor": "PF T", "Line1VoltsAmpsReactive": "Reactive Power R", "Line2VoltsAmpsReactive": "Reactive Power S", "Line3VoltsAmpsReactive": "Reactive Power T", "Line3ToLine1Voltage": "Cross Voltage TR", "Line1ToLine2Voltage": "Cross Voltage RS", "Line2ToLine3Voltage": "Cross Voltage ST", "Line1Voltage": "Voltage R", "Line2Voltage": "Voltage S", "Line3Voltage": "Voltage T", "NeutralCurrent": "Neutral Current", "p1": "P1", "p2": "P2", "p3": "P3", "p4": "P4", "p5": "P5", "p6": "P6", "p7": "P7", "p8": "P8", "p9": "P9", "p10": "P10", "p11": "P11", "p12": "P12", "p13": "P13", "p14": "P14", "p15": "P15", "p16": "P16", "p17": "P17", "p18": "P18", "p19": "P19", "p20": "P20", "p21": "P21", "p22": "P22", "p23": "P23", "p24": "P24", "p25": "P25", "p26": "P26", "p27": "P27", "p28": "P28", "p29": "P29", "p30": "P30", "lamptype": "Lamp Type", "dimmingRequest": "Dimming Request", "refreshLocation": "Refresh Location", "refreshPowerMeterData": "Refresh power meter data", "commpleteStep": "Complete step", "procedureStep": "Procedure step", "refreshPowerMeterDataSuccess": "Refresh power meter data success", "refreshPowerMeterDataError": "Refresh power meter data error", "refreshPowerMeterDataFailure": "Refresh power meter data failed", "addedScheduler": "Scheduler added", "cannotAddSchedule": "Cannot add scheduler", "deletedScheduler": "Deleted scheduler", "updatedScheduler": "Scheduler updated", "cannotUpdateSchedule": "Cannot update scheduler", "onDeletedScheduler": "Delete scheduler?", "addedRtsp": "RTSP added", "cannotAddRtsp": "Cannot add RTSP", "selectTypeOfTheFile": "Select type of the file", "CSVUpload": "CSV Upload", "rtspChannels": "RTSP channels", "dropFileOrClickToUpload": "Drop file or click to upload", "PowerMeter_MinVoltage": "PM <PERSON>", "PowerMeter_MaxVoltage": "PM <PERSON>", "PowerMeter_MinTotalPower": "PM <PERSON>", "PowerMeter_MaxTotalPower": "PM <PERSON> TP", "PowerMeter_MinVoltageBetweenPhases": "PM Min V BP", "PowerMeter_MaxVoltageBetweenPhases": "PM Max V BP", "PowerMeter_MinPowerFactor": "PM <PERSON>", "PowerMeter_MaxPowerFactor": "PM <PERSON>", "PowerMeter_Line1ActiveWatts": "PM L1 AW", "PowerMeter_Line2ActiveWatts": "PM L2 AW", "PowerMeter_Line3ActiveWatts": "PM L3 Aw", "PowerMeter_ActiveWattHours": "PM Active WH", "PowerMeter_Line1ApparentVoltsAmps": "PM L1 AVA", "PowerMeter_Line2ApparentVoltsAmps": "PM L2 AVA", "PowerMeter_Line3ApparentVoltsAmps": "PM L3 AVA", "PowerMeter_ApparentVoltAmpHours": "PM AVAH", "PowerMeter_CombinedActiveWatts_123": "PM CActW 123", "PowerMeter_CombinedApparentWatts_123": "PM CAppW 123", "PowerMeter_CombinedPowerFactor_123": "PM CPowF 123", "PowerMeter_CombinedReactiveWatts_123": "PM CReaW 123", "PowerMeter_Line1Current": "PM L1C", "PowerMeter_Line2Current": "PM L2C", "PowerMeter_Line3Current": "PM L3C", "PowerMeter_NeutralLineCurrent": "PM NLC", "PowerMeter_Line1PowerFactor": "PM L1PF", "PowerMeter_Line2PowerFactor": "PM L2PF", "PowerMeter_Line3PowerFactor": "PM L3PF", "PowerMeter_Line1VoltsAmpsReactive": "PM L1VAR", "PowerMeter_Line2VoltsAmpsReactive": "PM L2VAR", "PowerMeter_Line3VoltsAmpsReactive": "PM L3VAR", "PowerMeter_Line3ToLine1Voltage": "PM L3-L1-V", "PowerMeter_Line1ToLine2Voltage": "PM L2-Li-V", "PowerMeter_Line1ToLine3Voltage": "PM L1-L3-V", "PowerMeter_Line1Voltage": "PM L1 V", "PowerMeter_Line2Voltage": "PM L2 V", "PowerMeter_Line3Voltage": "PM L3 V", "PowerMeter_NeutralCurrent": "PM <PERSON>", "PowerMeter_Failure": "PM F", "showResourcesFromGroup": "Show Resources from Resource Group", "acknowledgeClick": "Click to acknowledge", "dismiss": "<PERSON><PERSON><PERSON>", "dismissAll": "Dismiss All", "acknowledge": "Acknowledge", "enableAutoAcknowledgeEvents": "Enable auto acknowledge", "enableAutoClearEvents": "Enable auto clear", "noElementsForSnapshotFound": "No elements for snapshot found", "deleteResource": "Resource was deleted", "cannotDeleteResources": "Resources cannot be deleted", "StatusChange": "Status Change", "LightColor": "Light Color", "LeditechStatusCode": "Leditech Status Code", "lightInformation": "Information", "document": "Document", "openDocument": "Open document", "ackEventSuccess": "Acknowledge event success", "ackEventError": "Acknowledge event error", "widgetOutOfBounds": "Widget removed because it doesn't fit on dashboard", "icon-view-1": "Present for 1 screen", "icon-view-4": "Present for 4 screens", "icon-view-9": "Present for 9 screens", "icon-view-10": "Present for 10 screens", "showMore": "Show more", "showLess": "Show less", "default": "<PERSON><PERSON><PERSON>", "low": "Low", "normal": "Normal", "important": "Important", "urgent": "<PERSON><PERSON>", "userName": "User Name", "enabled": "Enabled", "remove": "Remove", "selectCSVColumn": "Select CSV Column", "REPORT_COUNT": "Report count", "active_watt_hours": "Active watt hours", "apparent_volt_amp_hours": "Apparent volt amp hours", "combined_active_watts_123": "Combined active watts 123", "combined_apparent_watts_123": "Combined apparent watts 123", "combined_power_factor_123": "Combined power factor 123", "combined_reactive_watts_123": "Combined reactive watts 123", "line1_active_watts": "Line1 active watts", "line1_apparent_volt_amps": "Line1 apparent volt amps", "line1_current": "Line1 current", "line1_power_factor": "Line1 power factor", "line1_to_line2_voltage": "Line1 to line2 voltage", "line1_voltage": "Line1 voltage", "line1_volts_amps_reactive": "Line1 volts amps reactive", "line2_active_watts": "Line2 active watts", "line2_apparent_volt_amps": "Line2 apparent volt amps", "line2_current": "Line2 current", "line2_power_factor": "Line2 power factor", "line2_to_line3_voltage": "Line2 to line3 voltage", "line2_voltage": "Line2 voltage", "line2_volts_amps_reactive": "Line2 volts amps reactive", "line3_active_watts": "Line3 active watts", "line3_apparent_volt_amps": "Line3 apparent volt amps", "line3_current": "Line3 current", "line3_power_factor": "Line3 power factor", "line3_to_line1_voltage": "Line3 to line1 voltage", "line3_voltage": "Line3 voltage", "line3_volts_amps_reactive": "Line3 volts amps reactive", "max_power_factor": "Max power factor", "max_total_power": "Max total power", "max_voltage": "Max voltage", "max_voltage_between_phases": "Max voltage between phases", "min_power_factor": "Min power factor", "min_total_power": "Min total power", "min_voltage": "Min voltage", "min_voltage_between_phases": "Min voltage between phases", "neutral_current": "Neutral current", "neutral_line_current": "Neutral line current", "LEDITECH_CONSUMPTION": "Power Consumption", "number_items": "Number items", "select_item": "Select items", "generateChart": "Generate chart", "data_values": "Data values", "PowerMeter_AverageVoltage": "PM Avg V", "PowerMeter_AveragePowerFactor": "PM Avg PF", "PowerMeter_TotalPower": "PM Total P", "PowerMeter_Line2ToLine3Voltage": "PM L2-L3-V", "Core_RES_Light_Plural": "Lights", "Core_RES_Input_Plural": "Inputs", "Core_RES_Output_Plural": "Outputs", "Core_RES_PowerMeter_Plural": "Power Meters", "Core_RES_InputChannel_Plural": "Input Channels", "link": "Link", "invalidDomain": "Url must contain the local domain", "reportResults": "Report results", "gridColumnSize": "Grid column size", "dashboardHasDifferentResolution": "This dashboard was created for {{width}} x {{height}} screen size", "unableToSaveDashboardDueToScrollInfo": "Dashboard will not be saved if it exceeds vertical screen size", "unableToSaveDashboardDueToScrollError": "Dashboard cannot be saved because it exceeds vertical screen size", "selectStep": "Select step", "cameraAlreadyInUse": "Camera is already in use", "dateIsRequired": "Must select a date", "tableView": "Table view", "mapView": "Map view", "viewType": "View type", "dashboxResourcesView": "Dashbox Resources View", "dismissEventSuccess": "Dismiss event success", "dismissEventError": "Dismiss event error", "alreadyAckNotification": "You already acknowledge this event", "eventId": "Event ID", "rec": "Rec", "AMC_LPR_DATE_TIME": "LPR date time", "AMC_LPR_PLATE_NO": "LPR plate number", "AMC_LPR_PLATE_COUNTRY": "LPR plate country", "AMC_LPR_PLATE_CONFIDENCE": "LPR plate confidence", "AMC_LPR_MOVE_DIRECTION": "LPR move direction", "AMC_LPR_MS_IMAGE_PROCESSING": "LPR image processing", "AMC_LPR_VEHICLE_BRAND": "LPR vehicle brand", "AMC_LPR_VEHICLE_MODEL": "LPR vehicle model", "AMC_LPR_VEHICLE_TYPE": "LPR vehicle type", "AMC_LPR_VEHICLE_COLOR": "LPR vehicle color", "AMC_LPR_VEHICLE_CONFIDENCE": "LPR vehicle confidence", "AMC_LPR_SOURCE_CAMERA_NAME": "LPR source camera name", "AMC_LPR_SOURCE_CAMERA_ADDRESS": "LPR camera address", "AMC_PERSON_STATS": "Person stats", "AMC_VEHICLE_STATS": "Vehicle stats", "AMC_PEOPLE_COUNTING": "People counting", "AMC_PRESENCE_STATS": "Presence status", "AMC_PROFILE": "Profile", "AMC_STATE": "State", "AMC_AREA_ID": "Area ID", "AMC_RULE_NAME": "Rule name", "AMC_VIDEO_SOURCE": "Video Source", "AMC_SOURCE": "Source", "AMC_MOTION_TYPE": "Motion type", "AMC_VIDEO_ANALYTICS": "Video analytics", "AMC_DOOR_STATE": "Door state", "AMC_ENTITY_FIRST_NAME": "First name", "AMC_ENTITY_LAST_NAME": "Last name", "AMC_ENTITY_MIN_AGE": "Minimum age", "AMC_ENTITY_MAX_AGE": "Maximum age", "AMC_ENTITY_GENDER": "Gender", "AMC_ENTITY_USER_ID": "User ID", "AMC_ENTITY_CONFIDENCE": "Confidence", "AMC_DETECTION_TIME": "Detection time", "AMC_SOURCE_DESCRIPTION": "Source description", "AMC_SHOCK": "Shock", "detectionList": {"detectionList": "Traffic Detections", "camera": "Camera", "brand": "Brand", "color": "Color", "country": "Country", "licensePlate": "License Plate", "vehicleModel": "Vehicle Model", "vehicleType": "Vehicle Type", "cameraTime": "Camera Time", "plateConfidence": "Plate Confidence", "vehicleConfidence": "Vehicle Confidence", "platePhoto": "Plate Photo", "downloadPhotos": "Download photos", "downloadFailed": "Download photos failed", "platePhotoDeleted": "Plate photo was deleted", "platePhotoFailedToDownload": "Plate photo failed to download", "detectionPhotoDeleted": "Detection photo was deleted", "detectionPhotoFailedToDownload": "Detection photo failed to download", "actions": "Actions", "viewOnMap": "View on Map", "missingDetectionPhoto": "Detection photo is missing", "searchSpecific": "Are you looking for something specific?", "reset": "Reset filters", "noTrafficDetectionsFound": "No Traffic Detections Found"}, "follow": {"follow": "Follow", "items": "Items", "location": "GPS Location", "missingPlateImage": "Plate image is missing", "newDetectionHasOccurred": "A new detection has occurred"}, "vehicleTraffic": {"back": "Back", "pollutionStandardBroken": "This vehicle has broken the allowed pollution standard of the area", "accessFeeNotPayed": "This vehicle has entered this area without paying the access fee", "licenseExpiredOrSuspended": "The driver's license of the holder driver of this vehicle is expired or suspended", "vignetteNotPayed": "This vehicle does not have an active vignette", "technicalInspectionExpired": "This vehicle's technical inspection has expired", "vehicleTaxNotPayed": "This vehicle taxes are not payed to date"}, "detectionsChart": {"notEnoughData": "Not enough data to display a chart", "detectionsInInterval": "detections in interval"}, "parkingDetections": {"parkingDetections": "Parking Detections", "paidAccess": "Access has been paid", "status": "Status"}, "playerBackgroundImage": "Players background image", "resetBackgroundImage": "Reset players background image", "signalingServerDown": "Signaling Server is down !", "generalDetections": {"generalDetections": "General detections", "detectionId": "Detection ID", "description": "Description", "timeStamp": "Timestamp", "inputChannel": "Input channel", "startDate": "Start date", "endDate": "End date", "entityId": "Entity id", "plateId": "Plate id", "downloadArchive": "Download archive", "jumpToTrafficDetections": "Jump to traffic detections", "followOnTrafficDetections": "Follow on traffic detections", "jumpToDossier": "Jump to dossier", "generalDetection": "General detection", "reset": "Reset filters", "actions": "Actions", "noGeneralDetectionsFound": "No General Detections Found", "searchFirstField": "Search by first criteria", "searchSecondField": "Search by second criteria", "selectResourceGroup": "Select a resource group", "selectStartDate": "Select start date and time", "selectEndDate": "Select end date and time"}, "welcomeBack": "Welcome Back!", "enterLoginDetails": "Please enter login details below", "enterSystemIP": "Enter the system IP", "enterUsername": "Enter the username", "enterPassword": "Enter the password", "signIn": "Sign in", "version": "Version", "notificationsPage": {"totalRecordsFormat": "{totalRecords} Total Alerts", "value": "Value"}, "car": "Car", "truck": "Truck", "suv": "Suv", "van": "<PERSON>", "lcv": "<PERSON>", "lastOpenedCameras": "Last opened", "layouts": "Layouts", "systemStructure": "System Structure", "cameraStatusHistory": "Camera Status", "clearHistory": "Clear History", "noCommunication": "No Comm", "actualizationIn": "Auto update in", "sec": "sec", "alerts": "<PERSON><PERSON><PERSON>", "alarms": "Alarms", "warnings": "Warnings", "dispatch": "Dispatch", "camera": "Camera", "location": "Location", "detections": "Detections", "actions": "Actions", "loading": "Loading...", "playback": "Playback", "downloadArchive": "Download Archive", "sumarAlarmeUltimele72Ore": "Alarm summary for the last 72 hours", "unresolvedAlarms": "Unresolved Alarms", "nonconformingAlarms": "Non-compliant Alarms", "alarmsVerified": "Verified Alarms", "pm": "PM", "searchByCamera": "Search by camera, location...", "searchButton": "Search", "dossiers": "Dossiers", "dossierTable": {"title": "Dossier", "searchByName": "Search by name", "readOnly": "Read-Only", "all": "All", "onlyDossiers": "Only Dossiers", "onlyArchives": "Only Archives", "startTime": "Start time", "endTime": "End time", "autoDelete": "Auto-Delete", "name": "Name", "timestamp": "Timestamp", "actions": "Actions", "viewDetections": "View Detections", "toggleAutoDelete": "Toggle Auto-Delete", "viewDetails": "View Details", "viewHistory": "View History", "uploadDossier": "Upload Dossier", "downloadDossier": "Download Dossier", "noDossiersFound": "No dossiers found", "showingEntries": "Showing {first} to {last} of {totalRecords} dossiers", "autoDeleteTrue": "Enabled Auto-Delete(true)", "autoDeleteFalse": "Enabled Auto-Delete(false)", "reset": "Reset filters", "alreadyDeleted": "<PERSON><PERSON><PERSON> already deleted at: {{timestamp}}", "deletionTime": "Deletion Time"}, "dossierHistory": {"title": "History", "search": "Search...", "startTime": "Start time", "endTime": "End time", "details": "Details", "timestamp": "Timestamp", "actions": "Actions", "download": "Download", "noHistoryFound": "No history found", "showingEntries": "Showing {first} to {last} of {totalRecords} entries", "downloadError": "Failed to download dossier", "loadError": "Failed to load dossier history", "noDossierId": "No dossier ID found for deletion", "dossierDeletedSuccessfully": "<PERSON><PERSON><PERSON> deleted successfully", "failedDeleteDossier": "Failed to delete dossier", "noEventIdNavigation": "No event ID found for navigation", "noEventIdDownload": "No event ID found for download", "failedToLoadDossiers": "Failed to load dossiers", "failedToLoadDossierDetails": "Failed to load dossier details", "failedToggleReadOnly": "Failed to toggle read-only", "failedToggleAutoDelete": "Failed to toggle auto-delete", "reset": "Reset filters"}, "dossierDetails": {"title": "Dossier Details", "basicInformation": "Basic Information", "id": "ID", "eventId": "Event ID", "name": "Name", "creationTime": "Creation Time", "status": "Status", "autoDelete": "Auto Delete", "enabled": "Enabled", "disabled": "Disabled", "readOnly": "Read Only", "yes": "Yes", "no": "No", "liveDuration": "Live Duration", "archiveDuration": "Archive Duration", "hours": "Hours", "storage": "Storage", "path": "Path", "size": "Size", "close": "Close", "details": "Details", "timeStamp": "Timestamp", "fileList": "File list"}, "dossierUpload": {"noFileSelected": "No file selected", "fileUploadSuccessfully": "File uploaded successfully!", "invalidFileType": "Invalid file type. Please select a supported file: {{fileTypes}}"}, "dossier": {"autoDelete": "Auto-delete {{status}}", "readOnly": "Read-only {{status}}", "statusOptions": {"enabled": "enabled", "disabled": "disabled"}}, "audit": {"title": "LOG/Audit", "fromPage": "from page", "clicked": "clicked", "entered": "entered", "submitted": "submitted", "value": "value", "inField": "in field", "form": "form", "withValues": "with values", "element": "the element", "button": "the button", "inputField": "the input field", "textField": "the text field", "checkbox": "the checkbox", "radioButton": "the radio button", "passwordField": "the password field", "submitButton": "the submit button", "dropdown": "the dropdown", "textArea": "the text area", "menuItem": "the menu item", "selector": "the selector", "calendar": "the calendar", "withId": "with ID", "withName": "named", "withValue": "with value", "withClass": "with class", "primengComponent": "component", "currentPage": "current page"}, "infoNote": "Information Note", "infoNoteDescription": "Adaugat in portal nota de informare cu caracter personal", "viewInfoPage": "View Information Page", "accept": "Accept", "auditFilter": {"searchSpecific": "Are you looking for something specific?", "searchMessage": "Search by message", "searchUser": "Search by user", "searchIP": "Search by IP address", "startDate": "Start date", "endDate": "End date", "reset": "Reset"}, "auditTable": {"name": "User", "action": "Action", "message": "Message", "timeStamp": "Timestamp", "ipAddress": "IP Address", "noAuditsFound": "No audits found"}, "generalDetectionsDetails": {"type": "Type", "timestamp": "Timestamp", "actions": "Actions", "ack": "Acknowledge", "closeDetails": "Close Details", "noDetectionsDetailsFound": "No Details Detections Found"}, "reportSelection": "Report Selection", "latestAlerts": "Latest <PERSON><PERSON>s", "period": "Period", "selectEndDate": "Select End Date", "reportType": "Report Type", "alert": "<PERSON><PERSON>", "trigger": "<PERSON><PERSON>", "selectAlerts": "Select Alerts", "selectTriggers": "Select Triggers", "alertsSelected": "alerts selected", "triggersSelected": "triggers selected", "reportAlreadyGenerating": "A report is already being generated", "reportGenerationStarted": "Report generation started", "templateNotFound": "Template not found, it may have been deleted", "templateRequiresName": "A template must have a name", "templateNameExists": "A search template with this name already exists", "templateRemoved": "Template removed successfully", "failedToRemoveTemplate": "Failed to remove template", "updateSuccess": "Updated successfully", "saveSuccess": "Saved successfully", "saveFailed": "Failed to save", "invalidDateRange": "From date should be before to date", "selectValidDateRange": "Please select a valid date range", "invalidDateFilterType": "Invalid date filter type", "reportCancelled": "Report generation cancelled", "noDataToExport": "No data to export", "exportSuccess": "Export completed successfully", "exportFailed": "Export failed", "exportEndpointNotFound": "Export endpoint not found", "auditServiceIsNotRunning": "Audit service is not running", "pdfExportNotAvailable": "PDF export is temporarily unavailable. Please use CSV export instead.", "AM": "AM", "PM": "PM", "Mon": "Mon", "Tue": "<PERSON><PERSON>", "Wed": "Wed", "Thu": "<PERSON>hu", "Fri": "<PERSON><PERSON>", "Sat": "Sat", "Sun": "Sun", "January": "January", "February": "February", "March": "March", "April": "April", "May": "May", "June": "June", "July": "July", "August": "August", "September": "September", "October": "October", "November": "November", "December": "December", "alertsAlarms": {"plateNumber": "Plate number", "eventName": "Event Name", "noData": "No Data"}, "totalAlerts": "Total Alerts", "totalAlarms": "Total Alarms", "totalWarnings": "Total Warnings", "switchPosition": "Switch position", "watermark": {"user": "User", "date": "Date"}, "ai": {"title": "AI", "content": "AI Content", "loading": "Loading AI content...", "error": "Failed to load AI content. Please try again later."}, "noValue": "N/A", "selectFromResourceGroup": "Select From Resource Group", "selectChannelTour": "Select Channel Tour", "tour": "Tour", "videoWallSettings": "Video Wall Settings", "layout": "Layout", "export": "Export", "exporting": "Exporting", "exportToHTML": "Export To HTML", "weatherInfo": {"humidity": "<PERSON><PERSON><PERSON><PERSON>", "pressure": "Pressure", "title": "Weather Info", "loadingWeatherData": "Loading weather data.", "loadingWeatherDataError": "Could not load weather data."}, "selectPlayerContainer": "Please select a slot where the video should be displayed."}