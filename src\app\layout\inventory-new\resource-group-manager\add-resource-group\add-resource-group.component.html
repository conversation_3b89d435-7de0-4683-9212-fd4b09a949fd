<div class="resource-group-add-sidebar">
  <form role="form" class="form-format" [formGroup]="resourceGroupAddForm">

    <div class="row">
      <div class="col col-md-12">
        <h2 class="h2-format"> {{ 'resourceGroupManagerNew.resourceGroup' | translate }} </h2>
      </div>
    </div>

    <div class="row">
      <div class="col col-md-12">
        <div class="ui-g">
          <div class="ui-g-12 radio-div">
            <p-radioButton name="newOrOldResource" [(ngModel)]="resourceGroupAddModel.newOrOldResource"
              formControlName="newOrOldResource" [value]="true" class="radio-format"
              label="{{ 'resourceGroupManagerNew.newResourceGroup' | translate }}" inputId="createNew"></p-radioButton>
          </div>
          <div class="ui-g-12 radio-div">
            <p-radioButton name="newOrOldResource" [(ngModel)]="resourceGroupAddModel.newOrOldResource"
              formControlName="newOrOldResource" [value]="false" class="radio-format"
              label="{{ 'resourceGroupManagerNew.editResourceGroup' | translate }}"
              inputId="editResource"></p-radioButton>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col col-md-12">
        <p-dropdown [options]="groups" [(ngModel)]="resourceGroupAddModel.groupId"
          [styleClass]="'input-element dropdown-format'" required="true" formControlName="selectGroup"
          [dropdownIcon]="'fa fa-chevron-down'" placeholder="{{ 'resourceGroupManagerNew.selectGroup' | translate }}">
        </p-dropdown>
      </div>
    </div>

    <div class="row">
      <div class="col col-md-12">
        <p-dropdown [options]="groups" [(ngModel)]="resourceGroupAddModel.profileId"
          [styleClass]="'input-element dropdown-format'" required="true" formControlName="selectProfiles"
          [dropdownIcon]="'fa fa-chevron-down'"
          placeholder="{{ 'resourceGroupManagerNew.selectProfiles' | translate }}">
        </p-dropdown>
      </div>
    </div>

    <div class="row">
      <div class="col col-md-12">
        <pre>
            {{resourceGroupAddModel  | json  }}
          </pre>
      </div>
    </div>

    <div class="row">
      <div class="col col-md-12 div-to-bottom ">
        <div class="btn-toolbar pull-right">

          <button type="button" class="btn btn-outline-primary btn-cancel" (click)="cancel()">{{ 'cancel' | translate
            }}</button>
          <button type="button" class="btn btn-primary btn-save" [disabled]="resourceGroupAddForm.invalid">
            {{ 'add' | translate }}</button>
        </div>
      </div>
    </div>

  </form>

</div>