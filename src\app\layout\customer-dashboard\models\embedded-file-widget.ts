import { Widget } from "./widget";
import { DashboardWidget } from "./dashboard-widget.interface";
import { WidgetSize } from "../enums/widget-size.enum";

export class EmbeddedFileWidget extends Widget {
    
    resource: string | ArrayBuffer;
    
    constructor(obj?: DashboardWidget){
        super(obj);        
    }

    getWidgetSize(): WidgetSize[]{
        return [WidgetSize.xXSmall]
    }

}