import { EventsActionType } from '../enums/events-action-type.enum';
import { DefaultEventsEditorComponent } from '../components/default-events-editor/default-events-editor.component';
import { Type } from '@angular/core';
import { EditEntityComponent } from '../components/edit-entity/edit-entity.component';

export const EventsEditorComponents: {[id in EventsActionType]: Type<DefaultEventsEditorComponent>} = {
    addEntity: EditEntityComponent,
    editEntity: EditEntityComponent,
    saveEntity: null,
    cancel: null
}