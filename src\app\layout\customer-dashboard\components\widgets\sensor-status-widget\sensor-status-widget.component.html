<div class="sensor-status-wrapper" #widgetWrapper >
  <div class="no-data-set" *ngIf="filteredData && filteredData.length === 0">{{'customerDashboard.dataNotSet' |
    translate}}</div>
    <ng-turbo-table [data]="filteredData" [columns]="columns" [showResultsCountTop]="false" [showToggleColumns]="true"
      [numOfRows]="data?.widgetSettings?.numOfRows || '10'" [first]="data?.widgetSettings?.first || '0'" [enableRowClick]="false" [scrollHeight]="'calc(100% - 45px)'" (turboTablePaginatorEvent)="onTurboTablePaginatorEvent($event)">
    </ng-turbo-table>
  <ng-template #statusCell let-rowData>
    <app-resource-status [status]='rowData.status'></app-resource-status>
  </ng-template>
</div>