import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { SharedModule } from 'app/shared/modules/shared.module';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DragDropModule } from 'primeng/dragdrop';
import { BreadcrumbNestedTreeComponent } from './components/breadcrumb-nested-tree/breadcrumb-nested-tree.component';
import { DashboardActionsComponent } from './components/dashboard-actions/dashboard-actions.component';
import { DashboardBreadcrumbComponent } from './components/dashboard-breadcrumb/dashboard-breadcrumb.component';
import { DashboardLayoutComponent } from './components/dashboard-layout/dashboard-layout.component';
import { EditDashboardComponent } from './components/edit-dashboard/edit-dashboard.component';
import { GenericWidgetComponent } from './components/generic-widget/generic-widget.component';
import { NestedTreeComponent } from './components/nested-tree/nested-tree.component';
import { WidgetsModule } from './components/widgets/widgets.module';
import { CustomerDashboardRoutingModule } from './customer-dashboard-routing.module';
import { CustomerDashboardComponent } from './customer-dashboard.component';
import { DashboardDataService } from './services/dashboard-data.service';
import { DashboardUtilsService } from './services/dashboard-utils.service';


@NgModule({
  imports: [
    CommonModule,
    CustomerDashboardRoutingModule,
    SharedModule,
    DragDropModule,
    ConfirmDialogModule,
    WidgetsModule
  ],
  providers: [
    DashboardUtilsService,
    DashboardDataService
  ],
  declarations: [DashboardLayoutComponent, CustomerDashboardComponent, DashboardActionsComponent, NestedTreeComponent, DashboardBreadcrumbComponent, BreadcrumbNestedTreeComponent, GenericWidgetComponent, EditDashboardComponent],
  entryComponents: [EditDashboardComponent]
})
export class CustomerDashboardModule { }
