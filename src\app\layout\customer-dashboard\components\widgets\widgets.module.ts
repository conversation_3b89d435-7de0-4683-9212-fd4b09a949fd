import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { GaugeModule } from 'angular-gauge';
import { SharedModule } from 'app/shared/modules/shared.module';
import { DropdownModule } from 'primeng/dropdown';
import { DashboxWidgetComponent } from './dashbox-widget/dashbox-widget.component';
import { EditDashboxWidgetComponent } from './dashbox-widget/edit-dashbox-widget/edit-dashbox-widget.component';
import { DefaultWidgetComponent } from './default-widget/default-widget.component';
import { EditWidgetComponent } from './edit-widget/edit-widget.component';
import { EditGaugeWidgetComponent } from './gauge-widget/edit-gauge-widget/edit-gauge-widget.component';
import { GaugeWidgetComponent } from './gauge-widget/gauge-widget.component';
import { EditMapWidgetComponent } from './maps-widget/edit-map-widget/edit-map-widget.component';
import { MapWidgetComponent } from './maps-widget/map-widget.component';
import { EditNotificationWidgetComponent } from './notification-widget/edit-notification-widget/edit-notification-widget.component';
import { NotificationWidgetComponent } from './notification-widget/notification-widget.component';
import { EditPieChartWidgetComponent } from './pie-chart-widget/edit-pie-chart-widget/edit-pie-chart-widget.component';
import { PieChartWidgetComponent } from './pie-chart-widget/pie-chart-widget.component';
import { EditPlayerWidgetComponent } from './player-widget/edit-player-widget/edit-player-widget.component';
import { PlayerWidgetComponent } from './player-widget/player-widget.component';
import { EditSensorStatusWidgetComponent } from './sensor-status-widget/edit-sensor-status-widget/edit-sensor-status-widget.component';
import { SensorStatusWidgetComponent } from './sensor-status-widget/sensor-status-widget.component';
// import { ChartsModule as Ng2Charts } from 'ng2-charts';
import { CymbiotMapModule } from 'app/shared/modules/cymbiot-map/cymbiot-map.module';
import { InventoryTableModule } from 'app/shared/modules/inventory-table/inventory-table.module';
import { ChartWidgetComponent } from './chart-widget/chart-widget.component';
import { EditChartWidgetComponent } from './chart-widget/edit-chart-widget/edit-chart-widget.component';
import { EditEmbeddedFileComponent } from './embedded-file/edit-embedded-file/edit-embedded-file.component';
import { EmbeddedFileComponent } from './embedded-file/embedded-file.component';
import { EditEmptySpaceWidgetComponent } from './empty-space-widget/edit-empty-space-widget/edit-empty-space-widget.component';
import { EmptySpaceWidgetComponent } from './empty-space-widget/empty-space-widget.component';
import { EditUrlShortcutComponent } from './url-shortcut/edit-url-shortcut/edit-url-shortcut.component';
import { UrlShortcutComponent } from './url-shortcut/url-shortcut.component';


@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
    DropdownModule,
    SharedModule,
    GaugeModule.forRoot(),
    // Ng2Charts,
    CymbiotMapModule,
    InventoryTableModule
  ],
  declarations: [
    DefaultWidgetComponent,
    EditWidgetComponent,
    MapWidgetComponent,
    EditMapWidgetComponent,
    SensorStatusWidgetComponent,
    EditSensorStatusWidgetComponent,
    ChartWidgetComponent,
    PlayerWidgetComponent,
    EditPlayerWidgetComponent,
    DashboxWidgetComponent,
    EditDashboxWidgetComponent,
    GaugeWidgetComponent,
    EditGaugeWidgetComponent,
    NotificationWidgetComponent,
    EditNotificationWidgetComponent,
    PieChartWidgetComponent,
    EditPieChartWidgetComponent,
    ChartWidgetComponent,
    EditChartWidgetComponent,
    EmptySpaceWidgetComponent,
    EditEmptySpaceWidgetComponent,
    EmbeddedFileComponent,
    EditEmbeddedFileComponent,
    UrlShortcutComponent,
    EditUrlShortcutComponent
  ],
  entryComponents: [
    DefaultWidgetComponent,
    MapWidgetComponent,
    EditMapWidgetComponent,
    SensorStatusWidgetComponent,
    EditSensorStatusWidgetComponent,
    ChartWidgetComponent,
    PlayerWidgetComponent,
    EditPlayerWidgetComponent,
    DashboxWidgetComponent,
    EditDashboxWidgetComponent,
    GaugeWidgetComponent,
    EditGaugeWidgetComponent,
    NotificationWidgetComponent,
    EditNotificationWidgetComponent,
    PieChartWidgetComponent,
    EditPieChartWidgetComponent,
    ChartWidgetComponent,
    EditChartWidgetComponent,
    EmptySpaceWidgetComponent,
    EditEmptySpaceWidgetComponent,
    EmbeddedFileComponent,
    EditEmbeddedFileComponent,
    UrlShortcutComponent,
    EditUrlShortcutComponent
  ]
})
export class WidgetsModule { }
