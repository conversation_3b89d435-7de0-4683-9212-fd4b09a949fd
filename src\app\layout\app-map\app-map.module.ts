import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { FilterPipe } from 'app/filter.pipe';
import { CymbiotMapModule } from 'app/shared/modules/cymbiot-map/cymbiot-map.module';
import { SharedModule } from 'app/shared/modules/shared.module';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { AppMapRoutingModule } from './app-map-routing.module';
import { AppMapComponent } from './app-map.component';
import { MapActionsComponent } from './components/map-actions/map-actions.component';
import { MapEditComponent } from './components/map-edit/map-edit.component';
import { MapFiltersComponent } from './components/map-filters/map-filters.component';
import { MapGroupActionsComponent } from './components/map-group-actions/map-group-actions.component';
import { MapLayersEditComponent } from './components/map-layers-edit/map-layers-edit.component';
import { MapLayersFormComponent } from './components/map-layers-form/map-layers-form.component';
import { MapNavigationComponent } from './components/map-navigation/map-navigation.component';
import { MapSearchComponent } from './components/map-search/map-search.component';
import { mapDropDownActions } from './models/map-dropdown-actions';
import { mapEditActions } from './models/map-edit-actions';
import { mapSidebarActions } from './models/map-sidebar-actions';
import { mapTileSourceList } from './models/map-tile-source-list';


@NgModule({
  imports: [
    CommonModule,
    AppMapRoutingModule,
    CymbiotMapModule,
    SharedModule,
    ReactiveFormsModule,
    ConfirmDialogModule,
  
  ],
  declarations: [
    AppMapComponent,
    MapNavigationComponent,
    MapActionsComponent,
    MapEditComponent,
    MapLayersEditComponent,
    MapFiltersComponent,
    MapGroupActionsComponent,
    MapSearchComponent,
    MapLayersFormComponent,
    FilterPipe,
  ],
  entryComponents: [
    MapEditComponent,
    MapLayersEditComponent,
    MapFiltersComponent,
    MapGroupActionsComponent,
    MapSearchComponent,
    MapLayersFormComponent  
  ],
  exports:[
    FilterPipe,
  ],
  providers: [
    {provide: 'mapSidebarActions', useValue: mapSidebarActions},
    {provide: 'mapDropDownActions', useValue: mapDropDownActions},
    {provide: 'mapEditActions', useValue: mapEditActions},
    {provide: 'mapTileSourceList', useValue: mapTileSourceList}
  ]
})
export class AppMapModule { }
