{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": "68a820ff-707a-4175-bc8c-a8f9de07be8b"}, "version": 1, "newProjectRoot": "projects", "projects": {"rareClient": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "buildOptimizer": false, "aot": false, "assets": ["src/favicon.ico", "src/assets", "src/assets/public"], "styles": ["node_modules/bootstrap/dist/css/bootstrap.css", "node_modules/font-awesome/css/font-awesome.css", "node_modules/primeng/resources/primeng.min.css", "node_modules/primeng/resources/themes/bootstrap4-light-blue/theme.css", "node_modules/primeicons/primeicons.css", "node_modules/ol/ol.css", "node_modules/ol-ext/control/LayerSwitcher.css", "node_modules/ol-ext/control/Search.css", "node_modules/ol-ext/overlay/Popup.css", "node_modules/ol-ext/overlay/Popup.anim.css", "node_modules/vis/dist/vis.css", "node_modules/angular2-draggable/css/resizable.min.css", "src/assets/css/goldenlayout-base.css", "src/assets/css/goldenlayout-light-theme.css", "src/styles/app.scss", "src/styles/dashboard.scss", "src/assets/fonts/new-dashboard.css", "src/assets/fonts/style.css", "src/assets/styles/responsive-layout.css"], "scripts": ["node_modules/chart.js/dist/Chart.js", "node_modules/jquery/dist/jquery.min.js", "node_modules/signalr/jquery.signalR.js", "node_modules/vis/dist/vis.js", "src/assets/js/ol.js", "node_modules/ol-ext/dist/ol-ext.js", "node_modules/ol-ext/control/LayerSwitcher.js", "node_modules/ol-ext/control/Search.js", "node_modules/ol-ext/control/SearchFeature.js", "node_modules/chartjs-plugin-datalabels/dist/chartjs-plugin-datalabels.js", "src/assets/js/popupoverlay.js", "src/assets/js/goldenlayout.min.js", {"input": "src/assets/js/simplewebrtc.bundle.js", "inject": true, "bundleName": "simplewebrtc"}], "webWorkerTsConfig": "tsconfig.worker.json"}, "configurations": {"production": {"optimization": false, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "30mb"}, {"type": "anyComponentStyle", "maximumWarning": "1mb", "maximumError": "1mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "cc": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/main.ts", "with": "src/main-cc.ts"}]}, "cc-production": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}, {"replace": "src/main.ts", "with": "src/main-cc.ts"}]}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "rareClient:build:production"}, "development": {"browserTarget": "rareClient:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "rareClient:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/signalr/jquery.signalR.js", "node_modules/vis/dist/vis.js", "src/assets/js/ol.js", "node_modules/ol-ext/dist/ol3-ext.js", "node_modules/ol-ext/control/layerswitchercontrol.js", "node_modules/ol-ext/control/searchcontrol.js", "node_modules/ol-ext/control/searchfeaturecontrol.js", "src/assets/js/popupoverlay.js", "src/assets/js/goldenlayout.min.js", "src/assets/js/simplewebrtc.bundle.js"], "styles": ["node_modules/bootstrap/dist/css/bootstrap.css", "node_modules/font-awesome/css/font-awesome.css", "node_modules/primeng/resources/primeng.min.css", "node_modules/primeng/resources/themes/bootstrap4-light-blue/theme.css", "node_modules/primeicons/primeicons.css", "node_modules/ol/ol.css", "node_modules/ol-ext/control/layerswitchercontrol.css", "node_modules/ol-ext/control/searchcontrol.css", "node_modules/ol-ext/overlay/popupoverlay.css", "node_modules/ol-ext/overlay/popupoverlay.anim.css", "node_modules/vis/dist/vis.css", "src/assets/css/goldenlayout-base.css", "src/assets/css/goldenlayout-light-theme.css", "src/styles/app.scss"]}}}}}, "defaultProject": "rareClient"}