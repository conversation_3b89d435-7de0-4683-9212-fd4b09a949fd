import {AfterViewInit,Component,EventEmitter,Input,OnInit,Output} from '@angular/core';
import {DomSanitizer,SafeUrl} from "@angular/platform-browser";
import {EventData} from "app/layout/entities/event-data";

@Component({
    selector: 'entity-item',
    templateUrl: './entityItem.component.html',
    styleUrls: ['./entityItem.components.css']
})
export class EntityItemComponent implements OnInit,AfterViewInit {
    isExpanded: boolean = false;
    hoveredImage: string | null = null;
    hoveredImageStyle = {
        top: '0px',
        left: '0px'
    };
    @Input() entity: any;
    @Output() addEntity = new EventEmitter<EventData>();
    constructor(private sanitizer: DomSanitizer) { }

    ngAfterViewInit(): void {
    }

    ngOnInit(): void {
    }

    parseDescription(description: string): any {

        if (!description)
        {
            return null;
        }

        try {
            const parsedDesc = JSON.parse(description);
            const extraData = JSON.parse(parsedDesc.description.extra_data);
            return {
                ...parsedDesc,
                extra_data: extraData
            };
        } catch (error) {
            console.error('Error parsing description:', error);
            return null;
        }
    }

    onAddEntity():void {
        this.addEntity.emit(this.entity);
    }

    toggleExpand(): void {
        this.isExpanded = !this.isExpanded;
    }

    parseSafeUrl(url): SafeUrl {
        return this.sanitizer.bypassSecurityTrustUrl(url);
    }

    showImage(entity: EventData, event: MouseEvent) {
        this.hoveredImage = entity.images.length > 0 ? entity.images[0].source : null;
        this.hoveredImageStyle = {
            top: `${event.clientY}px`,
            left: `${event.clientX + 20}px` // Add a small offset to the right
        };
    }

    hideImage():void {
        this.hoveredImage = null;
    }

}
