import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { GenericMapModule } from "app/shared/modules/generic-map/generic-map.module";
import { SharedModule } from "app/shared/modules/shared.module";
import { DropdownModule } from "primeng/dropdown";
import { GpsMarkerComponent } from "./gps-marker/gps-marker.component";
import { GpslocationComponent } from "./gpslocation/gpslocation.component";
import { MobileLocationRoutingModule } from "./mobileLocation.routing.module";

@NgModule({
  declarations: [GpslocationComponent, GpsMarkerComponent],
  imports: [MobileLocationRoutingModule, CommonModule, DropdownModule, FormsModule, GenericMapModule, SharedModule],
  entryComponents: [GpsMarkerComponent],
})
export class MobileLocationModule {}
