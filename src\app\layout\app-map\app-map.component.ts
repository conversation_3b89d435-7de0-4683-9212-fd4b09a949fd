import { Component, ComponentFactoryRes<PERSON>ver, ComponentRef, ElementRef, Inject, OnDestroy, OnInit, Renderer2, Type, ViewChild, ViewContainerRef } from '@angular/core';
import { CymsidebarService } from 'app/shared/components/cym-sidebar/cym-sidebar.service';
import { Map as MapModel } from 'app/shared/modules/data-layer/models/map';
import { Observable, Subscription, combineLatest } from 'rxjs';

import { ActivatedRoute, Params } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { MapService } from 'app/services/map/map.service';

import { ToastTypes } from 'app/shared/enum/toast-types';
import { GlobalAction } from 'app/shared/models/global-action.interface';
import { CymbiotMapComponent } from 'app/shared/modules/cymbiot-map/components/cymbiot-map/cymbiot-map.component';
import { MapType } from 'app/shared/modules/data-layer/enum/map/map-type.enum';
import { DataChangeType } from 'app/shared/modules/data-layer/models/data-change-type.enum';
import { MapLayer } from 'app/shared/modules/data-layer/models/map-layer';
import { MapLayerService } from 'app/shared/modules/data-layer/services/map-layer/map-layer.service';
import { ExtentObj, Point } from 'app/shared/modules/generic-map/models/map.models';
import { NavigationService, Pages } from 'app/shared/services/navigation.service';
import { SettingsService } from 'app/shared/services/settings.service';
import { ConfirmationService, MessageService } from 'primeng/api';
import { take } from 'rxjs/operators';
import { DefaultMapEditorComponent } from './components/default-map-editor/default-map-editor.component';
import { MapActionType } from './enums/map-action-type.enum';
import { MapEditComponentOutput } from './enums/map-edit-component-output.enum';
import { DefaultMapEditorData } from './models/default-map-editor-data.interface';
import { MapEditorComponents } from './models/map-editor-components';
import { MapFilters } from './models/map-filters.interface';
import { MapObject } from './models/map-object.interface';
import { MapSearchProperties } from './models/map-search-properties.interface';
import { MapState } from './models/map-state.interface';
import { MapUtilsService } from './services/map-utils.service';
import {Guid} from "app/shared/enum/guid";
import { IMapProjectionTransformService } from 'app/shared/modules/generic-map/components/base-map-transform.service';
import {DefaultResources} from "app/shared/enum/enum";

@Component({ 
  selector: 'app-map',
  templateUrl: './app-map.component.html',
  styleUrls: ['./app-map.component.scss'],
  providers: [CymsidebarService, ConfirmationService]
})
export class AppMapComponent implements OnInit, OnDestroy {

  public opened: boolean = false;
  public dockedSize: string = "0px";

  public sidebarActions: GlobalAction[] = []

    public mapsModelStore: Map<string, MapModel> = new Map<string, MapModel>();
    //deeply refactor to a Map<string, MapModel> if you want to prove you are a good dev

  private defaultMap: MapModel = new MapModel({
    identity: DefaultResources.DefaultMap,
    name: 'defaultMap',
    type: MapType.GIS,
    mapLeft: 0,
    mapRight: 0,
    mapTop: 0,
    mapBottom: 0
  });

  public selectedMap: MapObject = {
    id: null,
    item: null
  }

  public state: MapState = {
    edit: false,
    addNew: false
  }
  private subscriptions: Subscription[] = [];
  private navParamName: string = 'map';

  @ViewChild('editComponentFactory', { read: ViewContainerRef, static: false }) editComponent: ViewContainerRef;
  componentRef: ComponentRef<DefaultMapEditorComponent>;

  @ViewChild('cymbiotMap', {static: false}) cymbiotMap: CymbiotMapComponent;
  @ViewChild('mapWrapper', {static: false}) mapWrapper: ElementRef;
  private validationContext: { [name: string]: boolean } = {};
  public mapFilterOptions: MapFilters;
  selectedMapAction: MapActionType = null;
  mapSearchProperties: MapSearchProperties = null;

  mapLayers: Map<string, MapLayer>;
  defaultItemId: string;

  constructor(
    private cymSidebarService: CymsidebarService,
    private navigationService: NavigationService,
    private mapService: MapService,
    private settingsService: SettingsService,
    private componentFactoryResolver: ComponentFactoryResolver,
    private renderer2: Renderer2,
    private mapUtilsService: MapUtilsService,
    private confirmationService: ConfirmationService,
    private translateService: TranslateService,
    private messageService: MessageService,
    private mapLayerService: MapLayerService,
    @Inject('mapSidebarActions') sidebarActions: GlobalAction[],
    private activeRouter: ActivatedRoute,
  ) {
    this.sidebarActions = sidebarActions;
  }
  ngOnInit(): void {
    let getParamsAndMapsSubscriptions = combineLatest(this.navigationService.getParams(), this.mapService.getAll().pipe(take(1))).subscribe((res:any) => {
        let params: Params = res[0];
        let maps: MapModel[] = res[1];
        this.initializeMapsModelStore(maps);
      this.mapsModelStore.set(DefaultResources.DefaultMap, this.defaultMap);
      this.selectedMap.id = params[this.navParamName];


      if (!this.selectedMap.id) {
        let defaultMapId = this.settingsService.get('defaultMap');
        if (defaultMapId) {
          this.navigationService.navigate(Pages.mapNew, { map: defaultMapId });
          return;
        }
        this.selectedMap.id = DefaultResources.DefaultMap;
      }

        this.selectedMap.item = this.mapsModelStore.get(this.selectedMap.id) || this.mapsModelStore.get(DefaultResources.DefaultMap);
    });
    this.subscriptions.push(getParamsAndMapsSubscriptions);

    let mapDataChangeSubscription = this.mapUtilsService.getMapDataChange().subscribe(res => {
      this.selectedMap.item = res;
    });
    this.subscriptions.push(mapDataChangeSubscription);

    let mapValidationContextSubscription = this.mapUtilsService.getMapValidatorChange().subscribe(form => {
      let formKey = Object.keys(form)[0];
      this.validationContext[formKey] = form[formKey];
      let actionIndex = this.sidebarActions.findIndex(action => {return action.type === MapActionType.save;});
      for (let key in this.validationContext) {
        if (!form[key]) {
          this.sidebarActions[actionIndex].disabled = true;
          return;
        }
        this.sidebarActions[actionIndex].disabled = false;
      }
    });
    this.subscriptions.push(mapValidationContextSubscription);

    let mapFilterChangeSubscription = this.mapUtilsService.getFilterDataChange().subscribe((response) => {
      if(response[this.cymbiotMap.instanceId]){
        this.mapFilterOptions = {
          selectedResourceGroups: response[this.cymbiotMap.instanceId]
        };
      }
    });
    this.subscriptions.push(mapFilterChangeSubscription);
    let mapLayersSubscription= this.getMapLayers(this.selectedMap.id).subscribe((res) => {
      this.mapLayers = res;
    });
    this.subscriptions.push(mapLayersSubscription);

    let mapLayersChangeResourceSubscription = this.mapLayerService.resourceChanged().subscribe((res) => {
      switch(res.type){
        case DataChangeType.Create:
        case DataChangeType.Update:
          this.updateOrCreateMapLayers(res.models);
          break;
        case DataChangeType.Delete:
          this.deleteMapLayers(res.models);
          break;
        default:
          console.error('resourceChanged() there is no action based on response type', res.type, DataChangeType);
          break;
      }
    });
    this.subscriptions.push(mapLayersChangeResourceSubscription);

    let mapServiceChangeSubscription = this.mapService.resourceChangedMethod().subscribe(res => {
      switch(res.type){
        case DataChangeType.Create:
        case DataChangeType.Update:
          this.updateOrCreateMaps(res.models);
          break;
        case DataChangeType.Delete:
          this.removeMaps(res.models);
          break;
        default:
          break;
      }
    });
    this.subscriptions.push(mapServiceChangeSubscription);

    let activeRouteSubscription = this.activeRouter.queryParams.subscribe((params) => {

      this.defaultItemId =params['identity'];
    });
    this.subscriptions.push(activeRouteSubscription);
  }

    private initializeMapsModelStore(maps: MapModel[]): void {
        const mapEntries: [string, MapModel][] = maps.map(map => {
            return [map.identity, map];
        });
        this.mapsModelStore = new Map<string, MapModel>(mapEntries);
    }

  updateOrCreateMaps(maps: MapModel[]): void{
    maps.forEach(map => {
        this.mapsModelStore.set(map.identity, map);
    });
  }

  removeMaps(maps: MapModel[]): void{
    maps.forEach(map => {
        this.mapsModelStore.delete(map.identity);
      if(this.selectedMap.id === map.identity){
        this.selectedMap.id = DefaultResources.DefaultMap;
        this.navigationService.navigate(Pages.mapNew, { map: DefaultResources.DefaultMap });
      }
    });
  }

  updateOrCreateMapLayers(layers: MapLayer[]): void {
    layers.forEach((layer: MapLayer) => {
      this.mapLayers.set(layer.identity, layer);
    });
    this.mapLayers = new Map(this.mapLayers);
  }

  deleteMapLayers(layers: MapLayer[]): void {
    layers.forEach((layer) => {
      this.mapLayers.delete(layer.identity);
    });
    this.mapLayers = new Map(this.mapLayers);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(subscription => {return subscription.unsubscribe();});
  }


  getMapLayers(mapId: string): Observable<Map< string, MapLayer>> {
    return this.mapLayerService.getAllLayers({filters: [{mapIdentity: mapId}]});
  }

  onMapAction(event: MapActionType): void {
    this.selectedMapAction = event;
    switch (event) {
      case MapActionType.addNew:
        this.setStateProperties({ edit: false, addNew: true });
        this.setSidebarActionsVisibilityProperties([MapActionType.cancel, MapActionType.save]);
        this.createEditComponent(MapEditorComponents[event], { state: this.state, selectedMap: this.selectedMap }, MapEditComponentOutput.onAddOverlay);
        this.openSidebar();
        break;
      case MapActionType.edit:
        this.setStateProperties({ edit: true, addNew: false });
        this.setSidebarActionsVisibilityProperties([MapActionType.cancel, MapActionType.save]);
        this.createEditComponent(MapEditorComponents[event], { state: this.state, selectedMap: this.selectedMap }, MapEditComponentOutput.onAddOverlay);
        this.openSidebar();
        break;
      case MapActionType.save:
        this.state.addNew ? this.createMap(this.selectedMap.item) : this.updateMap(this.selectedMap.item);
        this.cymSidebarService.closeSidebar();
        break;
      case MapActionType.cancel:
        this.cymSidebarService.closeSidebar();
        break;
      case MapActionType.delete:
        this.deleteMap(this.selectedMap.item);
        this.closeSidebar();
        break;
      case MapActionType.saveAsDefault:
        this.setDefaultMap(this.selectedMap.id);
        break;
      case MapActionType.editLayers:
        this.setStateProperties({ edit: false, addNew: false, editLayers: true });
        this.setSidebarActionsVisibilityProperties([MapActionType.finish]);
        this.createEditComponent(MapEditorComponents[event], { state: this.state, selectedMap: this.selectedMap }, MapEditComponentOutput.onAddLayer);
        this.openSidebar();
        break;
      case MapActionType.groupAction:
        this.setStateProperties({ edit: true, addNew: false });
        this.setSidebarActionsVisibilityProperties([MapActionType.finish]);
        this.createEditComponent(MapEditorComponents[event], { state: this.state, selectedMap: this.selectedMap }, MapEditComponentOutput.onGroupAction);
        this.openSidebar();
        break;
      case MapActionType.filter:
        this.setStateProperties({ edit: true, addNew: false });
        this.setSidebarActionsVisibilityProperties([MapActionType.finish]);
        this.createEditComponent(MapEditorComponents[event], { state: this.state, selectedMap: this.selectedMap, filters: this.mapFilterOptions, mapInstanceId: this.cymbiotMap.instanceId }, MapEditComponentOutput.onFilter);
        this.openSidebar();
        break;
      case MapActionType.search:
        this.setStateProperties({ edit: true, addNew: false });
        this.setSidebarActionsVisibilityProperties([MapActionType.finish]);
        this.createEditComponent(MapEditorComponents[event], { state: this.state, selectedMap: this.selectedMap, mapSearchProperties: this.mapSearchProperties }, MapEditComponentOutput.onSearch);
        this.openSidebar();
        break;
      case MapActionType.finish:
        this.setStateProperties({ edit: false, addNew: false, editLayers: false });
        this.selectedMapAction = null;
        this.closeSidebar();
        break;
      case MapActionType.resetSearch:
        this.mapSearchProperties = {fieldName: null, operand: null, freeText: null};
        break;
      default: {
        this.setStateProperties({ edit: false, addNew: false, editLayers: false });
        this.closeSidebar();
        break;
      }
    }
  }

  setEditComponentOutputData(output: MapEditComponentOutput, data: MapFilters | MapSearchProperties): void {
    switch (output) {
      case MapEditComponentOutput.onFilter:
        this.mapFilterOptions = data as MapFilters;
        break;
      case MapEditComponentOutput.onSearch:
        this.mapSearchProperties = data as MapSearchProperties;
        break;
      default:
        console.error('No edit component has this output: ', output);
        break;
    }
  }

  private openSidebar(): void {
    this.cymSidebarService.toggleDockSidebar("push");
    this.cymSidebarService.openSidebar();
  }

  private closeSidebar(): void {
    this.cymSidebarService.closeSidebar();
  }

  private createEditComponent(editComponent: Type<DefaultMapEditorComponent>, data: DefaultMapEditorData, output?: MapEditComponentOutput): void {
    if (!editComponent) {
      return;
    }

    if (this.componentRef) {
      this.componentRef.destroy();
    }
    const factory = this.componentFactoryResolver.resolveComponentFactory(editComponent as Type<DefaultMapEditorComponent>);
    this.componentRef = this.editComponent.createComponent(factory);
    this.renderer2.addClass(this.componentRef.location.nativeElement, 'dynamic-edit-component');
    this.componentRef.instance.data = data;
    if (output) {
      this.componentRef.instance[output].subscribe(event => {
        this.setEditComponentOutputData(output, event);
      });
    }
  }

  private setStateProperties(stateObject: MapState): void {
    for (let i in stateObject) {
      this.state[i] = stateObject[i];
    }
  }

  private createMap(map: MapModel): void {
    let mapServiceSubscription = this.mapService.create(map).subscribe(() => {
      this.navigationService.navigate(Pages.mapNew, { map: map.identity });
      this.setStateProperties({ addNew: false, edit: false });
        this.mapsModelStore.set(map.identity, map);
        this.messageService.add({severity: 'success', summary: this.translateService.instant(ToastTypes.success), detail: this.translateService.instant('appMap.mapSaved')});
    }, () => {
      this.messageService.add({severity: 'error', summary: this.translateService.instant(ToastTypes.error), detail: this.translateService.instant('failedToSaveMap')});
    });
    this.subscriptions.push(mapServiceSubscription);
  }

  private updateMap(map: MapModel): void {
   let mapServiceUpdateSubscription= this.mapService.update(map).subscribe(() => {
      this.setStateProperties({ edit: false });
      this.mapsModelStore.set(map.identity, map);
      let mapGuid=Guid.parse(map.identity);
      this.cymbiotMap.getMapInfo(mapGuid);
      this.messageService.add({severity: 'success', summary: this.translateService.instant(ToastTypes.success), detail: this.translateService.instant('appMap.mapSaved')});
    }, () => {
      this.messageService.add({severity: 'error', summary: this.translateService.instant(ToastTypes.error), detail: this.translateService.instant('failedToSaveMap')});
      this.setStateProperties({ edit: true });
    });
    this.subscriptions.push(mapServiceUpdateSubscription);
  }

  private deleteMap(map: MapModel): void {
    this.confirmationService.confirm({
      message: this.translateService.instant('appMap.deleteConfirmationMessage'),
      header: this.translateService.instant('appMap.deleteConfirmationHeader'),
      icon: 'fa fa-trash',
      acceptLabel: this.translateService.instant('yes'),
      rejectLabel: this.translateService.instant('no'),
      accept: () => {
       let mapServiceDeleteSubscription= this.mapService.delete(map).subscribe(() => {
          let defaultMapdId = this.settingsService.get('defaultMap');
          if (defaultMapdId === map.identity) {
            this.setDefaultMap(null, 'appMap.deletedDefaultMap');
          }
          this.mapsModelStore.delete(map.identity);
          let redirectToMapId = this.returnAvailableMapId();
          this.navigationService.navigate(Pages.mapNew, { map: redirectToMapId });
        });
        this.subscriptions.push(mapServiceDeleteSubscription);
      }
    });
  }

  private setDefaultMap(mapId: string, messageString?): void {

    this.settingsService.set('defaultMap', mapId);

    let message = messageString || 'appMap.mapSavedAsDefault';

    let saveSettingsSubscription = this.settingsService.saveSettings().subscribe(() => {
      this.messageService.add({severity: 'success', summary: this.translateService.instant(ToastTypes.success), detail: this.translateService.instant(message)});
    });
    this.subscriptions.push(saveSettingsSubscription);
  }

  returnAvailableMapId(): string {
    let id: string = Object.keys(this.mapsModelStore).length > 0 ? this.mapsModelStore[Object.keys(this.mapsModelStore)[0]].identity : null;
    return id;
  }

  onMapExtentChanged(event: ExtentObj): void {
    if(!this.selectedMap.item){
      return;
    }
    this.selectedMap.item.mapLeft = event.minX || 0;
    this.selectedMap.item.mapTop = event.minY || 0;
    this.selectedMap.item.mapRight = event.maxX || 0;
    this.selectedMap.item.mapBottom = event.maxY || 0;
  }

  setSidebarActionsVisibilityProperties(actions: MapActionType[]): void {
    this.sidebarActions.map(sidebarAction => {
      sidebarAction.isVisible = actions.some(action => {return action.includes(sidebarAction.type);});
    });
  }

}
