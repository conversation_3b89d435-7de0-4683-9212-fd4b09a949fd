import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Child, OnInit } from '@angular/core';
import { MessageService } from 'primeng/api';
import { DossierHistoryComponent } from './dossier-history/dossier-history.component';
import { DossierDetailsComponent } from './dossier-details/dossier-details.component';
import { Dossier } from './models/dosier.model';
import { DossierPageRequest } from './models/dosierPageRequest.model';
import { DossierService } from '../../services/dossier/dossier.service';
import { TranslateService } from '@ngx-translate/core';
import { ToastTypes } from './../../shared/enum/toast-types';
import { Subscription } from 'rxjs';
import * as _ from "lodash";
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-dossier',
  templateUrl: './dossier.component.html',
  styleUrls: ['./dossier.component.scss']
})
export class DossierComponent implements OnInit, On<PERSON><PERSON>roy {
  @ViewChild('historyDialog') historyDialog!: DossierHistoryComponent;
  @ViewChild('detailsDialog') detailsDialog!: DossierDetailsComponent;
  
  dossiers: Dossier[] = [];
  loading = false;
  currentPageIndex = 0;
  pageSize = 12;
  totalRecords = 0;
  allowedExtensions: string[] = ['pdf','doc','docx','txt','tif','gif','jpeg','jpg','png','mp4','avi','mkv'];
  fileAcceptTypes: string = this.allowedExtensions.map(ext=>{return `.${ext}`;}).join(',');
  subscriptions: Subscription[] = [];
  routeEventId = '';
  currentFilter: DossierPageRequest;
  constructor(
    private dossierService: DossierService,
    private translateService: TranslateService,
    private messageService: MessageService,
    private activatedRoute: ActivatedRoute,
    private router: Router
  ) {
    this.activatedRoute.queryParams.subscribe((params) => {
      const eventId: string = params['eventId'];
      this.routeEventId = eventId;
    });
	
  }

  ngOnInit(): void {
    const request: DossierPageRequest = {
      PageIndex: this.currentPageIndex,
      PageSize: this.pageSize,
    };
    if(this.routeEventId) {
      request.EventId = this.routeEventId;
    }
    this.currentFilter = request;
    this.loadDossiers(request);
  }

  loadDossiers(request: DossierPageRequest): void {
    this.loading = true;
    this.dossierService.dossierRequest(request)
      .subscribe({
        next: (response) => {
          if (response.status?.isSuccess === false) {
            this.messageService.add({
              severity: 'error',
              summary: this.translateService.instant(ToastTypes.error),
              detail: response.status.message || this.translateService.instant('dossierHistory.failedToLoadDossiers')
            });
            this.loading = false;
            return;
          }

          this.dossiers = response.items;
          this.totalRecords = response.totalCount;
          this.loading = false;
        },
        error: () => {
          this.loading = false;
          this.messageService.add({
            severity: 'error',
            summary: this.translateService.instant(ToastTypes.error),
            detail: this.translateService.instant('dossierHistory.failedToLoadDossiers')
          });
        }
      });
  }

  onToggleAutoDelete(dossier: Dossier): void{
    this.loading = true;
    const dossierClone = _.cloneDeep(dossier);
    const dossierDeletableStatus =  dossierClone.isDeletable = !dossierClone.isDeletable;

    this.dossierService.dossierAutoDeleteToggle(dossierClone.id, dossierDeletableStatus)
      .subscribe({
        next: () => {
          dossier.isDeletable = !dossier.isDeletable;
          const dossierStatus = dossier.isDeletable ? 'dossier.statusOptions.enabled' : 'dossier.statusOptions.disabled';
          this.messageService.add({
            severity: 'success',
            summary: this.translateService.instant(ToastTypes.success),
            detail: this.translateService.instant('dossier.autoDelete', { status: this.translateService.instant(dossierStatus) })
          });
          this.dossiers = this.dossiers.map((item) =>
            item.id === dossierClone.id ? { ...dossier } : item
          );
          this.loading = false;
        },
        error: () => {
          this.loading = false;
          this.messageService.add({
            severity: 'error',
            summary: this.translateService.instant(ToastTypes.error),
            detail: this.translateService.instant('dossierHistory.failedToggleAutoDelete')
          });
        }
      });
  }

  onUploadDossier(dossier: Dossier): void {
    this.openFileExplorer(dossier);
  }

  openFileExplorer(dossier: Dossier): void {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = this.fileAcceptTypes;
    fileInput.style.display = 'none';
  
    fileInput.addEventListener('change',(event: Event)=>{
      this.onFileSelected(event, dossier);
    });
    document.body.appendChild(fileInput);
    fileInput.click();
    document.body.removeChild(fileInput);
  }

  onFileSelected(event: Event, dossier: Dossier): void {
    const inputElement = event.target as HTMLInputElement;

    if (!inputElement.files || inputElement.files.length === 0) {
      this.messageService.add({
        severity:'error',
        summary:this.translateService.instant(ToastTypes.error),
        detail:this.translateService.instant('dossierUpload.noFileSelected')
      });
      return;
    }
  
    const file: File = inputElement.files[0];
    const fileExtension = file.name.split('.').pop()?.toLowerCase();

    if (!fileExtension || !this.allowedExtensions.includes(fileExtension)) {
      this.messageService.add({
        severity:'error',
        summary:this.translateService.instant(ToastTypes.error),
        detail:this.translateService.instant('dossierUpload.invalidFileType',{ fileTypes:this.fileAcceptTypes })
      });
      return;
    }
    this.uploadDossierFile(file, dossier);
  }

  uploadDossierFile(file: File, dossier: Dossier): void {
    this.loading = true;
    this.subscriptions.push(
      this.dossierService.uploadDossier(dossier.eventId,file).subscribe({
        next:()=>{
          this.messageService.add({
            severity:'success',
            summary:this.translateService.instant(ToastTypes.success),
            detail:this.translateService.instant('dossierUpload.fileUploadSuccessfully')
          });
        },
        error:()=>{
          this.messageService.add({
            severity:'error',
            summary:this.translateService.instant(ToastTypes.error),
            detail:this.translateService.instant('dossierUpload.invalidFileType',{ fileTypes:this.fileAcceptTypes })
          });
          this.loading = false;
        },
        complete:()=>{
          this.loading = false;
        }
      }));
  }


  onDownloadDossier(dossier: Dossier): void {
    if (!dossier?.eventId) {
      this.messageService.add({
        severity: 'error',
        summary: this.translateService.instant(ToastTypes.error),
        detail: this.translateService.instant('dossierHistory.noEventIdDownload')
      });
      return;
    }

    this.loading = true;
    const dossierName = dossier.name?.replace(/\.zip$/i, '') || 'dossier';
    this.dossierService.dossierDetections(dossier.eventId)
      .subscribe({
        next: (blob: Blob) => {
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `${dossierName}.zip`;
          link.click();
          window.URL.revokeObjectURL(url);
          this.loading = false;
        },
        error: () => {
          this.messageService.add({
            severity: 'error',
            summary: this.translateService.instant(ToastTypes.error),
            detail: this.translateService.instant('dossierHistory.downloadError')
          });
          this.loading = false;
        }
      });
  }

  onViewHistory(dossier: Dossier): void {
    if (this.historyDialog) {
      this.historyDialog.dossier = dossier;
      this.historyDialog.show();
    }
  }

  onViewDetails(dossier: Dossier): void {
    if (this.detailsDialog) {
      this.dossierService.dossierDetails(dossier.eventId)
        .subscribe({
          next: (response) => {
            if (!response) {
              this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant(ToastTypes.error),
                detail: this.translateService.instant('dossierHistory.failedToLoadDossierDetails')
              });
              return;
            }

            if (response && response.length > 0) {
              this.detailsDialog.dossiers = response;
              this.detailsDialog.dossierName = dossier.name;
              this.detailsDialog.show();
            }
          },
          error: () => {
            this.messageService.add({
              severity: 'error',
              summary: this.translateService.instant(ToastTypes.error),
              detail: this.translateService.instant('dossierHistory.failedToLoadDossierDetails')
            });
          }
        });
    }
  }
  ngOnDestroy(): void {
    this.subscriptions.forEach((i) => i.unsubscribe());
    this.routeEventId = '';
  }

  onDossierFilter(request: DossierPageRequest): void {
    if(this.routeEventId) {
      this.routeEventId = '';
      this._removeEventId();
    }
    this.currentPageIndex = 0;
    this.currentFilter = { ...request, PageIndex: 0 };
    this.loadDossiers(this.currentFilter);
  }

  onPageChange(newPageIndex: number): void {
    this.currentPageIndex = newPageIndex;
    if (this.currentFilter) {
      this.currentFilter = { ...this.currentFilter, PageIndex: newPageIndex };
      this.loadDossiers(this.currentFilter);
    }
  }

  private _removeEventId(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { eventId: null },
      queryParamsHandling: 'merge'
    });
  }

  private formatFileSize(mb: number): string {
    return `${mb} MB`;
  }
}