import { Component, OnInit, Input, Output, EventEmitter, ViewChild } from '@angular/core';
import { EventsTabOptions } from '../../models/events-tab-options.interface';
import { EventsTabs } from '../../enums/events-tabs.enum';
import { GlobalAction } from 'app/shared/models/global-action.interface';
import { EventsActionData } from '../../models/events-action-data.interface';


@Component({
  selector: 'app-events-actions',
  templateUrl: './events-actions.component.html',
  styleUrls: ['./events-actions.component.scss']
})
export class EventsActionsComponent implements OnInit {
  @Input('viewOptions') viewOptions: EventsTabOptions;
  @Input('dropDownActions') dropDownActions: GlobalAction[] = [];
  @Output('selectView') selectView: EventEmitter<EventsTabs> = new EventEmitter();
  @Output('selectAction') selectAction: EventEmitter<EventsActionData> = new EventEmitter();
  @ViewChild('overlayActions', {static: false}) overlayActions;
  constructor() { }

  ngOnInit() {
  }
}
