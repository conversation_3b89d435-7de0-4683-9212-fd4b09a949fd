import { Component, Input, OnChanges, SimpleChanges, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { DataChangeNotification } from 'app/shared/modules/data-layer/models/data-change-notification';
import { Map as MapModel } from 'app/shared/modules/data-layer/models/map';
import { NavigationService, Pages } from 'app/shared/services/navigation.service';
import { OverlayPanel } from 'primeng/overlaypanel';

import { MapObject } from '../../models/map-object.interface';
import {MapService} from "app/services/map/map.service";
import {DefaultResources} from "app/shared/enum/enum";

@Component({
  selector: 'app-map-navigation',
  templateUrl: './map-navigation.component.html',
  styleUrls: ['./map-navigation.component.scss']
})
export class MapNavigationComponent implements OnChanges {
    @Input('mapsModelStore') mapsModelStore: Map<string, MapModel> = new Map<string, MapModel>();
  @Input('selectedMap') selectedMap: MapObject = {
    id: null,
    item: null
  };
  @ViewChild('mapsOp', {static: false}) mapsOp: OverlayPanel;
  objectKeys = Object.keys;
  defaultMapId:string =DefaultResources.DefaultMap;
  currentMap:string;

  constructor(
    private navigationService: NavigationService,
    private mapService: MapService,
    private route:ActivatedRoute
  ) {
    this.currentMap=this.route.snapshot.queryParams['map'];
    this.mapService.resourceChanged.subscribe((res:DataChangeNotification<MapModel>)=>{

      res.models.map(item=>{
          if (this.mapsModelStore) {
              this.mapsModelStore.set(item.identity, item);
          }

      })


    })
  }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.mapsModelStore && changes.mapsModelStore.currentValue != null) {
            let parsedConfig = JSON.parse(localStorage.getItem('conf'));
            this.defaultMapId = parsedConfig.defaultMap ? parsedConfig.defaultMap : DefaultResources.DefaultMap;

            this.mapsModelStore.forEach((value, key) => {
                if (value.identity === this.defaultMapId.slice(1, -1) || value.identity === this.currentMap) {
                    this.selectedMap = {
                        id: this.defaultMapId.slice(1, -1),
                        item: value
                    };
                }
            });
        }
    }

  startNavigationPanel(event: MouseEvent): void{
      if (this.mapsModelStore.size > 1) {
          this.mapsOp.show(event);
      }
  }

    selectItem(item: MapModel): void {
        this.navigateTo(item);
    }

    navigateTo(item: MapModel): void {
        this.navigationService.navigate(Pages.mapNew, {map: item.identity});
        this.selectedMap = {
            id: item.identity,
            item: item
        };
        this.mapsOp.hide();
    }

}
