.container {
    border-radius: 8px;
    background-color: #fafafa;
    display: flex;
    align-items: center;
    gap: 20px;
    justify-content: space-between;
    padding: 8px 21px;
}

@media (max-width: 991px) {
    .container {
        flex-wrap: wrap;
        padding-right: 20px;
    }
}

.header {
    display: flex;
    gap: 12px;
    font-weight: 400;
    align-self: stretch;
}

.profile-img {
    aspect-ratio: 1;
    object-fit: auto;
    object-position: center;

}

.user-details {
    display: flex;
    flex-direction: column;
    margin: auto 0;
}

.label {
    color: rgba(60, 173, 255, 0.75);
    font: 8px Roboto, sans-serif;
    font-weight: bold;
    font-size: 13px;
}

.value {
    color: #000;
    margin-top: 4px;
    font: 12px Roboto, sans-serif;
    text-align: center;
    white-space: break-spaces;
}

.details {
    display: flex;
    gap: 20px;
    font-weight: 400;
    justify-content: space-between;
    align-self: stretch;
    margin: auto 0;
}

@media (max-width: 991px) {
    .details {
        flex-wrap: wrap;
    }
}

.detail-item {
    display: flex;
    flex-direction: column;
    white-space: nowrap;
}

@media (max-width: 991px) {
    .detail-item {
        white-space: initial;
    }
}

.icon-set {
    display: flex;
    gap: 12px;
    align-self: stretch;
    margin: auto 0;
}

.icon {
    aspect-ratio: 1;

    object-position: center;
    width: 20px;
    border-radius: 4px;
}

.container{
    width: 100%;
}

.expanded-details {
    width: 100%;
    background-color: #fafafa;
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 10px;
    max-height: 350px;
    overflow: scroll;
}

.entityItem{
    display:flex;
    flex-direction: column;
}

.image-preview {
    position: absolute;
    top: 50px;
    left: 100px;
    z-index: 1000;
    border: 1px solid #ccc;
    background-color: white;
    padding: 10px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.large-image {
    width: 200px;
    height: auto;
}


table {
    width: 100%;
    border-collapse: collapse;
    margin: 10px 0;


}

th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
    color:#000;
    font-size:13px;
}

th {
    font-weight: bold;
    color: rgba(60, 173, 255, 0.75);
}

tr:nth-child(even) {
    background-color: #f9f9f9;
}

tr:hover {
    background-color: #ddd;
}
.image{
    width: 80px;
    height: 80px;
    border-radius: 10px;
}
.container{
    position: relative;
}


.image-preview {
    position: fixed;
    z-index: 1000;
    border: 1px solid #ccc;
    background-color: white;
    padding: 10px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.large-image {
    width: 200px;
    height: auto;
}
.entity{
    margin-top:20px;
}
