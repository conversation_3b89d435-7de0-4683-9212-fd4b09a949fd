import { Injectable } from "@angular/core";
import { DataService } from "app/shared/services/data.service";
import { StringService } from "app/shared/services/string.service";
import { Observable } from "rxjs";

import { DashboardItem } from "../models/dashboard-item.interface";
import { DashboardTreeItem } from "../models/dashboard-tree-item.interface";

import { HttpClient } from "@angular/common/http";
import { apiMap } from "app/shared/services/api.map";
import { environment } from "environments/environment";


@Injectable({
  providedIn: "root"
})
export class DashboardDataService {
  constructor(
    private dataService: DataService,
    private stringService: StringService,
    private httpClient: HttpClient,
  ) { }

  public addUpdateDashboard(
    id: string,
    name: string,
    template: string,
    parentid: string
  ): Observable<any> {
    let data = {
      id: id,
      name: name,
      template: template,
      parentidentity: parentid
    };
    return this.httpClient.post(environment.apiUrl + apiMap.AddUpdateDashboard.url,data);

  }

  public deleteDashboard(id: string): Observable<any> {
   
    return this.httpClient.post(environment.apiUrl + apiMap.DeleteDashboard.url + id, null);
  }

  public getDashboard(id: string): Observable<DashboardItem> {

    return Observable.create(observer => {
      this.httpClient.get(environment.apiUrl+ apiMap.GetDashboard + id).subscribe(
        (res:any) => {
          res.Data = this.stringService.decodeBase64(res.Data);
          let dashboard: DashboardItem = res;
          observer.next(dashboard);
          observer.complete();
        },
        error => {
          observer.error();
          observer.complete();
        }
      );
     
      
    });
  }

  public getDashboardTree(): Observable<DashboardTreeItem[]> {
    return Observable.create(observer => {
      this.httpClient.get(environment.apiUrl+ apiMap.getDashboardsTree) .subscribe((res: DashboardTreeItem[]) => {
        // first response of api is null (don't know why).
        if (res) {
          observer.next(res);
        }
      });
    
    });
  }

  //TODO figure out searchtemplates type
  public getSearchTemplates(): Observable<any> {
    return Observable.create(observer => {
      this.httpClient.get(environment.apiUrl+ apiMap.getSearchTemplates).subscribe(res => {
        if (res) {
          observer.next(res)
        }
      });
     
    });
  }
}
