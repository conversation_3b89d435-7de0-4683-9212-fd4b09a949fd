import { WidgetType} from "./enums/widget-type.enum";
import { WidgetSize } from "./enums/widget-size.enum";

export const availableWidgetTypesData = [

    {
        type: WidgetType.map,
        size: WidgetSize.big,
        componentName: "MapWidget",
        hasTitle: true,
        hasInfoWrapper: true
    },
    {
        type: WidgetType.player,
        size: WidgetSize.big,
        componentName: "PlayerWidget",
        hasTitle: true,
        hasInfoWrapper: true
    },
    {
        type: WidgetType.dashbox,
        size: WidgetSize.small,
        componentName: "DashboxWidget",
        hasTitle: true,
        hasInfoWrapper: false
    },
    {
        type: WidgetType.gauge,
        size: WidgetSize.xSmall,
        componentName: "GaugeWidget",
        hasTitle: true,
        hasInfoWrapper: true
    },
    {
        type: WidgetType.notification,
        size: WidgetSize.big,
        componentName: "NotificationWidget",
        hasTitle: true,
        hasInfoWrapper: true,
    },
    {
        type: WidgetType.pieChart,
        size: WidgetSize.big,
        componentName: "PieChartWidget",
        hasTitle: true,
        hasInfoWrapper: true
    },
    {
        type: WidgetType.chart,
        size: WidgetSize.wide,
        componentName: "ChartWidget",
        hasTitle: true,
        hasInfoWrapper: true
    },
    {
        type: WidgetType.sensorStatus,
        size: WidgetSize.big,
        componentName: "SensorStatusWidget",
        hasTitle: true,
        hasInfoWrapper: true
    },
    {
        type: WidgetType.emptySpace,
        size: WidgetSize.small,
        componentName: "EmptySpaceWidget",
        hasTitle: true,
        hideActionsMenu: true,
        hasInfoWrapper: false
    },
    {
        type: WidgetType.embeddedFile,
        size: WidgetSize.xXSmall,
        componentName: "EmbeddedFileWidget",
        hasTitle: true,
        hasInfoWrapper: false
    },
    {
        type: WidgetType.urlShortcut,
        size: WidgetSize.xXSmall,
        componentName: "UrlShortcutWidget",
        hasTitle: true,
        hasInfoWrapper: false
    }
];