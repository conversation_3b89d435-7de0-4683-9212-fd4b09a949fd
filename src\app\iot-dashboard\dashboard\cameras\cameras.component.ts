import { Component, OnInit, ViewChild, After<PERSON>iew<PERSON>nit, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { PlayerComponent } from 'app/shared/components/player/player.component';
import { ResourceCacheService } from 'app/shared/modules/data-layer/services/resource/resource.cache.service';
import { ServerTypes } from 'app/shared/modules/data-layer/enum/resource/resource-server-types.enum';
import { Resource } from 'app/shared/modules/data-layer/models/resource';
import { Subscription } from 'rxjs';
import { PlayersService } from 'app/shared/services/players.service';
import { Guid } from 'app/shared/enum/guid';

@Component({
    selector: 'app-cameras',
    templateUrl: './cameras.component.html',
    styleUrls: ['./cameras.component.scss']
})
export class CamerasComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild('player') player: PlayerComponent;
    
    channels: Resource[] = [];
    private subscriptions: Subscription[] = [];
    private currentChannelIndex: number = -1;
    
    constructor(
        private resourceCacheService: ResourceCacheService,
        private playersService: PlayersService
    ) {
       
    }

    ngOnInit() {
        this.loadChannels();
    }

    ngAfterViewInit() {
        if (this.player && this.channels.length > 0) {
            this.openDefaultChannel();
        }
    }

    ngOnDestroy() {
        this.subscriptions.forEach(sub => sub.unsubscribe());
    }

    private loadChannels():void {
        // Subscribe to initial cache load
        const sub = this.resourceCacheService.storageCompleted.subscribe(() => {
            const resources = this.resourceCacheService.getAll();
            this.channels = resources.filter(resource => 
                resource.resourceType === ServerTypes.Core_RES_InputChannel
            );
            if (this.player && this.channels.length > 0) {
                this.openDefaultChannel();
            }
        });
        this.subscriptions.push(sub);
    }

    private openDefaultChannel():void {
        if (this.channels.length > 0) {
            this.currentChannelIndex = 0;
            this.playChannel(this.currentChannelIndex);
        }
    }

    playChannel(index: number):void {
        if (index >= 0 && index < this.channels.length) {
            const channel = this.channels[index];
            if (channel) {
                this.player.openChannel(channel.identity, false, this.player.playerId);
                this.currentChannelIndex = index;
            }
        }
    }

    playNextChannel():void {
        if (this.channels.length === 0) return;
        this.currentChannelIndex = (this.currentChannelIndex + 1) % this.channels.length;
        this.playChannel(this.currentChannelIndex);
    }

    playPreviousChannel():void {
        if (this.channels.length === 0) return;
        this.currentChannelIndex = this.currentChannelIndex - 1;
        if (this.currentChannelIndex < 0) {
            this.currentChannelIndex = this.channels.length - 1;
        }
        this.playChannel(this.currentChannelIndex);
    }

    onCloseChannel(channelId: string): void {
      
    }

    onOpenChannel(channelId: string): void {
        const channelIndex = this.channels.findIndex(channel => channel.identity === channelId);
        if (channelIndex !== -1) {
            this.currentChannelIndex = channelIndex;
        }
    }
}
