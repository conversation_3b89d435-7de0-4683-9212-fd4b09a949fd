import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges, TemplateRef, ViewChild } from '@angular/core';
import { TableColumnProperties } from 'app/shared/components/ng-turbo-table/models/table.models';
import { Entry } from 'app/shared/models/entry';
import { MarkerData } from 'app/shared/models/marker-data.interface';
import { ReportDataChunkPayload } from 'app/shared/modules/data-layer/models/report-data-chunk-payload';
import { EventService, EventType } from 'app/shared/services/event.service';
import { ReportsTurboTableService } from 'app/shared/services/reports-turbo-table.service';
import * as moment from 'moment';
import { ReportResultMenuActions } from '../enum/report-result-menu-actions.enum';
import { environment } from 'environments/environment';

import { DataPropertiesComponent } from 'app/shared/modules/cymbiot-map/components/data-properties/data-properties.component';
@Component({
  selector: 'app-report-results',
  templateUrl: './report-results.component.html',
  styleUrls: ['./report-results.component.scss']
})
export class ReportResultsComponent implements OnChanges {
  @Input("reportDataChunkPayload") reportDataChunkPayload: ReportDataChunkPayload;
  @Input("reportQueryId") reportQueryId: number = null;
  @Input("filterQueryparams") filterQueryparams: string;
  @Input("resultsLength") resultsLength: number = 0;
  @ViewChild('ioLightInfo', {static: true}) ioLightInfo: TemplateRef<DataPropertiesComponent>;
  tableColumns: TableColumnProperties[] = [];
  tableSearchFields = [];
  tableData = [];
  mapData:MarkerData[] = [];
  chartData = [];
  // Removed tabs and menu actions
  @Output('filterEvent') filterEvent: EventEmitter<boolean> = new EventEmitter();
  tableExportInfo: { title: string, fileName: string };
  storeObservable;
  rowsCount:number = 0;
  STREAM_ID="Stream_Id";
  filterValues=[];
  // Removed report menu actions
  constructor(
    private eventService: EventService,
    private reportsTurboTableService: ReportsTurboTableService
  ) { }

  ngOnChanges(changes: SimpleChanges): void {
    if(changes.filterQueryparams && changes.filterQueryparams.currentValue){

      this.tableExportInfo = { title: "", fileName: changes.filterQueryparams.currentValue.substring(0, 250) };
    }
    if(changes.reportDataChunkPayload && changes.reportDataChunkPayload.currentValue){



      let tableDataBuilded = this.reportsTurboTableService.buildTableData(changes.reportDataChunkPayload.currentValue.newTableData);
      let Obj;
      let columnHeaderelements=[];

      tableDataBuilded.forEach((elementData,tableIndex)=>{
        if(elementData.time_stamp_formatted){
          let date=this.formatDateTimeStringToDateTimeObject(elementData.time_stamp_formatted);
          elementData.time_stamp_formatted=date;
        }


        if(elementData.field_value) {
          try {
            let  parsedString;
            if(typeof elementData.field_value === "string"){
              parsedString = elementData.field_value;
            }else{
              parsedString=JSON.parse(elementData.field_value);
            }


          if(typeof parsedString == "object"){
            Obj=JSON.parse(elementData.field_value.toString());

            if(!columnHeaderelements.includes(this.STREAM_ID))
            {
              columnHeaderelements.push(this.STREAM_ID);
            }
            tableDataBuilded[tableIndex][this.STREAM_ID]=parsedString.StreamId;
            if(Obj.Stats){
              Obj.Stats.forEach((element,index)=>{
                if(!columnHeaderelements.includes(element.Name)){
                  columnHeaderelements.push(element.Name);

                  if(tableDataBuilded[tableIndex]){
                    tableDataBuilded[tableIndex][element.Name]=element.Value;

                  }

                }else{
                  tableDataBuilded[tableIndex][element.Name]=element.Value;
                }

                tableDataBuilded[tableIndex]["field_value"]="";
              });
            }

            if(Obj.Detections ){
              Obj.Detections.map((item,parseindeindex)=>{
              let keys=Object.keys(Obj.Detections[0]);
              let newObjToPush={};

              keys.forEach((key,index)=>{
                if(!columnHeaderelements.includes(key)){
                  columnHeaderelements.push(key);
                }
                newObjToPush[key] = item[key] != null ? item[key] : '';
              })
              tableDataBuilded.push(newObjToPush);
            });
            tableDataBuilded[tableIndex]["field_value"]="";
            }
          }
          } catch (error) {

          }
        }
      });
      columnHeaderelements.forEach((element ,index)=>{
        changes.reportDataChunkPayload.currentValue.tableColumns.push({fieldName: element, title: element, contentType: 2})
      });




      this.tableData = [...this.tableData,
         ...tableDataBuilded];
      tableDataBuilded.map((item)=>{
        const found = this.filterValues.some(el => el.value === item.field_value);
        if (!found) {
          this.filterValues.push({ label: item.field_value, value: item.field_value });
        }
      })

         this.tableColumns = changes.reportDataChunkPayload.currentValue.tableColumns.length > 0 ? this.reportsTurboTableService.buildTableColumns(changes.reportDataChunkPayload.currentValue.tableColumns,this.filterValues) : this.tableColumns;
      this.rowsCount=this.tableData.length+1;
      let mapAndChartData = this.createMapChartData(changes.reportDataChunkPayload.currentValue.newTableData);
      this.mapData = [...this.mapData, ...mapAndChartData.mapData];
      this.chartData = [...this.chartData, ...mapAndChartData.chartData];
    }
    if(changes.reportQueryId && changes.reportQueryId.currentValue){


      this.tableColumns = [];
      this.tableData = [];
      this.mapData = [];
      this.chartData = [];
    }

  }


  createMapChartData(rawData: {[key: string] : string}[]): {mapData: MarkerData[], chartData: {[key: string] : string}[]} {
    let data: {mapData: MarkerData[], chartData: {[key: string] : string}[]} = {
      mapData: [],
      chartData: []
    };
    rawData.forEach((element, index) => {
      //Remove prefix of object keys
      let elem1 = Object.keys(element);
      if (elem1[0].indexOf("EventFiredTriggers") !== -1) {
        let obj = {};
        elem1.forEach(key => {
          let newKey = key.substr(key.indexOf("_") + 1);
          obj[newKey] = element[key];
        });
        element = obj;
      }
      if (element.latitude && element.longitude &&
        element.latitude !== "" && element.longitude !== "") {

          let markerData:MarkerData = {
            location: {
              lat: parseInt(element.latitude),
              lng: parseInt(element.longitude)
            },
            entries: Object.keys(element).map(key => {
              return new Entry(key, element[key]);
            }),
            identity: index.toString(),
            name: element.resource_name
          };
          data.mapData.push(markerData);
      }
      data.chartData.push(element);
    });
    return data;
  }

  exportToCSV(): void {
    // Use the direct method from the service instead of emitting an event
    this.reportsTurboTableService.exportToCSV(this.tableData, this.tableExportInfo?.fileName || 'reportExport');
  }



  public exportToPDF():void {
    this.reportsTurboTableService.exportToPDF(this.tableData);
  }
  //format datetime string to date object
  private formatDateTimeStringToDateTimeObject(dateTimeString: string): string {
    let x = moment.utc(dateTimeString, environment.timeFormats.datetime).toDate().toISOString();

    return x;
  }
}
