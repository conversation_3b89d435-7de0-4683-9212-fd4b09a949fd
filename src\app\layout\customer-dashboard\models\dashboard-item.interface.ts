import { DashboardWidget } from "./dashboard-widget.interface";
import { DashboardSettings } from "./dashboard-settings.interface";
import { DashboardLabel } from "./dashboard-label.interface";
import { Guid } from "app/shared/enum/guid";
import { Widget } from "./widget";

export interface DashboardItem {
    Data?: {
        widgets: DashboardWidget[];
        settings?: DashboardSettings; 
    };
    Identity: string;
    Name: string;
    ParentIdentity: string;
    labels?: DashboardLabel;
    DataSource?: any;
    SystemIdentity?: string;
}

export class Dashboard implements DashboardItem {
    Data?: {
        widgets: DashboardWidget[];
        settings?: DashboardSettings; 
    };
    Identity: string;
    Name: string;
    ParentIdentity: string;
    labels?: DashboardLabel;
    DataSource?: any;
    SystemIdentity?: string;
    widgets: Widget[];
    isNewDashboard: boolean;
    
    constructor(obj?: DashboardItem){
        this.Identity = Guid.create().toString();
        this.Name = '';
        this.ParentIdentity ='';
        this.Data = {
            settings: {
                isNewDashboard: true
            },
            widgets: []
        };
        Object.assign(this, obj);
    }
}