@import '~styles/colors';
:host {
    .flexbox {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        h2 {
            margin-bottom: 0;
        }
        input {
            margin-left: 15px;
        }
        .validator-input {
            max-width: 35%;
        }
        .input-error {
            right: 0;
            left: auto;
        }
    }
    .thrColor {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        margin-left: 10px;
    }
}

::ng-deep {
    .ui-colorpicker-preview {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border: 1px solid $widgetDefault;
    }
}

.default, .blue, .green, .red, .yellow , .white{
    display: inline-block;
    height: 24px;
    width: 24px;
    border-radius: 50%;
    margin-right: 5px;
    cursor: pointer;
}

.default {
    background-color: $white;
    border: 1px solid $widgetDefault;
}
.white {
    background-color: $widgetDefault;      
}
.blue {
    background-color: $widgetBlue;
}
.green {
    background-color: $widgetGreen;
}
.red {
    background-color: $widgetRed;
}
.yellow {
    background-color: $widgetYellow;
}