.dashboard-rectangle{
    width: 100%;
}

.cameras-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    gap: 16px;
    height: 300px;

    .player-container {
        position: relative;
        width: 100%;
        height: 100%;
        min-height: 0;
        background: #f2f6f3;
        border-radius: 8px;
        overflow: hidden;

        app-player {
            width: 100%;
            height: 100%;
            display: block;
        }

        .nav-button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: transparent;
            border: none;
            color: transparent;
            width: 40px;
            height: 80px;
           
            cursor: pointer;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s;

            &:hover {
                background: transparent
            }

            &.nav-button-left {
                left: 20px;
            }

            &.nav-button-right {
                right: 20px;
            }
        }
    }

    .controls-container {
        height: auto;
        background: var(--surface-card);
        border-radius: 8px;
        padding: 16px;
    }

    .channels-indicator {
        display: flex;
        justify-content: center;
        margin-top: 20px;
        
        .channels-dots {
            display: flex;
            gap: 10px;
            align-items: center;
            
            .channel-dot {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                background-color: rgba(255, 255, 255, 0.3);
                cursor: pointer;
                transition: all 0.3s ease;
                
                &.active {
                    background-color: var(--primary-color, #33cd32);
                    transform: scale(1.2);
                }
                
                &:hover {
                    background-color: rgba(255, 255, 255, 0.5);
                }
            }
        }
    }
}
