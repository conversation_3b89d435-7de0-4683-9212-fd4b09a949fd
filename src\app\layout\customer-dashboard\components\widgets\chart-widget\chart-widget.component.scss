:host {
    .line-chart-wrapper {
        position: relative;
        padding: 10px;

    }

    .data-time-options {
        position: absolute;
        right: 35px;
        top: 5px;
        z-index: 999999;

    }

    .total-count {
        position: absolute;
        top: 12px;
        left: 35%;
        right: 46%;
        display: flex;
        justify-content: flex-end;
    }

    .line-chart-legend {
        max-height: 20%;
        min-height: 20%;
        overflow-y: scroll;

        ::ng-deep {
            ul {
                list-style-type: none;
                padding: 0;
                display: flex;
                flex-wrap: wrap;

                li {
                    margin-right: 5px;
                    cursor: pointer;

                    span {
                        display: inline-block;
                        width: 15px;
                        height: 10px;
                        margin-right: 5px;
                        background-color: var(--primary-1);
                    }

                    &.disabled {
                        text-decoration: line-through;
                    }
                }

            }
        }
    }

    canvas {
        max-height: 80%;
    }

    .progress-bar-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--secondary-highlight-3);
    }
}

:host-context(.rtl) {
    .data-time-options {
        left: 35px;
        right: unset;
    }

    .total-count {
        right: 50%;
        left: 50%;
    }
}

::ng-deep .ui-dialog {
    max-width: 90%;
}

$breakpoint-tablet: 320px;

@media (max-width: 720px) {
    .total-count {
        right: 61% !important;
        left: 35% !important;
    }
}
