<div class="map-group-actions-wrapper">
  <div class="title-wrapper">
    <h2>{{ 'appMap.groupActions' | translate}}</h2>
  </div>
  <div class="form-group mb-4">
    <input class="form-control" type="text" [(ngModel)]="searchText" placeholder="{{'search...' | translate}}">
  </div>

  <ul class="elements">
    <li *ngFor="let element of resourceGroups | filter: searchText; let index = index">
      <span class="element-title">{{element?.name}}</span>
      <span class="actions">
          <div class="btn-group-sm">
            <ng-container *ngFor="let action of element.actions">
              <button class="btn edit" (click)="executeAction(action.type, element.identity, element.name, index)"
                title="{{ action.name | translate}}">
                <i class="{{action.iconClass}}" aria-hidden="true"></i>
              </button>
            </ng-container>
          </div>  
      </span>
      <ng-container #dynamicAction>
         
      </ng-container>
    </li>
  </ul>
</div>