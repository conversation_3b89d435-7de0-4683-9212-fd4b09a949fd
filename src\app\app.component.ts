import { environment } from './../environments/environment';
import { Component } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ToastTypes } from './shared/enum/toast-types';
import { ResourcePort } from "app/shared/modules/data-layer/services/resource/resource-port";
import { ResourceCacheService } from "app/shared/modules/data-layer/services/resource/resource.cache.service";
import { AuthService } from "app/shared/services/auth.service";
import { UrlNavigation } from "app/shared/modules/data-layer/models/navigation/url-navigation";
import { Router } from "@angular/router";
import { forkJoin } from "rxjs";
import { FactorsModel } from "app/shared/models/factors.model";
import { LumenFactorService } from "app/shared/services/lumen-factor.service";
import { TrafficFactorService } from "app/shared/services/traffic-factor.service";
import { WeatherFactorService } from "app/shared/services/weather-factor.service";
import { FactorCompensationService } from "app/shared/services/factor-compensation.service";
import { SettingsService } from 'app/shared/services/settings.service';

@Component({
    selector: 'app-root',
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.scss']
})
export class AppComponent {
    toastTypes = ToastTypes;
    constructor(translate: TranslateService, 
        resourcePort: ResourcePort, 
        resourceCacheService: ResourceCacheService,
        authService: AuthService,
        router:Router, 
        private lumenFactorService: LumenFactorService,
        private trafficFactorService: TrafficFactorService,
        private weatherFactorService: WeatherFactorService,
        private factorCompensationService: FactorCompensationService,
        private settingsService: SettingsService) {
                
        const lang= this.settingsService.get(SettingsService.Language);
        let availableLangs = ['en', 'fr', 'ur', 'he', 'ro', 'es'];
        let defaultLang = availableLangs.some(item => item === lang?.l) ? lang?.l : 'en';
        translate.addLangs(availableLangs);
        translate.setDefaultLang(defaultLang);
        if (defaultLang == "he")
        {
            document.body.classList.add('rtl');
        }

        authService.afterRenew.subscribe((urlNavigation: UrlNavigation) => {

            forkJoin({resources: resourcePort.getAll(), lumenFactor: this.lumenFactorService.getLumenFactor(),
                trafficFactor: this.trafficFactorService.getTrafficFactor(),
                weatherFactor: this.weatherFactorService.getWeatherFactor()})
                .subscribe({
                    next: ({resources, lumenFactor, trafficFactor, weatherFactor }) => {

                        resourceCacheService.put(resources);
                        resourceCacheService.setStorageComplete();

                        const factors: FactorsModel = {
                            lumen: lumenFactor,
                            traffic: trafficFactor,
                            weather: weatherFactor
                        };
                        this.factorCompensationService.factorData = factors;

                        router.navigateByUrl(urlNavigation.Url, urlNavigation.Extras);
                    },
                    error: (error) => {
                        console.error('Error fetching resources and factors', error);
                    }
                });
        });

    }

    public onContextMenu($event: MouseEvent): boolean {
        if (environment.production) {
            return false;
        }
        return true;
    }
}