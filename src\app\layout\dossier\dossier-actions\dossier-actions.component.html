<div class="action-buttons">
    <button pButton 
        icon="fa fa-search" 
        class="p-button-rounded p-button-info p-button-text"
        pTooltip="View Detections"
        (click)="navigateToDetections()"
        [disabled]="loading || !dossier?.eventId">
    </button>
    
    <button pButton 
        icon="pi pi-download" 
        class="p-button-rounded p-button-info p-button-text"
        pTooltip="Download Dossier"
        (click)="downloadDossier()"
        [disabled]="loading || !dossier?.eventId">
    </button>
    
    <button pButton 
        icon="pi pi-history" 
        class="p-button-rounded p-button-warning p-button-text"
        pTooltip="View History"
        (click)="viewHistory()"
        [disabled]="loading">
    </button>

    <button pButton 
        *ngIf="dossier?.isDeletable"
        icon="pi pi-trash" 
        class="p-button-rounded p-button-danger p-button-text"
        pTooltip="Delete Dossier"
        (click)="deleteDossier()"
        [disabled]="loading">
    </button>
</div>

<app-dossier-history
    #historyDialog
    [dossierId]="dossier?.id">
</app-dossier-history> 