<div class="total-count">
  <label> {{ 'resultsAmount' | translate }} {{ filteredResultCount }} </label>
</div>
<div class="data-time-options" >
  <p-dropdown [options]="displayedDataTimeOptions" [(ngModel)]="data.widgetData.displayedDataTimeSettings"
     styleClass="p-tiny" (onChange)="onWidgetTimeOptionChange()"
    [disabled]="lineChartDataSets && lineChartDataSets.length === 0">
    <ng-template let-item pTemplate="selectedItem">
      <span>{{item.label| translate}}</span>
    </ng-template>
    <ng-template let-item pTemplate="item">
      <div class="ui-helper-clearfix">
        <div>{{item.label | translate}}</div>
      </div>
    </ng-template>
  </p-dropdown>
</div>
<div class="line-chart-wrapper">
  <div class="progress-bar-wrapper" *ngIf="reportRequestInProgress">
    <p-progressBar mode="indeterminate"></p-progressBar>
  </div>

  <!-- <canvas baseChart #chart [data]="datax" [options]="lineChartOptions" [type]="lineChartType">
  </canvas> -->

  <canvas baseChart [data]="chartData" [options]="lineChartOptions" [type]="'line'">
  </canvas>
</div>

<app-modal #turboTableModal [title]="'reportResults'">
  <ng-container ngProjectAs="contentModal">
    <ng-turbo-table [data]="turboTableData.tableData" [columns]="turboTableData.tableColumns"
      [showResultsCountTop]="true" [showToggleColumns]="true" [numOfRows]="10" [enableRowClick]="false"
      [scrollHeight]="'600px'" [showRowIndex]="true">
    </ng-turbo-table>
  </ng-container>
</app-modal>