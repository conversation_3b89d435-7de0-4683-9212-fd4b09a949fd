$red: #FF0000;
$green: #00D600;
$textColor: #808381;

.notification-tab {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

:host {
  display: block;
  height: 100% !important;
  overflow: hidden;
  box-sizing: border-box;
}

:host ::ng-deep .notification-tab .p-tabview-panels {
  flex: 1;
  overflow: hidden;
}

.dashboard-rectangle-16 {
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Tab styling
:host ::ng-deep .notification-tab .p-tabview-nav {
  display: flex;
  background: #D9D9D9;
  border-radius: 10px;
  padding: 4px;
}

:host ::ng-deep .notification-tab .p-tabview-nav li {
  flex: 1;
  margin: 0 4px;
  list-style: none;
}

:host ::ng-deep .notification-tab .p-tabview-nav li a {
  &:hover, &:focus {
    text-decoration: none;
    span {
      color: $green;
    }

    &:focus {
      box-shadow: inset 0 0 0 0.2rem rgba(0, 214, 0, 0.5) !important;
    }
  }
  span {
    font-size: 16px;
  }
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.7rem 1rem;
  color: #1E1B39;
  font-weight: 700;
  background: transparent;
  border: 0px;
  border-radius: 10px;
}

:host ::ng-deep .notification-tab .p-tabview-nav li.p-highlight a {
  background: #ffffff;
  color: $green;
}

:host ::ng-deep .notification-tab .p-tabview-panel {
  padding: 0;
}

:host ::ng-deep .p-tabview .p-tabview-panels {
  background: transparent;
  padding: 10px 0px 0px;
}

.list-header {
  display: flex;
  background: #ffffff;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom: 1px solid #e0e0e0;
  padding: 1rem;
  font-weight: 700;
  color: #667085;
  font-size: 0.875rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.12);
}

.header-item {
  flex: 1;
}

.scrollable-container {
  height: 360px;
  overflow-y: auto;
  background: #ffffff;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.12);
  position: relative;
}

.notification-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.notification-item {
  border-bottom: 1px solid #EAECF0;
  cursor: pointer;

  &:nth-child(even) {
    background: #F9FAFB;
  }

  &:hover {
    background: #f0f7ff !important;
  }
}

.item-content {
  display: flex;
  padding: 0.875rem 1rem;
}

.item-cell {
  flex: 1;
  font-size: 0.875rem;
  color: #667085;
  padding: 0px 5px;
  align-content: center;
}

.entity-id {
  display: block;
  width: 100px;
  border: 1px solid #667085;
  padding: 5px 10px;
  border-radius: 8px;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.image-inline {
  display: inline-flex;
  align-items: center;

  img {
    padding-right: 6px;
  }
}

.no-data {
  padding: 2rem;
  text-align: center;
  color: #667085;
  font-size: 0.875rem;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;

  p {
    margin-top: 20px;
    color: #667085;
  }
}


.loading-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

/* Bottom loading indicator that appears at the bottom of the scrollable area */
.bottom-loading-indicator {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
  z-index: 5;
  border-top: 1px solid #e0e0e0;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid $green;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

