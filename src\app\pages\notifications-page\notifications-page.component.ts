import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { NotificationsService } from '../../shared/components/header/notifications/notifications.service';
import { INotificationData } from '../../shared/components/header/notifications/INotificationData';
import { Subscription } from 'rxjs';
import { LazyLoadEvent, MessageService } from 'primeng/api';
import { TranslateService } from '@ngx-translate/core';
import { AppNotificationService } from '../../services/app-notification.service';
import { AuthService } from '../../shared/services/auth.service';
import { ToastTypes } from '../../shared/enum/toast-types';
import moment from 'moment';
import { NotificationJsonData } from '../../shared/models/notifications/notification-json-data';
import { NotificationExtradata } from '../../shared/models/notifications/notification-extradata';
import { ActivatedRoute, Router } from '@angular/router';
import { NavigationService, Pages } from 'app/shared/services/navigation.service';
import { TriggerTypes } from 'app/shared/enum/trigger-types';


@Component({
  selector: 'app-notifications-page',
  templateUrl: './notifications-page.component.html',
  styleUrls: ['./notifications-page.component.scss']
})
export class NotificationsPageComponent implements OnInit, OnDestroy {
  notifications: INotificationData[] = [];
  selectedNotifications: INotificationData[] = [];
  private subscriptions: Subscription[] = [];
  animateNotification: { [key: string]: boolean } = {};
  pageIndex: number = 0;
  pageSize: number = 10;
  first: number;
  last: number;
  totalRecords: number;
  totalRecordsFormat:string = this.translateService.instant("notificationsPage.totalRecordsFormat");
  loading = false;
  constructor(
    private notificationService: NotificationsService,
    private translateService: TranslateService,
    private messageService: MessageService,
    private router: Router,
    private route: ActivatedRoute,
    private navigationService: NavigationService
  ) {}

  ngOnInit(): void {
    this.loadNotifications();
    const newNotificationSub = this.notificationService.newNotification.subscribe(notification => {
      this.prepareNotification(notification);
      this.notifications.unshift(notification);
    }, () => {
      this.loading = false;
    });
    this.subscriptions.push(newNotificationSub);
  }

  loadNotifications(): void {
    this.loading = true;
    const subscription = this.notificationService.getNotificationsPage(this.pageIndex, this.pageSize, [])
    .subscribe(notificationsPage => {      
      this.notifications = notificationsPage.Items;
      this.loading = false;
      this.first = this.pageIndex * this.pageSize;
      this.last = this.pageIndex * (this.pageSize + 1);
      this.totalRecords = notificationsPage.TotalCount;
    }, () => {
      this.loading = false;
    });
    this.subscriptions.push(subscription);
  }

  private prepareNotification(notificationData : INotificationData): void
  {
    if (notificationData.EventData && notificationData.EventData.length > 0)
    {
      let jsonData = notificationData.EventData[0].JsonData;
      
      if (jsonData && (!notificationData.Value || notificationData.Value < 0))
      {
        let notificationJsonData: NotificationJsonData = JSON.parse(jsonData);
        
        notificationData.ForeignId = notificationJsonData.foreignId;
        
        if (!notificationData.Labels) {
          notificationData.Labels = [];
        }

        if (notificationData.Types && notificationData.Types.length > 0) {
          notificationData.Types.forEach(item => {
            const enumKey = this.getEnumKeyByValue(TriggerTypes, item);
            if (enumKey) {
              notificationData.Labels.push(TriggerTypes[enumKey]);
            }
          });
        }

        if (notificationJsonData.description)
        {
          notificationData.ValueAsString = notificationJsonData.description.entity_id;

          if (notificationJsonData.description.extra_data)
          {
            notificationData.IsVehicle = (notificationJsonData.description.extra_data.indexOf("vehicle_type") >= 0);

            let notificationExtradata: NotificationExtradata = JSON.parse(notificationJsonData.description.extra_data);
          
            if ( notificationExtradata.vehicle_type)
            {
              if (notificationExtradata.vehicle_type && notificationExtradata.vehicle_type.toLocaleLowerCase() !=="undefined")
              {
                let translatedLabel = this.translateService.instant(notificationExtradata.vehicle_type.toLowerCase());
                notificationData.Labels.push(translatedLabel);
              }
            }
          }
        }
      }
    }
  }

   getEnumKeyByValue<T>(enumObject: T, value: number): string | undefined {
    return Object.keys(enumObject).find(
        key => (enumObject as any)[key] === value
    );
  }

  jumpToMap(notificationData: INotificationData): void
  {
    let plateValue : string = notificationData.ValueAsString;
    let itemId : string = notificationData.ForeignId;

    this.navigationService.navigate(Pages.vehicleTraffic,  { plateNumber: plateValue, itemId: itemId,
      search: plateValue, pageIndex: this.pageIndex }); 

  }

  jumpTpMap(notificationData: INotificationData): void
  {
    let plateValue : string = notificationData.ValueAsString;
    let itemId : string = notificationData.ForeignId;

    this.router.navigate(['vehicleTraffic'],
      {relativeTo:this.route, queryParams: { 'plateNumber': plateValue, 'itemId': itemId,
      'search': plateValue, 'pageIndex': this.pageIndex }});
  }

  formatMoment(value: Date): string{
    return moment(value).format("DD/MM/YYYY HH:mm:ss.SSS");
  }

  pageIndexChanged(event: LazyLoadEvent) {

      if (!event.first)
      {
        return;
      }

      this.pageIndex =event.first / this.pageSize;
 
      this.loadNotifications();
    }

  acknowledgeNotification(notification: INotificationData): void {
    this.notificationService.acknowledge(notification);
      
    notification.IsAcknowledged = true;
    this.messageService.add({
      severity: 'success',
      summary: this.translateService.instant(ToastTypes.success),
      detail: this.translateService.instant('ackEventSuccess')
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
  }
}
