import { ChangeDetectorRef, Component, ElementRef, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { DefaultMarkerComponent } from 'app/shared/modules/cymbiot-map/components/default-marker/default-marker.component';


@Component({
  selector: 'app-gps-marker',
  templateUrl: './gps-marker.component.html',
  styleUrls: ['./gps-marker.component.scss']
})
export class GpsMarkerComponent extends DefaultMarkerComponent implements OnInit {
  @ViewChild('marker', {static: false}) private markerEl: ElementRef;
  getElementRef(): ElementRef<any> {
    return this.markerEl;
  }
  showPopUp(): void {
    
  }
  hidePopUp(): void {
   
  }
  togglePopUp(): void {
    
  }

  @Output() onClick: EventEmitter<{detectionId: string, event: MouseEvent}> = new EventEmitter();
  @Output('removePopUp') removePopUp: EventEmitter<boolean> = new EventEmitter();
  @Output('beforeShowPopup') beforeShowPopup: EventEmitter<boolean> = new EventEmitter();
  

  isPopupVisible: boolean = false;
  public isSelected : boolean = false;

  constructor(
    private cdRef: ChangeDetectorRef
  ) {
    super()
  }

  ngOnInit(): void{
      this.markerEl;
  }

 


  

 
}
