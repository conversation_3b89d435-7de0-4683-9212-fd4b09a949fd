$red: #FF0000;
$green: #00D600;
$textColor: #808381;

.green  {
    color: $green;
}

.red  {
    color: $red;
}

.bold {
    font-weight: bold;
}

:host {
    display: block;
    width: 100%;
}

:host ::ng-deep {

    .table-title {
        display: flex;
        align-items: center;
       
    }
    .p-datatable {
        width: 100%;
        height: 100%;
        background: #ffffff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.12);
        border-radius: 8px;
        position: relative;
        z-index: 1;
        
        .p-datatable-header {
            border: none;
            padding: 8px 15px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            border-bottom: 1px solid #dee2e6;
            background: #e8f5e9 !important;
        }

        .p-datatable-thead > tr > th {
            border: none;
            border-bottom: 1px solid #e0e0e0;
            color: #667085;
            font-weight: 500;
            padding: 1rem;
            font-size: 0.875rem;

            &:first-child {
                border-top-left-radius: 8px;
            }

            &:last-child {
                border-top-right-radius: 8px;
            }

            .p-checkbox {
                width: 1.25rem;
                height: 1.25rem;

                .p-checkbox-box {
                    border-radius: 4px;
                    border: 2px solid #6c757d;

                    &.p-highlight {
                        background: $green;
                        border-color: $green;
                    }
                }
            }
        }
        
        .p-datatable-tbody > tr {          
            border: 1px solid #EAECF0;
            &:focus {
                outline: none;
            }

            &:nth-child(even) {
                background: #F9FAFB;
            }

            &.p-highlight {
                background: #e8f5e9 !important;
                outline: none;
            }

            > td {
                padding: 0.875rem 1rem;
                font-size: 0.875rem;
                color: #667085;
                text-align: left;
                .p-checkbox {
                    width: 1.25rem;
                    height: 1.25rem;

                    .p-checkbox-box {
                        border-radius: 4px;
                        border: 2px solid #6c757d;

                        &.p-highlight {
                            background: $green;
                            border-color: $green;
                        }
                    }
                }
            }

            &:hover {
                background: #f0f7ff !important;
            }
        }
        
        .p-paginator {
            background: #ffffff;
            border: none;
            padding: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-top: 1px solid #e0e0e0;
            position: relative;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;

            .p-paginator-left-content {
                position: absolute;
                left: 1rem;
                font-size: 0.875rem;
                color: #6c757d;
            }

            .p-paginator-current {
                display: none;
            }

            .p-paginator-pages {
                display: flex;
                gap: 0.25rem;

                .p-paginator-page {
                    min-width: 2rem;
                    height: 2rem;
                    margin: 0;
                    border-radius: 4px;
                    color: #6c757d;
                    font-weight: 500;
                    border: none;
                    background: #E4ECE6;
                    font-size: 0.875rem;

                    &.p-highlight {
                        background: $green;
                        color: #ffffff;
                    }

                    &:hover:not(.p-highlight) {
                        background: $green;
                        color: #ffffff;
                    }
                }
            }

            .p-paginator-first,
            .p-paginator-prev,
            .p-paginator-next,
            .p-paginator-last {
                width: 2rem;
                height: 2rem;
                margin: 0 0.25rem;
                border-radius: 4px;
                color: $green;
                border: none;
                background: #E4ECE6;
                display: flex;
                align-items: center;
                justify-content: center;

                &:hover {
                    background: $green;
                    color: #ffffff;
                }

                &.p-disabled {
                    opacity: 0.5;
                    background: #E4ECE6;
                    color: #6c757d;
                }
            }
        }

        .action-buttons {
            display: flex;
            gap: 0.25rem;
            justify-content: flex-end;
            min-width: 13rem;
            margin-left: auto;

            .p-button.p-button-text {
                width: 2rem;
                height: 2rem;
                border-radius: 4px;
                padding: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #E4ECE6;
                border: none;
                margin: 0;
                color: $green;
                border: 1px solid #D9D9D9;

                // Auto-delete button states
                &[icon="fa fa-lock"],
                &[icon="fa fa-unlock"] {
                    &.active {
                        background: $green;
                        color: #ffffff;
                    }

                    &:not(.active) {
                        background: #E4ECE6;
                        color: $green;
                    }
                }

                // All action buttons (including auto-delete)
                &:hover {
                    background: $green;
                    color: #ffffff;
                }

                &.active {
                    background: $green;
                    color: #ffffff;
                }

                &:focus {
                    box-shadow: none;
                }

                .fa {
                    font-size: 1rem;
                }
            }
        }

        .flex {
            display: flex;
            align-items: center;
            gap: 0.75rem;

            .fa-file-o {
                width: 2.5rem;
                height: 2.5rem;
                display: flex;
                align-items: center;
                justify-content: center;
                background: rgba(#E040FB, 0.1);
                border-radius: 50%;
                color: $green;
                font-size: 1.25rem;
                margin: 0;
            }

            span {
                &:first-of-type {
                    font-size: 0.875rem;
                    color: #667085;
                }
            }

            .text-gray-500 {
                color: #6c757d;
                font-size: 0.75rem;
            }
        }

        .text-green-500 {
            color: $green;
        }

        .text-gray-500 {
            color: #6c757d;
        }
    }
}
