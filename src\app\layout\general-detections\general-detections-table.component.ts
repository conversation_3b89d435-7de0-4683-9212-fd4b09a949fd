import { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import { TablePaginatorEvent } from '../../shared/components/ng-turbo-table/models/table-paginator-event.interface';
import { GeneralDetection } from '../../shared/modules/data-layer/models/general-detections/general-detection';

@Component({
  selector: 'general-detections-table',
  templateUrl: './general-detections-table.component.html',
  styleUrls: ['./general-detections-table.component.scss'],
})

export class GeneralDetectionsTableComponent implements OnDestroy {
  @Input() data: GeneralDetection[] = [];
  @Input() totalRecords: number = 0;
  @Input() pageSize: number = 12;
  @Input() showActions: boolean = true;
  @Input() pageIndex: number = 0;
  @Input() loading: boolean = false;
  @Input() selectedRow: GeneralDetection | null = null;

  @Output() pageChange = new EventEmitter<number>();
  @Output() selectedRowDetection = new EventEmitter<GeneralDetection | null>();

  onPageChange(event: TablePaginatorEvent): void {
    const pageIndex = event.first / event.rows;
    this.pageChange.emit(pageIndex);
  }

  onRowSelect(event: GeneralDetection): void {
    this.selectedRowDetection.emit(event);
  }

  ngOnDestroy(): void {
    this.data = [];
    this.totalRecords = 0;
  }
}