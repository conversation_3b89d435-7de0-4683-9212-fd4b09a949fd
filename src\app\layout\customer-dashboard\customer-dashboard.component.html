<app-cym-sidebar 
    [(opened)]="opened" 
    [dockedSize]="dockedSize" 
    [modeNum]="'push'" 
    inputClass="vms-sidebar">
    
    <div class="side-container" side>
      <p-confirmDialog appendTo="body" acceptButtonStyleClass="btn-primary" rejectButtonStyleClass="btn-secondary"></p-confirmDialog>
      
      <ng-container #editComponentFactory></ng-container>

      <div class="sidebar-actions">
        <button class="btn btn-{{action.importance}}" *ngFor="let action of sidebarActions" [disabled]="action.disabled" (click)="onDashboardAction(action.type)">{{ action.name | translate }}</button>
      </div>
    </div>
    <div class="dashboard-content content-snapshot" content>
      <div *ngIf="selectedDashboard.item" class="dashboard-priority {{selectedDashboard.item.priority}}"></div>
      <div class="dashboard-actions-wrapper">
        <app-dashboard-breadcrumb *ngIf="dashboardsTree" [dashboardsTree]="dashboardsTree" 
        [selectedDashboardId]="selectedDashboard.id" (onSelectDashboard)="selectDashboard($event)"></app-dashboard-breadcrumb>
        <app-dashboard-actions [selectedDashboardId]="selectedDashboard.id" (dashboardAction)="onDashboardAction($event)"></app-dashboard-actions>
      </div>
      <h1 *ngIf="dashboardsTree?.length === 1 && !state.edit">{{ 'customerDashboard.noDashboardsAvailable' | translate }}</h1>
      <app-dashboard-layout 
        [widgets]="selectedDashboard.widgets" 
        [editMode]="state.edit" 
        [selectedWidget]="state.selectedWidget" 
        (onAddWidget)="onDashboardAddWidget($event)" 
        (onChangeWidgetOrdering)="onDashboardChangeWidgetOrdering($event)"></app-dashboard-layout>
    </div>
</app-cym-sidebar>
