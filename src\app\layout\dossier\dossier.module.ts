import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DossierRoutingModule } from './dossier-routing.module';
import { DossierComponent } from './dossier.component';
import { DossierTableComponent } from './dossier-table/dossier-table.component';
import { DossierHistoryComponent } from './dossier-history/dossier-history.component';
import { DossierActionsComponent } from './dossier-actions/dossier-actions.component';
import { DossierDetailsComponent } from './dossier-details/dossier-details.component';
import { SharedModule } from './../../shared/modules/shared.module';
import { DossierFilterComponent } from './dossier-filter/dossier-filter.component';

@NgModule({
  declarations: [
    DossierComponent,
    DossierTableComponent,
    DossierHistoryComponent,
    DossierActionsComponent,
    DossierDetailsComponent,
    DossierFilterComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    DossierRoutingModule
  ]
})
export class DossierModule { } 