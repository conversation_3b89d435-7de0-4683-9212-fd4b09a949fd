import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class AiService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

    getAiUrl(): Observable<string> {
      // const url = `${environment.apiUrl}/token/apiUrl`;
      // return this.http.get(url, { responseType: 'text' });
      return of('http://ai.inspectorulpadurii.org/datasets/SideV1');
      // TODO: remove the above line and use the http call
  }


  
}
