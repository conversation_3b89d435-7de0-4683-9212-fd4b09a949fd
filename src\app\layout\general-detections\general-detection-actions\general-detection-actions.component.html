<div class="action-buttons">
  <button pButton icon="icon-widget-camera" class="p-button-rounded p-button-text"  pTooltip="{{ 'playback' | translate }}" [tooltipOptions]="tooltipOptions" (click)="playback(row)">
  </button>
  <button pButton icon="fa fa-map-o" class="p-button-rounded p-button-text"  pTooltip="{{ 'jumpToMap' | translate }}" [tooltipOptions]="tooltipOptions" (click)="jumpToMap(row)">
  </button>
    <button pButton icon="fa fa-list" class="p-button-rounded p-button-text"  pTooltip="{{ 'jumpToProcedures' | translate }}" [tooltipOptions]="tooltipOptions" (click)="jumpToProcedures(row)">
    </button>
  <button pButton icon="fa fa-car" class="p-button-rounded p-button-text"  pTooltip="{{ 'generalDetections.followOnTrafficDetections' | translate }}" [tooltipOptions]="tooltipOptions" (click)="followOnTrafficDetections(row)">
  </button>
  <button pButton icon="fa fa-download" class="p-button-rounded p-button-text"  pTooltip="{{ 'generalDetections.downloadArchive' | translate }}" [tooltipOptions]="tooltipOptions" (click)="downloadEventArchive(row)">
  </button>
  <button pButton icon="icon-maps" class="p-button-rounded p-button-text" pTooltip="{{ 'generalDetections.jumpToTrafficDetections' | translate }}" [tooltipOptions]="tooltipOptions" (click)="jumpToTrafficDetections(row)">
  </button>
  <button pButton icon="fa fa-folder-open" class="p-button-rounded p-button-text" pTooltip="{{ 'generalDetections.jumpToDossier' | translate }}" [tooltipOptions]="tooltipOptions" (click)="jumpToDossier(row)">
  </button>
</div>
