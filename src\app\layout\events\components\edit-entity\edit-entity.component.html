<form role="form" [formGroup]="editEntityForm" (ngSubmit)="onSubmit()">
    <h2>{{(selectedEntity ? 'editEntity' : 'addEntity') | translate}}</h2>
    <div class="row">
        <div class="col col-md-6">
            <input type="text" name="firstName" formControlName="firstName" placeholder="{{'firstName' | translate}}" />
        </div>
        <div class="col col-md-6">
            <input type="text" name="lastName" formControlName="lastName" placeholder="{{'lastName' | translate}}" />
        </div>
    </div>
    <div class="row">
        <div class="col col-md-6">
            <input type="text" name="userName" formControlName="userName" placeholder="{{'userName' | translate}}" />
        </div>
        <div class="col col-md-6">
            <input type="text" name="id" formControlName="id" placeholder="{{'id' | translate}}" />
        </div>
    </div>
    <div class="row">
        <div class="col col-md-6">
            <input type="text" name="phone" formControlName="phone" placeholder="{{'phone' | translate}}" />
        </div>
        <div class="col col-md-6">
            <input type="text" name="email" formControlName="email" placeholder="{{'email' | translate}}" />
        </div>
    </div>
    <div class="row">
        <div class="col col-md-6">
            <cymbiot-calendar formControlName="startDate" [monthNavigator]="true" dateFormat="dd.mm" [showTime]="true"
                [showIcon]="true"></cymbiot-calendar>
        </div>
        <div class="col col-md-6">
            <cymbiot-calendar formControlName="endDate" [monthNavigator]="true" dateFormat="dd.mm" [showTime]="true"
                [showIcon]="true"></cymbiot-calendar>
        </div>
    </div>

    <div class="row">
        <div class="col col-md-12">
            <h2>{{ 'associatedDevices' | translate }}</h2>
            <button type="button" class="btn btn-secondary add-button" (click)="addAsociatedResource()">
                <i class="fa fa-plus" aria-hidden="true"></i>
            </button>
        </div>
        <div formGroupName="associatedResources" class="col col-md-12">
            <div formArrayName="resources"
                *ngFor="let item of editEntityForm.get('associatedResources.resources')['controls']; let i = index">
                <div [formGroupName]="i" class="row">
                    <div class="col col-md-2">
                        <button class="btn btn-secondary" (click)="removeAssociatedResource(i)">
                            <i class="fa fa-trash-o" aria-hidden="true"></i>
                        </button>
                    </div>
                    <div class="col col-md-5">
                        <p-dropdown [options]="associatedResourcesSelectItems" formControlName="deviceId"
                            [styleClass]="'input-element'" placeholder="{{'selectDevice' | translate}}">
                            <ng-template let-item pTemplate="selectedItem">
                                <span>{{item.label| translate}}</span>
                            </ng-template>
                            <ng-template let-item pTemplate="item">
                                <div class="ui-helper-clearfix">
                                    <div>{{item.label | translate}}</div>
                                </div>
                            </ng-template>
                        </p-dropdown>
                    </div>
                    <div class="col col-md-5">
                        <input type="text" name="entityValue" formControlName="value"
                            placeholder="{{'entityValue' | translate}}" />
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col col-md-12">
            <p-multiSelect formControlName="associatedResourceGroups" [options]="associatedResourceGroupsSelectItems"
                [filter]="true" filterBy="label" [styleClass]="'input-element'"
                selectedItemsLabel="{{ 'nrItemsSelected' | translate }}"
                defaultLabel="{{ 'associatedResourceGroups' | translate }}" [maxSelectedLabels]="0" [appendTo]="'body'">
                <ng-template let-item pTemplate="item">
                    {{item.label | translate}}
                </ng-template>
            </p-multiSelect>
        </div>
    </div>
    <div class="row">
        <div class="sidebar-actions">
            <button class="btn btn-secondary" type="button" (click)="onCancel()">{{ 'cancel' | translate}}</button>
            <button class="btn btn-secondary" type="button" (click)="onDelete()" [disabled]="!selectedEntity">{{
                'delete' | translate}}</button>
            <button class="btn btn-primary" type="submit" [disabled]="!editEntityForm.valid">{{(selectedEntity ?
                'update':'create') | translate}}</button>
        </div>
    </div>


</form>