import { LoginSettings } from './../models/login-settings';
import { Injectable } from "@angular/core";
import { NavigationExtras, Router } from "@angular/router";
import { Store } from "@ngrx/store";
import { Observable, Subject, throwError as observableThrowError } from "rxjs";
import { catchError, map, mergeMap, tap } from "rxjs/operators";
import * as fromAppReducers from "./../../store/app.reducers";
import { SettingsService } from "./settings.service";

import { HttpClient } from "@angular/common/http";
import { environment } from "environments/environment";
import * as moment from "moment";
import { UserRoles } from "../enum/user-roles";
import { AiSettings, Renew } from "../models/renew.model";
import { VideoWall } from "../models/videoWall.model";
import { User } from "../models/user.model";
import { UserClass } from "../models/user.class";

import { apiMap } from "./api.map";
import { DataService } from "./data.service";
import { LocalStorageService } from "./local-storage.service";
import { ResourcePort } from "../modules/data-layer/services/resource/resource-port";
import { ResourceCacheService } from "../modules/data-layer/services/resource/resource.cache.service";
import { UrlNavigation } from "app/shared/modules/data-layer/models/navigation/url-navigation";
import {ApiCommands} from "app/shared/enum/enum";
import { Pages } from './navigation.service';

@Injectable({
  providedIn: "root",
})
export class AuthService {
  private _token: string;
  private _user: User;
  private _loggedIn: boolean = false;
  private _renewTokenHandle;
  private _defaultPagePermissions = {
    dashboard: "true",
    map: "true",
    "dashboard-new": "true",
    "map-new": "true",
    "inventory-new": "true",
    events: environment.enableEvents ? "true" : "false",
    swarmLPR: "true",
    assetsManagement: "true",
    "mobile-location": "true",
    procedures: "true",
    parkingLPR: "true",
    generalDetections: "true",
    vehicleTraffic: "true",
    ai: "true"
  };
  videoWallsCountStorage= "VIDEO_WALL_COUNT";
  private _pagePermissions: Map<string, boolean> = new Map<string, boolean>();
  SSO = false;
  public logOutEvent = new Subject<void>();
  public afterRenew: Subject<UrlNavigation> = new Subject<UrlNavigation>();

  constructor(
    private dataService: DataService,
    private localStorage: LocalStorageService,
    private store: Store<fromAppReducers.AppState>,
    private settingsService: SettingsService,
    private router: Router,
    private httpClient: HttpClient,
    private localStorageService: LocalStorageService,
  ) {
    this.checkForOpenSession();
  }

  private checkForOpenSession(): void
  {
      let savedCredentials = this.getCredentialsFromLocalStorage();
      if (savedCredentials) {
          this._token = savedCredentials.token;
          this.addTokenToHeaders();
          let requestedPath = window.location.pathname + window.location.search;
          this.httpClient.post(environment.apiUrl + apiMap.renew.url, {}).subscribe(
              (res: Renew) => {
                  if (res && res.token) {
                      let userCreds = this.getCredentialsFromLocalStorage();
                      this._user = userCreds.user;

                      this.loadPermissions(res.pagePermissions);
                      this.setLoggedIn(res.token, res.Expired);
                      let loginData = this.localStorage.get("LOGIN");
                      this.localStorageService.set("LOGIN", {ip: loginData.ip, userName: loginData.username});

                      const navigationExtras: NavigationExtras = {
                          queryParamsHandling: "preserve",
                          preserveFragment: true,
                      };

                      let urlNavigation: UrlNavigation = new UrlNavigation(requestedPath, navigationExtras);
                      this.afterRenew.next(urlNavigation);
                  } else {
                      this.remCredentialsFromLocalStorage();
                  }
              },
              (err) => {
                  console.log(err);
              }
          );
      }
  }

  /**
   * Logs the user in. loginData: 'username=...&password=...'
   * for cc-component we use captchaToken instead of password as sessionId
   */
   loginFlow(ip, username, password, captchaToken): Observable<Renew> {
    let sendData = new FormData();
    sendData.append("ip", ip);
    sendData.append("username", username);
    sendData.append("password", password);
    if(captchaToken == ''){
      sendData.append("captchaToken", captchaToken);
    }else{
      sendData.append("sessionId", captchaToken);
    }

    return this.httpClient.post(environment.apiUrl + apiMap.login.url, sendData).pipe(
      mergeMap((res) => {
        return this.createUserFlow(res);

      })
    );
  }

  createUserFlow(res): Observable<any> {
    if (!res || !res.token) {
      console.error("no token was provided!");
      throw "no token was provided!";
    }
    return this.getUserDeatils(res).pipe(
      catchError((err) => {
        this.remTokenFromHeaders();
        this._token = null;
        return observableThrowError(err);
      }),
      map((user) => {
        return this.createUserJson(user);
      })
    );
  }
  logoutFlow = (): Observable<any> => {
    return this.httpClient.post(environment.apiUrl + apiMap.logout.url, null).pipe(tap(this.setLoggedOut));
  };

  private loadPermissions(permissionsString: string)
  {
    this._pagePermissions = new Map<string, boolean>();

    let permissions = JSON.parse(permissionsString);
    const keys = Object.keys(permissions);
    for (let i = 0; i < keys.length; i++)
    {
      this._pagePermissions.set(Pages[keys[i]], permissions[keys[i]]);
    }
  }

  getUserDeatils(res) {
    this.setAiLink(res);
    this.loadPermissions(res.pagePermissions);

    this.setLoggedIn(res.token, res.Expired);
    return this.dataService.apiEx(ApiCommands.GetUserDetails);
  }

  setAiLink(value: Renew): void {
    const settings = JSON.parse(value.aiSettings) as AiSettings;
    this.localStorage.set("aiLink",  settings.url);
  }

  getUserRole(): UserRoles {
    // Convert string to UserRoles enum value
    return this._user.AccountType as unknown as UserRoles;
  }

  /**
   * Clears user-credentials from authService
   */
  private setLoggedOut = () => {

    this.logOutEvent.next();
    this.clearRenewTokenHandle();
    this.remTokenFromHeaders();
    this.remCredentialsFromLocalStorage();
    this._token = null;
    this._user = null;
    this.localStorage.remove("token");
    this.localStorage.remove("aiLink");
  };

  private setLoggedIn(token, tokenExpiredDate) {

    this._token = token;
    let tokenValidity = (moment(tokenExpiredDate) && moment(tokenExpiredDate).diff(moment(), "milliseconds")) || 60 * 60 * 1000;

    this.tryRenewToken(tokenValidity / 2 - 1000 * 60);
    this.addTokenToHeaders();
    this.httpClient.get(environment.apiUrl + apiMap.getVideoWalls.url).subscribe((res:VideoWall[]) => {

      this.localStorage.set(this.videoWallsCountStorage, res.length);
    })
    this._loggedIn = true;
    this._user = this.getCredentialsFromLocalStorage()?.user;
  }

  public setLoggedFromRedirect(token, type): Observable<any> {
    this.SSO = true;
    this._token = token;
    this.addTokenToHeaders();
    this._loggedIn = true;
    return this.httpClient.post(environment.apiUrl + apiMap.loginByRedirect.url + `?token=${this._token}&sso_type=${type}`, null).pipe(
      mergeMap((res) => {
        return this.createUserFlow(res);
      })
    );
  }
  /**
   * A flag, indicating whether the user is logged-in. Will search for
   * credentials in local-storage just to make sure.
   */
  get isLoggedIn(): boolean {
    return this._loggedIn;
  }


  public isAllowedPage(page: string): boolean {

    return page == "" || this._pagePermissions.get(page);
  }

  getToken(): string {
    return this._token;
  }

  /**
   * Gets or sets the user-data (user-role)
   */
  get user(): User {
    return this._user;
  }
  set user(val: User) {
    this._user = val;
  }

  /**
   * Gets the current user object
   */
  getUser(): User {
    return this._user;
  }

  private tryRenewToken(interval) {
    this._renewTokenHandle = setInterval(() => {
      this.renewToken().subscribe(
        (res) => {
          if (res && res.token) {
            this._token = res.token;
            this.localStorage.set("token", this._token);
            this.addTokenToHeaders();
          }
        },
        (err) => console.log(err)
      );
    }, interval);
  }

  public renewToken(): Observable<any> {
    return this.httpClient.post(environment.apiUrl + apiMap.renew.url, null);
  }

  private addTokenToHeaders() {
    this.localStorage.set("token", this._token);
    this.dataService.addHeaders({
      Authorization: this._token,
    });
  }

  private createUserJson(user) {
    this._user = UserClass.fromApiResponse(user);
    this.saveCredentialsInLocalStorage();
    this.settingsService.setInitConfig(user.WebConfig);

    return user;
  }

  private remTokenFromHeaders() {
    this.localStorage.remove("token");
    this.dataService.remHeaders(["Authorization"]);
  }

  private clearRenewTokenHandle() {
    if (this._renewTokenHandle) {
      clearInterval(this._renewTokenHandle);
    }
  }

  private getCredentialsFromLocalStorage() {
    // this is a simple form of decription -
    // so it will be difficult for the user to see meaningful data in local-storage
    const abbr = environment.useCredentialStorage ? this.localStorage.get("CREDENTIALS") : null;
    if (!abbr) {
      return null;
    } else {
      // Create a User object from the decrypted data
      const user = new UserClass({
        MobilePhone: this.caesarEncript(abbr.u.m, -1),
        Identity: this.caesarEncript(abbr.u.i, -1),
        ProfileIdentity: this.caesarEncript(abbr.u.p, -1),
        Username: this.caesarEncript(abbr.u.u, -1),
        IsEditor: this.caesarEncript(abbr.u.ie, -1),
        Email: this.caesarEncript(abbr.u.e, -1),
        SystemName: this.caesarEncript(abbr.u.s, -1),
        ShowSysName: this.caesarEncript(abbr.u.sn, -1),
        UserImage: abbr.u.um,
        AccountType: this.caesarEncript(abbr.u.a, -1),
      });

      return {
        token: abbr.t,
        user: user
      };
    }
  }

  private saveCredentialsInLocalStorage() {
    // this is a simple form of encription -
    // so it will be difficult for the user to see meaningful data in local-storage
    let item = {
      t: this._token,
      u: {
        m: this.caesarEncript(this._user.MobilePhone, 1),
        i: this.caesarEncript(this._user.Identity, 1),
        p: this.caesarEncript(this._user.ProfileIdentity, 1),
        u: this.caesarEncript(this._user.Username, 1),
        ie: this.caesarEncript(this._user.IsEditor, 1),
        e: this.caesarEncript(this._user.Email, 1),
        s: this.caesarEncript(this._user.SystemName, 1),
        sn: this.caesarEncript(this._user.ShowSysName, 1),
        um: this._user.UserImage,
        a: this.caesarEncript(this._user.AccountType, 1),
      },
    };
    this.localStorage.set("CREDENTIALS", item);
  }

  private remCredentialsFromLocalStorage() {
    this.localStorage.remove("CREDENTIALS");
  }

  private caesarEncript(str: string, offset: number) {
    let charCodes = [];
    if (str) {
      for (let i = 0; i < str.length; i++) {
        charCodes.push(str.charCodeAt(i) + offset);
      }
    }
    return String.fromCharCode(...charCodes);
  }

  /**
   * Gets the current user object
   * @returns The current user as a User object
   */
  public getCurrentUser(): User {
    return this._user;
  }

  public getLoginSettings():Observable<LoginSettings>{
    const url = `${environment.apiUrl}/token/login-settings`;

    return this.httpClient.get<LoginSettings>(url);
  }
}
