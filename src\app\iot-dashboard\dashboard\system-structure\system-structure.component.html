    <div class="dashboard-rectangle-16">
        <div class="system-structure-container">
            <div class="system-header">
                <h2>{{ 'cameras' | translate }}</h2>
                <div class="refresh-container">
                    <button type="button" class="refresh-button" (click)="manualRefresh()" aria-label="">
                        <i class="fa fa-refresh" [class.spinning]="isSpinning"></i>
                    </button>
                    <span class="refresh-timer">
                        {{ 'actualizationIn' | translate }} : {{minutes}}:{{seconds < 10 ? '0' + seconds : seconds}}
                    </span>
                </div>
            </div>

            <div class="camera-status-summary">
                <div class="status-card">
                    <div class="status-icon online">
                        <i class="fa fa-video-camera"></i>
                    </div>
                    <div class="status-info">
                        <div class="status-count">{{onlineCameras}}</div>
                        <div class="status-label">{{ 'online' | translate }}</div>
                    </div>
                </div>

                <div class="status-card">
                    <div class="status-icon offline">
                        <i class="fa fa-video-camera"></i>
                    </div>
                    <div class="status-info">
                        <div class="status-count">{{offlineCameras}}</div>
                        <div class="status-label">{{ 'offline' | translate }}</div>
                    </div>
                </div>
            </div>
            <div class="camera-logs">
                <div class="logs-header">
                    <h3>{{ 'cameraStatusHistory' | translate }}</h3>
                    <button type="button" (click)="clearLogs()" class="clear-logs-btn">
                        <i class="fa fa-trash"></i>
                        {{ 'clearHistory' | translate }}
                    </button>
                </div>
                <div class="logs-container" #scrollContainer>
                    <div class="logs-wrapper">
                        <div *ngFor="let log of logs" class="log-entry" [ngClass]="{'manual-refresh': log.type === 'manual'}">
                            <div class="log-timestamp">
                                <i class="fa" [ngClass]="{'fa-refresh': log.type === 'manual', 'fa-clock-o': log.type === 'auto'}"></i>
                                {{ log.timestamp }}
                            </div>
                            <div class="log-details">
                                <div class="log-stat online">
                                    <div class="stat-value">{{ log.onlineCameras }}</div>
                                    <div class="stat-label">{{ 'online' | translate }}</div>
                                </div>
                                <div class="log-stat offline">
                                    <div class="stat-value">{{ log.offlineCameras }}</div>
                                    <div class="stat-label">{{ 'offline' | translate }}</div>
                                </div>
                                <div class="log-stat error">
                                    <div class="stat-value">{{ log.errorCameras }}</div>
                                    <div class="stat-label">{{ 'error' | translate }}</div>
                                </div>
                                <div class="log-stat no-comm">
                                    <div class="stat-value">{{ log.noCommunicationCameras }}</div>
                                    <div class="stat-label">{{ 'noCommunication' | translate }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>