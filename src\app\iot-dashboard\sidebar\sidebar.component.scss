a:focus, a:hover {
    text-decoration: none;
}

:host {

  .dashboard-background-selected a {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Dashboard.svg');
  }

  .dashboard-background-selected a:hover,
  .dashboard-background-selected a.active {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Icons-White/Dashboard.svg');
  }

  .dashboard-t-vms a {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Cameras.svg');
  }

  .dashboard-t-vms a:hover,
  .dashboard-t-vms a.active {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Icons-White/Cameras.svg');
  }

  .dashboard-t-video-wall a {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/VideoWall.svg');
    cursor: pointer;
  }

  .dashboard-t-video-wall a:hover,
  .dashboard-t-video-wall a.active {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Icons-White/VideoWall.svg');
    cursor: pointer;
  }

  .dashboard-t-map a {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Maps.svg');
  }

  .dashboard-t-map a:hover,
  .dashboard-t-map a.active {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Icons-White/Maps.svg');
  }

  .dashboard-t-inventory a {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Inventory.svg');
  }

  .dashboard-t-inventory a:hover,
  .dashboard-t-inventory a.active {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Icons-White/Inventory.svg');
  }

  .dashboard-t-asset-management a {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/AssetsManagement.svg');
  }

  .dashboard-t-asset-management a:hover,
  .dashboard-t-asset-management a.active {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Icons-White/AssetsManagement.svg');
  }

  .dashboard-t-reports a {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Reports.svg');
  }

  .dashboard-t-reports a:hover,
  .dashboard-t-reports a.active {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Icons-White/Reports.svg');
  }

  .dashboard-t-lpr a {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/LPR.svg');
  }

  .dashboard-t-lpr a:hover,
  .dashboard-t-lpr a.active {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Icons-White/LPR.svg');
  }

  .dashboard-t-procedures a {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Procedures.svg');
  }

  .dashboard-t-procedures a:hover,
  .dashboard-t-procedures a.active {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Icons-White/Procedures.svg');
  }

  .dashboard-t-general-detection a {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/GeneralDetections.svg');
  }

  .dashboard-t-general-detection a:hover,
  .dashboard-t-general-detection a.active {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Icons-White/GeneralDetections.svg');
  }

  .dashboard-t-dossier a {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Dossier.svg');
  }

  .dashboard-t-dossier a:hover,
  .dashboard-t-dossier a.active {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Icons-White/Dossier.svg');
  }

  .dashboard-t-ai a {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/AI.svg');
  }

  .dashboard-t-ai a:hover,
  .dashboard-t-ai a.active {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Icons-White/AI.svg');
  }

  .dashboard-t-file-manager a {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Entities.svg');
  }

  .dashboard-t-file-manager a:hover,
  .dashboard-t-file-manager a.active {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Icons-White/Entities.svg');
  }

  .dashboard-t-audit a {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Audit.svg');
  }

  .dashboard-t-audit a:hover,
  .dashboard-t-audit a.active {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Icons-White/Audit.svg');
  }

  .dashboard-t-settings a {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Settings.svg');
    cursor: pointer;
  }

  .dashboard-t-settings a:hover,
  .dashboard-t-settings a.active {
    background-image: url('../../../assets/public/assets/IOT-Sidebar/Icons/Icons-White/Settings.svg');
    cursor: pointer;
  }
}



/* Sidebar toggle button at the end of the list */
.sidebar-toggle-container {
  margin-top: auto;
  padding: 15px 0;
  display: flex;
  justify-content: center;

  .sidebar-toggle {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: 1px solid rgba(0, 0, 0, 0.1);
    background-color: #E4ECE6;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0;

    &:hover {
      background-color: #00d603;

      i {
        color: white;
      }
    }

    i {
      color: #8c8c8c;
      font-size: 14px;
      transition: color 0.3s ease;
    }
  }
}

.sidebar-footer-space {
  height: 60px;
  width: 100%;
}