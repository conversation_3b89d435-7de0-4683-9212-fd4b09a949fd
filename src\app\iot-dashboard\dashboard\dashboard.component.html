<div class="dashboard-header">
    <h1 class="dashboard-title">IoT Dashboard</h1>
</div>

<div class="viewport-container">
  <div class="container-dashboard viewport-content">
  <div class="column">
    <div class="dash-panel panel-1">
      <app-cameras></app-cameras>
    </div>
    <div class="dash-panel panel-2">
      <app-system-structure></app-system-structure>
    </div>
    <div class="dash-panel panel-3">
        <app-weather-info></app-weather-info>
    </div>
  </div>
  <div class="column">
    <div class="dash-panel panel-3">
      <app-alarm-summary
        [totalAlarmRecords]="totalAlarmRecords"
        [totalAlertRecords]="totalAlertRecords"
        [totalNotificationRecords]="totalNotificationRecords">
      </app-alarm-summary>
    </div>
    <div class="dash-panel panel-4">
      <div class="dashboard-maps">
        <app-map></app-map>
      </div>
    </div>
  </div>

  <div class="column">
    <div class="dash-panel panel-5">
      <ng-container *ngIf="isWarningFirst; else notificationsFirst">
        <warning-list
          [notifications]="notifications"
          [totalNotificationRecords]="totalNotificationRecords"
          [pageSize]="pageSize"
          [notificationPageIndex]="notificationPageIndex"
          (notificationLazyLoad)="onNotificationLazyLoad($event)"
          (rowSelect)="onNotificationSelect($event)"></warning-list>

        <button class="switch-position" (click)="switchPosition()">
          {{ 'switchPosition' | translate }}
          <img src="assets/public/assets/dashboard-order.svg" loading="lazy" />
        </button>

        <app-notification-list
          [alarms]="alarms"
          [totalAlarmRecords]="totalAlarmRecords"
          [alerts]="alerts"
          [alarmPageIndex]="alarmPageIndex"
          [alertPageIndex]="alertPageIndex"
          [totalAlertRecords]="totalAlertRecords"
          [loading]="loading"
          [alertLoading]="alertLoading"
          [pageSize]="pageSize"
          (alarmsLazyLoad)="onAlarmsLazyLoad($event)"
          (alertsLazyLoad)="onAlertsLazyLoad($event)"
          (rowSelect)="onRowSelect($event)">
        </app-notification-list>

      </ng-container>
      <ng-template #notificationsFirst>

        <app-notification-list
          [alarms]="alarms"
          [totalAlarmRecords]="totalAlarmRecords"
          [alerts]="alerts"
          [alarmPageIndex]="alarmPageIndex"
          [alertPageIndex]="alertPageIndex"
          [totalAlertRecords]="totalAlertRecords"
          [loading]="loading"
          [alertLoading]="alertLoading"
          [pageSize]="pageSize"
          (alarmsLazyLoad)="onAlarmsLazyLoad($event)"
          (alertsLazyLoad)="onAlertsLazyLoad($event)"
          (rowSelect)="onRowSelect($event)">
        </app-notification-list>

        <button class="switch-position" (click)="switchPosition()" >
          {{ 'switchPosition' | translate }}
          <img src="assets/public/assets/dashboard-order.svg" loading="lazy" />
        </button>

        <warning-list
          [notifications]="notifications"
          [totalNotificationRecords]="totalNotificationRecords"
          [pageSize]="pageSize"
          [notificationPageIndex]="notificationPageIndex"
          [notificationLoading]="notificationLoading"
          (notificationLazyLoad)="onNotificationLazyLoad($event)"
          (rowSelect)="onNotificationSelect($event)"></warning-list>


      </ng-template>
    </div>
  </div>
</div>
</div> <!-- Close viewport-container -->
