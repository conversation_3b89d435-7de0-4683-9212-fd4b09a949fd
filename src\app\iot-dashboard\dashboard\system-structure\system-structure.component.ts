import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subscription, interval } from 'rxjs';
import { ResourceService } from '../../../services/resource/resource.service';
import { CameraStatusType } from '../../../shared/enum/camera-status.enum';
import { ServerTypes } from '../../../shared/modules/data-layer/enum/resource/resource-server-types.enum';
import { DataChangeNotification } from "../../../shared/modules/data-layer/models/data-change-notification";
import { DataChangeType } from '../../../shared/modules/data-layer/models/data-change-type.enum';
import { Resource } from '../../../shared/modules/data-layer/models/resource';
import { ResourceCacheService } from '../../../shared/modules/data-layer/services/resource/resource.cache.service';
import { CameraCountService } from '../../../shared/services/camera-count.service';
import { CameraLog } from './camera-log.interface';

@Component({
  selector: 'app-system-structure',
  templateUrl: './system-structure.component.html',
  styleUrls: ['./system-structure.component.scss']
})
export class SystemStructureComponent implements OnInit, OnDestroy {
  onlineCameras: number = 0;
  offlineCameras: number = 0;
  errorCameras: number = 0;
  noCommunicationCameras: number = 0;
  minutes: number = 1;
  seconds: number = 0;
  isSpinning: boolean = false;
  logs: CameraLog[] = [];
  readonly MAX_LOGS = 10;
  private subscriptions: Subscription[] = [];
  private readonly UPDATE_INTERVAL = 60000; // 1 minute in milliseconds
  private readonly COUNTDOWN_INTERVAL = 1000; // 1 second in milliseconds

  constructor(
    private cameraCountService: CameraCountService,
    private resourceCacheService: ResourceCacheService,
    private resourceService: ResourceService
  ) {
    this.loadLogsFromStorage();
  }

  ngOnInit() {
    this.getAllData();
    this.setupPeriodicUpdate();
    this.setupCountdownTimer();

    this.subscriptions.push(
      this.cameraCountService.onlineCameras$.subscribe(
        count => this.onlineCameras = count
      ),
      this.cameraCountService.offlineCameras$.subscribe(
        count => this.offlineCameras = count
      ),
      this.cameraCountService.errorCameras$.subscribe(
        count => this.errorCameras = count
      ),
      this.cameraCountService.noCommunicationCameras$.subscribe(
        count => this.noCommunicationCameras = count
      ),
      this.resourceService.resourceChanged.subscribe((resource: DataChangeNotification<Resource>) => {
        this.notifyOperation(resource);
      })
    );
  }

  private loadLogsFromStorage(): void {
    const savedLogs = localStorage.getItem('camera-refresh-logs');
    if (savedLogs) {
      this.logs = JSON.parse(savedLogs);
    }
  }

  private saveLogsToStorage(): void {
    localStorage.setItem('camera-refresh-logs', JSON.stringify(this.logs));
  }

  private addLog(type: CameraStatusType): void {
    const newLog: CameraLog = {
      timestamp: new Date().toLocaleString(),
      onlineCameras: this.onlineCameras,
      offlineCameras: this.offlineCameras,
      errorCameras: this.errorCameras,
      noCommunicationCameras: this.noCommunicationCameras,
      type: type
    };

    this.logs.unshift(newLog);

    if (this.logs.length > this.MAX_LOGS) {
      this.logs = this.logs.slice(0, this.MAX_LOGS);
    }

    this.saveLogsToStorage();
  }

  private setupCountdownTimer(): void {
    const countdownSubscription = interval(this.COUNTDOWN_INTERVAL).subscribe(() => {
      if (this.seconds > 0) {
        this.seconds--;
      } else {
        if (this.minutes > 0) {
          this.minutes--;
          this.seconds = 59;
        } else {
          this.resetTimer();
        }
      }
    });
    this.subscriptions.push(countdownSubscription);
  }

  private resetTimer(): void {
    this.minutes = 1;
    this.seconds = 0;
  }

  private setupPeriodicUpdate(): void {
    this.refreshCameraStatuses(CameraStatusType.Auto);

    const updateSubscription = interval(this.UPDATE_INTERVAL).subscribe(() => {
      this.refreshCameraStatuses(CameraStatusType.Auto);
      this.resetTimer();
    });
    this.subscriptions.push(updateSubscription);
  }

  public manualRefresh(): void {
    if (this.isSpinning) return;

    this.isSpinning = true;
    this.refreshCameraStatuses(CameraStatusType.Manual);
    this.resetTimer();

    setTimeout(() => {
      this.isSpinning = false;
    }, 1000);
  }

  public clearLogs(): void {
    this.logs = [];
    this.saveLogsToStorage();
  }

  private refreshCameraStatuses(type: CameraStatusType): void {
    this.resourceService.getAllStatuses().subscribe(statuses => {
      statuses.forEach(status => {
        const resource = this.resourceCacheService.get(status.Id.toString());
        if (!resource || resource.status === status.State) {
          return;
        }
        
        resource.status = status.State;
        this.resourceService.update(resource);
      });

      this.updateCameraCount();
      this.addLog(type);
    });
  }

  private getAllData(): void {
    let resourcesSubscription = this.resourceCacheService.storageCompleted.subscribe(() => {
      let statusesSubscription = this.resourceService.getAllStatuses().subscribe(statuses => {
        statuses.forEach(status => {
          const resource = this.resourceCacheService.get(status.Id.toString());
          if (!resource || resource.status === status.State) {
            return;
          }
          
          resource.status = status.State;
          this.resourceService.update(resource);
        });

        this.updateCameraCount();
      });
      this.subscriptions.push(statusesSubscription);
    });
    this.subscriptions.push(resourcesSubscription);
  }

  private notifyOperation(notification: DataChangeNotification<Resource>): void {
    const inputChannels = notification.models.filter(
      resource => resource.resourceType === ServerTypes.Core_RES_InputChannel
    );

    if (inputChannels.length === 0) return;

    switch(notification.type) {
      case DataChangeType.Update:
      case DataChangeType.Create:
        this.resourceCacheService.put(inputChannels);
        break;
      case DataChangeType.Delete:
        this.resourceCacheService.delete(inputChannels);
        break;
      default:
        console.error("Operation type is not supported:", notification.type);
        return;
    }

    this.updateCameraCount();
  }

  private updateCameraCount(): void {
    const cameras = this.resourceCacheService.getAll()
      .filter(resource => resource.resourceType === ServerTypes.Core_RES_InputChannel);
    this.cameraCountService.updateCameraCountsFromResources(cameras);
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
}