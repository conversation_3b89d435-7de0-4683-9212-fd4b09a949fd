
<div class="iot-table">
    <p-table
    [value]="audits"
    [loading]="loading"
    [paginator]="true"
    [rows]="pageSize"
    [totalRecords]="totalRecords"
    [lazy]="true"
    [first]="pageIndex * pageSize"
    [showCurrentPageReport]="true"
    styleClass="p-datatable-striped"
    (onPage)="onPageChange($event)">
    <ng-template pTemplate="header">
        <tr>
            <th>{{ 'auditTable.name' | translate }}</th>
            <th>{{ 'auditTable.message' | translate }}</th>
            <th>{{ 'auditTable.timeStamp' | translate }}</th>
            <th>{{ 'auditTable.ipAddress' | translate }}</th>
        </tr>
    </ng-template>

    <ng-template pTemplate="body" let-item>
        <tr>
            <td >{{item?.user || 'N/A'}}</td>
            <td> {{item?.message || 'N/A'}} </td>
            <td>{{item?.timestamp ? (item.timestamp | date:'dd/MM/yyyy HH:mm:ss') : 'N/A'}}</td>
            <td>{{item?.ip || 'N/A'}}</td>
        </tr>
    </ng-template>

    <ng-template pTemplate="emptymessage">
        <tr>
            <td colspan="4" class="text-center">{{ 'auditTable.noAuditsFound' | translate }}</td>
        </tr>
    </ng-template>

</p-table>
