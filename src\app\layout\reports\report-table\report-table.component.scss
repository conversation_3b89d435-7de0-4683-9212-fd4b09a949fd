$green: #00D600;
$textColor: #808381;

:host ::ng-deep {
    .iot-table {
        background: #F2F6F3;
        border-radius: 24px;
        padding: 15px;
        max-height: calc(100vh - 250px);
        display: flex;
        flex-direction: column;

        // Table Controls
        .table-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            padding: 0 0.5rem;

            .results-count {
                .result-counter {
                    font-size: 0.875rem;
                    color: #667085;
                    margin: 0;
                }
            }

            .column-selector {
                .p-multiselect {
                    .p-multiselect-label {
                        font-size: 0.875rem;
                        padding: 0.5rem 0.75rem;
                    }

                    &.p-multiselect-chip .p-multiselect-token {
                        background: rgba(0, 214, 0, 0.1);
                        color: $green;
                        margin-right: 0.25rem;
                    }

                    .p-multiselect-footer {
                        padding: 0.5rem;
                        border-top: 1px solid #e0e0e0;

                        .column-footer {
                            display: flex;
                            justify-content: space-between;
                            width: 100%;

                            .p-button-text {
                                font-size: 0.75rem;
                                padding: 0.25rem 0.5rem;
                                color: #667085;

                                &:hover {
                                    background: rgba(0, 214, 0, 0.1);
                                    color: $green;
                                }
                            }
                        }
                    }
                }
            }
        }

        .p-datatable {
            width: 100%;
            background: #ffffff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12);
            border-radius: 8px;
            position: relative;
            z-index: 1;

            .p-datatable-header {
                border: none;
                padding: 1rem;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }

            .p-datatable-wrapper {
                max-height: calc(100vh - 450px);
                overflow-y: auto;
            }

            .p-datatable-thead > tr > th {
                border: none;
                border-bottom: 1px solid #e0e0e0;
                color: #667085;
                font-weight: 500;
                padding: 0.75rem 1rem;
                font-size: 0.875rem;
                background: #ffffff;
                position: relative;

                &:first-child {
                    border-top-left-radius: 8px;
                }

                &:last-child {
                    border-top-right-radius: 8px;
                }

                // Filter icon
                .filter-icon {
                    margin-left: 0.5rem;
                    color: #667085;
                    cursor: pointer;
                    font-size: 0.875rem;

                    &:hover {
                        color: $green;
                    }
                }

                // Active filter indicator
                &.filter-active {
                    .filter-icon {
                        color: $green;
                    }

                    &:after {
                        content: '';
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        height: 2px;
                        background-color: $green;
                    }
                }
            }

            .p-datatable-tbody > tr {
                border: 1px solid #EAECF0;

                &:nth-child(even) {
                    background: #F9FAFB;
                }

                &.p-highlight {
                    background: #e8f5e9 !important;
                }

                > td {
                    padding: 0.625rem 1rem;
                    font-size: 0.875rem;
                    color: #667085;
                    text-align: left;
                    border-bottom: 1px solid #EAECF0;
                }

                &:hover {
                    background: #f0f7ff !important;
                }
            }

            .p-paginator {
                background: #ffffff;
                border: none;
                padding: 0.75rem;
                display: flex;
                align-items: center;
                justify-content: center;
                border-top: 1px solid #e0e0e0;
                position: relative;
                border-bottom-left-radius: 8px;
                border-bottom-right-radius: 8px;

                .p-paginator-left-content {
                    position: absolute;
                    left: 1rem;
                    font-size: 0.875rem;
                    color: #6c757d;
                }

                .p-paginator-current,
                .p-paginator-current.ng-star-inserted,
                .p-paginator-current > span,
                .p-paginator-current > span.ng-star-inserted {
                    margin: 0 1rem;
                    color: #6c757d;
                    font-size: 0.875rem;
                    font-weight: 500;
                    background: transparent;
                    border: none;
                    min-width: auto;
                    height: auto;
                    padding: 0;
                    border-radius: 0;
                    box-shadow: none;
                }

                .p-paginator-pages {
                    display: flex;
                    gap: 0.25rem;
                    margin: 0 1rem;

                    .p-paginator-page {
                        min-width: 2rem;
                        height: 2rem;
                        margin: 0;
                        border-radius: 4px;
                        color: #6c757d;
                        font-weight: 500;
                        border: none;
                        background: #E4ECE6;
                        font-size: 0.875rem;

                        &.p-highlight {
                            background: $green;
                            color: #ffffff;
                        }

                        &:hover:not(.p-highlight) {
                            background: $green;
                            color: #ffffff;
                        }
                    }
                }

                .p-paginator-first,
                .p-paginator-prev,
                .p-paginator-next,
                .p-paginator-last {
                    margin: 0 0.2rem;
                    min-width: 2rem;
                    height: 2rem;
                    border-radius: 4px;
                    background: #E4ECE6;
                    color: #6c757d;
                    border: none;

                    &:hover {
                        background: $green;
                        color: #ffffff;
                    }

                    &.p-disabled {
                        opacity: 0.5;
                        background: #E4ECE6;
                        color: #6c757d;
                    }
                }
            }
        }
    }

    // Filter overlay panel
    .p-overlaypanel {
        .filter-content {
            padding: 0.5rem;
            min-width: 200px;

            .input-filter {
                width: 100%;
                padding: 0.5rem;
                font-size: 0.875rem;
                border: 1px solid #ced4da;
                border-radius: 4px;

                &:focus {
                    outline: none;
                    border-color: $green;
                    box-shadow: 0 0 0 0.2rem rgba(0, 214, 0, 0.25);
                }
            }

            .multiselect-container {
                width: 100%;

                .p-multiselect {
                    width: 100%;

                    .p-multiselect-label {
                        font-size: 0.875rem;
                        padding: 0.5rem 0.75rem;
                    }

                    &.p-multiselect-chip .p-multiselect-token {
                        background: rgba(0, 214, 0, 0.1);
                        color: $green;
                        margin-right: 0.25rem;
                    }
                }
            }
        }
    }

    // Flex container for content
    .flex {
        display: flex;
        align-items: center;
        gap: 0.75rem;

        .fa {
            width: 2.5rem;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 214, 0, 0.1);
            border-radius: 50%;
            color: $green;
            font-size: 1.25rem;
            margin: 0;
        }

        span {
            &:first-of-type {
                font-size: 0.875rem;
                color: #667085;
            }
        }
    }

    // Text colors
    .text-green-500 {
        color: $green;
    }

    .text-gray-500 {
        color: #6c757d;
    }

    .bold {
        font-weight: bold;
    }

    // Export buttons
    .export-buttons {
        display: flex;
        gap: 0.5rem;

        .p-button-text {
            color: #667085;

            &:hover {
                color: $green;
                background: rgba(0, 214, 0, 0.1);
            }
        }
    }
}
