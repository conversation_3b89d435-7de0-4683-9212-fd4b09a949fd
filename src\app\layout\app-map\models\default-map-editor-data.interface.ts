import { MapFilters } from './map-filters.interface';
import { MapObject } from './map-object.interface';
import { MapSearchProperties } from './map-search-properties.interface';
import { MapState } from './map-state.interface';

export interface DefaultMapEditorData {
    state: MapState;
    selectedMap: MapObject;
    filters?: MapFilters;
    mapInstanceId?: string;
    mapSearchProperties?: MapSearchProperties
  }