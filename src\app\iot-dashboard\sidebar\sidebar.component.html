<div class="dashboard-management-panel">
    <div class="dashboard-background-selected">
      <a [routerLink]="['/new-dashboard']" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" [title]="'dashboard' | translate" *ngIf="isAllowedPage(pages.newDashboard)"></a>
    </div>

    <div class="dashboard-t-vms" *ngIf="isAllowedPage(pages.vms)">
      <a [routerLink]="['/new-dashboard/vms']" [queryParams]="{vms: 4}" routerLinkActive="active" [title]="'cameras' | translate"></a>
    </div>

    <div class="dashboard-t-video-wall" [title]="'videoWall' | translate" *ngIf="isAllowedPage(pages.videoWall) && videoWallCount > 0">
      <a (click)="navigate(pages.videoWall)" routerLinkActive="active"></a>
    </div>

    <div class="dashboard-t-map" [title]="'map' | translate" *ngIf="isAllowedPage(pages.mapNew)">
      <a [routerLink]="['/new-dashboard/map-new']" routerLinkActive="active"></a>
    </div>

    <div class="dashboard-t-inventory" [title]="'inventory.inventory' | translate" *ngIf="isAllowedPage(pages.inventory)">
      <a [routerLink]="['/new-dashboard/inventory']" routerLinkActive="active"></a>
    </div>

    <div class="dashboard-t-asset-management" [title]="'assetsManagement' | translate" *ngIf="isAllowedPage(pages.assetsManagement)">
      <a [routerLink]="['/new-dashboard/assetsManagement']" routerLinkActive="active"></a>
    </div>

    <div class="dashboard-t-reports" [title]="'reports' | translate" *ngIf="isAllowedPage(pages.reports)">
      <a [routerLink]="['/new-dashboard/reports']" routerLinkActive="active"></a>
    </div>

    <div class="dashboard-t-lpr" [title]="'trafficDetections' | translate" *ngIf="isAllowedPage(pages.trafficDetections)">
      <a [routerLink]="['/new-dashboard/swarmLPR']" routerLinkActive="active"></a>
    </div>

    <div class="dashboard-t-procedures" [title]="'procedures' | translate" *ngIf="isAllowedPage(pages.procedures)">
      <a [routerLink]="['/new-dashboard/procedures']" routerLinkActive="active"></a>
    </div>

    <div class="dashboard-t-general-detection" [title]="'generalDetections.generalDetections' | translate" *ngIf="isAllowedPage(pages.generalDetections)">
      <a [routerLink]="['/new-dashboard/generalDetections']" routerLinkActive="active"></a>
    </div>

    <div class="dashboard-t-dossier" [title]="'dossiers' | translate" *ngIf="isAllowedPage(pages.dossier)">
      <a [routerLink]="['/new-dashboard/dossier']" routerLinkActive="active"></a>
    </div>

    <div class="dashboard-t-file-manager" [title]="'entities' | translate" *ngIf="isAllowedPage(pages.entities)">
      <a [routerLink]="['/new-dashboard/entities']" routerLinkActive="active"></a>
    </div>

   <div class="dashboard-t-audit" [title]="'audit.title' | translate">
      <a [routerLink]="['/new-dashboard/audit']" routerLinkActive="active"></a>
    </div>

    <div class="dashboard-t-background-notifications">
        <div class="dashboard-background-notifications">
            <a [routerLink]="['/new-dashboard/notifications']" routerLinkActive="active">
                <i class="fa fa-bell-o"></i>
            </a>
        </div>
    </div> 
    <div class="dashboard-t-ai" [title]="'ai.title' | translate" *ngIf="aiLinkExists">
      <a [routerLink]="['/new-dashboard/ai']" routerLinkActive="active"></a>
    </div>

    <div class="dashboard-t-background-notifications">
      <div class="dashboard-t-settings" [title]="'settings' | translate">
        <a routerLinkActive="active" (click)="openSettingsModal()"></a>
      </div>
    </div>

    <div class="sidebar-toggle-container" *ngIf="isSmallScreen">
      <button
        type="button"
        class="sidebar-toggle"
        (click)="toggleSidebar()"
        [attr.aria-label]="isSidebarExpanded ? 'Collapse sidebar' : 'Expand sidebar'"
        [title]="isSidebarExpanded ? 'Collapse sidebar' : 'Expand sidebar'">
        <i class="fa" [ngClass]="isSidebarExpanded ? 'fa-chevron-left' : 'fa-chevron-right'"></i>
        <span class="sr-only">{{ isSidebarExpanded ? 'Collapse sidebar' : 'Expand sidebar' }}</span>
      </button>
    </div>

    <div class="sidebar-footer-space"></div>
  </div>

<app-settings #settingsModal></app-settings>
