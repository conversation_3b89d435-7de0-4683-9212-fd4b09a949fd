$red: #FF0000;
$green: #00D600;
$textColor: #808381;

:host {
    width: 100%;
    display: block;
}

.page-format {
    height: 100vh;
    display: flex;
    flex-direction: column;
    padding: 0px;
    overflow: hidden;
}

.content-wrapper {
    flex: 1;
    overflow: hidden;
    width: 100%;
    margin: 0 auto;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.iframe-wrapper {
    flex: 1;
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
}

.ai-iframe {
    width: 100%;
    height: 100%;
    border: none;
    flex: 1;
}

.loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;

    p {
        margin-top: 20px;
        font-size: 16px;
        color: #666;
    }
}

.spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: green;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}


