import { Component, OnInit, OnChanges, SimpleChanges, ViewChild, OnDestroy } from '@angular/core';
import { EmbeddedFileWidget } from 'app/layout/customer-dashboard/models/embedded-file-widget';
import { Dom<PERSON>anitizer, SafeResourceUrl } from '@angular/platform-browser';
import { Papa } from 'ngx-papaparse';
import { AppModal } from 'app/shared/components/app-modal/app-modal.component';
import { FileType } from 'app/shared/enum/file-type.enum';
import { StringService } from 'app/shared/services/string.service';
@Component({
  selector: 'app-embedded-file',
  templateUrl: './embedded-file.component.html',
  styleUrls: ['./embedded-file.component.scss']
})
export class EmbeddedFileComponent implements OnInit, OnDestroy{
  @ViewChild('document', {static: false}) document: AppModal;
  data: { index: number, widgetData: EmbeddedFileWidget };
  resourceLink: SafeResourceUrl = '';
  allowedFileEnum = FileType;
  constructor(public sanitizer: <PERSON><PERSON><PERSON><PERSON><PERSON>, private papa: Papa,
  private stringService: StringService) { }

  ngOnInit() {
    this.data.widgetData = new EmbeddedFileWidget(this.data.widgetData);
    if (this.data.widgetData.resource) {
      this.getData();
    }
  }

  getData(): void {
    let fileName : string = <string>this.data.widgetData.resource;
    let type = this.stringService.extractFileTypeFromUrlFileName(fileName);
    let dataURI = this.data.widgetData.resource;
    switch (type) {
      case FileType.pdf:
        this.generatePDF(dataURI);
        break;
      default:
        console.error('Type not found');
        break;
    }
  }


  generatePDF(dataURI): void {
    var byteString;
    if (dataURI.split(',')[0].indexOf('base64') >= 0)
      byteString = atob(dataURI.split(',')[1]);
    else
      byteString = unescape(dataURI.split(',')[1]);
    var mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];
    var ia = new Uint8Array(byteString.length);
    for (var i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }
    let blob = new Blob([ia], { type: mimeString });
    let link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    this.resourceLink = this.sanitizer.bypassSecurityTrustResourceUrl(link.href);
  }


  // It's not used but in the future will be - base64 to csv and parse
  generateCSV(resource): void {
    let data = resource.split(',').pop();
    var dataCSV = atob(data);
    this.papa.parse(dataCSV, {
      complete: (parsedData, file) => {
        let headerRow = parsedData.data.splice(0, 1)[0];
        let csvData = parsedData.data;
      }
    });
  }
  
  openDocument(): void{
    this.document.openModal();
  }

  close(): void{
    this.document.closeModal();
  }

  ngOnDestroy(): void {
    this.close();
  }

}
