<div class="iot-table">
    <p-table
    [value]="dossiers"
    [loading]="loading"
    [paginator]="true"
    [rows]="pageSize"
    [totalRecords]="totalRecords"
    [lazy]="true"
    [first]="pageIndex * pageSize"
    [showCurrentPageReport]="true"
    [(selection)]="selectedDossiers"
    (onPage)="onPageChange($event)"
    styleClass="p-datatable-striped">
    <ng-template pTemplate="header">
        <tr>
            <th>{{ 'dossierTable.name' | translate }}</th>
            <th>{{ 'dossierTable.timestamp' | translate }}</th>
            <th>{{ 'dossierTable.autoDelete' | translate }}</th>
            <th>{{ 'dossierTable.readOnly' | translate }}</th>
            <th>{{ 'dossierTable.deletionTime' | translate }}</th>
            <th *ngIf="showActions" style="text-align: right">{{ 'dossierTable.actions' | translate }}</th>
        </tr>
    </ng-template>

    <ng-template pTemplate="body" let-dossier>
        <tr>
            <td>
                <div class="flex">
                    <i class="fa fa-file-o"></i>
                    <div class="dossier-name">
                        <span class="bold">{{dossier.name}}</span>
                    </div>
                </div>
            </td>
            <td>{{dossier.timeStamp | iotTimestamp }}</td>
            <td>
                <span [class]="dossier.isDeletable ? 'text-green-500' : 'text-gray-500'">
                    {{dossier.isDeletable ? 'True' : 'False'}}
                </span>
            </td>
            <td>
                <span [class]="dossier.isReadonly ? 'text-green-500' : 'text-gray-500'">
                    {{dossier.isReadonly ? 'True' : 'False'}}
                </span>
            </td>
            <td>
                 {{ dossier.deletionTime? (dossier.deletionTime | iotTimestamp) : '–' }}
            </td>
            <td *ngIf="showActions" class="action-buttons">
                <button pButton
                    icon="fa fa-bell"
                    class="p-button-rounded p-button-text"
                    [class.active]="dossier.hasNotifications"
                    (click)="onActionClick(dossierAction.Search  , dossier)"
                    pTooltip="{{ 'dossierTable.viewDetections' | translate }}">
                </button>
                <button pButton
                    [icon]="dossier.isDeletable ? 'fa fa-lock' : 'fa fa-unlock'"
                    class="p-button-rounded p-button-text"
                    [class.active]="dossier.isDeletable"
                    [disabled]="dossier.deletionTime"
                    (click)="onActionClick(dossierAction.Menu, dossier)"
                    pTooltip="{{ 'dossierTable.toggleAutoDelete' | translate }}">
                </button>
                <button pButton
                    icon="fa fa-upload"
                    class="p-button-rounded p-button-text"
                    [disabled]="dossier.deletionTime"
                    (click)="onActionClick(dossierAction.Upload, dossier)"
                    pTooltip="{{ 'dossierTable.uploadDossier' | translate }}"
                    tooltipPosition="top">
                </button>
                <button pButton
                    icon="fa fa-download"
                    class="p-button-rounded p-button-text"
                    [disabled]="dossier.deletionTime"
                    (click)="onActionClick(dossierAction.Download, dossier)"
                    pTooltip="{{ 'dossierTable.downloadDossier' | translate }}"
                    tooltipPosition="top">
                </button>
                <button pButton
                    icon="fa fa-history"
                    class="p-button-rounded p-button-text"
                    (click)="onActionClick(dossierAction.History, dossier)"
                    pTooltip="{{ 'dossierTable.viewHistory' | translate }}"
                    tooltipPosition="top">
                </button>
                <button pButton
                    icon="fa fa-info-circle"
                    class="p-button-rounded p-button-text"
                    [disabled]="dossier.deletionTime"
                    (click)="onActionClick(dossierAction.Details, dossier)"
                    pTooltip="{{ 'dossierTable.viewDetails' | translate }}"
                    tooltipPosition="top">
                </button>
            </td>
        </tr>
    </ng-template>

    <ng-template pTemplate="emptymessage">
        <tr>
            <td colspan="6" class="text-center">{{ 'dossierTable.noDossiersFound' | translate }}</td>
        </tr>
    </ng-template>

</p-table>