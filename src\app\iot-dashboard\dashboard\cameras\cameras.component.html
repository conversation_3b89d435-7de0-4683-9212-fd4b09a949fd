<div class="cameras-container">
    <div class="player-container">
        <button class="nav-button nav-button-left" (click)="playPreviousChannel()">
            <img src="assets/public/assets/OouiNextLtr.svg" style="transform: rotate(180deg);" alt="Previous">
        </button>
        <app-player #player [showPlayerActions]="true" (CloseChannelEvent)="onCloseChannel($event)" (OpenChannelEvent)="onOpenChannel($event)"></app-player>
        <button class="nav-button nav-button-right" (click)="playNextChannel()">
            <img src="assets/public/assets/OouiNextLtr.svg" alt="Next">
        </button>
    </div>
   
</div>
<div class="channels-indicator">
    <div class="channels-dots">
        <div *ngFor="let channel of channels; let i = index" 
             class="channel-dot"
             [class.active]="i === currentChannelIndex"
             (click)="playChannel(i)">
        </div>
    </div>
</div>
