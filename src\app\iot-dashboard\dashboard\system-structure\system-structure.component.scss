.dashboard-rectangle-16 {
  height: 100%;
  width: 100%;
  background-color: #f2f6f3;
  border-radius: 24px;
  padding: 20px;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.system-structure-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  font-family: "Rounded Mplus 1c Bold", ui-sans-serif, system-ui, sans-serif;
  overflow: hidden;
  position: relative; 
}

.system-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;

  h2 {
    font-size: 22px;
    color: #3a3a3a;
    margin: 0;
    font-weight: 600;
  }
}

.refresh-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.refresh-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s;
  border-radius: 50%;

  &:hover {
    background-color: rgba(0, 214, 3, 0.1);
  }

  i {
    font-size: 18px;
    color: #3a3a3a;
  }

  .spinning {
    animation: spinRefresh 1s ease-in-out;
  }
}

.refresh-timer {
  font-size: 14px;
  color: #666;
}

.camera-status-summary {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.status-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  flex: 1;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  }
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  &.online {
    background-color: rgba(46, 213, 115, 0.1);

    i {
      color: #2ed573;
    }
  }

  &.offline {
    background-color: rgba(255, 71, 87, 0.1);

    i {
      color: #ff4757;
    }
  }

  i {
    font-size: 20px;
  }
}

.status-info {
  display: flex;
  flex-direction: column;
}

.status-count {
  font-size: 24px;
  font-weight: bold;
  color: #3a3a3a;
}

.status-label {
  font-size: 14px;
  color: #666;
}

.camera-logs {
  background-color: #ffffff;
  border-radius: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  min-height: 0;
  height: calc(100% - 20px); 
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
  height: 50px;

  h3 {
    margin: 0;
    font-size: 18px;
    color: #3a3a3a;
  }

  .clear-logs-btn {
    padding: 8px 16px;
    background: rgba(0, 214, 3, 0.1);
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: inherit;
    font-size: 14px;
    color: #3a3a3a;

    &:hover {
      background: #00d603;
      color: white;
    }

    i {
      font-size: 14px;
    }
  }
}

.logs-container {
  flex: 1;
  overflow-y: scroll !important;
  padding: 15px;
  min-height: 0;
  display: block;
  height: 350px;
  max-height: 350px;
}

.logs-wrapper {
      height: 100%;
}

.log-entry {
  padding: 15px;
  margin-bottom: 15px;
  background: #f9f9f9;
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  }

  &.manual-refresh {
    border-left: 4px solid #00d603;
  }

  .log-timestamp {
    color: #666;
    font-size: 12px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;

    i {
      color: #00d603;
    }
  }

  .log-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
  }

  .log-stat {
    padding: 10px;
    border-radius: 8px;
    font-size: 12px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    text-align: center;

    .stat-value {
      font-size: 18px;
      font-weight: bold;
    }

    .stat-label {
      color: #666;
      font-size: 12px;
    }

    &.online {
      background-color: rgba(46, 213, 115, 0.1);
      .stat-value { color: #2ed573; }
    }

    &.offline {
      background-color: rgba(255, 71, 87, 0.1);
      .stat-value { color: #ff4757; }
    }

    &.error {
      background-color: rgba(255, 159, 67, 0.1);
      .stat-value { color: #ff9f43; }
    }

    &.no-comm {
      background-color: rgba(108, 92, 231, 0.1);
      .stat-value { color: #6c5ce7; }
    }
  }
}

@keyframes spinRefresh {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Animation for new logs
@keyframes logEntryAnimation {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.log-entry {
  animation: logEntryAnimation 0.3s ease-out;
}

