<div class="dashboardSettings">
    <app-edit-widget [selectedWidget]="data"></app-edit-widget>

    <div class="form-item">
        <h2>{{ 'chartType' | translate }}</h2>
        <p-dropdown [options]="chartTypeOptions" [(ngModel)]="data.chartType" [styleClass]="'input-element'"
            (onChange)="onWidgetDataChange()">
            <ng-template let-item pTemplate="selectedItem">
                <span>{{item.label| translate}}</span>
            </ng-template>
            <ng-template let-item pTemplate="item">
                <div class="ui-helper-clearfix">
                    <div>{{item.label | translate}}</div>
                </div>
            </ng-template>
        </p-dropdown>
    </div>

    <div class="form-item">
        <h2>{{ 'customerDashboard.chartDataType' | translate }}</h2>
        <p-dropdown [options]="lineChartDataTypeList" [(ngModel)]="data.lineChartDataType"
            [styleClass]="'input-element'" (onChange)="onWidgetDataChange()">
            <ng-template let-item pTemplate="selectedItem">
                <span>{{item.label| translate}}</span>
            </ng-template>
            <ng-template let-item pTemplate="item">
                <div class="ui-helper-clearfix">
                    <div>{{item.label | translate}}</div>
                </div>
            </ng-template>
        </p-dropdown>
    </div>

    <div class="form-item" *ngIf="data.lineChartDataType === 0">
        <h2>{{ 'eventName' | translate }}</h2>
        <p-multiSelect [options]="eventsNameData" [filter]="true" filterBy="label" [(ngModel)]="data.selectedEventsIds"
            [styleClass]="'input-element'" (onChange)="onWidgetDataChange()"
            selectedItemsLabel="{{ 'nrItemsSelected' | translate }}" defaultLabel="{{ 'selectEvent' | translate }}"
            [maxSelectedLabels]="0">
            <ng-template let-event pTemplate="item">
                {{ event.label }}
            </ng-template>
        </p-multiSelect>
    </div>

    <div class="form-item">
        <h2>{{ 'customerDashboard.selectGroup' | translate }}</h2>
        <p-dropdown [options]="groupList" [filter]="true" filterBy="label" [(ngModel)]="data.selectedGroupId"
            [styleClass]="'input-element'" (onChange)="onWidgetDataChange()">
            <ng-template let-item pTemplate="selectedItem">
                <span>{{item.label| translate}}</span>
            </ng-template>
            <ng-template let-item pTemplate="item">
                <div class="ui-helper-clearfix">
                    <div>{{item.label | translate}}</div>
                </div>
            </ng-template>
        </p-dropdown>
    </div>

    <div class="form-item">
        <h2>{{ 'customerDashboard.selectResource' | translate }}</h2>
        <p-multiSelect [options]="resourceList" [filter]="true" filterBy="label" [(ngModel)]="data.selectedResourceIds"
            [styleClass]="'input-element'" (onChange)="onWidgetDataChange()"
            selectedItemsLabel="{{ 'nrItemsSelected' | translate }}"
            defaultLabel="{{ 'customerDashboard.selectResource' | translate }}" [maxSelectedLabels]="0"
            [virtualScroll]="true" itemSize="30">
            <ng-template let-item pTemplate="item">
                {{item.label | translate}}
            </ng-template>
        </p-multiSelect>
    </div>

    <div class="form-item" *ngIf="data.lineChartDataType === 1">
        <h2>{{ 'customerDashboard.selectTrigger' | translate }}</h2>
        <p-multiSelect [options]="triggersList" [(ngModel)]="data.selectedTriggers" [styleClass]="'input-element'"
            (onChange)="onWidgetDataChange()" selectedItemsLabel="{{ 'nrItemsSelected' | translate }}"
            defaultLabel="{{ 'customerDashboard.selectTrigger' | translate }}" [maxSelectedLabels]="0">
            <ng-template let-item pTemplate="item">
                {{item.label | translate}}
            </ng-template>
        </p-multiSelect>
    </div>

    <div class="form-item" *ngIf="data.lineChartDataType === 2">
        <h2>{{ 'customerDashboard.selectResourceState' | translate }}</h2>
        <p-multiSelect [options]="statusList" [(ngModel)]="data.selectedResourceStates" [styleClass]="'input-element'"
            (onChange)="onWidgetDataChange()" selectedItemsLabel="{{ 'nrItemsSelected' | translate }}"
            defaultLabel="{{ 'customerDashboard.selectResourceState' | translate }}" [maxSelectedLabels]="0">
            <ng-template let-item pTemplate="item">
                {{item.label | translate}}
            </ng-template>
        </p-multiSelect>
    </div>

    <div class="form-item">
        <h2>{{ 'customerDashboard.displayMethod' | translate }}</h2>
        <p-dropdown [options]="displayResultList" [(ngModel)]="data.displayResultsMethod"
            (onChange)="selectDisplayResult($event.value)"
            placeholder="{{'customerDashboard.displayMethod' | translate}}" #displayResultsMethod="ngModel">
            <ng-template let-item pTemplate="selectedItem">
                <span>{{item.label| translate}}</span>
            </ng-template>
            <ng-template let-item pTemplate="item">
                <div class="ui-helper-clearfix">
                    <div>{{item.label | translate}}</div>
                </div>
            </ng-template>
        </p-dropdown>
    </div>

    <div class="form-item">
        <h2>{{ 'customerDashboard.selectTimePeriod' | translate }}</h2>
        <p-dropdown [options]="dateFilterOptions" [(ngModel)]="data.selectedDateRangeType"
            [styleClass]="'input-element'" (onChange)="onWidgetDataChange()">
            <ng-template let-item pTemplate="selectedItem">
                <span>{{item.label| translate}}</span>
            </ng-template>
            <ng-template let-item pTemplate="item">
                <div class="ui-helper-clearfix">
                    <div>{{item.label | translate}}</div>
                </div>
            </ng-template>
        </p-dropdown>
    </div>

    <div class="form-item multiple">
        <div class="group">

            <app-datetime-picker *ngIf="data.selectedDateRangeType === 0" [(date)]="data.fromDate"
                [showDateInForm]="true" [appendTo]="'body'" placeholder='{{ "fromDate" | translate }}'
                [styleClass]="'widget-date'"
                (valueChange)="onDatePickerValueChange($event, 'from')"></app-datetime-picker>

            <input *ngIf="data.selectedDateRangeType === 1" type="number" name="last" [(ngModel)]="data.unitLastValue"
                (ngModelChange)="unitLastValue$.next($event)" />

        </div>
        <div class="group" [ngClass]="{'full': data.selectedDateRangeType === 2}">

            <app-datetime-picker *ngIf="data.selectedDateRangeType === 0" [(date)]="data.toDate" [showDateInForm]="true"
                [appendTo]="'body'" placeholder='{{ "toDate" | translate }}' [styleClass]="'widget-date'"
                (valueChange)="onDatePickerValueChange($event, 'to')"></app-datetime-picker>

            <app-datetime-picker *ngIf="data.selectedDateRangeType === 2" [(date)]="data.currentDate" [showTime]="false"
                [showDateInForm]="true" [appendTo]="'body'" placeholder='{{ "selectDate" | translate }}'
                [styleClass]="'widget-date'"
                (valueChange)="onDatePickerValueChange($event, 'current')"></app-datetime-picker>

            <p-dropdown *ngIf="data.selectedDateRangeType === 1" [options]="dateFilterLastOptions"
                [(ngModel)]="data.selectedDateUnitLast" [styleClass]="'input-element'"
                (onChange)="onWidgetDataChange()">
                <ng-template let-item pTemplate="selectedItem">
                    <span>{{item.label| translate}}</span>
                </ng-template>
                <ng-template let-item pTemplate="item">
                    <div class="ui-helper-clearfix">
                        <div>{{item.label | translate}}</div>
                    </div>
                </ng-template>
            </p-dropdown>
        </div>
    </div>
</div>