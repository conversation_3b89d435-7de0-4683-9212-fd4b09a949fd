<div class="filters-container">
    <div class="filters-wrapper">
        <div class="title">
            {{ 'dossierTable.title' | translate }}
        </div>

        <div class="filters-content">
            <div class="filter-element-container">
                <div class="filter-element">
                    <i class="fa fa-search green"></i>
                    <input type="text" pInputText placeholder="{{ 'dossierTable.searchByName' | translate }}"
                        [(ngModel)]="searchQuery" (input)="onSearch()" />
                </div>
            </div>

            <div class="filter-element-container">
                <div class="filter-element">
                    <i class="fa fa-trash-o red"></i>
                    <span>{{ 'dossierTable.autoDelete' | translate }}</span>
                     <p-dropdown [options]="autoDeleteOptions" [(ngModel)]="selectedAutoDelete" 
                        (onChange)="onFilterChange()" appendTo="body" placeholder="{{ 'dossierTable.all' | translate }}"
                        optionLabel="label" optionValue="value">
                    </p-dropdown> 
                </div>
            </div>

            <div class="filter-element-container">
                <div class="filter-element">
                    <img src="assets/public/assets/readOnly.svg" class="dossier-read-only" alt="dossier read only" />
                    <span>{{ 'dossierTable.readOnly' | translate }}</span>
                    <p-dropdown [options]="readOnlyOptions" [(ngModel)]="selectedReadOnly" 
                        (onChange)="onFilterChange()" appendTo="body">
                    </p-dropdown>
                </div>
            </div>

            <div class="filter-element-container">
                <div class="filter-element">
                    <i class="fa fa-calendar green"></i>
                    <span>{{ 'dossierTable.startTime' | translate }}</span>

                    <p-calendar [(ngModel)]="startDate" [showTime]="true" [showSeconds]="true"
                        dateFormat="dd/mm/yy" placeholder="--/--/----" (onSelect)="onDateSelect()" appendTo="body">
                    </p-calendar>
                </div>
            </div>

            <div class="filter-element-container">
                <div class="filter-element">
                    <i class="fa fa-calendar green"></i>
                    <span>{{ 'dossierTable.endTime' | translate }}</span>
                    <p-calendar [(ngModel)]="endDate" [showTime]="true" [showSeconds]="true"
                        dateFormat="dd/mm/yy" placeholder="--/--/----" (onSelect)="onDateSelect()" appendTo="body">
                    </p-calendar>
                </div>
            </div>

            <div class="filter-element">
                <button pButton class="p-button-rounded p-button-text "
                        [pTooltip]="'dossierTable.reset' | translate" [tooltipOptions]="tooltipOptions"
                        [disabled]="isResetDisabled" (click)="resetFilterTable()">
                    <i class="fa fa-refresh red"></i>
                </button>

            </div>
        </div>
    </div>
</div> 