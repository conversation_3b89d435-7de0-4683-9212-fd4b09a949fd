import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { PieChartWidget } from 'app/layout/customer-dashboard/models/pie-chart-widget';
import { DefaultWidgetEditorComponent } from '../../default-widget/default-widget-editor.component';
import { ResourceGroupService } from 'app/shared/modules/data-layer/services/resource-group/resource-group.service';
import { SelectItem } from 'primeng/api';
import { Guid } from 'app/shared/enum/guid';
import { DashboardUtilsService } from 'app/layout/customer-dashboard/services/dashboard-utils.service';
import { PieChartData } from 'app/layout/customer-dashboard/enums/pie-chart-data.enum';
import { ResourceTriggersService } from 'app/shared/modules/data-layer/services/resource-triggers/resource-triggers.service';
import { ResourceTriggerGroup } from 'app/shared/modules/data-layer/models/resource-trigger-group';

import { Subject, EMPTY, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';
import { ResourceGroup } from 'app/shared/modules/data-layer/models/resource-group';
import { ResourceState } from 'app/shared/modules/data-layer/enum/resource/resource-state.enum';
import {DateRangeSearchType,DateUnit} from "app/shared/enum/enum";


@Component({
  selector: 'app-edit-pie-chart-widget',
  templateUrl: './edit-pie-chart-widget.component.html',
  styleUrls: ['./edit-pie-chart-widget.component.scss']
})
export class EditPieChartWidgetComponent extends DefaultWidgetEditorComponent implements OnInit, OnDestroy {
  data: PieChartWidget;

  groupList: SelectItem[] = [{label: "customerDashboard.showAllGroups", value: Guid.EMPTY}];
  resourceStateList: SelectItem[] = [];
  triggersList: SelectItem[] = [];

  pieChartDataTypeList: SelectItem[] = [
    {label: "customerDashboard.selectDataType", value: Guid.EMPTY},
    {label: "customerDashboard.siteReadiness", value: PieChartData.siteReadiness},
    {label: "customerDashboard.tasks", value: PieChartData.tasks},
    {label: "customerDashboard.tasksUrgency", value: PieChartData.tasksUrgency},
    {label: "triggers", value: PieChartData.triggers}
  ];

  dateFilterOptions: SelectItem[] = [
    { label: "latest", value: DateRangeSearchType.Latest },
    { label: "date", value: DateRangeSearchType.Date },
    { label: "last", value: DateRangeSearchType.Last },
    { label: "dateRange", value: DateRangeSearchType.Range,}
  ];

  dateFilterLastOptions: SelectItem[] = [
    { label: "seconds", value: DateUnit.Seconds },
    { label: "minutes", value: DateUnit.Minutes },
    { label: "hours", value: DateUnit.Hours },
    { label: "days", value: DateUnit.Days},
    { label: "weeks", value: DateUnit.Weeks },
    { label: "months", value: DateUnit.Months }
  ];

  unitLastValue$ = new Subject<string>();
  subscriptions: Subscription[] = [];

  constructor(
    private resourceGroupService: ResourceGroupService,
    private dashboardUtilsService: DashboardUtilsService,
    private resourceTriggersService: ResourceTriggersService
  ) {
    super()
  }

  ngOnInit() {
    this.data = new PieChartWidget(this.data);

    let searchTermSubscription = this.unitLastValue$.pipe(
      debounceTime(500),
      distinctUntilChanged(),
      switchMap(number => {
        this.onDatePickerValueChange(number, 'last');
        return EMPTY;
      })
    ).subscribe();
    this.subscriptions.push(searchTermSubscription);

    this.getData();
  }

  ngOnDestroy(){
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
  }

  getData(){
    switch(this.data.pieChartDataType){
      case PieChartData.siteReadiness:
        this.getResourceGroups();
        break;
      case PieChartData.triggers:
        this.getUserTriggers();
        this.getResourceGroups();
        this.data.fromDate = this.data.fromDate ? new Date(this.data.fromDate) : new Date();
        this.data.toDate = this.data.toDate ? new Date(this.data.toDate) : new Date();
        this.data.currentDate = this.data.currentDate ? new Date(this.data.currentDate) : new Date();
        break;
    }
  }


  onWidgetDataChange(){
    this.getData();
    this.dashboardUtilsService.setWidgetDataChange(this.data);
  }

  private getResourceGroups(): void {
    let resourceGroupSubscription =this.resourceGroupService.getAll().subscribe(res => {
      this.buildSiteReadinessData(res);
    });
    this.subscriptions.push(resourceGroupSubscription);
  }

  private getUserTriggers(): void {
   let resourceTriggerSubscription= this.resourceTriggersService.getAll().subscribe(res => {
       const resourceTriggerMap = new Map<string, ResourceTriggerGroup>(Object.entries(res));
      this.buildTriggersData(resourceTriggerMap);
    })
    this.subscriptions.push(resourceTriggerSubscription);
  }

  private buildSiteReadinessData(data:{[id:string]:ResourceGroup}): void {
    this.groupList = this.groupList.slice(0,1);
    this.resourceStateList = [];
    for (const key in data) {
        this.groupList.push({label: data[key].name, value: data[key].identity})
    }

    for(let state in ResourceState){
      this.resourceStateList.push({label: state, value: state})
    }
  }

    private buildTriggersData(data: Map<string, ResourceTriggerGroup>): void {
        this.triggersList = [];
        data.forEach((group, key) => {
            if (group && group.Triggers) {
                group.Triggers.forEach(trigger => {
                    this.triggersList.push({ label: trigger.Name, value: trigger.SurpriseCode });
                });
            }
        });
    }

  onDatePickerValueChange(val, model): void {
    switch(model){
      case 'from':
        this.data.fromDate = val;
        break;
      case 'to':
        this.data.toDate = val;
        break;
      case 'current':
        this.data.currentDate = val;
        break;
      case 'last':
        this.data.unitLastValue = val;
        break;
    }
    this.dashboardUtilsService.setWidgetDataChange(this.data);
  }
}
