import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TemplateRef, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SensorStatusWidget } from 'app/layout/customer-dashboard/models/sensor-status-widget';
import { WidgetSettingsService } from 'app/layout/customer-dashboard/services/widget-settings.service';
import { StatusTableCell } from 'app/shared/components/ng-turbo-table/models/status-table-element';
import { TablePaginatorEvent } from 'app/shared/components/ng-turbo-table/models/table-paginator-event.interface';
import { TableCell, TableColumnProperties, TableFilterMatchMode } from 'app/shared/components/ng-turbo-table/models/table.models';
import { ResourceStatusComponent } from 'app/shared/components/resource-status/resource-status.component';
import { Guid } from 'app/shared/enum/guid';
import { ServerTypes } from 'app/shared/modules/data-layer/enum/resource/resource-server-types.enum';
import { ResourceState } from 'app/shared/modules/data-layer/enum/resource/resource-state.enum';
import { Resource } from 'app/shared/modules/data-layer/models/resource';
import { ResourcePort } from "app/shared/modules/data-layer/services/resource/resource-port";
import { ResourceCacheService } from "app/shared/modules/data-layer/services/resource/resource.cache.service";
import * as _ from "lodash";
import { SelectItem } from 'primeng/api';
import { Subscription } from 'rxjs';
import { DefaultWidgetComponent } from '../default-widget/default-widget.component';
import { sensorStatusActiveColumns } from './active-columns';
@Component({
  selector: 'app-sensor-status-widget',
  templateUrl: './sensor-status-widget.component.html',
  styleUrls: ['./sensor-status-widget.component.scss']
})
export class SensorStatusWidgetComponent extends DefaultWidgetComponent implements OnInit, OnDestroy {
  @ViewChild('widgetWrapper', {static: false}) widgetWrapper: ElementRef;
  @ViewChild('statusCell', {static: true}) statusCell: TemplateRef<ResourceStatusComponent>;
  columns: TableColumnProperties[] = [];
  availableDevices: Resource[] = [];
  subscriptions: Subscription[] = [];
  filteredData: { [columnField: string]: TableCell | string }[] = [];
  data: { index: number, widgetData: SensorStatusWidget, widgetSettings: {[propertyName: string]: string }};

  constructor(
    private translateService: TranslateService,
    private resourceService: ResourcePort,
    private widgetSettingsService: WidgetSettingsService,
    private resourceCacheService: ResourceCacheService
  ) {
    super();
  }

  ngOnInit(): void {

    this.data.widgetData = new SensorStatusWidget(this.data.widgetData);
    this.getAllData();
  }

  getAllData(): void{
      let resourcesSubscription = this.resourceCacheService.storageCompleted.subscribe(() =>{
          let resources = this.resourceCacheService.getAll()

          this.availableDevices = this.filterResources(resources);
          const result: { data: { [columnField: string]: TableCell | string }[],fields: { [key: string]: boolean } } = this.getFlattenObject(this.availableDevices);
          this.filteredData = result.data;
          this.generateColumnsTable(result.fields);
      });
      this.subscriptions.push(resourcesSubscription);
  }

  filterResources(data: Resource[]): Resource[]{
    let resources: Resource[] = [];
      data.filter((item) => {
        let groupsBool = (this.data.widgetData.selectedGroupId !== Guid.EMPTY)?
                item.groups.some(group => {return group.identity === this.data.widgetData.selectedGroupId;}) : true;
        let statusBool = (this.data.widgetData.resourceStates && this.data.widgetData.resourceStates.length) > 0 ?
              (this.data.widgetData.resourceStates.includes(item.status) ? true : false) : false;
      return (groupsBool || statusBool);
      }).forEach(e => {
          resources.push(e);
      });
      return resources;


  }

  getFlattenObject(data: Resource[]): {data: { [columnField: string]: TableCell | string }[], fields: {[key: string]: boolean}} {
    const results = [];
    const fields = {};
    data.map((item: Resource) => {
      let temp: { [columnField: string]: TableCell | string } = {};
      temp.name = item.name;
      fields['name'] = true;
      temp.type = this.translateService.instant(ServerTypes[item.resourceType].toString());
      fields['type'] = true;
      temp.status = new StatusTableCell(this.statusCell, item.status);
      fields['status'] = true;
      results.push(temp);
    });
    return {
      data: results,
      fields: fields
    };
  }


  generateColumnsTable(fields: {[key: string]: boolean}): void {
    const keys = Object.keys(fields);
    keys.map((key: string) => {
      this.columns.push(new TableColumnProperties({
        field: key,
        header: key,
        visibile: sensorStatusActiveColumns[key] ? sensorStatusActiveColumns[key].visibile : false,
        frozen: sensorStatusActiveColumns[key] ? sensorStatusActiveColumns[key].frozen : false,
        isFilterable: sensorStatusActiveColumns[key] ? sensorStatusActiveColumns[key].isFilterable : true,
        sortable: sensorStatusActiveColumns[key] ? sensorStatusActiveColumns[key].sortable : true,
        filterOptions: this.returnFilterOptions(key),
        filterMatchMode: sensorStatusActiveColumns[key] ? sensorStatusActiveColumns[key].filterMatchMode : TableFilterMatchMode.contains,
      }));
    });
  }

  returnFilterOptions(key: string): SelectItem[] {
    let filterOptions: SelectItem[] = [];
    switch (key) {
      case 'status':
        for (let state in ResourceState) {
          filterOptions.push({ label: state, value: state });
        }
        break;
      case 'type':
        for (let item in ServerTypes) {
          if (!isNaN(Number(item))) {
            filterOptions.push({ label: ServerTypes[item], value: this.translateService.instant(ServerTypes[item].toString()) });
          }
        }
        break;
      default:
        break;
    }
    return filterOptions;
  }

  onTurboTablePaginatorEvent(event: TablePaginatorEvent): void {
    let widgetSettings: {key: string, value:string}[] = [
      {key: 'numOfRows', value: event.rows.toString()},
      {key: 'first', value: event.first.toString()}
    ];
    this.widgetSettingsService.setWidgetSettings(this.data.widgetData.id, widgetSettings);
  }

  ngOnDestroy(): void{
    this.subscriptions.forEach(subscription => {return subscription.unsubscribe();});
  }
}
