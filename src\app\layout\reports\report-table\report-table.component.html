<div class="iot-table" >
    <div class="table-controls">
        <div class="results-count" *ngIf="totalRecords > 0">
            <label class="result-counter">
                {{ 'resultsAmount' | translate }} {{ totalRecords }}
            </label>
        </div>

        <div class="column-selector" *ngIf="tableColumns && tableColumns.length > 0">
            <p-multiSelect
                [options]="tableColumns"
                [(ngModel)]="visibleColumns"
                optionLabel="header"
                selectedItemsLabel="{{ 'colsSelected' | translate }}"
                defaultLabel="{{ 'selectCol' | translate }}"
                [style]="{minWidth: '200px', width: '200px'}"
                styleClass="p-small"
                [filter]="true"
                filterBy="header"
                (onChange)="onColumnToggle()">
                <ng-template let-column pTemplate="item">
                    {{ column.header || column.field | translate }}
                </ng-template>
                <ng-template pTemplate="footer">
                    <div class="column-footer">
                        <button pButton label="{{ 'reset' | translate }}" class="p-button-text p-button-sm" (click)="resetColumnSelection($event)"></button>
                        <button pButton label="{{ 'selectAll' | translate }}" class="p-button-text p-button-sm" (click)="selectAllColumns($event)"></button>
                    </div>
                </ng-template>
            </p-multiSelect>
        </div>
    </div>

    <p-table
        #dt
        [value]="tableData"
        [loading]="loading"
        [paginator]="true"
        [rows]="pageSize"
        [totalRecords]="totalRecords"
        [lazy]="false"
        [first]="pageIndex * pageSize"
        [showCurrentPageReport]="true"
        (onPage)="onPageChange($event)"
        styleClass="p-datatable-striped"
        [reorderableColumns]="true"
        [resizableColumns]="true"
        (onFilter)="onFilter($event)">

        <ng-template pTemplate="header">
            <tr>
                <ng-container *ngFor="let col of visibleColumns">
                    <th [pSortableColumn]="col.field" pReorderableColumn>
                        {{ col.header || col.field | translate }}
                        <p-sortIcon [field]="col.field"></p-sortIcon>

                        <!-- Filter Icon -->
                        <i *ngIf="col.isFilterable" class="fa fa-filter filter-icon"
                           (click)="openFilter($event, col, filterOverlay)"></i>
                    </th>
                </ng-container>
            </tr>
        </ng-template>

        <ng-template pTemplate="body" let-rowData>
            <tr>
                <ng-container *ngFor="let col of visibleColumns">
                    <td>
                        <div class="flex" *ngIf="col.field === visibleColumns[0].field; else regularCell">
                            <i class="fa fa-file-text-o"></i>
                            <span class="bold">{{ rowData[col.field] }}</span>
                        </div>
                        <ng-template #regularCell>
                            <span>{{ rowData[col.field] }}</span>
                        </ng-template>
                    </td>
                </ng-container>
            </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage">
            <tr>
                <td [attr.colspan]="visibleColumns.length" class="text-center">{{ 'noData' | translate }}</td>
            </tr>
        </ng-template>

        <ng-template pTemplate="paginatorleft">
            <div class="export-buttons">
                <button pButton
                    icon="fa fa-file-pdf-o"
                    class="p-button-rounded p-button-text"
                    (click)="exportToPDF()"
                    pTooltip="{{ 'exportPDF' | translate }}">
                </button>
                <button pButton
                    icon="fa fa-file-excel-o"
                    class="p-button-rounded p-button-text"
                    (click)="exportToCSV()"
                    pTooltip="{{ 'exportToCSV' | translate }}">
                </button>
            </div>
        </ng-template>
    </p-table>
</div>

<!-- Filter Overlay Panel -->
<p-overlayPanel #filterOverlay [showTransitionOptions]="'20ms'" [hideTransitionOptions]="'20ms'">
    <div class="filter-content">
        <ng-container *ngIf="currentFilterColumn && !currentFilterColumn.filterOptions">
            <input type="text"
                   pInputText
                   placeholder="{{ currentFilterColumn.header | translate }}"
                   [(ngModel)]="filterValues[currentFilterColumn.field]"
                   (input)="dt.filter($event.target.value, currentFilterColumn.field, 'contains')"
                   class="p-small input-filter" />
        </ng-container>

        <ng-container *ngIf="currentFilterColumn && currentFilterColumn.filterOptions && currentFilterColumn.filterOptions.length > 0">
            <div class="multiselect-container">
                <p-multiSelect
                    [(ngModel)]="filterValues[currentFilterColumn.field]"
                    [options]="currentFilterColumn.filterOptions"
                    styleClass="p-small"
                    [inputId]="currentFilterColumn.field"
                    selectedItemsLabel="{{ 'nrItemsSelected' | translate }}"
                    defaultLabel="{{ currentFilterColumn.header | translate }}"
                    [maxSelectedLabels]="0"
                    (onChange)="dt.filter($event.value, currentFilterColumn.field, 'in')">
                    <ng-template let-item pTemplate="item">
                        {{ item.label | translate }}
                    </ng-template>
                </p-multiSelect>
            </div>
        </ng-container>
    </div>
</p-overlayPanel>
