import { URI } from 'app/shared/enum/uri';
import * as moment from 'moment';
import {Icons,TableButtonType,TriggerTypes} from "app/shared/enum/enum";


export class EventData {
    eventId;
    eventDescription;
    timestamp;
    deviceId;
    resourceName;
    images = [];
    confidence;
    entityValue;
    entityInfo;
    resourceInfo = [];
    resourceActions :URI[];
    triggerCode;
    userId?:string;
    events?:EventData[];
    DynamicAttributesJsonString : string;
    description:string;


    constructor(eventId: number, description: string, timestamp: string, deviceId: string, resource: string, confidence: number,
    entityValue: string, images: any, entityInfo: any, resourceInfo: any, triggerCode: any, private unknownEntityID:string,extraData:string,userId?:string) {
        this.eventId = eventId;
        this.eventDescription = description;
        this.timestamp = moment(timestamp);
        this.deviceId = deviceId;
        this.resourceName = resource;
        this.confidence = confidence;
        this.entityValue = entityValue;
        this.triggerCode = triggerCode;
        this.DynamicAttributesJsonString=extraData;
        if (this.triggerCode === TriggerTypes.IdTag) {
            entityInfo ? this.setEntityInfo(entityInfo) : this.setIdTagEntity(images);
        } else {
            this.setEntityInfo(entityInfo);
            this.setImages(images);
        }
        this.resourceActions = resourceInfo.map(res => new URI(res));
        this.userId=userId
    }

    setIdTagEntity(images) {
        this.images.push({ source: "data:image/png;base64, " + images.carImage, alt: "primeryImage", title: this.entityValue });
        this.resourceInfo.push({
            name: "addNewEntity",
            new: true,
            icon: Icons.AddEntity,
            entity: {
                Img: "data:image/png;base64, " + images.carImage
            },
            type: TableButtonType.AddEntity
        });
    }

    setImages(images) {
        if (images.carImage)
        {
            this.images.push({ source: "data:image/png;base64, " + images.carImage, alt: "primeryImage", title: this.entityValue });
        }

        if (images.plateImage)
        {
            this.images.push({ source: "data:image/png;base64, " + images.plateImage, alt: "secImage", title: this.entityValue });
        }

        if (images.length && images.length > 0)
        {
            this.images.push({ source: "data:image/png;base64, " + images[0], alt: "cropImage", title: this.entityValue });
        }
    }

    setEntityInfo(entityInfo) {
        if (entityInfo) {
            if (entityInfo.Img) {
                this.images.push({ source: entityInfo.Img, alt: "entityImage", title: entityInfo.FirstName + " " + entityInfo.LastName });
            }
            this.entityInfo = entityInfo;
            this.resourceInfo.push({
                name: entityInfo.FirstName + " " + entityInfo.LastName,
                icon: Icons.EditEntity,
                new: false,
                entity: this.entityInfo,
                type: TableButtonType.AddEntity
            });
        } else {
            this.resourceInfo.push({
                name: "addNewEntity",
                new: true,
                icon: Icons.AddEntity,
                entity: {},
                type: TableButtonType.AddEntity
            });
        }
    }

    get UnknownEntityID()
    {
        return this.unknownEntityID;
    }
}
