<div class="filters-container">
    <div class="filters-wrapper">
        
        <div class="title">
                {{ 'detectionList.detectionList' | translate }}
        </div>
        <div class="filters-content">
            <div class="filter-element-container">
                <div class="filter-element">
                    <i class="fa fa-search green"></i>
                    <input type="text" pInputText placeholder="{{ 'detectionList.searchSpecific' | translate }}"
                        [(ngModel)]="searchQuery" (input)="onSearch()" />
                </div>
            </div>

            <div class="filter-element reset-button">
                <button pButton class="p-button-rounded p-button-text "
                        [pTooltip]="'detectionList.reset' | translate" [tooltipOptions]="tooltipOptions"
                        [disabled]="isResetDisabled" (click)="resetFilterTable()">
                    <i class="fa fa-refresh red"></i>
                </button>
            </div>
        </div>
    </div>
</div>
