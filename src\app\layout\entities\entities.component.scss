.lm_header .lm_tab {
    font-size: 0.75rem;
}
.entities-container {
    padding: 30px;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    overflow-x: scroll;
}
.entityPhoto{
    width: 50px;
    height: 50px;
}

.entityListItem{
    display: flex;
    margin-bottom: 30px;
    -webkit-box-shadow: 5px 5px 5px 4px rgba(0,0,0,0.1);
    box-shadow: 5px 5px 5px 4px rgba(0,0,0,0.1);
    border-radius: 4px;
    border: 1px solid #ddd;
}
.pi-bars,.pi-th-large {
    font-size:44px;
}
.pi-th-large{
    margin-left: 8px !important;
}

.p-dialog {
    width: 500px !important;
}
:host ::ng-deep .custom-dialog {
    .p-dialog {
        box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
    }

    .p-dialog-header {
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
    }

    .p-dialog-content {
        padding: 2rem;
    }

    .p-dialog-footer {
        padding: 1rem;
        border-top: 1px solid #dee2e6;
    }
}

:host ::ng-deep .p-dialog-mask {
    background-color: rgba(0, 0, 0, 0.4);
}
.entity-list-detail {
    display: flex;
    align-items: center;
}

.headerRow{
    display: flex;
    justify-content: space-between;
}

.product-name{
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-top: 20px;
}
.entity-list-detail img {
    margin-right: 50px;
    height: 200px;
    width: 200px;
    margin-left: 50px;
}
.ui-g{
    height:720px !important;
    overflow-x: scroll !important;
}
.entity-grid-item-content img{
    width: 250px;
    height: 250px;
    margin-left: auto;
    margin-right: auto;
    display: block;
    margin-bottom:20px;
}

.card {
    padding: 2rem;
    box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14), 0 1px 3px 0 rgba(0, 0, 0, 0.12);
    border-radius: 4px;
    margin-bottom: 2rem;
}
.gridDataview{
    width:100%;
}
.buttonsGrid{
    display: flex;
    justify-content: flex-end;
    padding-right: 51px;
}

.ui-dataview .ui-dataview-emptymessage {
    padding: .5em .75em;
    text-align: center;
    font-size: 25px;
    margin-top: 50px;
}

@media screen and (max-width: 576px) {
	:host ::ng-deep .product-list-item {
		flex-direction: column;
		align-items: center;

		img {
			width: 75%;
			margin: 2rem 0;
		}

		.product-list-detail {
			text-align: center;
		}

		.product-price {
			align-self: center;
		}

		.product-list-action {
			display: flex;
			flex-direction: column;
		}

		.product-list-action {
			margin-top: 2rem;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			width: 100%;
		}
	}

}

.addEntityButton{
    width: 100px !important;
    height: 30px !important;
    background-color: #0275d8 !important;
    color: #fff !important;
}

.entityItem,.listItem{
    margin-bottom: 20px;
    width: 100%;
}

