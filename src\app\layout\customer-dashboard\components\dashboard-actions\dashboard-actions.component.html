<div class="dashboard-actions-wrapper">
  <div class="actions">
    <button class="btn btn-link" id="dashboardOptionButton" (click)="overlayActions.toggle($event)"><i class="fa fa-ellipsis-v"></i></button>
    <p-overlayPanel appendTo="body" #overlayActions>
      <ul class="actions-menu">
        <ng-container *ngFor="let action of dropDownActions"> 
          <li *ngIf="action.isVisible" (click)="emitDashboardAction(action.type)">
            <a href="javascript:void(0)">{{action.name | translate}}</a>
          </li>
        </ng-container>
      </ul>
    </p-overlayPanel>
  </div>  
</div>