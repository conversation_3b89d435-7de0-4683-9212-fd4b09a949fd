import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, Output } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Guid } from 'app/shared/enum/guid';
import { ServerTypes } from 'app/shared/modules/data-layer/enum/resource/resource-server-types.enum';
import { Entity } from 'app/shared/modules/data-layer/models/entity';
import { EntityAssociatedDevice } from 'app/shared/modules/data-layer/models/entity-associated-device.interface';
import { Resource } from 'app/shared/modules/data-layer/models/resource';
import { ResourceGroup } from 'app/shared/modules/data-layer/models/resource-group';
import { ResourceGroupService } from 'app/shared/modules/data-layer/services/resource-group/resource-group.service';
import { SelectItem } from 'primeng/api';
import { Subscription } from 'rxjs';
import { EventsActionType } from '../../enums/events-action-type.enum';
import { EventsActionData } from '../../models/events-action-data.interface';
import { DefaultEventsEditorComponent } from '../default-events-editor/default-events-editor.component';
import { ResourceService } from "../../../../services/resource/resource.service";

@Component({
  selector: 'app-edit-entity',
  templateUrl: './edit-entity.component.html',
  styleUrls: ['./edit-entity.component.scss']
})
export class EditEntityComponent extends DefaultEventsEditorComponent implements OnInit, OnDestroy {
  selectedEntity: Entity
  resourceGroupModelStore: {[id: string]: ResourceGroup};
  associatedResourceGroupsSelectItems: SelectItem[] = [];
  resourcesModelStore: {[id: string]: Resource};//TODO make it a Map<Guid, Resource>
  associatedResourcesSelectItems: SelectItem[] =[];
  editEntityForm: FormGroup;
  subscriptions: Subscription[] = [];
  @Output() onEditEntityComponentAction: EventEmitter<EventsActionData> = new EventEmitter();

  constructor(
    private formBuilder: FormBuilder,
    private resourceGroupService: ResourceGroupService,
    private resourceService: ResourceService
  ) {
    super();
  }

  set data(value: Entity){
    this.selectedEntity = new Entity(value);
  }

  ngOnInit(): void{
    let resourceGroupSubscription = this.resourceGroupService.getAll().subscribe(res => {
      this.resourceGroupModelStore = res;
      for(let i in res){
        if(res[i].isHomogeneous && res[i].allResourcesType === ServerTypes.Core_RES_Entity){
          this.associatedResourceGroupsSelectItems.push({value: res[i].identity, label: res[i].name});
        }
      }
    })
    this.subscriptions.push(resourceGroupSubscription);

    let resourceSubscription = this.resourceService.getAll().subscribe(res => {
        let store: {[id: string]: Resource} = {};
        res.forEach((resource) => {
            store[resource.identity] = resource;
        });

      this.resourcesModelStore = store;
      for(let i in res){
        if(res[i].providesIdentification){
          this.associatedResourcesSelectItems.push({value: res[i].identity, label: res[i].name});
        }
      }
    })
    this.subscriptions.push(resourceSubscription);

    this.generateForm(this.selectedEntity);
  }

  private generateForm(data: Entity): void {
    let today = new Date();
    this.editEntityForm = this.formBuilder.group({
      identity: data ? data.identity : Guid.create().toString(),
      firstName: new FormControl(data ? data.firstName : null, Validators.required),
      lastName: new FormControl(data ? data.lastName : null, Validators.required),
      userName: new FormControl(data ? data.userName : null),
      id: new FormControl(data ? data.uniqueId : null),
      phone: new FormControl(data ? data.mobilePhone : null),
      email: new FormControl(data ? data.email : null, Validators.email),
      startDate: new FormControl(data && data.startDate? new Date(data.startDate) : today),
      endDate: new FormControl(data && data.endDate ? new Date(data.endDate) : today),
      associatedResources: new FormGroup({
        resources: this.formBuilder.array(data && data.associatedDevices && data.associatedDevices.length > 0 ? data.associatedDevices.map(item => this.createAssociatedResourceFromData(item)) : [])
      }),
      associatedResourceGroups: new FormControl(data ? data.resourceGroups : null)
    });
  }

  createAssociatedResourceFromData(associatedDevice?: EntityAssociatedDevice): FormGroup {
    console.log("createAssociatedResourceFromData", associatedDevice);
    return this.formBuilder.group({
      deviceId: new FormControl(associatedDevice ? associatedDevice.deviceId : null, Validators.required),
      value: new FormControl(associatedDevice ? associatedDevice.value : null,  Validators.required)
      // TODO
      // taken from the old form, don't know what they are for
      // photoValue: new FormControl(null, Validators.required)
      // name: new FormControl(null)
    })
  }

  addAsociatedResource(): void {
    (<FormArray>this.editEntityForm.get('associatedResources.resources')).push(this.createAssociatedResourceFromData(null));
  }

  removeAssociatedResource(index: number): void{
    (this.editEntityForm.get('associatedResources.resources') as FormArray).removeAt(index);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(item => item.unsubscribe());
    this.editEntityForm.reset();
  }

  onSubmit(){
    this.onEditEntityComponentAction.next({action: EventsActionType.saveEntity, entityInfo: this.returnEntityFromFormValues(this.editEntityForm)});
  }

  onCancel(){
    this.onEditEntityComponentAction.next({action: EventsActionType.cancel, entityInfo: null});
  }

  returnEntityFromFormValues(form:FormGroup): Entity {
    return new Entity({
      identity: this.editEntityForm.value.identity,
      firstName: this.editEntityForm.value.firstName,
      lastName: this.editEntityForm.value.lastName,
      userName: this.editEntityForm.value.userName,
      email: this.editEntityForm.value.email,
      mobilePhone: this.editEntityForm.value.phone,
      uniqueId: this.editEntityForm.value.id,
      startDate: this.editEntityForm.value.startDate.toISOString(),
      endDate: this.editEntityForm.value.endDate.toISOString(),
      associatedDevices: this.editEntityForm.value.associatedResources.resources.map(resource => {
        return {
          deviceId: resource.deviceId,
          deviceName: this.resourcesModelStore[resource.deviceId].name,
          value: resource.value
        }
      }),
      resourceGroups: this.editEntityForm.value.associatedResourceGroups.map(group => {
        return this.resourceGroupModelStore[group]
      })
    });
  }

  onDelete(): void {

  }
}
