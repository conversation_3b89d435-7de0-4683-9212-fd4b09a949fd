{"all": "toute", "rows": "<PERSON><PERSON><PERSON>", "newDashboard": "Nouveau tableau de bord", "dashboard": "Tableau de bord", "charts": "hit-parade", "tables": "tableaux", "froms": "Froms", "boostrapElement": "bootstrap élément", "boostrapGrid": "bootstrap grille", "component": "composant", "menu": "menu", "unassigned": "Non assigné", "submenu": "Sous-menu", "blankpage": "blanc page", "moretheme": "plus thèmes", "lightTheme": "<PERSON><PERSON><PERSON>", "darkTheme": "Sombre", "downloadNow": "télécharger", "language": "langue", "Year": "ann<PERSON>", "saveLayout": "sauvegarder la mise en page", "rec": "Rec", "layoutSelected": "mise en page sélectionnée", "playerAdded": "joueur a<PERSON>", "selectStep": "étape de sélection", "openChannelFail503": "Veuillez vérifier : si le service Webstreamer est correctement configuré dans le gestionnaire et si le service Webstreammer a démarré sur l'adresse/le port configuré", "StreamNotSupported": "Le navigateur ne prend pas en charge le streaming", "FailedToConnect": "Une erreur de serveur interne inattendue s'est produite. Vérifier les journaux du serveur", "apply": "appliquer", "errorCreatingProcedure": "Erreur de création de procédure", "noProcedureTemplates": "Aucun modèle de procédure n'est disponible", "proceduresServiceIsNotRunning": "Le service de procédures ne fonctionne pas", "created": "c<PERSON><PERSON>", "open": "ouvrir", "closed": "fermé", "refreshLocation": "rafraîchir l'emplacement", "in_progress": "en cours", "waiting": "en attente", "snoozed": "en sommeil", "commpleteStep": "étape complète", "archived": "archiv<PERSON>", "updated": "mis à jour", "locationUpdated": "emplacement mis à jour", "locationUpdateError": "erreur de mise à jour de l'emplacement", "locationManagement": "gestion de l'emplacement", "createProcedure": "c<PERSON>er procédure", "options": "options", "resources": "ressources", "saveLocation": "sauvegarder l'emplacement", "comment": "commentaire", "actions": "actions", "handledBy": "pris en charge par", "completeStep": "étape complète", "lightInformation": "information légère", "guid": "GUID", "copyToClipboard": "Copier dans le presse-papier", "copiedToClipboard": "Copié dans le presse-papier", "vmsPageTitle": "Ajouter des caméras", "help": "<PERSON><PERSON><PERSON>moi", "exportPDF": "Exporter un PDF", "lumenFactor": "Facteur de luminosité", "searchBox": "<PERSON><PERSON><PERSON>", "addEditEntity": "Ajouter/Modifier une entité", "dismissAll": "Rejeter la totalité", "ioCommands": "I/O commandes", "changeStatus": "Changer le statut", "enableTrafficFactor": "<PERSON><PERSON> le facteur de trafic", "enableWeatherFactor": "<PERSON>r le facteur mé<PERSON>", "changeTheme": "Change le thème", "closeAll": "<PERSON><PERSON><PERSON>", "dayTheme": "Thème de la journée", "nightTheme": "Thème de la nuit", "enableAutoLoginSwitch": "Activer la connexion automatique", "fullScreen": "Plein écran", "Temperature": "Température", "LightStrength": "Force lumineuse", "LightPower": "Puissance légère", "LightEnergy": "Énergie lumineuse", "LightCommQuality": "Qualité Comm Light", "LightFailure": "Défaillance lumineuse", "bar": "Diagramme à bandes", "doughnut": "Graphique en beignet", "radar": "Carte radar", "pie": "Graphique à tarte", "polarArea": "Carte des zones polaires", "line": "Graphique en ligne", "LightVoltage": "Tension légère", "LightCurrent": "<PERSON><PERSON><PERSON>", "trafficDetections": "Détection du trafic", "assetsManagement": "<PERSON><PERSON><PERSON><PERSON> humaines", "procedures": "Procédures", "notifications": "Notifications", "LightPowerFactor": "Facteur de puissance lumineuse", "firstName": "Nom", "lastName": "Prénom", "startDate": "Date de début", "expirationDate": "Date d'expiration", "phone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "associatedDevices": "Périphériques associés", "associatedResourceGroups": "Groupes de ressources associés", "fieldsMarkedWithAreMandatory": "Les champs avec * sont obligatoires", "save": "Enregistrer", "Core_RES_LightGW": "la lumière GW", "Core_RES_Asset": "Partenaires", "generatingReport": "Génération du rapport", "identity": "Identité", "transformation": "Transformations", "downloadEntitiesTemplate": "Télécharger le modèle d'entité", "downloadLightsTemplate": "Télécharger le modèle de lumière", "upload": "Télécharger", "on": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "off": "<PERSON><PERSON><PERSON>", "OFF": "<PERSON><PERSON><PERSON>", "ON": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "customerDashboard": {"editDashboard": "Modifier le tableau de bord", "addNewDashboard": "Ajouter un nouveau tableau de bord", "saveTemplateDashboard": "Enregistrer le tableau de bord en tant que modèle", "saveDashboard": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler", "addDashboardTitle": "Ajouter un nouveau tableau de bord", "editDashboardTitle": "Modifier le tableau de bord", "addWidgetsTitle": "Ajouter des widgets", "selectHierarchyLevel": "Sélectionnez le niveau hiérarchique", "noDashboardParent": "Sans parent", "delete": "Supp<PERSON>er le tableau de bord", "deleteConfirmationMessage": "Voulez-vous supprimer ce tableau de bord? ?", "deleteConfirmationHeader": "Supprimer la confirmation du tableau de bord ?", "noWidgetsAvailable": "Il n'y a pas de widgets disponibles.", "isOldDashboard": "C'est un vieux tableau de bord.", "noDashboardsAvailable": "Il n'y a pas de tableaux de bord disponibles.", "errorGettingDashboard": "Erreur lors de l'obtention du tableau de bord", "editWidget": "Modifier le widget", "deleteWidget": "Supprimer le widget", "widgetSize": {"map": {"xXBig": "Gros", "xBig": "<PERSON><PERSON><PERSON>", "big": "<PERSON>"}, "player": {"big": "Gros", "medium": "<PERSON><PERSON><PERSON>"}, "dashbox": {"small": "<PERSON>", "xMedium": "<PERSON><PERSON><PERSON>"}, "gauge": {"xSmall": "<PERSON>", "medium": "<PERSON><PERSON><PERSON>", "big": "Gros"}, "notification": {"big": "Gros", "medium": "<PERSON><PERSON><PERSON>", "tall": "Grande"}, "pieChart": {"big": "Gros", "medium": "<PERSON><PERSON><PERSON>", "tall": "Grande"}, "lineChart": {"big": "<PERSON>", "wide": "<PERSON><PERSON><PERSON>", "xWide": "Gros"}, "sensorStatus": {"big": "Gros"}, "emptySpace": {"xXBig": "Gros (5x5)", "xBig": "Gros (4x4)", "big": "Gros (3x3)", "medium": "<PERSON><PERSON><PERSON> (2x2)", "xMedium": "<PERSON><PERSON><PERSON> (1x3)", "small": "Petit (1x2)", "xSmall": "<PERSON> (2x1)", "xXSmall": "Petit (1x1)", "tall": "Grande (4x2)", "xTall": "Grande (4x1)", "wide": "Large (3x4)", "xWide": "Large (3x5)"}, "embeddedFile": {"xXSmall": "Super petit"}, "xXBig": "Gros (5x5)", "xBig": "Gros (4x4)", "big": "Gros (3x3)", "medium": "<PERSON><PERSON><PERSON> (2x2)", "xMedium": "<PERSON><PERSON><PERSON> (1x3)", "small": "Petit (1x2)", "xSmall": "<PERSON> (2x1)", "xXSmall": "Petit (1x1)", "tall": "Grande (4x2)", "xTall": "Grande (4x1)", "wide": "Large (3x4)", "xWide": "Large (3x5)"}, "editWidgetTitle": "Modifier le widget", "editWidgetSize": "Modifier la taille du widget", "selectMap": "Sélectionner une carte", "selectResourceGroup": "Sélectionner dans le groupe de ressources", "itemsFound": "objets trouvés", "Core_RES_InputChannel": "Cameras", "videoWall": "<PERSON><PERSON> vid<PERSON><PERSON>", "makeFullScreen": "<PERSON>dre plein écran", "showSideBar": "Afficher la barre latérale", "Core_RES_Light": "Lights", "Core_RES_Cabinet": "Cabinet", "Core_RES_ALPRLane": "ALPRLane", "Core_RES_Dashboard": "Dashboard", "Core_RES_Device": "<PERSON><PERSON>", "Core_RES_Input": "Input", "Core_RES_Output": "Output", "Core_RES_Layout": "Layout", "Core_RES_Map": "Map", "Core_RES_Entity": "Entity", "Core_RES_Asset": "<PERSON><PERSON>", "Core_RES_LightGW": "Light GW", "Core_RES_System": "Système", "Core_RES_PowerMeter": "<PERSON><PERSON><PERSON><PERSON>", "selectGroup": "Sélectionner un groupe", "selectResource": "Sélectionner une ressource", "selectTrigger": "Sélection<PERSON><PERSON> le déclencheur", "showAllGroups": "<PERSON>rer tous les groupes", "defaultMap": "Carte par défaut", "widgetType": {"status": "Statut", "map": "<PERSON><PERSON>", "player": "Vidéo", "dashbox": "<PERSON><PERSON><PERSON>", "timeline": "Chronologie", "chart": "Graphique", "gauge": "Jauge", "notification": "Panneau de notification", "pieChart": "Diagramme circulaire", "lineChart": "Graphique en ligne", "sensorStatus": "Etat du capteur", "embeddedFile": "Fichier incorporé", "urlShortcut": "<PERSON><PERSON><PERSON>ci URL"}, "thresholdPoints": "Points de seuil", "minMaxMeasurmentValues": "Valeurs Min et Max et Mesure", "dataNotSet": "Données non définies", "thresholdStart": "début", "thresholdEnd": "fin", "thresholdEventName": "Nom de l'événement", "selectNotificationNumber": "Numéro de notification", "chartDataType": "Type de données", "selectDataType": "Sélectionnez le type de données", "siteReadiness": "Statut de la ressource", "tasks": "Les tâches", "tasksUrgency": "Tâches Urgence", "dataIsZero": "Aucune donnée à afficher", "pieChartLabels": {"open": "Ouvert", "closed": "<PERSON><PERSON><PERSON>", "inprogress": "En cours", "important": "Important", "low": "Faible", "normal": "Ordinaire", "none": "Aucun", "online": "En ligne", "offline": "<PERSON><PERSON> ligne", "free": "Libre", "occupied": "<PERSON><PERSON><PERSON><PERSON>", "alarmed": "Alarmé", "error": "<PERSON><PERSON><PERSON>", "saved": "Enregistré", "on": "Sur", "off": "De", "unknown": "Inconnu", "offbyradio": "Désactivé par la radio", "dim": "Faible", "offByPower": "<PERSON><PERSON>ur temporaire", "tempError": "Temporary Error", "notSpecified": "Non précisé", "withoutCommunication": "Pas de communication"}, "select": "s<PERSON><PERSON><PERSON><PERSON>", "deselect": "Désé<PERSON><PERSON>ner", "selectResourceState": "Sélectionner l'état de la ressource", "selectTimePeriod": "Sélectionnez la période", "Core_RES_Unknown": "Core RES Inconnu", "selectResourceType": "Sélectionnez le type de ressource", "selectColorClass": "Sélectionnez la couleur", "selectBakgroundColorClass": "Sélectionnez la couleur d'arrière-plan", "selectTextColorClass": "Sélectionnez la couleur du texte", "saveAsDefault": "Enregistrer par défaut", "displayMethod": "Méthode d'affichage", "countResults": "Compter les résultats", "resultValue": "Valeur du résultat", "groupedValue": "Valeur groupée", "dashboardPriority": "Priorité du tableau de bord", "displayInInventory": "Afficher dans l'inventaire"}, "dim": "Faible", "offByRadio": "Off par radio", "checkLight": "Vérifier la lumière", "Checklight": "Vérifier la lumière", "checklight": "Vérifier la lumière", "offMaintenanceCheck": "OFF (Vérification d'entretien)", "offParamsFluctuation": "OFF (Fluctuation des paramètres) !", "OFF PARAMS FLUCTUATION": "OFF (Fluctuation des paramètres) !", "onParamsFluctuation": "ON (Fluctuation des paramètres) !", "ON PARAMS FLUCTUATION": "ON (Fluctuation des paramètres) !", "OFF MAINTENANCE CHECK": "OFF (Vérification d'entretien)", "SIM ISSUE": "La passerelle SIM n'est pas connectée au réseau", "simIssue": "La passerelle SIM n'est pas connectée au réseau", "sim issue": "La passerelle SIM n'est pas connectée au réseau", "off maintenance check": "OFF (Vérification d'entretien)", "maintenance check": "ON (vérification de maintenance)", "maintenanceCheck": "ON (vérification de maintenance)", "MAINTENANCE CHECK": "ON (vérification de maintenance)", "NO COMM": "Pas de communication", "CHECK LIGHT": "ON (Vérifier la lumière)", "offByPower": "Hors tension", "tempError": "<PERSON><PERSON>ur temporaire", "notSpecified": "Non précisé", "withoutCommunication": "Pas de communication", "projectNameHe": "Nom du projet il", "area": "Zone", "streetName": "Nom de rue", "poleId": "Pole Id", "poleHeight": "<PERSON><PERSON> <PERSON> poteau", "lampPower": "Puissance de la lampe", "lampModel": "<PERSON><PERSON><PERSON><PERSON>", "projectNameEn": "Nom du projet En", "lampDriver": "<PERSON>e de lampe", "powerBoxId": "Identifiant du boîtier d'alimentation", "pBoxNameHe": "P Box Name He", "pBoxLatitude": "P Box Latitude", "pBoxLongitude": "Longitude de la zone P", "areaId": "Identifiant de zone", "areaHeb": "Zone Heb", "projectLampId": "Id lampe de projet", "streetId": "Identifiant de la rue", "arm": "Bras", "nrItemsSelected": "{0} éléments sélectionnés", "min": "Min", "max": "Max", "measurementUnit": "Unité de mesure", "formErrors": {"required": "Champs obligatoires", "startEndRequired": "Champs de début et de fin obligatoires", "pattern": "Le code ne satisfait pas le modèle"}, "dashboardSavedAsDefault": "Tableau de bord enregistré par défaut", "deletedDefaultDashboard": "Tableau de bord par défaut supprimé", "noDataWaitingForEvents": "Pas de données, en attente d'événements", "micError": "Impossible de démarrer l'enregistrement de votre microphone", "appMap": {"noMapsAvailable": "Il n'y a pas de cartes disponibles", "addMap": "Ajouter une carte", "editMap": "Modifier la carte", "deleteMap": "Supprimer la carte", "saveAsDefault": "Enregistrer par défaut", "deletedDefaultMap": "Carte par défaut supprimée", "editLayers": "Couches", "tileSource": {"OSM": "Open Street Map", "SW": "Stamen Water Color", "raster": "<PERSON><PERSON>"}, "saveMap": "Enregistrement de la carte", "mapSaved": "<PERSON><PERSON> en<PERSON>", "failedToSaveMap": "Échec de l'enregistrement de la carte", "deleteConfirmationMessage": "Voulez-vous supprimer cette carte?", "deleteConfirmationHeader": "Supprimer la confirmation de carte?", "mapSavedAsDefault": "Carte enregistrée par défaut", "selectTileSource": "Sélectionnez la source Tyle", "mapName": "Nom de la carte", "layers": "Couches", "manage": "<PERSON><PERSON><PERSON>", "addNewLayer": "Ajouter un nouveau calque", "layerName": "Nom du calque", "selectResourceGroup": "Sélectionner un groupe de ressources", "selectResource": "Sélectionnez une ressource", "selectResourceState": "Sélectionnez l'état de la ressource", "selectResourceType": "Sélectionnez le type de ressource", "availableDevices": "Ressources disponibles", "updateMapElement": "Mettre à jour l'élément de carte", "mapElementSaved": "Élément de carte enregistré", "failedToUpdateMapElement": "Impossible de mettre à jour l'élément de carte", "saveMapElement": "Enregistrer l'élément de carte", "deleteMapElement": "Supprimer l'élément de carte", "mapElementDeleted": "Élément de carte supprimé", "failedToDeleteMapElement": "Impossible de supprimer l'élément de carte", "filter": "Filtre", "groupActions": "Actions de groupe", "search": "Recherche de carte", "searchSuccessful": " La recherche est réussie", "searchHasResultsNumber": "V<PERSON> avez {{resultsNumber}} résultats.", "noResultsSummary": "Ne pas appliquer de filtres de recherche.", "resetSearch": "Réinitialiser la recherche"}, "outdatedBrowser": "Veuillez mettre à jour vers la dernière version!", "outdatedBrowserTitle": "Votre navigateur est obsolète", "free": "Libre", "occupied": "<PERSON><PERSON><PERSON><PERSON>", "alarmed": "Alarmé", "takeSnapshot": "Prenez un instantané", "finish": "<PERSON><PERSON><PERSON>", "update": "Mise à jour", "updatedResource": "<PERSON><PERSON>urce mise à jour", "setLightIntensity": "Régler l'intensité lumineuse", "setWeatherFactor": "Définir le facteur météo", "editFactors": "Modifier les facteurs", "setLumenFactor": "Définir le facteur de luminosité", "setTrafficFactor": "Définir le facteur de trafic", "enableDisableGroups": "Activer/Désactiver les groupes", "compensatedValue": "<PERSON><PERSON> compensée", "setFactorsSucces": "Facteurs définis avec succès", "setLightIntensitySucces": "L'intensité lumineuse réglée a été réglée avec succès", "changeCompensationFactors": "Modifier les facteurs de compensation", "setLightIntensityError": "Impossible de régler l'intensité lumineuse", "setLightIntensityFailure": "Dé<PERSON>ut de régler l'intensité lumineuse", "openCamera": "Ouv<PERSON>r la caméra", "addDevice": "Ajouter un appareil", "inventory": {"inventory": "Inventaire", "addDevice": "Ajouter un appareil", "editDevice": "Modifier l'appareil", "results": "Résultats", "selectManufacturer": "Sélectionnez le fabricant", "selectModel": "Sélectionnez le modèle", "credentials": "Identifiants", "httpPort": "Port HTTP", "rtspPort": "Port RTSP", "storageDrive": "Lecteur de stockage", "rtspLink": "Lien RTSP", "deleteConfirmationMessage": "Voulez-vous supprimer ces ressources?", "deleteConfirmationHeader": "Supprimer la confirmation de ressource?"}, "resourceGroupManagerNew": {"resourceGroup": "Gestionnaire de groupe de ressources", "newResourceGroup": "Créer un nouveau groupe de ressources", "editResourceGroup": "Modifier le groupe de ressources existant", "selectGroup": "Sélectionnez un groupe", "selectProfiles": "Sélectionnez les profils"}, "formValidation": {"field": "Champ requis!", "numberAndSymbolsNotAllowed": "Les chiffres et les symboles ne sont pas autorisés!", "portField": "Le champ est obligatoire (ex: 127.0.0.1)!", "ipAddressField": "n'est pas une adresse IP valide!", "minFourCharacters": "Doit contenir au moins 4 caractères!"}, "swalMessages": {"selectEntity": "Sélectionnez l'entité", "selectEntityMessage": "Le gestionnaire de ressources nécessite la sélection d'au moins une entité. Veuillez faire une sélection pour poursuivre votre action."}, "resetTable": "Réinitialiser la table et les filtres", "location": "Emplacement", "propertiesNoDataWasFound": "Aucune donnée supplémentaire n'a été trouvée", "viewInfo": "Afficher les informations", "refreshLightData": "Actualiser les données d'éclairage", "refreshLightDataSuccess": "Actualisez les succès des données légères", "refreshLightDataError": "Actualiser l'erreur des données de lumière", "refreshLightDataFailure": "L'actualisation des données d'éclairage a échoué", "lat": "Latitude", "lng": "Longitude", "resourceType": "Type de ressource", "groups": "Groupes", "lampGUID": "GUID de la lampe", "refreshData": "Actualiser les données", "importCSV": "Importer CSV", "colsSelected": "{0} colonnes sélectionnées", "selectCol": "Sélectionnez les colonnes à afficher", "resultsAmount": "Nombre de résultats :", "of": "de", "jumpToMap": "Aller à la carte", "jumpToInventory": "Aller à l'inventaire", "VCA5_LINECOUNTER_A": "VCA Traversée de la ligne A", "VCA5_DWELL": "VCA Habiter", "VCA5_PRESENCE": "VCA Presence", "VCA5_ENTER": "VCA Entrer", "VCA5_EXIT": "VCA Sortie", "VCA5_APPEAR": "VCA Apparaître", "VCA5_DISAPPEAR": "VCA Disparaître", "VCA5_STOP": "VCA Arrêtez", "VCA5_DIRECTION": "VCA Direction", "VCA5_SPEED": "VCA La vitesse", "VCA5_TAILGATING": "VCA Tailgating", "VCA5_LINECOUNTER_B": "VCA Passage à niveau B", "VCA5_ABOBJ": "VCA Abobj", "VCA5_SMOKE": "VCA Fumée", "VCA5_FIRE": "VCA Feu", "VCA5_COLSIG": "VCA ColSig", "VCA5_UNKNOWN": "VCA Inconnue", "VCA5_PRESENCE_End": "VCA Fin de présence", "VCA5_ENTER_End": "VCA Entrez fin", "VCA5_EXIT_End": "VCA Quitter Fin", "VCA5_APPEAR_End": "VCA Apparaître à la fin", "VCA5_DISAPPEAR_End": "VCA Disparaître fin", "VCA5_STOP_End": "VCA Arrêter la fin", "VCA5_DWELL_End": "VCA Dwell End", "VCA5_DIRECTION_End": "VCA Fin de la direction", "VCA5_SPEED_End": "VCA Fin de vitesse", "VCA5_TAILGATING_End": "VCA Fin du talonnage", "VCA5_LINECOUNTER_A_End": "VCA Compteur de ligne A End", "VCA5_LINECOUNTER_B_End": "VCA Compteur de ligne B End", "VCA5_ABOBJ_End": "VCA Abobj End", "VCA5_RMOBJ_End": "VCA Rmobj End", "VCA5_SMOKE_End": "VCA Fin de fumée", "VCA5_FIRE_End": "VCA Fin de feu", "VCA5_COLSIG_End": "VCA Fin du broyage", "VCA5_UNKNOWN_End": "VCA Fin inconnue", "selectFieldName": "Sélectionnez le nom du champ", "selectOperand": "Sélectionnez l'opérande", "freeText": "Texte libre", "reset": "Réinitialiser", "operand": {"equals": "<PERSON><PERSON>", "notEquals": "<PERSON><PERSON>", "contains": "Contient", "notContains": "Ne contient pas"}, "selectedRows": "lignes sélectionnées", "selectedRow": "ligne sélection<PERSON>ée", "deviceNotSelected": "Capteur non sélectionné", "marketPlace": "Place du marché", "eventName": "Nom de l'événement", "total": "Total", "allEvents": "Tous les évènements", "selectEvent": "Sélectionnez un événement", "resetResource": "Réinitialiser la ressource", "resetResourceSuccess": "Réinitialiser la ressource avec succès", "resetResourceError": "Réinitialiser l'erreur de ressource", "Core_RES_Unknown": "Inconnue", "Core_RES_Node": "<PERSON><PERSON><PERSON>", "Core_RES_MapElement": "Élément de carte", "Core_RES_MapPreset": "Carte prédéfinie", "Core_RES_MapText": "Texte de la carte", "Core_RES_OutputChannel": "Canal de sortie", "Core_RES_LayoutPreset": "Preset de disposition", "Core_RES_Account": "<PERSON><PERSON><PERSON>", "Core_RES_AccountProfile": "Profil du compte", "Core_RES_ChannelTour": "Tour de la Manche", "Core_RES_PTZPreset": "Présélection PTZ", "Core_RES_ICN": "ICN", "Core_RES_VideoWall": "<PERSON><PERSON> <PERSON>", "Core_RES_ResourceGroup": "Groupe de ressources", "Core_RES_PTZPattern": "Modèle PTZ", "Core_RES_PTZAuxiliary": "Auxiliaire PTZ", "Core_RES_ProcedureTemplate": "<PERSON><PERSON><PERSON><PERSON> procé<PERSON>", "Core_RES_Sensor": "Capteur", "Core_RES_Sensor_Plural": "Capteurs", "Core_RES_LightResourceGroup": "Groupe de ressources légères", "Core_RES_Geofence": "Clôture géo", "Core_RES_WebStreamer": "Web Streamer", "successfullyDownloaded": "Téléchargé avec succès!", "downloadError": "Ne peut pas être téléchargé!", "openNewTab": "Ouvrez un nouvel onglet", "generatingReportCancelled": "Génération du rapport annulée", "MinVoltage": "Tension min", "MaxVoltage": "Tension maximum", "MinTotalPower": "Puissance totale minimale", "MaxTotalPower": "Puissance totale maximale", "MinVoltageBetweenPhases": "Tension minimale entre les phases", "MaxVoltageBetweenPhases": "Tension maximale entre les phases", "MinPowerFactor": "Facteur de puissance minimum", "MaxPowerFactor": "Facteur de puissance max", "Line1ActiveWatts": "Ligne 1 Watts actifs", "Line2ActiveWatts": "Ligne 2 Watts actifs", "Line3ActiveWatts": "Ligne 3 Watts actifs", "ActiveWattHours": "Watt-heures actives", "Line1ApparentVoltsAmps": "Ampères apparents de la ligne 1", "Line2ApparentVoltsAmps": "Ampères apparents de la ligne 2", "Line3ApparentVoltsAmps": "Ampères apparents de la ligne 3", "ApparentVoltAmpHours": "Volt Ampères apparentes", "CombinedActiveWatts123": "Watts actifs combinés 123", "CombinedApparentWatts123": "Watts apparents combinés 123", "CombinedPowerFactor123": "Facteur de puissance combiné 123", "CombinedReactiveWatts123": "Watts réactifs combinés 123", "Line1Current": "Courant de la ligne 1", "Line2Current": "Courant de la ligne 2", "Line3Current": "Courant de la ligne 3", "NeutralLineCurrent": "Courant de ligne neutre", "Line1PowerFactor": "Facteur de puissance de la ligne 1", "Line2PowerFactor": "Facteur de puissance de la ligne 2", "Line3PowerFactor": "Facteur de puissance de la ligne 3", "Line1VoltsAmpsReactive": "Ligne 1 Volts Ampères Réactifs", "Line2VoltsAmpsReactive": "Ligne 2 Volts Ampères Réactifs", "Line3VoltsAmpsReactive": "Ligne 3 Volts Ampères Réactifs", "Line3ToLine1Voltage": "Tension ligne 3 à ligne 1", "Line1ToLine2Voltage": "Tension ligne 1 à ligne 2", "Line2ToLine3Voltage": "Tension ligne 2 à ligne 3", "AMC_RESOURCE_STATUS": "Statut de la ressource", "AMC_ENVIRONMENT_DUST_PM1": "concentration de poussière PM1", "AMC_ENVIRONMENT_DUST_PM25": "concentration de poussière  PM 2.5", "AMC_ENVIRONMENT_DUST_PM10": "concentration de poussière  PM 10", "AMC_ENVIRONMENT_SO2_LEVEL": "Niveau de SO2", "AMC_ENVIRONMENT_NO2_LEVEL": "Niveau de NO2", "AMC_ENVIRONMENT_O3_LEVEL": "Niveau de O3", "AMC_PRESENCE_STATS": "Statut de présence", "AMC_PROFILE": "Profil", "AMC_STATE": "État", "AMC_AREA_ID": "ID de zone", "AMC_RULE_NAME": "Nom de la règle", "AMC_VIDEO_SOURCE": "Source vidéo", "AMC_SOURCE": "Source", "AMC_MOTION_TYPE": "Type de mouvement", "AMC_VIDEO_ANALYTICS": "<PERSON><PERSON><PERSON> vid<PERSON>", "AMC_DOOR_STATE": "État de la porte", "AMC_ENTITY_FIRST_NAME": "Prénom", "AMC_ENTITY_LAST_NAME": "Nom de famille", "AMC_ENTITY_MIN_AGE": "Âge minimum", "AMC_ENTITY_MAX_AGE": "Âge maximum", "AMC_ENTITY_GENDER": "Genre", "AMC_ENTITY_USER_ID": "ID utilisateur", "AMC_ENTITY_CONFIDENCE": "Confiance", "AMC_DETECTION_TIME": "Temps de détection", "AMC_SOURCE_DESCRIPTION": "Description de la source", "AMC_SHOCK": "Cho<PERSON>", "PEOPLE_COUNT": "Nombre de personnes", "AMC_SENSOR_CO": "Niveau de CO", "PERSON_STATUS": "Statistiques de la personne", "VEHICLE_STATS": "Statistiques du véhicule", "FACE_STATS": "Statistiques de la visage", "AMC_IO_ALARM_OUTPUT": "Alarme sortie", "AMC_IO_ANALOG_VALUE": "Valeur analogique", "AMC_IO_VALUE_0_VOLT": "Valeur 0 Volt", "AMC_IO_VALUE_10_VOLT": "Valeur 10 Volt", "AMC_IO_COMMUNICATION_STATUS": "Statut de la communication", "AMC_IO_COMMUNICATION_MODE": "Mode de communication", "AMC_IO_ALARM_TEXT": "Texte d'alarme", "AMC_IO_ALARM_ID": "ID de l'alarme", "AMC_IO_ALARM_SEVERITY": "Sévérité de l'alarme", "Line1Voltage": "Tension ligne 1", "Line2Voltage": "Tension ligne 2", "Line3Voltage": "Tension ligne 13", "NeutralCurrent": "<PERSON>urant neutre", "p1": "P1", "p2": "P2", "p3": "P3", "p4": "P4", "p5": "P5", "p6": "P6", "p7": "P7", "p8": "P8", "p9": "P9", "p10": "P10", "p11": "P11", "p12": "P12", "p13": "P13", "p14": "P14", "p15": "P15", "p16": "P16", "p17": "P17", "p18": "P18", "p19": "P19", "p20": "P20", "p21": "P21", "p22": "P22", "p23": "P23", "p24": "P24", "p25": "P25", "p26": "P26", "p27": "P27", "p28": "P28", "p29": "P29", "p30": "P30", "lamptype": "Type de lampe", "dimmingRequest": "Demande de gradation", "refreshPowerMeterData": "Actualiser les données du wattmètre", "refreshPowerMeterDataSuccess": "Actualiser le succès des données du wattmètre", "refreshPowerMeterDataError": "Actualiser l'erreur des données du wattmètre", "refreshPowerMeterDataFailure": "Échec de l'actualisation des données du wattmètre", "addedScheduler": "<PERSON><PERSON><PERSON>", "cannotAddSchedule": "Impossible d'ajouter le calendrier", "deletedScheduler": "Planificateur supprimé", "updatedScheduler": "Planificateur mis à jour", "cannotUpdateSchedule": "Impossible de mettre à jour le planificateur", "onDeletedScheduler": "Supprimer le planificateur?", "FireTrigger": "Le feu comme déclencheur", "IoCommand": "Définir le statut du groupe", "SetLightGroupStrength": "Définir la force du groupe de lumière", "areYouSureYouWantToDeleteThisSchedule": "Voulez-vous vraiment supprimer ce programme?", "customDate": "Date personnalisée", "scheduler": "Planificateur", "addSchedule": "Ajouter un planificateur", "LightWorkingHours": "Heures de travail de la lumière", "LightId": "ID de la lumière", "AlarmConfigurationText": "Configuration de l'alarme", "AlarmSeverityValue": "Alarme de gravité", "AlarmStatusValue": "Statut de l'alarme", "LightTemperature": "Température de la lumière", "IsAuxOn": "Est aux on", "AuxEnergy": "Énergie aux", "AuxPower": "Puissance aux", "LightNominalPower": "Puissance nominal de la lumière", "PhotoSensorPresence": "Présence d'un capteur de photo", "name": "Nom", "action": "Action", "type": "Type", "SpecificDay": "Jours spécifiques", "days": "Jours", "choose": "Choi<PERSON>", "priority": "Priorité", "interval": "Intervalle", "timeType": "Type d'heure", "specificTime": "Temps spécifique", "sunrise": "Lever du soleil", "sunset": "Le coucher du soleil", "time": "<PERSON><PERSON>", "yearly": "<PERSON><PERSON>", "monthly": "<PERSON><PERSON><PERSON>", "add": "Ajouter", "edit": "É<PERSON>er", "duplicate": "<PERSON><PERSON><PERSON><PERSON>", "yes": "O<PERSON>", "cancel": "Annuler", "sun": "<PERSON><PERSON><PERSON>", "mon": "<PERSON><PERSON>", "tue": "<PERSON><PERSON>", "wed": "<PERSON><PERSON><PERSON><PERSON>", "thu": "<PERSON><PERSON>", "fri": "<PERSON><PERSON><PERSON><PERSON>", "sat": "<PERSON><PERSON>", "entities": "Entités", "settings": "Paramètres", "resourceGroups": "Groupes de ressources", "actionLevel": "Niveau d'action", "dayOfMonth": "<PERSON>ur du mois", "Custom": "<PERSON><PERSON><PERSON>", "RepeatYearly": "<PERSON><PERSON><PERSON><PERSON><PERSON> chaque année", "RepeatMonthly": "<PERSON><PERSON><PERSON><PERSON><PERSON> tous les mois", "RepeatWeekly": "<PERSON><PERSON><PERSON><PERSON><PERSON> chaque semaine", "Every": "<PERSON><PERSON>", "Years": "<PERSON><PERSON>", "addedRtsp": "RTSP ajouté", "cannotAddRtsp": "Impossible d'ajouter RTSP", "selectTypeOfTheFile": "Sélectionnez le type de fichier", "CSVUpload": "Téléchargement CSV", "rtspChannels": "Canaux RTSP", "dropFileOrClickToUpload": "<PERSON><PERSON><PERSON><PERSON> le fichier ou cliquer pour télécharger", "PowerMeter_MinVoltage": "PM <PERSON>", "PowerMeter_MaxVoltage": "PM <PERSON>", "PowerMeter_MinTotalPower": "PM <PERSON>", "PowerMeter_MaxTotalPower": "PM <PERSON> TP", "PowerMeter_MinVoltageBetweenPhases": "PM Min V BP", "PowerMeter_MaxVoltageBetweenPhases": "PM Max V BP", "PowerMeter_MinPowerFactor": "PM <PERSON>", "PowerMeter_MaxPowerFactor": "PM <PERSON>", "PowerMeter_Line1ActiveWatts": "PM L1 AW", "PowerMeter_Line2ActiveWatts": "PM L2 AW", "PowerMeter_Line3ActiveWatts": "PM L3 Aw", "PowerMeter_ActiveWattHours": "PM Active WH", "PowerMeter_Line1ApparentVoltsAmps": "PM L1 AVA", "PowerMeter_Line2ApparentVoltsAmps": "PM L2 AVA", "PowerMeter_Line3ApparentVoltsAmps": "PM L3 AVA", "PowerMeter_ApparentVoltAmpHours": "PM AVAH", "PowerMeter_CombinedActiveWatts_123": "PM CActW 123", "PowerMeter_CombinedApparentWatts_123": "PM CAppW 123", "PowerMeter_CombinedPowerFactor_123": "PM CPowF 123", "PowerMeter_CombinedReactiveWatts_123": "PM CReaW 123", "PowerMeter_Line1Current": "PM L1C", "PowerMeter_Line2Current": "PM L2C", "PowerMeter_Line3Current": "PM L3C", "PowerMeter_NeutralLineCurrent": "PM NLC", "PowerMeter_Line1PowerFactor": "PM L1PF", "PowerMeter_Line2PowerFactor": "PM L2PF", "PowerMeter_Line3PowerFactor": "PM L3PF", "PowerMeter_Line1VoltsAmpsReactive": "PM L1VAR", "PowerMeter_Line2VoltsAmpsReactive": "PM L2VAR", "PowerMeter_Line3VoltsAmpsReactive": "PM L3VAR", "PowerMeter_Line3ToLine1Voltage": "PM L3-L1-V", "PowerMeter_Line1ToLine2Voltage": "PM L2-Li-V", "PowerMeter_Line1ToLine3Voltage": "PM L1-L3-V", "PowerMeter_Line1Voltage": "PM L1 V", "PowerMeter_Line2Voltage": "PM L2 V", "PowerMeter_Line3Voltage": "PM L3 V", "PowerMeter_NeutralCurrent": "PM <PERSON>", "PowerMeter_Failure": "PM F", "showResourcesFromGroup": "Afficher les ressources du groupe de ressources", "acknowledgeClick": "Cliquez pour confirmer", "dismiss": "<PERSON><PERSON><PERSON>", "acknowledge": "Reconnaître", "enableAutoAcknowledgeEvents": "Activer la reconnaissance automatique", "enableAutoClearEvents": "Activer l'effacement automatique", "noElementsForSnapshotFound": "Aucun élément pour l'instantané trouvé", "deleteResource": "La ressource a été supprimée", "cannotDeleteResources": "Les ressources ne peuvent pas être supprimées", "StatusChange": "Changement de statut", "resourceStatus": "Statut de la ressource", "LightColor": "<PERSON><PERSON><PERSON> claire", "LeditechStatusCode": "Code de statut Leditech", "fontSizeSmall": "<PERSON>", "fontSizeNormal": "Ordinaire", "fontSizeLarge": "Grand", "ackEventSuccess": "Reconnaître le succès de l'événement", "ackEventError": "Acquitter l'erreur d'événement", "widgetOutOfBounds": "Widget supprimé car il ne rentre pas sur le tableau de bord", "icon-view-1": "Présent pour 1 écran", "icon-view-4": "Présent pour 4 écrans", "icon-view-9": "Présent pour 9 écrans", "icon-view-10": "Présent pour 10 écrans", "showMore": "Montre plus", "showLess": "<PERSON><PERSON> moins", "default": "Défaut", "low": "Faible", "normal": "Ordinaire", "important": "Important", "urgent": "<PERSON><PERSON>", "ipAddress": "Adresse IP", "userName": "Nom d'utilisateur", "enabled": "Activé", "remove": "<PERSON><PERSON><PERSON>", "selectCSVColumn": "Sélectionnez la colonne CSV", "REPORT_COUNT": "Nombre de rapports", "active_watt_hours": "Wattheures actives", "apparent_volt_amp_hours": "Volt ampères heures apparents", "combined_active_watts_123": "Watts actifs combinés 123", "combined_apparent_watts_123": "Watts apparents combinés 123", "combined_power_factor_123": "Facteur de puissance combiné 123", "combined_reactive_watts_123": "Watts réactifs combinés 123", "line1_active_watts": "Ligne1 watts actifs", "line1_apparent_volt_amps": "Ampères volts apparents Line1", "line1_current": "Courant Line1", "line1_power_factor": "Facteur de puissance Line1", "line1_to_line2_voltage": "Tension ligne1 à ligne2", "line1_voltage": "Tension ligne1", "line1_volts_amps_reactive": "Amplis Line1 volts réactifs", "line2_active_watts": "Line2 watts actifs", "line2_apparent_volt_amps": "Ampères volts apparents Line2", "line2_current": "Courant Line2", "line2_power_factor": "Facteur de puissance Line2", "line2_to_line3_voltage": "Tension ligne2 à ligne3", "line2_voltage": "Tension Line2", "line2_volts_amps_reactive": "Amplis Line2 volts réactifs", "line3_active_watts": "Line3 watts actifs", "line3_apparent_volt_amps": "Ampères volts apparents Line3", "line3_current": "Courant Line3", "line3_power_factor": "Facteur de puissance Line3", "line3_to_line1_voltage": "Tension ligne3 à ligne1", "line3_voltage": "Tension Line3", "line3_volts_amps_reactive": "Line3 volts ampères réactifs", "max_power_factor": "Facteur de puissance max", "max_total_power": "Puissance totale maximale", "max_voltage": "Tension max", "max_voltage_between_phases": "Tension max entre phases", "min_power_factor": "Facteur de puissance min", "min_total_power": "Puissance totale min", "min_voltage": "Tension min", "min_voltage_between_phases": "Tension minimale entre phases", "neutral_current": "<PERSON>urant neutre", "neutral_line_current": "Courant de ligne neutre", "LEDITECH_CONSUMPTION": "Consommation d'énergie", "number_items": "Nombre d'articles", "select_item": "Sélectionnez les articles", "generateChart": "Générer un graphique", "data_values": "Valeurs des données", "PowerMeter_AverageVoltage": "PM Avg V", "PowerMeter_AveragePowerFactor": "PM Avg PF", "PowerMeter_TotalPower": "PM Total P", "PowerMeter_Line2ToLine3Voltage": "PM L2-L3-V", "Core_RES_Light_Plural": "Lumières", "Core_RES_Input_Plural": "Contributions", "Core_RES_Output_Plural": "Production", "Core_RES_PowerMeter_Plural": "Compteurs de puissance", "Core_RES_InputChannel_Plural": "Canaux d'entrée", "link": "<PERSON><PERSON>", "invalidDomain": "L'URL doit contenir le domaine local", "chartType": "Type de graphique", "reportResults": "Résultats de raport", "gridColumnSize": "<PERSON>lle de la colonne de la grille", "dashboardHasDifferentResolution": "Ce tableau de bord a été créé pour {{width}} x {{height}} taille d'écran", "unableToSaveDashboardDueToScrollInfo": "Le tableau de bord ne sera pas enregistré s'il dépasse la taille verticale de l'écran", "unableToSaveDashboardDueToScrollError": "Le tableau de bord ne peut pas être enregistré car il dépasse la taille verticale de l'écran", "cameraAlreadyInUse": "La caméra est déjà utilisée", "dateIsRequired": "<PERSON><PERSON> s<PERSON>ner une date", "tableView": "<PERSON><PERSON> de tableau", "mapView": "Vue de la carte", "viewType": "Type de vue", "dashboxResourcesView": "Vue des ressources de la Dashbox", "dismissEventSuccess": "Ignorer le succès de l'événement", "dismissEventError": "Ignorer l'erreur d'événement", "alreadyAckNotification": "Vous reconnaissez déjà cet événement", "AMC_LPR_DATE_TIME": "Date heure LPR", "AMC_LPR_PLATE_NO": "Numéro de plaque LPR", "AMC_LPR_PLATE_COUNTRY": "Pays de la plaque LPR", "AMC_LPR_PLATE_CONFIDENCE": "Confiance de la plaque LPR", "AMC_LPR_MOVE_DIRECTION": "Direction de déplacement LPR", "AMC_LPR_MS_IMAGE_PROCESSING": "Traitement d'image LPR", "AMC_LPR_VEHICLE_BRAND": "Marque du véhicule LPR", "AMC_LPR_VEHICLE_MODEL": "Modèle de véhicule LPR", "AMC_LPR_VEHICLE_TYPE": "Type de véhicule LPR", "AMC_LPR_VEHICLE_COLOR": "Couleur du véhicule LPR", "AMC_LPR_VEHICLE_CONFIDENCE": "Confiance du véhicule LPR", "AMC_LPR_SOURCE_CAMERA_NAME": "Nom de la caméra source LPR", "AMC_LPR_SOURCE_CAMERA_ADDRESS": "Adresse de caméra LPR", "AMC_PERSON_STATS": "Statistiques de la personne", "AMC_VEHICLE_STATS": "Statistiques du véhicule", "AMC_PEOPLE_COUNTING": "Comptage des personnes", "detectionList": {"detectionList": "Détections de trafic", "camera": "Caméra", "brand": "Marque", "color": "<PERSON><PERSON><PERSON>", "country": "Pays", "licensePlate": "Plaque d'immatriculation", "vehicleModel": "<PERSON><PERSON><PERSON><PERSON> véhicule", "vehicleType": "Type de véhicule", "cameraTime": "Heure de la caméra", "plateConfidence": "<PERSON><PERSON>tte Confiance", "vehicleConfidence": "Confiance du véhicule", "platePhoto": "Assiette Photo", "downloadPhotos": "Télécharger des photos", "downloadFailed": "Échec du téléchargement des photos", "platePhotoDeleted": "La photo de la plaque a été supprimée", "platePhotoFailedToDownload": "Échec du téléchargement de la photo de la plaque", "detectionPhotoDeleted": "La photo de détection a été supprimée", "detectionPhotoFailedToDownload": "Échec du téléchargement de la photo de détection", "actions": "Actions", "viewOnMap": "Voir sur la carte", "missingDetectionPhoto": "La photo de détection est manquante", "searchSpecific": "Cherchez-vous quelque chose de spécifique ?", "reset": "Réinitialiser les filtres", "noTrafficDetectionsFound": "Aucune détection de trafic trouvée"}, "follow": {"follow": "<PERSON><PERSON><PERSON><PERSON>", "items": "Articles", "location": "Localisation GPS", "missingPlateImage": "L'image de la plaque est manquante", "newDetectionHasOccurred": "Une nouvelle détection a eu lieu"}, "vehicleTraffic": {"back": "<PERSON><PERSON><PERSON>", "pollutionStandardBroken": "Ce véhicule a enfreint la norme de pollution autorisée de la région", "accessFeeNotPayed": "Ce véhicule est entré dans cette zone sans payer les frais d'accès", "licenseExpiredOrSuspended": "Le permis de conduire du conducteur titulaire de ce véhicule est expiré ou suspendu", "vignetteNotPayed": "Ce véhicule n'a pas de vignette active", "technicalInspectionExpired": "Le contrôle technique de ce véhicule a expiré", "vehicleTaxNotPayed": "Ce véhicule les taxes ne sont pas payés à ce jour"}, "detectionsChart": {"notEnoughData": "Pas assez de données pour afficher un graphique", "detectionsInInterval": "détections dans l'intervalle"}, "parkingDetections": {"parkingDetections": "Détections de stationnement", "paidAccess": "L'accès a été payé", "status": "Statut"}, "playerBackgroundImage": "Image de fond des joueurs", "resetBackgroundImage": "Réinitialiser l'image de fond des joueurs", "vcaOptions": "Options d'analyse vidéo", "showVCAObjects": "Afficher les objets VCA", "showVCAConfig": "Afficher la configuration VCA", "showPTZControls": "Afficher les commandes PTZ", "openToggleTimeline": "Calendrier des enregistrements", "entitySavedSuccessfully": "Entité Enregistrée Avec Succès", "signalingServerDown": "Le serveur de signalisation est en panne !", "procedureStep": "Étape de procédure", "jumpToProcedures": "Accéder aux procédures", "generalDetections": {"generalDetections": "Détections générales", "detectionId": "Numéro de détection", "description": "Description", "timeStamp": "Horodatage", "inputChannel": "Canal d'entrée", "startDate": "Date de début", "endDate": "Date de fin", "entityId": "Identifiant de l'entité", "plateId": "Numéro <PERSON>", "downloadArchive": "Télécharger l'archive", "jumpToTrafficDetections": "Accéder aux détections de trafic", "followOnTrafficDetections": "Su<PERSON>z les détections de trafic", "jumpToDossier": "Accéder au dossier", "generalDetection": "Détection générale", "reset": "Réinitialiser les filtres", "actions": "Actions", "noGeneralDetectionsFound": "Aucune détection générale trouvée"}, "welcomeBack": "Content de vous revoir!", "enterLoginDetails": "Veuillez saisir les détails de connexion ci-dessous", "enterSystemIP": "Entrez l'IP du système", "enterUsername": "Entrez le nom d'utilisateur", "enterPassword": "Entrez le mot de passe", "signIn": "Se connecter", "version": "Version", "notificationsPage": {"totalRecordsFormat": "{totalRecords} Alertes totales", "value": "<PERSON><PERSON>"}, "car": "Voiture", "truck": "Camion", "suv": "Suv", "van": "<PERSON>", "lcv": "<PERSON>", "lastOpenedCameras": "Dernière ouverture", "layouts": "Mises en page", "systemStructure": "Structure du Système", "cameras": "<PERSON><PERSON><PERSON>", "online": "En ligne", "offline": "<PERSON><PERSON> ligne", "cameraStatusHistory": "Historique des États des Caméras", "clearHistory": "Effacer l'historique", "error": "<PERSON><PERSON><PERSON>", "noCommunication": "Pas de Communication", "actualizationIn": "Mise à jour automatique dans", "sec": "sec", "alerts": "<PERSON><PERSON><PERSON>", "alarms": "Alarmes", "warnings": "Avertissements", "camera": "Caméra", "dispatch": "Répartition", "eventId": "ID de l'événement", "dispecerat": "Répartition", "detections": "Détections", "loading": "Chargement...", "playback": "Lecture", "downloadArchive": "Télécharger l'Archive", "sumarAlarmeUltimele72Ore": "Résumé des alarmes des dernières 72 heures", "unresolvedAlarms": "Alarmes non résolues", "nonconformingAlarms": "Alarmes non conformes", "alarmsVerified": "Alarmes vérifiées", "today": "<PERSON><PERSON><PERSON>'hui", "pm": "PM", "hello": "Bonjour", "search": "<PERSON><PERSON><PERSON>", "searchByCamera": "Rechercher par caméra, emplacement...", "searchButton": "<PERSON><PERSON><PERSON>", "dossiers": "Dossiers", "dossierTable": {"title": "Dossier", "searchByName": "Rechercher par nom", "readOnly": "Lecture seule", "all": "<PERSON>ut", "onlyDossiers": "Dossiers uniquement", "onlyArchives": "Archives uniquement", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "autoDelete": "Suppression automatique", "name": "Nom", "timestamp": "Horodatage", "actions": "Actions", "viewDetections": "Voir les détections", "toggleAutoDelete": "Basculer la suppression automatique", "uploadDossier": "Télécharger le dossier", "viewDetails": "Voir les détails", "noDossiersFound": "<PERSON>cun dossier trouvé", "showingEntries": "Affichage de {first} à {last} sur {totalRecords} dossiers", "autoDeleteTrue": "Suppression automatique activée (true)", "autoDeleteFalse": "Suppression automatique activée (false)", "reset": "Réinitialiser les filtres", "alreadyDeleted": "Dossier déjà supprimé à : {{timestamp}}", "deletionTime": "Heure de suppression"}, "dossierHistory": {"title": "Historique", "search": "Rechercher...", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "details": "Détails", "timestamp": "Horodatage", "actions": "Actions", "download": "Télécharger", "noHistoryFound": "Aucun historique trouvé", "showingEntries": "Affichage de {first} à {last} sur {totalRecords} entrées", "downloadError": "Échec du téléchargement du dossier", "loadError": "Échec du chargement de l'historique du dossier", "noDossierId": "Aucun identifiant de dossier n'a été trouvé pour suppression", "dossierDeletedSuccessfully": "Dossier supprimé avec succès", "failedDeleteDossier": "Impossible de supprimer le dossier", "noEventIdNavigation": "No event ID found for navigation", "noEventIdDownload": "Aucun identifiant d'événement trouvé pour le téléchargement", "failedToLoadDossiers": "Impossible de charger les dossiers", "failedToLoadDossierDetails": "Impossible de charger les détails du dossier", "failedToggleReadOnly": "Impossible de basculer en lecture seule", "failedToggleAutoDelete": "Impossible d'activer la suppression automatique"}, "dossierDetails": {"title": "<PERSON><PERSON><PERSON> du dossier", "basicInformation": "Informations de base", "id": "ID", "eventId": "ID de l'événement", "name": "Nom", "creationTime": "Date de création", "status": "Statut", "autoDelete": "Suppression automatique", "enabled": "Activé", "disabled": "Désactivé", "readOnly": "Lecture seule", "yes": "O<PERSON>", "no": "Non", "liveDuration": "Durée en direct", "archiveDuration": "Durée d'archivage", "hours": "heures", "storage": "Stockage", "path": "Chemin", "size": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "details": "Détails", "timeStamp": "Horodatage", "fileList": "Liste des fichiers"}, "dossierUpload": {"noFileSelected": "<PERSON><PERSON><PERSON> fichier s<PERSON>", "fileUploadSuccessfully": "Fichier téléchargé avec succès!", "invalidFileType": "Type de fichier non valide. Veuillez sélectionner un fichier pris en charge: {{fileTypes}}"}, "dossier": {"autoDelete": "Suppression automatique {{status}}", "readOnly": "Lecture seule {{status}}", "statusOptions": {"enabled": "activé", "disabled": "désactiv<PERSON>"}}, "audit": {"title": "LOG/Audit"}, "auditFilter": {"searchSpecific": "Cherchez-vous quelque chose de spécifique ?", "searchMessage": "Rechercher par message", "searchUser": "Rechercher par utilisateur", "searchIP": "Rechercher par adresse IP", "startDate": "Date de début", "endDate": "Date de fin", "reset": "Réinitialiser"}, "selectFromResourceGroup": "Sélectionner à partir du groupe de ressources", "selectChannelTour": "Sélectionner un tour de canal", "tour": "Tour", "videoWallSettings": "Paramètres du mur vidéo", "layout": "Disposition", "auditTable": {"name": "Utilisa<PERSON>ur", "action": "Action", "message": "Message", "timeStamp": "Horodatage", "ipAddress": "Adresse IP", "noAuditsFound": "Aucun audit trouvé"}, "generalDetectionsDetails": {"type": "Type", "timestamp": "Horodatage", "actions": "Actions", "ack": "Accuser réception", "closeDetails": "<PERSON><PERSON><PERSON> les d<PERSON>", "noDetectionsDetailsFound": "<PERSON><PERSON><PERSON> dé<PERSON> de détection trouvé"}, "reports": "Rapports", "reportSelection": "Sélection de rapport", "dateFilter": "Filtre de date", "latestAlerts": "Dernières alertes", "period": "Période", "endDate": "Date de fin", "selectEndDate": "Sélectionner la date de fin", "reportType": "Type de rapport", "alert": "<PERSON><PERSON><PERSON>", "trigger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectAlerts": "Sélectionner les alertes", "selectTriggers": "Sélectionner les déclencheurs", "alertsSelected": "alertes sélectionnées", "triggersSelected": "déclencheurs sélectionnés", "weeks": "Se<PERSON>ines", "months": "<PERSON><PERSON>", "reportAlreadyGenerating": "Un rapport est déjà en cours de génération", "reportGenerationStarted": "La génération du rapport a commencé", "templateNotFound": "<PERSON><PERSON><PERSON>le non trouvé, il a peut-être été supprimé", "templateRequiresName": "Un modèle doit avoir un nom", "templateNameExists": "Un modèle de recherche avec ce nom existe déjà", "templateRemoved": "Mod<PERSON>le supprimé avec succès", "failedToRemoveTemplate": "Échec de la suppression du modèle", "updateSuccess": "Mis à jour avec succès", "saveSuccess": "Enregistré avec succès", "saveFailed": "Échec de l'enregistrement", "invalidDateRange": "La date de début doit être antérieure à la date de fin", "selectValidDateRange": "Veuillez sélectionner une plage de dates valide", "invalidDateFilterType": "Type de filtre de date invalide", "reportCancelled": "Génération du rapport annulée", "noDataToExport": "Aucune donnée à exporter", "exportToCSV": "Exporter vers CSV", "exportToPDF": "Exporter vers PDF", "exportSuccess": "Exportation terminée avec succès", "exportFailed": "L'exportation a échoué", "exportEndpointNotFound": "Point de terminaison d'exportation introuvable", "auditServiceIsNotRunning": "Le service d'audit ne fonctionne pas", "pdfExportNotAvailable": "L'exportation PDF est temporairement indisponible. Veuillez utiliser l'exportation CSV à la place.", "alertsAlarms": {"plateNumber": "Numéro <PERSON>", "eventName": "Nom de l'événement", "noData": "Pas de don<PERSON>"}, "totalAlerts": "Alertes totales", "totalAlarms": "Alarmes totales", "totalWarnings": "Avertissements totaux", "switchPosition": "Position de l'interrupteur", "watermark": {"user": "Utilisa<PERSON>ur", "date": "Date"}, "infoNote": "Note d'information", "infoNoteDescription": "Adaugat in portal nota de informare cu caracter personal", "viewInfoPage": "Voir la page d'information", "accept": "Accepter", "export": "Exporter", "exporting": "Exportation en cours", "exportToHTML": "Exporter vers HTML", "ai": {"title": "Intelligence Artificielle", "content": "Contenu IA", "loading": "Chargement du contenu IA...", "error": "Échec du chargement du contenu IA. Veuillez réessayer plus tard."}, "noValue": "Sans objet", "weatherInfo": {"humidity": "<PERSON><PERSON><PERSON><PERSON>", "pressure": "Pression", "title": "<PERSON><PERSON><PERSON>", "loadingWeatherData": "Chargement des données météo.", "loadingWeatherDataError": "Impossible de charger les données météorologiques."}}