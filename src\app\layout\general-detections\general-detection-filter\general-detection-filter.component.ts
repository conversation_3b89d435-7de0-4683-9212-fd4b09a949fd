import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild, ViewEncapsulation } from '@angular/core';
import { ResourceGroup } from '../../../shared/modules/data-layer/models/resource-group';
import { ResourceGroupService } from '../../../shared/services/resourceGroup.service';
import { TranslateService } from '@ngx-translate/core';
import { Guid } from '../../../shared/enum/guid';
import { Subject, Subscription } from 'rxjs';
import { GeneralDetectionsFilter } from '../models/general-detections-filter.model';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import * as moment from 'moment';
import { MenuItem, MessageService } from 'primeng/api';
import { ExportTypes } from '../../../shared/enum/export-types.enum';
import { SplitButton } from 'primeng/splitbutton';

@Component({
  selector: 'app-general-detection-filter',
  templateUrl: './general-detection-filter.component.html',
  styleUrls: ['./general-detection-filter.component.scss'],
  encapsulation: ViewEncapsulation.Emulated
})
export class GeneralDetectionFilterComponent implements OnInit, OnDestroy {
  @Input() routeEventId = '';
  @Output() detectionsFilter = new EventEmitter<GeneralDetectionsFilter>();
  @Output() exportRequest = new EventEmitter<{type: string, filters: GeneralDetectionsFilter}>();
  @ViewChild('exportBtn') exportBtn!: SplitButton;

  searchValue1:string;
  searchValue2:string;
  resourceGroups: ResourceGroup[];
  startDate: Date | null = null;
  endDate: Date | null = null;
  selectedResourceGroup: ResourceGroup;
  search1Subject: Subject<string> = new Subject<string>();
  search2Subject: Subject<string> = new Subject<string>();

  // Export state
  exportLoading = false;
  exportType: ExportTypes | null = null;
  exportTypes = ExportTypes;

  tooltipOptions = {
    showDelay: 150,
    autoHide: false,
    tooltipEvent: 'hover',
    tooltipPosition: 'top'
  };

  subscriptions: Subscription[] = [];
  items: MenuItem[];

  get isResetDisabled(): boolean {
    return (
      !this.searchValue1 &&
      !this.searchValue2 &&
      !this.startDate &&
      !this.endDate &&
      !(this.selectedResourceGroup?.identity !== Guid.EMPTY) &&
      !this.routeEventId);
  }

  constructor(
    private resourceGroupService: ResourceGroupService,
    private translateService: TranslateService,
    private messageService: MessageService) {
      this.items = [
        {
          label: this.translateService.instant('exportToCSV'),
          icon: 'fa fa-file-excel-o',
          command: () => this.export(ExportTypes.csv)
        },
        {
          label: this.translateService.instant('exportToPDF'),
          icon: 'fa fa-file-pdf-o',
          command: () => this.export(ExportTypes.pdf)
        },
        {
          label: this.translateService.instant('exportToHTML'),
          icon: 'fa fa-html5',
          command: () => this.export(ExportTypes.html)
        }
      ];
  }

  ngOnInit(): void {
    this.getResourceGroups();

    this.subscriptions.push(
      this.search1Subject.pipe(debounceTime(700), distinctUntilChanged()).subscribe(() => {
        this.updateFilter();
      })
    );

    this.subscriptions.push(
      this.search2Subject.pipe(debounceTime(700), distinctUntilChanged()).subscribe(() => {
        this.updateFilter();
      })
    );
  }


  getResourceGroups(): void {
    this.subscriptions.push(
      this.resourceGroupService.getAll().subscribe(groups => {
          let all = new ResourceGroup();
          all.name = this.translateService.instant("all");
          all.identity = Guid.EMPTY;
          groups.splice(0, 0, all);
          this.resourceGroups = groups;
    }));
  }


  searchValue1Changed(value: string): void {
    this.searchValue1 = value;
    this.search1Subject.next(value);
  }

  searchValue2Changed(value: string): void {
    this.searchValue2 = value;
    this.search2Subject.next(value);
  }

  selectedGroupChanged(value: ResourceGroup): void {
    this.selectedResourceGroup = value;
    this.updateFilter();
  }

  onStartTimeStampChanged(event: Date): void {
    this.startDate = event;
    this.updateFilter();
  }

  onEndTimeStampChanged(event: Date): void {
    this.endDate = event;
    this.updateFilter();
  }

  resetFilterTable(): void {
    this.searchValue1 = '';
    this.searchValue2 = '';
    this.selectedResourceGroup = new ResourceGroup();
    this.startDate = null;
    this.endDate = null;
    this.routeEventId = '';
    this.updateFilter();
  }

  private updateFilter(): void {
    const filter: GeneralDetectionsFilter = {
      searchValue1: this.searchValue1 ? this.searchValue1 : null,
      searchValue2: this.searchValue2 ? this.searchValue2 : null,
      resourceGroupId: this.selectedResourceGroup.identity,
      startDate: this.startDate ? moment.utc(this.startDate).toDate() : null,
      endDate: this.endDate ? moment.utc(this.endDate).toDate() : null,
      eventId: this.routeEventId
    };
    this.detectionsFilter.emit(filter);
  }

   onDefaultClick(event: MouseEvent): void {
    this.exportBtn.menu.toggle(event);
  }

  export(type: ExportTypes): void {
    if (this.exportLoading) {
       return;
    }

    this.exportLoading = true;
    this.exportType = type;

    const currentFilter = this.getCurrentFilter();
    this.exportRequest.emit({ type: this.exportType, filters: currentFilter });
  }

  private getCurrentFilter(): GeneralDetectionsFilter {
    return {
      searchValue1: this.searchValue1 ? this.searchValue1 : null,
      searchValue2: this.searchValue2 ? this.searchValue2 : null,
      resourceGroupId: this.selectedResourceGroup?.identity || Guid.EMPTY,
      startDate: this.startDate ? moment.utc(this.startDate).toDate() : null,
      endDate: this.endDate ? moment.utc(this.endDate).toDate() : null,
      eventId: this.routeEventId
    };
  }

  resetExportState(): void {
    this.exportLoading = false;
    this.exportType = null;
  }

  ngOnDestroy(): void {
      this.subscriptions.forEach((i) => i.unsubscribe());
  }
}
