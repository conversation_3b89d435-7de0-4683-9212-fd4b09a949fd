@import 'ol/ol.css';

:host {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  max-height: 100%;
  overflow: hidden;
}

.dashboard-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.map-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 24px;
  overflow: hidden;
  background-color: #F2F6F3;
  flex: 1;
}

.map {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.map-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.ol-popup {
  position: absolute;
  background-color: white;
  box-shadow: 0 1px 4px rgba(0,0,0,0.2);
  border-radius: 10px;
  border: 1px solid #cccccc;
  min-width: 280px;
  max-width: 400px;
  z-index: 1000;
  padding-bottom: 20px;

  &:after, &:before {
    bottom: -10px;
    left: 50%;
    border: solid transparent;
    content: "";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
  }

  &:after {
    border-top-color: white;
    border-width: 10px;
    margin-left: -10px;
  }

  &:before {
    border-top-color: #cccccc;
    border-width: 11px;
    margin-left: -11px;
    bottom: -11px;
  }
}

:host ::ng-deep {
  .ol-control {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    padding: 2px;

    button {
      background-color: #4a90e2;
      border: none;
      border-radius: 2px;
      color: white;
      font-size: 16px;
      font-weight: bold;
      margin: 1px;
      padding: 0;
      height: 24px;
      width: 24px;
      line-height: 24px;
      text-align: center;

      &:hover {
        background-color: #357abd;
      }
    }
  }

  .ol-zoom {
    top: 1rem;
    left: 1rem;
  }

  .ol-attribution {
    bottom: 0.5rem;
    right: 0.5rem;
  }

  .ol-viewport {
    border-radius: 24px;
  }
}

.dashboard-maps{
    padding: 0px !important;
}