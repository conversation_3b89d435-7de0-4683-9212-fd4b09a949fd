<div class="dashboardSettings">
    <app-edit-widget [selectedWidget]="data"></app-edit-widget>
    <app-add-video-channel  [selectedWidgetId]="data.id"
     (onChannelSelect)="setSelectedChannel($event)"></app-add-video-channel>
   
    <div class="form-item">
        <h2>{{ 'vcaOptions' | translate }}</h2>  
        <div class="widget-vca-options">
            <p-checkbox *ngFor="let option of cameraOptions | keyvalue" (onChange)="onCameraOptionsChange($event, option.key)"
             label="{{option.value | translate}}" binary="true" [(ngModel)]="data.config[option.key]"></p-checkbox>
        </div>
    </div>
</div>
