<div class="col inline-headers align-self-center">
    <ul class="list-inline heading-list list-format">
        <li class="list-inline-item li-heading" *ngFor="let view of viewOptions">
            <a class="pointer" href="javascript:void(0)" (click)="selectView.next(view.id)" [ngClass]="view.isActive ? 'active' : ''  ">
                {{ view.name | translate }}
            </a>
        </li>
    </ul>
</div>
<div class="col structure-floating align-self-center">
    <ul class="list-inline icon-list list-format">
        <li class="list-inline-item">
            <button class="btn btn-link" (click)="overlayActions.toggle($event)">
                <i class="fa fa-ellipsis-v"></i>
            </button>
        </li>
        
        <p-overlayPanel appendTo="body" #overlayActions>
            <ul>
                <li *ngFor="let action of dropDownActions">
                    <a (click)="selectAction.next({action: action.type, entityInfo: {}})"> {{ action.type | translate}} </a>
                </li>
            </ul>
        </p-overlayPanel>
    </ul>
</div>
