import { Injectable } from "@angular/core";
import { DataLayerModule } from "app/shared/modules/data-layer.module";
import {
    DeviceStatusService
} from "app/shared/modules/data-layer/services/device-status/device-status.service";
import { EventServiceNew } from "app/shared/modules/data-layer/services/event/eventServiceNew";
import { SignalRService } from "app/shared/services/signalR.service";
import { ISignalRConnection } from "ng2-signalr";
import { Observable } from "rxjs";
import { ApiRequestName } from "../../models/api-request-name.enum";
import { DataChangeType } from "../../models/data-change-type.enum";
import { AppNotificationApiService } from "../app-notification/app-notification-api.service";
import { ResourceGroupApiService } from "../resource-group/resource-group-api.service";
import { ResourceGroupDTO } from "../resource-group/resource-group-dto";
import {MapService} from "app/services/map/map.service";
import {ResourceService} from "app/services/resource/resource.service";



@Injectable({
  providedIn: DataLayerModule
})
//TODO subscribing to signalr from a service is wrong, services can be cofigured transient, 
  // which means any signalR connection can be closed at any time during user navigation
  //more than that,what if a part of the application does not need to listen for some specific SignalR notifications?
  //this needs extensive refactoring
export class DispatcherService {
    private videoHubNgSignalRConnections: ISignalRConnection;
    private mapHubNgSignalRConnections: ISignalRConnection;
    constructor(
        private signalRService: SignalRService,
        private resourceGroupApiService: ResourceGroupApiService,
        private notificationApiService: AppNotificationApiService,
        private resourceService: ResourceService,
        private eventServiceNew: EventServiceNew,
        private mapApiService: MapService,
        private deviceStatusService: DeviceStatusService
    )
    {
      this.signalRService.GetConnection('VideoHub').subscribe(res => {
          res.listenFor<string>("webServerEvents").subscribe(payload => {
              const wsEvents: {
                data?: string,
                ResourcesList?: string,
                changeType: string
              } = JSON.parse(payload);
              switch(wsEvents.changeType){
                case 'resourceGroupChanged': {
                  // Commented out due to new messages comming through nottify message service
                  // this.resourceGroupApiService.onResourceChanged(DataChangeType.Update, wsEvents.data);
                  // this.resourceApiService.onResourceChanged(DataChangeType.Update, wsEvents.data, ApiRequestName.getResourceGroups);
                  break;
                }
                case 'resourceStateChanged': {

                  this.deviceStatusService.onResourceChanged(DataChangeType.Update, wsEvents.data);
                  this.resourceService.onResourceChanged(DataChangeType.Update, wsEvents.data, ApiRequestName.getStatuses);
                  break;
                }
                case 'mapChanged': {
                  this.mapApiService.onResourceChanged(DataChangeType.Update, wsEvents.data);
                  break;
                }
                case 'mapAdded': {
                  this.mapApiService.onResourceChanged(DataChangeType.Create, wsEvents.data);
                  break;
                }
                case 'mapRemoved': {
                  this.mapApiService.onResourceChanged(DataChangeType.Delete, wsEvents.data);
                  break;
                }
                case 'dashboardChanged': {
                  //TODO data returned matches the old implementation of getDashboards
                  break;
                }
                case 'newSurpriseInfo': {
                  this.notificationApiService.onResourceChanged(DataChangeType.Create, wsEvents.data);
                  break;
                }
                case "resourcesLocationsChanged": {
                  this.resourceService.onResourceChanged(DataChangeType.Update, wsEvents.data, ApiRequestName.getLocations);
                  break;
                }
                case "resourcesExtraDataChanged": {
                  this.resourceService.onResourceChanged(DataChangeType.Update, wsEvents.ResourcesList, ApiRequestName.getExtraData);
                  break;
                }
                default:
                  break;
              }
          });
      });
    }

    public connectToHub(): void{
      if(this.videoHubNgSignalRConnections){
          this.videoHubNgSignalRConnections.stop();
      }
      if(this.mapHubNgSignalRConnections){
        this.mapHubNgSignalRConnections.stop();
      }

      this.getConnection('VideoHub').subscribe(connection => {
          this.videoHubNgSignalRConnections = connection;
          this.listenForVideoHubMessages(connection);
      }, error => {
          console.error(error);
      });

      this.getConnection('MapHub').subscribe(connection => {
        this.mapHubNgSignalRConnections = connection;
        this.listenForMapHubMessages(connection);
      }, error => {
          console.error(error);
      });
    }

    private getConnection(notificationHub: string): Observable<ISignalRConnection> {
      return this.signalRService.GetConnection(notificationHub);
    }

    private listenForVideoHubMessages(connection: ISignalRConnection): void {
      connection.listenFor<string>('webServerEvents').subscribe((payload:string) => {
        const wsEvents: {
          data?: string,
          ResourcesList?: string,
          changeType: string
        } = JSON.parse(payload);
        switch(wsEvents.changeType){
          case 'resourceGroupChanged': {
            this.resourceGroupApiService.onResourceChanged(DataChangeType.Update, wsEvents.data);
            this.resourceService.onResourceChanged(DataChangeType.Update, wsEvents.data, ApiRequestName.getResourceGroups);
            break;
          }
          case 'resourceStateChanged': {
            this.deviceStatusService.onResourceChanged(DataChangeType.Update, wsEvents.data);
            this.resourceService.onResourceChanged(DataChangeType.Update, wsEvents.data, ApiRequestName.getStatuses);
            break;
          }
          case 'mapChanged': {
            this.mapApiService.onResourceChanged(DataChangeType.Update, wsEvents.data);
            break;
          }
          case 'mapAdded': {
            this.mapApiService.onResourceChanged(DataChangeType.Create, wsEvents.data);
            break;
          }
          case 'mapRemoved': {
            this.mapApiService.onResourceChanged(DataChangeType.Delete, wsEvents.data);
            break;
          }
          case 'dashboardChanged': {
            //TODO data returned matches the old implementation of getDashboards
            break;
          }
          case 'newSurpriseInfo': {
            this.notificationApiService.onResourceChanged(DataChangeType.Create, wsEvents.data);
            break;
          }
          case "resourcesLocationsChanged": {
            this.resourceService.onResourceChanged(DataChangeType.Update, wsEvents.data, ApiRequestName.getLocations);
            break;
          }
          case "resourcesExtraDataChanged": {
            this.resourceService.onResourceChanged(DataChangeType.Update, wsEvents.ResourcesList, ApiRequestName.getExtraData);
            break;
          }
          default:
            break;
        }
      });
    }



    public generateResourceGroupDTO(data:string){
      return JSON.parse(data).map(element => {
         return new ResourceGroupDTO(element).toModel();
       });
     }

    private listenForMapHubMessages(connection: ISignalRConnection):void {
      connection.listenFor<string>("updateElements").subscribe(mapElements => {
        this.resourceService.onResourceChanged(DataChangeType.Update, mapElements, ApiRequestName.mapHubUpdateElement);
      });

      connection.listenFor<string>("addEvent").subscribe(event => {
        this.eventServiceNew.eventResourceChanged(DataChangeType.Create, event, ApiRequestName.mapHubAddEvent);
      });
    }

    public handleVideoHubEvent(event: any): void {
      const wsEvents = JSON.parse(event);
      switch(wsEvents.changeType) {
        case 'resourceGroupChanged': {
          this.resourceGroupApiService.onResourceChanged(DataChangeType.Update, wsEvents.data);
          this.resourceService.onResourceChanged(DataChangeType.Update, wsEvents.data, ApiRequestName.getResourceGroups);
          break;
        }
        case 'resourceStateChanged': {
          this.deviceStatusService.onResourceChanged(DataChangeType.Update, wsEvents.data);
          this.resourceService.onResourceChanged(DataChangeType.Update, wsEvents.data, ApiRequestName.getStatuses);
          break;
        }
        case 'mapChanged': {
          this.mapApiService.onResourceChanged(DataChangeType.Update, wsEvents.data);
          break;
        }
        case 'mapAdded': {
          this.mapApiService.onResourceChanged(DataChangeType.Create, wsEvents.data);
          break;
        }
        case 'mapRemoved': {
          this.mapApiService.onResourceChanged(DataChangeType.Delete, wsEvents.data);
          break;
        }
        case 'dashboardChanged': {
          //TODO data returned matches the old implementation of getDashboards
          break;
        }
        case 'newSurpriseInfo': {
          this.notificationApiService.onResourceChanged(DataChangeType.Create, wsEvents.data);
          break;
        }
        case "resourcesLocationsChanged": {
          this.resourceService.onResourceChanged(DataChangeType.Update, wsEvents.data, ApiRequestName.getLocations);
          break;
        }
        case "resourcesExtraDataChanged": {
          this.resourceService.onResourceChanged(DataChangeType.Update, wsEvents.ResourcesList, ApiRequestName.getExtraData);
          break;
        }
        default:
          break;
      }
    }

    public handleMapHubEvent(event: any): void {
      const mapElements = JSON.parse(event);
      this.resourceService.onResourceChanged(DataChangeType.Update, mapElements, ApiRequestName.mapHubUpdateElement);
    }

}
