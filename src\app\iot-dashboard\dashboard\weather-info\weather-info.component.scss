:host {
  display: block;
  height: 100%;
  width: 100%;
}

.weather-info-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 15px; /* Reduced padding for better space utilization */
  border: 1px solid rgba(220, 220, 220, 0.5);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  height: 100%; /* Take full height of container */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 12px; /* Reduced gap for better space utilization */
  animation: logEntryAnimation 0.3s ease-out;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.12);
  }

  &.loading {
    justify-content: center;
    align-items: center;
    font-style: italic;
    color: #666666;
  }

  .weather-info-title {
    text-align: center;
    font-size: 16px; /* Reduced font size */
    font-weight: bold;
    margin: 0;
    color: #1e1e1e;
    padding-bottom: 6px; /* Reduced padding */
    border-bottom: 1px solid #e0e0e0;
  }

  .weather-info-box {
    padding-top: 8px;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .weather-info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: "Rounded Mplus 1c Bold", ui-sans-serif, system-ui, sans-serif;
    font-size: 14px; /* Reduced font size for better fit */

    label {
      font-weight: 600;
      color: #2e2e2e;
    }

    span {
      text-align: right;
      color: #444444;
    }
  }
}

@keyframes logEntryAnimation {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
