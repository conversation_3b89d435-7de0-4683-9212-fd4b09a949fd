:host {
  display: block;
  height: 100%;
  width: 100%;
}

.weather-info-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 12px; /* Further reduced padding */
  border: 1px solid rgba(220, 220, 220, 0.5);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  height: 100%; /* Take full height of container */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 8px; /* Further reduced gap */
  animation: logEntryAnimation 0.3s ease-out;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  justify-content: flex-start; /* Align content to top */

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.12);
  }

  &.loading {
    justify-content: center;
    align-items: center;
    font-style: italic;
    color: #666666;
  }

  .weather-info-title {
    text-align: center;
    font-size: 15px; /* Further reduced font size */
    font-weight: bold;
    margin: 0;
    color: #1e1e1e;
    padding-bottom: 4px; /* Further reduced padding */
    border-bottom: 1px solid #e0e0e0;
  }

  .weather-info-box {
    padding-top: 4px; /* Reduced padding */
    display: flex;
    flex-direction: column;
    gap: 6px; /* Reduced gap between rows */
  }

  .weather-info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: "Rounded Mplus 1c Bold", ui-sans-serif, system-ui, sans-serif;
    font-size: 13px; /* Further reduced font size for compact display */
    line-height: 1.2; /* Reduced line height for tighter spacing */

    label {
      font-weight: 600;
      color: #2e2e2e;
    }

    span {
      text-align: right;
      color: #444444;
    }
  }
}

@keyframes logEntryAnimation {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
