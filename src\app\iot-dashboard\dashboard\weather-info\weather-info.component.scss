.weather-info-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(220, 220, 220, 0.5);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 16px;
  animation: logEntryAnimation 0.3s ease-out;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.12);
  }

  &.loading {
    justify-content: center;
    align-items: center;
    font-style: italic;
    color: #666666;
  }

  .weather-info-title {
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin: 0;
    color: #1e1e1e;
    padding-bottom: 8px;
    border-bottom: 1px solid #e0e0e0;
  }

  .weather-info-box {
    padding-top: 8px;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .weather-info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: "Rounded Mplus 1c Bold", ui-sans-serif, system-ui, sans-serif;
    font-size: 15px;

    label {
      font-weight: 600;
      color: #2e2e2e;
    }

    span {
      text-align: right;
      color: #444444;
    }
  }
}

@keyframes logEntryAnimation {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
