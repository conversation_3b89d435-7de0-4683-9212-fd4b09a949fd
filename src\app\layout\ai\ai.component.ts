import { AiService } from './../../services/ai/ai.service';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { Subscription } from 'rxjs';
import { LocalStorageService } from '../../shared/services/local-storage.service';

@Component({
  selector: 'app-ai',
  templateUrl: './ai.component.html',
  styleUrls: ['./ai.component.scss']
})
export class AiComponent implements OnInit, OnDestroy {
  safeUrl: SafeResourceUrl;
  loading: boolean = true;
  private subscriptions: Subscription[] = [];

  constructor(
    private sanitizer: DomSanitizer,
    private localStorageService: LocalStorageService) { }

  ngOnInit(): void {
    this.loadAiLink();
  }

  loadAiLink(): void {
    this.loading = true;
    const aiLink = this.localStorageService.get('aiLink');
    const link = aiLink !== null && aiLink.trim() !== ''
    if(link) {
        this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(aiLink);
        this.loading = false;
    } else {
      this.loading = false;
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
}
