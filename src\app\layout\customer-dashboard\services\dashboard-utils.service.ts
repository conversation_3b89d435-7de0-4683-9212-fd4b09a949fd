import { Injectable } from "@angular/core";
import { Guid } from "app/shared/enum/guid";
import { Dashboard } from "app/shared/modules/data-layer/models/dashboard";
import { Observable, Subject } from "rxjs";
import { DashboardWidget } from "../models/dashboard-widget.interface";

import { ServerTypes } from 'app/shared/modules/data-layer/enum/resource/resource-server-types.enum';
import * as _ from 'lodash';
import { DashboardActionType } from "../enums/dashboard-action-type.enum";
import { ChartWidget } from "../models/chart-widget";
import { DashboardPath } from "../models/dashboard-path";
import { DashboxWidget } from "../models/dashbox-widget";
import { EmbeddedFileWidget } from "../models/embedded-file-widget";
import { EmptySpaceWidget } from "../models/empty-space-widget";
import { GaugeWidget } from "../models/gauge-widget";
import { MapWidget } from "../models/map-widget";
import { NotificationWidget } from "../models/notification-widget";
import { PieChartWidget } from "../models/pie-chart-widget";
import { PlayerWidget } from "../models/player-widget";
import { SensorStatusWidget } from "../models/sensor-status-widget";
import { UrlShortcutWidget } from '../models/url-shortcut-widget';
import { Widget } from "../models/widget";
@Injectable({
    providedIn: "root"
})
export class DashboardUtilsService {
    draggedWidgetData = new Subject<DashboardWidget>();
    private selectParentDashboardTreeitem = new Subject<Dashboard>();
    private widgetAction = new Subject<{widget: Widget, actionType: DashboardActionType}>();
    private widgetDataChange = new Subject<{widget: Widget}>();
    private sidebarActionState = new Subject<{actionType: DashboardActionType, disabled: boolean}>();

    constructor() {}

    setDragAndDropWidget(widget: DashboardWidget): void {
        this.draggedWidgetData.next(widget);
    }

    findInTree(tree: DashboardPath[], value: string): DashboardPath {
        for (let i = 0; i < tree.length; i++) {
            let node = tree[i];
            if (node.identity === value) {
                return node;
            }

            if (node.children && node.children.length) {
                let retVal = this.findInTree(node.children, value);
                if (retVal) {
                    return retVal;
                }
            }
        }
        return null;
    }

    buildPathTree(
        tree: DashboardPath[],
        parent?: DashboardPath
    ): DashboardPath[] {
        for (let i = 0; i < tree.length; i++) {
            tree[i].path = parent
                ? (parent.path !== null ? parent.path + "," : "") +
                  tree[i].parent.identity
                : null;

            if (tree[i].children && tree[i].children.length) {
                this.buildPathTree(tree[i].children, tree[i]);
            }
        }
        return tree;
    }

    buildBreadcrumbTree(
        tree: DashboardPath[],
        selectedId: string
    ): Dashboard {
        let selectedTreeItem: DashboardPath = this.findInTree(
            tree,
            selectedId
        );
        if (!selectedTreeItem) {
            for (let i = 0; i < tree.length; i++) {
                let item = tree[i];
                if (Guid.isGuid(item.identity) && item.identity !== Guid.EMPTY) {
                    return item;
                }
            }
        }

        let topmostParentId =
            selectedTreeItem && selectedTreeItem.path !== null
                ? selectedTreeItem.path.split(",")[0]
                : selectedTreeItem ? selectedTreeItem.identity : null;

        let topmostParent: Dashboard = this.findInTree(
            tree,
            topmostParentId
        );

        return topmostParent;
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    findTreeIndex(tree: Dashboard[], value: string, oldIndex?: number): number {
        for (let i = 0; i < tree.length; i++) {
            let node = tree[i];
            if (node.identity === value) {
                return i;
            }
            if (node.children && node.children.length) {
                let newIndexChild = this.findTreeIndex(node.children, value, i);
                if (newIndexChild > -1) {
                    return i;
                }
            }
        }
        return -1;
    }

    transformToArray(flat: { [id: string]: Dashboard }): Dashboard[] {
        let array: Dashboard[] = [];
        //TODO: check why duplicate dashboards are created
        Object.keys(flat).forEach((key) => {
            const item = flat[key];
            const existingItem = array.find((existing) => existing.identity === item.identity);
            if (!existingItem) {
                array.push(item);
            }
        });
        return array;
    }

    createDashboardTreeArray(flatArray: Dashboard[]): Dashboard[]{
        _(flatArray).forEach((f:Dashboard) => {
            f.children =_(flatArray).filter((g:Dashboard) => {return g.identity !== Guid.EMPTY && g.parent.identity === f.identity;}).value();
        });
        let resultArray =_(flatArray).filter((f:Dashboard) => {return f.parent.identity === null || f.parent.identity === Guid.EMPTY;}).value();
        return resultArray;
    }

    buildDashboardsTree(flat: {[id: string] : Dashboard}): Dashboard[]{
        let flatTree = this.transformToArray(flat);
        let tree: Dashboard[] = this.createDashboardTreeArray(flatTree);
        return tree;
    }

    setSelectParentDashboardTreeitem(item: Dashboard): void {
        this.selectParentDashboardTreeitem.next(item);
    }

    getSelectParentDashboardTreeitem(): Observable<Dashboard> {
        return this.selectParentDashboardTreeitem.asObservable();
    }

    setWidgetAction(item: Widget, action: DashboardActionType): void {
        this.widgetAction.next({widget: item, actionType: action});
    }

    getWidgetAction(): Observable<{widget: Widget, actionType: DashboardActionType}> {
        return this.widgetAction.asObservable();
    }

    setWidgetDataChange(item: Widget):void {
        this.widgetDataChange.next({widget: item});
    }

    getWidgetDataChange(): Observable<{widget: Widget}> {
        return this.widgetDataChange.asObservable();
    }

    getSidebarActionState():Observable<{actionType: DashboardActionType, disabled: boolean}>{
        return this.sidebarActionState.asObservable();
    }

    setSidebarActionState(item: {actionType: DashboardActionType, disabled: boolean}): void {
        this.sidebarActionState.next(item);
    }

    returnWidgetClass(widgetData: DashboardWidget): Widget {
        let widgetClass;
        switch(widgetData.type){
            case 'map':
                widgetClass = new MapWidget(widgetData);
                break;
            case 'player':
                widgetClass = new PlayerWidget(widgetData);
                break;
            case 'dashbox':
                widgetClass = new DashboxWidget(widgetData);
                break;
            case 'gauge':
                widgetClass = new GaugeWidget(widgetData);
                break;
            case 'notification':
                widgetClass = new NotificationWidget(widgetData);
                break;
            case 'pieChart':
                widgetClass = new PieChartWidget(widgetData);
                break;
            case 'chart':
            case 'lineChart':
                widgetClass = new ChartWidget(widgetData);
                break;
            case 'sensorStatus':
                widgetClass = new SensorStatusWidget(widgetData);
                break;
            case 'emptySpace':
                widgetClass = new EmptySpaceWidget(widgetData);
                break;
            case 'embeddedFile':
                widgetClass = new EmbeddedFileWidget(widgetData);
                break;
            case 'urlShortcut':
                widgetClass = new UrlShortcutWidget(widgetData);
                break;
            default:
                widgetClass = new Widget(widgetData);
                break;
        }
        return widgetClass;
    }

    arrayMove(arr: DashboardWidget[], oldIndex: number, newIndex: number): DashboardWidget[] {
        if (newIndex >= arr.length) {
            let k = newIndex - arr.length + 1;
            while (k--) {
                arr.push(undefined);
            }
        }
        arr.splice(newIndex, 0, arr.splice(oldIndex, 1)[0]);
        return arr;
    }

    includesServerTypes(type:ServerTypes): boolean {
        return (type === ServerTypes.Core_RES_ALPRLane) ||
        (type === ServerTypes.Core_RES_Cabinet) ||
        (type === ServerTypes.Core_RES_Device) ||
        (type === ServerTypes.Core_RES_InputChannel) ||
        (type === ServerTypes.Core_RES_Light) ||
        (type === ServerTypes.Core_RES_Node) ||
        (type === ServerTypes.Core_RES_Output) ||
        (type === ServerTypes.Core_RES_Input) ||
        (type === ServerTypes.Core_RES_PowerMeter);
    }

    includesSiteReadinessTypes(type: ServerTypes): boolean {
        return (type === ServerTypes.Core_RES_Light) ||
        (type === ServerTypes.Core_RES_InputChannel) ||
        (type === ServerTypes.Core_RES_Input) ||
        (type === ServerTypes.Core_RES_Output) ||
        (type === ServerTypes.Core_RES_Sensor) ||
        (type === ServerTypes.Core_RES_ALPRLane);

    }

    includeServerTypesWithStatuses(type: ServerTypes): boolean {
       return type === ServerTypes.Core_RES_Account ||
            type === ServerTypes.Core_RES_ALPRLane ||
            type === ServerTypes.Core_RES_Cabinet ||
            type === ServerTypes.Core_RES_Device ||
            type === ServerTypes.Core_RES_Input ||
            type === ServerTypes.Core_RES_InputChannel ||
            type === ServerTypes.Core_RES_Light ||
            type === ServerTypes.Core_RES_LightGW ||
            type === ServerTypes.Core_RES_Subelement ||
            type === ServerTypes.Core_RES_Output ||
            type === ServerTypes.Core_RES_Sensor;
    }
}
