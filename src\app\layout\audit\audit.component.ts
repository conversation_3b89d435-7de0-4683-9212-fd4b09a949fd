import { AuditService, AuditPageRequest } from './../../services/audit/audit.service';
import { Audit } from './models/audit.model';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-audit',
  templateUrl: './audit.component.html',
  styleUrls: ['./audit.component.scss']
})
export class AuditComponent implements OnInit, OnDestroy {
  audits: Audit[] = [];
  loading = false;
  currentPageIndex = 0;
  pageSize = 12;
  totalRecords = 0;

  // Filter parameters
  startTimestamp: string = '';
  endTimestamp: string = '';
  searchMessage: string = '';
  searchIp: string = '';
  searchUser: string = '';

  private destroy$ = new Subject<void>();

  constructor(private auditService: AuditService) { }

  ngOnInit(): void {
    // Set default date range to last 24 hours
    const now = new Date();
    this.endTimestamp = now.toISOString();
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    this.startTimestamp = yesterday.toISOString();

    this.getAudits();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getAudits(): void {
    this.loading = true;

    const request: AuditPageRequest = {
      PageIndex: this.currentPageIndex,
      PageSize: this.pageSize,
      StartTimestamp: this.startTimestamp,
      EndTimestamp: this.endTimestamp
    };

    // Add optional filter parameters if they exist
    if (this.searchMessage) {
      request.Message = this.searchMessage;
    }

    if (this.searchIp) {
      request.IP = this.searchIp;
    }

    if (this.searchUser) {
      request.User = this.searchUser;
    }

    this.auditService.getAudits(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe(response => {
        if (response && response.items) {
          // Ensure all required fields are present
          this.audits = response.items.map(item => {
            const processedItem = {
              ...item,
              // Ensure these fields exist, even if empty
              id: item.id || '',
              user: item.user || '',
              message: item.message || '',
              timestamp: item.timestamp || '',
              ip: item.ip || ''
            };
            if (!processedItem.message) {
              console.error('Missing message in processed item:', processedItem);
            }
            return processedItem;
          });
          this.totalRecords = response.totalCount || 0;
        } else {
          console.error('Invalid response format:', response);
          this.audits = [];
          this.totalRecords = 0;
        }

        this.loading = false;
      }, error => {
        console.error('Error fetching audit logs:', error);
        this.loading = false;
      });
  }

  onPageChange(newPageIndex: number): void {
    this.currentPageIndex = newPageIndex;
    this.getAudits();
  }

  /**
   * Update filter parameters from the filter component
   */
  onFilterChange(filters: any): void {
    this.searchMessage = filters.searchQuery || '';
    this.searchUser = filters.user || '';
    this.searchIp = filters.ip || '';
    this.startTimestamp = filters.startDate || this.startTimestamp;
    this.endTimestamp = filters.endDate || this.endTimestamp;

    // Reset to first page when filters change
    this.currentPageIndex = 0;
    this.getAudits();
  }
}
