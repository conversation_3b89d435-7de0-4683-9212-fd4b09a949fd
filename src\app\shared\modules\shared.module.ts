import { VideoChannelsComponent } from './../components/video-channels/video-channels.component';
import { InfoNoteModalComponent } from './../components/info-note-modal/info-note-modal.component';
import { InfoNoteContainerComponent } from './../components/info-note-container/info-note-container.component';
import { PositionConverter } from './../components/app-modal/position-converter';
import { PopupContainerComponent } from './../components/popup-container/popup-container.component';
import { PageTitleComponent } from './../components/page-title/page-title.component';
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TranslateModule } from "@ngx-translate/core";
import { AngularSvgIconModule } from "angular-svg-icon";
import { AngularDraggableModule } from "angular2-draggable";
import { EntityUploaderComponent } from "app/layout/entities/entity-uploader/entityUploader.component";
import { SharedPipesModule } from "app/shared";
import { BusyIndicatorComponent } from "app/shared/components/busy-indicator/busy-indicator.component";
import { ConfigUploaderComponent } from "app/shared/components/config-uploader/config-uploader.component";
import { CymSidebar } from "app/shared/components/cym-sidebar/cym-sidebar.component";
import { FileUploaderComponent } from "app/shared/components/file-uploader/file-uploader.component";
import { FullscreenComponent } from "app/shared/components/fullscreen/fullscreen.component";
import { IconComponent } from "app/shared/components/icon/icon.component";
import { OpenVideoChannelComponent } from "./../components/open-video-channel/open-video-channel.component";

import { ImageGalleryComponent } from "app/shared/components/image-gallery/image-gallery.component";
import { LayoutDividerComponent } from "app/shared/components/layout-divider/layout-divider.component";
import { PlayerComponent } from "app/shared/components/player/player.component";
import { SpinnerComponent } from "app/shared/components/spinner/spinner.component";
import { SubHeaderComponent } from "app/shared/components/sub-header/sub-header.component";
import { TimelineComponent } from "app/shared/components/timeline/timeline.component";
import { VideoExtractorComponent } from "app/shared/components/video-extractor/video-extractor.component";
import { SharedDirectiveModule } from "app/shared/directives/shared-directive.module";
import { DimActionComponent } from "app/shared/enum/actions-teplates/dim-action.component";
import { SidebarModule } from "ng-sidebar";

import {
    AccordionModule,
} from "primeng/accordion";
import { CalendarModule } from "primeng/calendar";
import { CaptchaModule } from "primeng/captcha";
import { CheckboxModule } from "primeng/checkbox";
import { ColorPickerModule } from "primeng/colorpicker";
import { ContextMenuModule } from "primeng/contextmenu";
import { DialogModule } from "primeng/dialog";
import { DragDropModule } from "primeng/dragdrop";
import { DropdownModule } from "primeng/dropdown";
import { FileUploadModule } from "primeng/fileupload";
import { GalleriaModule } from "primeng/galleria";
import { InputSwitchModule } from "primeng/inputswitch";
import { InputTextModule } from "primeng/inputtext";
import { InputTextareaModule } from "primeng/inputtextarea";
import { MultiSelectModule } from "primeng/multiselect";
import { OverlayPanelModule } from "primeng/overlaypanel";
import { ProgressBarModule } from "primeng/progressbar";
import { SliderModule } from "primeng/slider";
import { TabViewModule } from "primeng/tabview";
import { TooltipModule } from "primeng/tooltip";
import { TreeModule } from "primeng/tree";

import { TreeTableModule } from "primeng/treetable";

import { DatetimePickerPopupComponent } from "app/shared/components/ng-datetime-picker/ng-datetime-picker.component";
import { DatetimeRangePickerComponent } from "app/shared/components/ng-range/ng-range.component";
import { ResourceGroupManagerComponent } from "app/shared/components/resourceGroupManager/resourceGroupManager.component";
import { SettingsComponent } from "app/shared/components/settings-page/settings.component";
import { TreeDataComponent } from "app/shared/components/tree-table/tree-table.component";
import { RadioButtonModule } from 'primeng/radiobutton';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { AddVideoChannelComponent } from "../components/add-video-channel/add-video-channel.component";
import { AppModalCancel } from '../components/app-modal/app-modal-cancel/app-modal-cancel.component';
import { AppModal } from '../components/app-modal/app-modal.component';
import { CymUploaderComponent } from '../components/cym-uploader/cym-uploader.component';
import { EntititesUploadComponent } from '../components/cym-uploader/entitites-upload/entitites-upload.component';
import { RtspUploadComponent } from '../components/cym-uploader/rtsp-upload/rtsp-upload.component';
import { CymbiotCalendar } from '../components/cymbiot-calendar/cymbiot-calendar.component';
import { DisplayImageComponent } from '../components/display-image/display-image.component';
import { GenericMapPopupComponent } from "../components/generic-map-popup/generic-map-popup.component";
import { LightsUploaderComponent } from "../components/lights-uploader/lightsUploader.component";
import { InfoRowTableModal } from '../components/ng-turbo-table/components/info-row-table-modal/info-row-table-modal.component';
import { NgTurboTableComponent } from '../components/ng-turbo-table/ng-turbo-table.component';
import { ResourceStatusComponent } from '../components/resource-status/resource-status.component';
import { TreeComponent } from "../components/tree/tree.component";
import { CymSliderComponent } from './cymbiot-map/components/cym-slider/cym-slider.component';
import { DimLightComponent } from "./cymbiot-map/components/dim-light/dim-light.component";
// import { CymbiotChartComponent } from '../components/cymbiot-chart/cymbiot-chart.component';
import { EntityFormComponent } from "app/layout/entities/entity-form/entity-form.component";
import { NgChartsModule } from 'ng2-charts';
import { ToggleButtonModule } from 'primeng/togglebutton';
import { CymbiotChartComponent } from "../components/cymbiot-chart/cymbiot-chart.component";
import { IocommandsComponent } from "../components/iocommands/iocommands.component";
import { SimpleThemeToggleModule } from "../components/simple-theme-toggle/simple-theme-toggle.module";
import { IocommandsmodalcontentComponent } from "../components/iocommandsmodalcontent/iocommandsmodalcontent.component";
import { IoLightInfModalComponent } from "../components/iolightinfomodal/iolightinfomodal.component";
import { IoLigntInfoComponent } from "../components/ioligntinfo/ioligntinfo.component";
import { LightPowerActionComponent } from "./inventory-table/components/light-check-on-off/light-power-action/light-power-action.component";

import {FullCalendarModule} from "@fullcalendar/angular";
import {ExtractVideoService} from "app/shared/services/extract-video.service";
import {NotificationsPageComponent} from "app/pages/notifications-page/notifications-page.component";
import { VirtualScrollerModule } from 'primeng/virtualscroller';
import { SplitButtonModule } from 'primeng/splitbutton';
@NgModule({
    imports:[
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        TranslateModule,
        CalendarModule,
        MultiSelectModule,
        DropdownModule,
        InputTextModule,
        ContextMenuModule,
        SidebarModule,
        ToggleButtonModule,
        OverlayPanelModule,
        GalleriaModule,
        DialogModule,
        TableModule,
        FileUploadModule,
        CheckboxModule,
        SliderModule,
        AngularDraggableModule,
        InputTextareaModule,
        AngularSvgIconModule,
        SharedDirectiveModule,
        SharedPipesModule,
        TreeModule,
        TreeTableModule,
        TabViewModule,
        ReactiveFormsModule,
        CaptchaModule,
        TooltipModule,
        DragDropModule,
        AccordionModule,
        ColorPickerModule,
        RadioButtonModule,
        InputSwitchModule,
        ProgressBarModule,
        ToastModule,
        NgChartsModule,
        FullCalendarModule,
        FullCalendarModule,
        VirtualScrollerModule,
        SimpleThemeToggleModule,
        SplitButtonModule
    ],
    declarations: [
        BusyIndicatorComponent,
        PlayerComponent,
        DatetimeRangePickerComponent,
        DatetimePickerPopupComponent,
        NgTurboTableComponent,
        SpinnerComponent,
        LayoutDividerComponent,
        CymSidebar,
        ImageGalleryComponent,
        FileUploaderComponent,
        DimActionComponent,
        ConfigUploaderComponent,
        FullscreenComponent,
        VideoExtractorComponent,
        TimelineComponent,
        SubHeaderComponent,
        IconComponent,
        ResourceGroupManagerComponent,
        TreeDataComponent,
        SettingsComponent,
        OpenVideoChannelComponent,
        EntityUploaderComponent,
        LightsUploaderComponent,
        TreeComponent,
        AddVideoChannelComponent,
        GenericMapPopupComponent,
        DimLightComponent,
        AppModal,
        AppModalCancel,
        InfoRowTableModal,
        ResourceStatusComponent,
        CymSliderComponent,
        CymUploaderComponent,
        DisplayImageComponent,
        CymbiotCalendar,
        RtspUploadComponent,
        EntititesUploadComponent,
        CymbiotChartComponent,
        EntityFormComponent,
        IocommandsComponent,
        IoLigntInfoComponent,
        IoLightInfModalComponent,
        IocommandsmodalcontentComponent,
        LightPowerActionComponent,
        NotificationsPageComponent,
        VideoChannelsComponent,
        InfoNoteModalComponent,
        InfoNoteContainerComponent,
        PopupContainerComponent,
        PageTitleComponent

    ],
    exports: [
        BusyIndicatorComponent,
        FormsModule,
        ReactiveFormsModule,
        CommonModule,
        TranslateModule,
        ContextMenuModule,
        PlayerComponent,
        CalendarModule,
        MultiSelectModule,
        FileUploadModule,
        DialogModule,
        TableModule,
        DropdownModule,
        InputTextModule,
        CheckboxModule,
        SliderModule,
        InputTextareaModule,
        DatetimeRangePickerComponent,
        DatetimePickerPopupComponent,
        NgTurboTableComponent,
        SpinnerComponent,
        LayoutDividerComponent,
        CymSidebar,
        SidebarModule,
        OverlayPanelModule,
        ImageGalleryComponent,
        FileUploaderComponent,
        DimActionComponent,
        AngularDraggableModule,
        ConfigUploaderComponent,
        SharedPipesModule,
        SharedDirectiveModule,
        FullscreenComponent,
        VideoExtractorComponent,
        TimelineComponent,
        SubHeaderComponent,
        IconComponent,
        ResourceGroupManagerComponent,
        TreeModule,
        TreeTableModule,
        TreeDataComponent,
        SettingsComponent,
        OpenVideoChannelComponent,
        TabViewModule,
        CaptchaModule,
        EntityUploaderComponent,
        LightsUploaderComponent,
        TreeComponent,
        AddVideoChannelComponent,
        ColorPickerModule,
        RadioButtonModule,
        TooltipModule,
        GenericMapPopupComponent,
        DimLightComponent,
        AppModal,
        AppModalCancel,
        InputSwitchModule,
        InfoRowTableModal,
        ResourceStatusComponent,
        CymSliderComponent,
        CymUploaderComponent,
        DisplayImageComponent,
        ToastModule,
        CymbiotCalendar,
        RtspUploadComponent,
        EntititesUploadComponent,
        CymbiotChartComponent,
        NgChartsModule,
        EntityFormComponent,
        IocommandsComponent,
        IoLigntInfoComponent,
        IoLightInfModalComponent,
        IocommandsmodalcontentComponent,
        LightPowerActionComponent,
        VideoChannelsComponent,
        VirtualScrollerModule,
        InfoNoteModalComponent,
        InfoNoteContainerComponent,
        PopupContainerComponent,
        PageTitleComponent,
        SplitButtonModule
    ],
    providers:[
        ExtractVideoService,
        PositionConverter
    ],
    entryComponents: [DimActionComponent, DimLightComponent, EntityFormComponent,LightPowerActionComponent]
})
export class SharedModule { }
