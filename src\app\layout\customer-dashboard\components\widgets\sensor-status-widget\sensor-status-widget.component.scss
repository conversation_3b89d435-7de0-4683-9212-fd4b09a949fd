:host {
    ::ng-deep {
        font-size: 0.75rem;
        line-height: 1rem;
        .ui-table {
            .custom-table-caption{
                padding: 0px !important;
            }
            .ui-table-caption {
                padding: 5px 10px !important;
            }
            .ui-paginator-bottom {
                padding: 0px !important;
                border-bottom: 0px !important;
                margin-top: 0px !important;
            }
            .ui-paginator {
                .ui-dropdown {
                    height: 25px !important;
                }
                .ui-inputtext {
                    border: 0px !important;
                    line-height: 15px !important;
                }
            }
            .ui-paginator-bottom  {
                a {
                    width: 25px !important;
                    height: 25px !important;
                    line-height: 25px !important;
                 }
                 .ui-paginator-pages a {
                    margin-top: -2px !important;
                 }
            }
            .row-checkbox , .ui-table-tbody td:first-child{
                display: none;
            }
            .ui-table-tbody td:nth-child(2), .ui-table-thead th:nth-child(2) {
                padding-left: 5px;
            }
        }
        .search-box {
            i {
                line-height: 2.3em !important;
            }
        }

        .no-frozen-cols {
            colgroup col {
                width: 110px !important;
            }
        }
        .no-frozen-cols .ui-table-scrollable-body-table .ui-table-tbody tr td:first-child {
            position: inherit !important;
            width: auto !important;
        }
        .filtred-table-row, .filtred-table-row th{
            padding: 5px 15px !important;
        }
        .reset-table {
            padding-bottom: 0px !important;
        }
    }
}

.sensor-status-wrapper {
    overflow: visible;
    .no-data-set {
        display: flex;
        justify-content: center;
        align-content: center;
        height: 100%;
        align-items: center;
    }
}
::ng-deep {
    .layout-container {
        overflow-y: visible !important;
    }
    .widget{
        overflow: visible !important;
        overflow-y: scroll !important;
    }
}

