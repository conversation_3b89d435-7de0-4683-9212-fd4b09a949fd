{"all": "Toate", "rows": "<PERSON><PERSON><PERSON><PERSON>", "dashboards": "<PERSON><PERSON><PERSON><PERSON> de bord", "newDashboard": "<PERSON><PERSON><PERSON> de bord nou", "dashboard": "Dashboard", "tables": "<PERSON><PERSON><PERSON>", "forms": "Forme", "boostrapElement": "Boostrap Element", "boostrapGrid": "Boostrap Grid", "component": "Componente", "menu": "<PERSON><PERSON>", "submenu": "Submeniu", "blankpage": "Pagin<PERSON> goală", "moretheme": "Mai multe teme", "lightTheme": "Luminos", "darkTheme": "Întunecat", "downloadNow": "Descarcă acum", "unassigned": "Nesemnat", "language": "Limba", "viewAll": "<PERSON><PERSON><PERSON> tot", "channels": "Canale", "open": "<PERSON><PERSON><PERSON>", "closed": "<PERSON><PERSON><PERSON>", "rec": "Rec", "Core_RES_Sensor_Plural": "Resurse", "in_progress": "În desfăș<PERSON>", "waiting": "În așteptare", "snoozed": "Amânat", "openChannelFail503": "Vă rugăm să verificați: dacă serviciul webstreamer este configurat corect în manager și dacă serviciul webstreamer a pornit pe adresa/portul configurat", "StreamNotSupported": "Streamul nu este suportat", "FailedToConnect": "A apărut o eroare internă neașteptată a serverului. Verificați jurnalele serverului", "archived": "<PERSON><PERSON><PERSON><PERSON>", "apply": "Aplică", "created": "<PERSON><PERSON><PERSON>", "errorCreatingProcedure": "Eroare la crearea procedurii", "proceduresServiceIsNotRunning": "Serviciul de proceduri nu funcționează", "selectStep": "Selectează pas", "noProcedureTemplates": "Nu există template-uri de proceduri", "updated": "Actualizat", "refreshLocation": "Actualizează locația", "createProcedure": "Creează procedură", "commpleteStep": "Pas complet", "options": "Opțiuni", "comment": "<PERSON><PERSON><PERSON><PERSON>", "locationUpdated": "Locație actualizată", "locationUpdateError": "Eroare la actualizarea locației", "locationManagement": "Management locație", "handledBy": "<PERSON><PERSON><PERSON><PERSON> de", "completeStep": "Pas complet", "lightInformation": "Informații", "maps": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resources": "Resurse", "saveLocation": "Salvează locația", "exportPDF": "Exportați PDF", "dashboardEditor": "Editor tab<PERSON><PERSON> de bord", "changeCompensationFactors": "Schimbați factorii de compensare", "enableTrafficFactor": "Activați Factorul de trafic", "enableWeatherFactor": "Activați Factorul meteo", "dismissAll": "Anulează tot", "liveDashboard": "<PERSON><PERSON><PERSON><PERSON> de bord - Live", "addEditEntity": "Ad<PERSON><PERSON><PERSON><PERSON> / Editați entitate", "reports": "Rapoarte", "selectSearchTemplate": "Selectează Raport", "searchBy": "<PERSON><PERSON><PERSON> du<PERSON>", "textFilter": "Text filtru", "resourceGroup": "Grup de resurse", "resource": "Resurse", "dateFilter": "<PERSON><PERSON><PERSON>", "selectDate": "Selectează data", "fromDate": "De la data", "toDate": "Până la data", "addNewSearchTemplate": "Adaugă un template nou de căutare", "triggerType": "Tip declansator", "exportToPDF": "Exporta în PDF", "exportRoute": "Exporta ruta", "saveTemplate": "Salvează Template", "generateReport": "<PERSON><PERSON><PERSON>", "execute": "Execută", "clear": "Resetează", "newTemplate": "Template nou", "enterNewTemplateName": "Introduceți numele noului template", "selectOperation": "Selectati operația", "events": "Evenimente", "triggers": "Declan<PERSON><PERSON><PERSON>", "heatMap": "<PERSON><PERSON>", "date": "Data", "last": "Ultimul", "dateRange": "Interval", "latest": "<PERSON><PERSON> mai recente", "seconds": "Secunde", "minutes": "Minute", "days": "Zile", "hours": "Ore", "months": "<PERSON><PERSON>", "weeks": "Saptă<PERSON>ân<PERSON>", "map": "Hartă", "table": "<PERSON><PERSON>", "chart": "<PERSON><PERSON>", "selectChartType": "Selectați Tip grafic:", "bar": "<PERSON>ic <PERSON>", "doughnut": "<PERSON><PERSON>", "radar": "Grafic Radar", "pie": "<PERSON><PERSON>", "polarArea": "Grafic Polar Area", "line": "Grafic Line", "search...": "Caută...", "ioCommands": "Comenzi I/O", "changeStatus": "Schimbă statusul", "systemIP": "IP Sistem", "password": "Pa<PERSON><PERSON>", "username": "Nume utilizator", "trafficDetections": "Detecții trafic", "assetsManagement": "Management persoane", "procedures": "Pro<PERSON><PERSON>", "notifications": "<PERSON><PERSON><PERSON>", "login": "<PERSON><PERSON>", "logout": "Logout", "badCredentialsMsg": "Numele de utilizator sau parola sunt greșite", "error": "Eroare", "yes": "Da", "no": "<PERSON>u", "confirm": "Confirmă", "delete": "Șterge", "ok": "OK", "cancel": "<PERSON><PERSON><PERSON>", "DeviceConnection": "Conexiune dispozitiv", "GenericSensorValue": "Valoare Senzor Generic", "GenericSensorString": "Text Senzor Generic", "GenericSensorBoolTrue": "<PERSON><PERSON>", "GenericSensorBoolFalse": "Senzor Generic Fals", "allTriggers": "<PERSON><PERSON>-ele", "noResponseFromServer": "Nici un răspuns de la server", "loginFirst": "Va rugăm sa va autentificati", "removeTemplate": "Șterge Template", "updateTemplate": "Modifică Template", "dashboardEditorText": "Editor tab<PERSON> de bord", "reportTransformation": "Transformare", "none": "<PERSON><PERSON> unul", "id": "ID", "Id": "ID", "RuleDescription": "Des<PERSON><PERSON><PERSON>", "TimeStamp": "Dată și Oră", "ResourceName": "Nume resursă", "TriggerType": "<PERSON><PERSON>", "longitude": "<PERSON><PERSON><PERSON><PERSON>", "latitude": "Latitudine", "Longitude": "<PERSON><PERSON><PERSON><PERSON>", "Latitude": "Latitudine", "battery_level": "Nivel baterie", "sensor_id": "ID Senzor", "RuleId": "ID Regulă", "event_id": "ID Eveniment", "resource_name": "Nume resursă", "trigger_type": "<PERSON><PERSON>", "heading": "<PERSON><PERSON><PERSON><PERSON>", "resource_id": "ID Resursă", "time_stamp": "Dată și Oră", "foreign_id": "ID Cheie externă", "last_moved": "Ultimul mutat", "NoMovement": "<PERSON><PERSON> o <PERSON>care", "en": "עברית", "he": "Engleză", "areYouSureDelete": "Ești sigur ?", "searchTemplateWithThisNameAlredyExist": "Caută Template cu acest nume care există deja", "chooseDifferentName": "Alegeți un alt nume", "exportToCSV": "Exportă în CSV", "toggleSidebar": "Comută bara", "allResourcesInGroup": "Toate resursele din grup", "allResources": "Toate resursele", "fromDateShouldBeBeforeToDate": "'Până la data' trebuie sa fie după 'De la data'", "Date": "Data", "Name": "Nume", "Sensor": "<PERSON><PERSON>", "Start Time": "<PERSON>p start", "End Time": "<PERSON><PERSON>", "Work Hours": "Ore de muncă", "Distance": "Distanța", "DISTANCE": "Distanța", "distance": "Distanța", "Under Limit": "Sub Limită", "close": "<PERSON><PERSON><PERSON>", "now": "Acum", "Vmd": "VMD", "Relay": "<PERSON><PERSON><PERSON>", "SignalLoss": "Semnal pierdut", "HDFailure": "Eroare HD", "LocationChanged": "Locație schimbată", "BatteryLevel": "Nivel baterie", "SensorActive": "Senzor Activ", "SensorInactive": "Senzor Inactiv", "GarbageSensorDailyPickups": "<PERSON><PERSON><PERSON><PERSON> zil<PERSON> gunoi senzori", "GarbageTruckLate": "Camionul de gunoi întârziat", "GarbageTruckForbiddenArea": "Zonă interzisă camioanelor de gunoi", "ALPR": "ALPR", "IdTag": "ID Tag", "BELOW_WORK_LIMITS_TRIGGERS": "Declanșarea limitelor de lucru de mai jos", "NO_WORK_TRIGGERS": "Nu există declanșatoare de lucru", "WORKING_HOURS_TRIGGERS": "Declanșatoare ore de lucru", "Last Comm": "Ultimul comentariu", "last_comm": "Ultimul comentariu", "Comm Error": "Eroare de comunicație", "event_type": "Tip eveniment", "reportGenerationFailed": "Generare raport eș<PERSON>ă", "noData": "Nu există date", "entity_id": "ID Entitate", "entity_value": "Valoare entitate", "extractVideo": "Extragere Video", "selectVideoFormat": "Selectează Format", "maxRangeExceeded": "Interval maxim depășit", "extractionFail": "Extractie eșuată", "openChannelFail": "Deschidere canal eșuată", "openingChannel": "Deschidere canal", "startExtraction": "Începe extracț<PERSON>", "generateReportFail": "Generarea raportului a eșuat", "cantFindTemplate": "Șablonul nu poate fi găsit", "cantFindSession": "Vă rugăm să vă autentificați", "selectLang": "Alege limba", "selectFontSize": "Alege mărimea textului", "fontSizeSmall": "Mic", "fontSizeNormal": "Normal", "fontSizeLarge": "Mare", "firstName": "Nume", "lastName": "Prenume", "startDate": "Data de început", "expirationDate": "Data de expirare", "phone": "Telefon", "associatedDevices": "Dispozitive Asociate", "associatedResourceGroups": "Grupuri de resurse asociate", "fieldsMarkedWithAreMandatory": "Câmpurile cu * sunt obligatorii", "save": "Salvează", "startTimeNightTheme": "Ora de inceput pentru temă de noapte", "endTimeNightTheme": "Ora de sfârsit pentru temă de noapte", "dayNightSwitch": "Întrerupător auto temă zi/noapte", "enableAutoLoginSwitch": "Activați autentificarea automată", "fullScreen": "<PERSON><PERSON>ran complet", "userLacksPermissions": "Utilizatorul nu are această permisiune", "pleaseVerifyYourCredentials": "Vă rugăm să verificați credențialele", "maxAllowedUsersAreCurrentlyLoggedIn": "Numărul maxim de utilizatori autentificați este atins", "userAlreadyLoggedIn": "Utilizatorul este deja autentificat", "unableToConnectToSystemControl": "Conectarea la Control Sistem nu poate fi realizată", "applicationErrorUnableToConnectTimeOut": "Eroare - Conexiune eșuată - Time Out", "invalidLicense": "Licență invalidă", "operationFailed": "Operațiunea a eșuat", "both_limits": "Distanța și orele", "guid": "GUID", "copyToClipboard": "Copiați în clipboard", "copiedToClipboard": "Copiat în Clipboard", "vmsPageTitle": "<PERSON><PERSON><PERSON> camere", "help": "<PERSON><PERSON><PERSON>", "searchBox": "Caut<PERSON>", "changeTheme": "Schimba temă", "closeAll": "înch<PERSON>", "dayTheme": "Temă de zi", "nightTheme": "Temă de no<PERSON>", "Temperature": "Temperatură", "LightStrength": "Intensitate Luminoasă", "LightPower": "Putere Luminoasă", "LightEnergy": "Energie Ușoară", "LightCommQuality": "Calitate Comm Luminoasă", "LightFailure": "Lipsa lumină", "LightVoltage": "Voltaju<PERSON>", "LightCurrent": "Curentul Luminii", "LightPowerFactor": "Factor de putere luminos", "on": "Pornit", "off": "<PERSON><PERSON>", "OFF": "<PERSON><PERSON>", "ON": "Pornit", "customerDashboard": {"editDashboard": "Editați tabloul de bord", "addNewDashboard": "Adăugați un nou tablou de bord", "saveTemplateDashboard": "Salvați tabloul de bord ca șablon", "saveDashboard": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "addDashboardTitle": "Adăugați un nou tablou de bord", "editDashboardTitle": "Editați tabloul de bord", "addWidgetsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> widget-uri", "selectHierarchyLevel": "Selectați nivelul de ierarhie", "noDashboardParent": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Ștergeți tabloul de bord", "deleteConfirmationMessage": "Do<PERSON><PERSON>i să ștergeți acest tablou de bord ?", "deleteConfirmationHeader": "Confirma<PERSON> tablou de bord", "noWidgetsAvailable": "Nu sunt disponibile widget-uri.", "isOldDashboard": "Acesta este un tablou de bord vechi.", "noDashboardsAvailable": "Nu există tablouri de bord disponibile.", "errorGettingDashboard": "Eroare la obținerea tabloului de bord", "editWidget": "<PERSON><PERSON><PERSON>", "deleteWidget": "<PERSON><PERSON><PERSON>", "widgetSize": {"map": {"xXBig": "Mare", "xBig": "Me<PERSON>u", "big": "Mic"}, "player": {"big": "Mare", "medium": "Me<PERSON>u"}, "dashbox": {"small": "Mic", "xMedium": "Me<PERSON>u"}, "gauge": {"xSmall": "Mic", "medium": "Me<PERSON>u", "big": "Mare"}, "notification": {"big": "Mare", "medium": "Me<PERSON>u", "tall": "<PERSON><PERSON><PERSON>"}, "pieChart": {"big": "Mare", "medium": "Me<PERSON>u", "tall": "<PERSON><PERSON><PERSON>"}, "lineChart": {"big": "Mic", "wide": "Me<PERSON>u", "xWide": "Mare"}, "sensorStatus": {"big": "Mare"}, "emptySpace": {"xXBig": "Mare (5x5)", "xBig": "Mare (4x4)", "big": "Mare (3x3)", "medium": "Mediu (2x2)", "xMedium": "Mediu (1x3)", "small": "Mic (1x2)", "xSmall": "Mic (2x1)", "xXSmall": "Mic (1x1)", "tall": "Î<PERSON>t (4x2)", "xTall": "Înalt (4x1)", "wide": "Lat (3x4)", "xWide": "Lat (3x5)"}, "embeddedFile": {"xXSmall": "Ultra mic"}, "xXBig": "Mare (5x5)", "xBig": "Mare (4x4)", "big": "Mare (3x3)", "medium": "Mediu (2x2)", "xMedium": "Mediu (1x3)", "small": "Mic (1x2)", "xSmall": "Mic (2x1)", "xXSmall": "Mic (1x1)", "tall": "Î<PERSON>t (4x2)", "xTall": "Înalt (4x1)", "wide": "Lat (3x4)", "xWide": "Lat (3x5)"}, "editWidgetTitle": "<PERSON><PERSON><PERSON><PERSON> widget", "editWidgetSize": "Modificați dimensiunea widgetului", "selectMap": "Selectează <PERSON>", "selectResourceGroup": "Selectați din grupul de resurse", "itemsFound": "Obiecte găsite", "Core_RES_InputChannel": "Camere", "Core_RES_Light": "<PERSON><PERSON>", "Core_RES_Cabinet": "Cabinet", "Core_RES_ALPRLane": "ALPRLane", "Core_RES_Dashboard": "Dashboard", "Core_RES_Device": "<PERSON><PERSON>", "Core_RES_Input": "Input", "Core_RES_Output": "Output", "Core_RES_Layout": "Layout", "Core_RES_Map": "Map", "Core_RES_Entity": "Entity", "Core_RES_Asset": "<PERSON><PERSON>", "Core_RES_LightGW": "Light GW", "Core_RES_System": "Sistem", "selectGroup": "Selectează grup", "selectResource": "Selectează resursă", "selectTrigger": "Selectează declansator", "showAllGroups": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> toate grup<PERSON><PERSON>", "defaultMap": "Harta implicită", "widgetType": {"status": "Stare", "map": "Hartă", "player": "Video", "dashbox": "<PERSON><PERSON>", "timeline": "Cronologie", "chart": "Diagramă", "gauge": "Cadran", "notification": "Panou de notificari", "pieChart": "Graficul proporțiilor", "lineChart": "<PERSON><PERSON> linia<PERSON>", "sensorStatus": "Stare senzori", "embeddedFile": "<PERSON><PERSON><PERSON>", "urlShortcut": "Comandă rapidă URL"}, "thresholdPoints": "Puncte de prag", "minMaxMeasurmentValues": "Valori min & max & măsurare", "dataNotSet": "Datele nu sunt setate", "thresholdStart": "Start", "thresholdEnd": "Sfârșit", "thresholdEventName": "Numele evenimentului", "selectNotificationNumber": "<PERSON><PERSON><PERSON> notific<PERSON>", "chartDataType": "Tip de date", "selectDataType": "Selectați tip de date", "siteReadiness": "<PERSON><PERSON> resurselor", "tasks": "<PERSON><PERSON><PERSON>", "tasksUrgency": "<PERSON><PERSON><PERSON>", "dataIsZero": "Nu există date de afișat", "pieChartLabels": {"open": "<PERSON><PERSON><PERSON>", "closed": "<PERSON><PERSON><PERSON>", "inprogress": "În progres", "important": "Important", "low": "<PERSON><PERSON><PERSON><PERSON>", "normal": "Normal", "none": "<PERSON><PERSON> unul", "online": "Conectat", "offline": "Deconectat", "free": "Liber", "occupied": "Ocupat", "alarmed": "Alarmă", "error": "Eroare", "saved": "<PERSON><PERSON>", "on": "Pornit", "off": "<PERSON><PERSON>", "unknown": "Necunoscut", "dim": "<PERSON><PERSON> putere", "offyradio": "Oprit de catre Radio", "offbypower": "Oprit de energie", "temperror": "<PERSON><PERSON><PERSON> temporara", "notspecified": "Nespecificat", "withoutcommunication": "Lipsa comunicare"}, "select": "Selectează", "deselect": "Deselecteaza", "selectResourceState": "Selectați starea resursei", "selectTimePeriod": "Selectați perioada de timp", "Core_RES_Unknown": "Core RES Necunoscut", "selectResourceType": "Selectați tipul resursei", "selectColorClass": "Selectați culoarea", "selectBakgroundColorClass": "Selectați culoarea fundalului", "selectTextColorClass": "Selectați culoarea textului", "saveAsDefault": "Salvați ca implicit", "displayMethod": "Metoda a<PERSON>șare", "countResults": "Numara rezultatele", "resultValue": "Valoare rezultat", "groupedValue": "Valoare grupată", "dashboardPriority": "Prioritate Tablou de Bord", "displayInInventory": "Afișați în inventar"}, "dim": "Întunecos", "offByRadio": "Oprit de radio", "checkLight": "Verifică status", "checklight": "Verifică status", "Checklight": "Verifică status", "offMaintenanceCheck": "OFF (Verificare întreținere)", "OFF MAINTENANCE CHECK": "OFF (Verificare întreținere)", "offParamsFluctuation": "OFF (Fluctuații parametri)", "OFF PARAMS FLUCTUATION": "OFF (Fluctuații parametri)", "onParamsFluctuation": "ON (Fluctuații parametri)", "ON PARAMS FLUCTUATION": "ON (Fluctuații parametri)", "SIM ISSUE": "Gateway sim nu este conectat la rețea", "simIssue": "Gateway sim nu este conectat la rețea", "sim issue": "Gateway sim nu este conectat la rețea", "off maintenance check": "OFF (Verificare întreținere)", "maintenance check": "ON (Verificare întreținere)", "maintenanceCheck": "ON (Verificare întreținere)", "MAINTENANCE CHECK": "ON (Verificare întreținere)", "NO COMM": "Lipsă comunicare", "CHECK LIGHT": "Pornit (Verifică status)", "offByPower": "<PERSON><PERSON>", "tempError": "<PERSON><PERSON><PERSON>", "notSpecified": "Nespecificat", "withoutCommunication": "Nici o comunicare", "projectNameHe": "Nume Proiect He", "area": "Zona", "streetName": "<PERSON>ume <PERSON>rad<PERSON>", "poleId": "Id Stalp", "poleHeight": "Inăltime Stâlp", "lampPower": "<PERSON><PERSON>", "lampModel": "Model lampă", "projectNameEn": "Nume proiect En", "lampDriver": "<PERSON> lamp<PERSON>", "powerBoxId": "Putere Box Id", "pBoxNameHe": "P Box Name He", "pBoxLatitude": "P Box Latitude", "pBoxLongitude": "P <PERSON> Longitudine", "areaId": "Zona Id", "areaHeb": "Zona Heb", "projectLampId": "Proiect lampă Id", "streetId": "Stradă Id", "arm": "Armează", "nrItemsSelected": "{0} elemente selectate", "min": "Min", "max": "Max", "measurementUnit": "Unitate de masură", "formErrors": {"required": "Obligator<PERSON>", "startEndRequired": "Câmpurile de început și de sfârșit obligatorii", "pattern": "Codul nu satisface modelul"}, "dashboardSavedAsDefault": "Panoul de bord a fost salvat ca implicit", "deletedDefaultDashboard": "<PERSON><PERSON><PERSON><PERSON> de bord implicit șters", "noDataWaitingForEvents": "Fără date, în așteptarea evenimentelor", "micError": "Nu s-a putut începe înregistrarea microfonului", "appMap": {"noMapsAvailable": "Nu există hărți disponibile", "addMap": "<PERSON><PERSON><PERSON> hart<PERSON>", "editMap": "Șterge<PERSON><PERSON> hart<PERSON>", "deleteMap": "Salvați ca implicit", "saveAsDefault": "Salvat ca implicit", "deletedDefaultMap": "Hartă implicită ștersă", "editLayers": "<PERSON><PERSON><PERSON>", "tileSource": {"OSM": "Open Street Map", "SW": "Stamen Water Color", "raster": "<PERSON><PERSON>"}, "saveMap": "<PERSON><PERSON><PERSON>", "mapSaved": "Hartă salvată", "failedToSaveMap": "Salvarea hărții nu a reușit", "deleteConfirmationMessage": "Do<PERSON>ți să ștergeți această hartă?", "deleteConfirmationHeader": "Ștergeți confirmarea hărții?", "mapSavedAsDefault": "Harta salvată ca implicită", "selectTileSource": "Selectați Sursa Straturi", "mapName": "<PERSON><PERSON> hart<PERSON>", "layers": "<PERSON><PERSON><PERSON>", "manage": "Administreaza", "addNewLayer": "Adăugați un strat nou", "layerName": "Nume strat", "selectResourceGroup": "Selectați grupul de resurse", "selectResource": "Selectați resursă", "selectResourceState": "Selectați starea resursei", "selectResourceType": "Selectați tipul resursei", "availableDevices": "Resurse disponibile", "updateMapElement": "Actualizați elementul de hartă", "mapElementSaved": "Elementul de hartă a fost salvat", "failedToUpdateMapElement": "Actualizarea elementului de hartă nu a reușit", "saveMapElement": "Salvați elementul de hartă", "deleteMapElement": "Ștergeți elementul de hartă", "mapElementDeleted": "Elementul de hartă a fost șters", "failedToDeleteMapElement": "Stergerea elementului de hartă nu a reușit", "filter": "Fitre", "groupActions": "Grupuri active", "search": "<PERSON><PERSON><PERSON><PERSON> hart<PERSON>", "searchSuccessful": "Căutarea s-a efectuat cu succes", "searchHasResultsNumber": "Aveți {{resultsNumber}} rezultate.", "noResultsSummary": "Nu se aplică filtrele de căutare.", "resetSearch": "Resetați căutarea"}, "outdatedBrowser": "Va rugăm sa actualizați la cea mai recentă versiune!", "outdatedBrowserTitle": "Navigatorul dvs. este învechit", "free": "Liber", "occupied": "Ocupat", "alarmed": "Alarmat", "takeSnapshot": "Captură ecran", "finish": "Finalizare", "update": "Actualizați", "setLightIntensity": "Setați intensitatea luminii", "setWeatherFactor": "Setați factorul de vreme", "editFactors": "Editați factorii", "enableDisableGroups": "Activați / dezactivați grupurile", "compensatedValue": "Valoare compensată", "setFactorsSucces": "Factorii stabiliți cu succes", "setLightIntensitySucces": "Intensitatea luminii a fost setată cu succes", "setLightIntensityError": "Nu se poate seta intensitatea luminii", "setLumenFactor": "Setare factorul de lumeni", "setTrafficFactor": "Setare factorul de trafic", "updatedResource": "Resursă actualizată", "setLightIntensityFailure": "Setarea intensității luminii a eșuat", "openCamera": "Deschide camera", "inventory": {"inventory": "Inventar", "addDevice": "Adaugă dispozitiv", "editDevice": "Editați dispozitivul", "results": "Rezultate", "selectManufacturer": "Selectați producătorul", "selectModel": "selectează modelul", "credentials": "Date identificare", "httpPort": "Port HTTP", "rtspPort": "Port RTSP", "storageDrive": "Unitate de stocare", "rtspLink": "Link RTSP", "deleteConfirmationMessage": "<PERSON><PERSON><PERSON>i să ștergeți aceste resurse?", "deleteConfirmationHeader": "Confirma<PERSON>i ștergere resurselor?"}, "resourceGroupManagerNew": {"resourceGroup": "Managerul grupului de resurse", "newResourceGroup": "Creați un nou grup de resurse", "editResourceGroup": "Editați Grupul de resurse existent", "selectGroup": "Selectați un grup", "selectProfiles": "Selectați profiluri"}, "formValidation": {"field": "Câmpul este obligatoriu!", "numberAndSymbolsNotAllowed": "Numerele și simbolurile nu sunt permise!", "portField": "Câmpul este obligatoriu (ex: 127.0.0.1)!", "ipAddressField": "Nu este o adresă IP validă!", "minFourCharacters": "Trebuie să aibă cel puțin 4 caractere!"}, "swalMessages": {"selectEntity": "Selectați entitatea", "selectEntityMessage": "Managerul de resurse necesită selectarea a cel puțin unei entități. Vă rugăm să faceți o selecție pentru a continua cu acțiunea dvs."}, "resetTable": "Resetați tabelul și filtrele", "location": "Locație", "propertiesNoDataWasFound": "Nu au fost găsite date suplimentare", "viewInfo": "Vizualizați detalii", "refreshLightData": "Actualizare date stalp iluminat", "refreshLightDataSuccess": "Actualizarea luminii a fost facută cu success", "refreshLightDataError": "Actualizarea luminii a întâmpinat o eroare", "refreshLightDataFailure": "Actualizarea luminii a eșuat", "lat": "Latitudine", "lng": "<PERSON><PERSON><PERSON><PERSON>", "resourceType": "Tipul resursei", "groups": "<PERSON><PERSON><PERSON>", "LightWorkingHours": "Ore de lucru", "LightId": "Id-ul luminii", "AlarmConfigurationText": "Configurare alarmă", "AlarmSeverityValue": "Severitate alarmă", "AlarmStatusValue": "Stare alarmă", "LightTemperature": "Temperatura", "IsAuxOn": "Lumina auxiliara este pornita", "AuxEnergy": "Energie auxiliară", "AuxPower": "Putere auxiliară", "LightNominalPower": "Putere nominală", "PhotoSensorPresence": "Prezența senzorului fotocelulă", "actions": "Acțiuni", "status": "Stare", "lampGUID": "GUID lampă", "refreshData": "<PERSON><PERSON><PERSON><PERSON> datele", "importCSV": "Importa CSV", "colsSelected": "{0} coloane selectate", "selectCol": "Selectați coloanele", "resultsAmount": "<PERSON><PERSON><PERSON><PERSON> de rezulta<PERSON>:", "of": "din", "jumpToMap": "<PERSON><PERSON>te <PERSON>n hart<PERSON>", "jumpToInventory": "Du-te în inventar", "name": "Nume", "identity": "Identitate", "VCA5_LINECOUNTER_A": "VCA Trecerea liniei A", "VCA5_DWELL": "VCA Oprire", "VCA5_PRESENCE": "VCA Prezență", "VCA5_ENTER": "VCA Intrare", "VCA5_EXIT": "VCA Ieșire", "VCA5_APPEAR": "VCA Apare", "VCA5_DISAPPEAR": "VCA Dispare", "VCA5_STOP": "VCA Stop", "VCA5_DIRECTION": "VCA Direcţie", "VCA5_SPEED": "VCA Viteză", "VCA5_TAILGATING": "VCA Urmărire", "VCA5_LINECOUNTER_B": "VCA Trecerea Liniei B", "VCA5_ABOBJ": "VCA Abobj", "VCA5_SMOKE": "VCA Fum", "VCA5_FIRE": "VCA Foc", "VCA5_COLSIG": "VCA ColSig", "VCA5_UNKNOWN": "VCA Necunoscut", "VCA5_PRESENCE_End": "VCA Sfârșitul Prezenței", "VCA5_ENTER_End": "VCA Intrare Sfârșit", "VCA5_EXIT_End": "VCA Ieșire Final", "VCA5_APPEAR_End": "VCA Apariția Finală", "VCA5_DISAPPEAR_End": "VCA Dispariție Finală", "VCA5_STOP_End": "VCA Final Sfârșit", "VCA5_DWELL_End": "VCA Dwell Sfârșit", "VCA5_DIRECTION_End": "VCA Directie Sfârșit", "VCA5_SPEED_End": "VCA Viteza Sfârșit", "VCA5_TAILGATING_End": "VCA Urmarire Sfârșit", "VCA5_LINECOUNTER_A_End": "VCA Contor Linie A Sfârșit", "VCA5_LINECOUNTER_B_End": "VCA Capătul Contorului De Linie B", "VCA5_ABOBJ_End": "VCA Abobj Final", "VCA5_RMOBJ_End": "VCA Rmobj Final", "VCA5_SMOKE_End": "VCA Fumat <PERSON>", "VCA5_FIRE_End": "VCA Foc Sfârșit", "VCA5_COLSIG_End": "VCA Incheiere Sfârșit", "VCA5_UNKNOWN_End": "VCA Sfârșit Necunoscut", "selectFieldName": "Selectați numele câmpului", "selectOperand": "Selectați operand", "freeText": "Text", "reset": "Resetați", "operand": {"equals": "Egal", "notEquals": "Nu este egal", "contains": "<PERSON><PERSON><PERSON>", "notContains": "<PERSON><PERSON>"}, "selectedRows": "<PERSON><PERSON> selectate", "selectedRow": "Rand selectat", "deviceNotSelected": "<PERSON><PERSON> neselectat", "marketPlace": "Market place", "eventName": "Nume eveniment", "total": "Total", "allEvents": "Listă evenimente", "selectEvent": "Selectați evenimentul", "resetResource": "Resetați resursă", "resetResourceSuccess": "Resursa a fost resetată", "resetResourceError": "Resetarea resursei a eșuat", "Core_RES_Unknown": "Necunoscut", "Core_RES_Node": "Nod", "Core_RES_MapElement": "Element hart<PERSON>", "Core_RES_MapPreset": "<PERSON>set<PERSON> hartă", "Core_RES_MapText": "Text hartă", "Core_RES_OutputChannel": "Canal de ieșire", "Core_RES_LayoutPreset": "<PERSON>zen<PERSON><PERSON> mac<PERSON>i", "Core_RES_Account": "<PERSON><PERSON>", "Core_RES_AccountProfile": "Profilul contului", "Core_RES_ChannelTour": "<PERSON><PERSON>l canalului", "Core_RES_PTZPreset": "Presetare PTZ", "Core_RES_ICN": "ICN", "Core_RES_VideoWall": "Perete video", "Core_RES_ResourceGroup": "Grup de resurse", "Core_RES_PTZPattern": "Model PTZ", "Core_RES_PTZAuxiliary": "PTZ auxiliar", "Core_RES_ProcedureTemplate": "Model procedură", "Core_RES_Sensor": "<PERSON><PERSON>", "Core_RES_LightResourceGroup": "Grup lumini", "Core_RES_Geofence": "Geofence", "Core_RES_WebStreamer": "Streamer Web", "Core_RES_LightGW": "Lumină GW", "Core_RES_Asset": "Persoane", "Core_RES_PowerMeter": "<PERSON><PERSON><PERSON>", "successfullyDownloaded": "Descărcat cu succes!", "downloadError": "Nu poate fi descărcat!", "openNewTab": "Deschide o filă nouă", "generatingReport": "Raport în curs de generare", "transformation": "Transformare", "generatingReportCancelled": "Generarea raportului a fost anulată", "MinVoltage": "<PERSON>", "MaxVoltage": "<PERSON>", "MinTotalPower": "Putere totală minimă", "MaxTotalPower": "Putere maximă totală", "MinVoltageBetweenPhases": "Voltaj Minim între faze", "MaxVoltageBetweenPhases": "Voltaj maxim între faze", "MinPowerFactor": "Factor de putere min", "MaxPowerFactor": "Factor de putere maximă", "AMC_RESOURCE_STATUS": "Stare resursă", "AMC_ENVIRONMENT_DUST_PM1": "Concentrația de praf PM1", "AMC_ENVIRONMENT_DUST_PM25": "Concentrația de praf PM 2.5", "AMC_ENVIRONMENT_DUST_PM10": "Concentrația de praf PM 10", "AMC_ENVIRONMENT_SO2_LEVEL": "Nivel SO2", "AMC_ENVIRONMENT_NO2_LEVEL": "Nivel NO2", "AMC_ENVIRONMENT_O3_LEVEL": "Nivel O3", "AMC_PRESENCE_STATS": "Starea prezenței", "AMC_PROFILE": "Profil", "AMC_STATE": "Stare", "AMC_AREA_ID": "ID zonă", "AMC_RULE_NAME": "<PERSON><PERSON>le regulii", "AMC_VIDEO_SOURCE": "Sursa video", "AMC_SOURCE": "Sursă", "AMC_MOTION_TYPE": "<PERSON><PERSON>", "AMC_VIDEO_ANALYTICS": "Analitică video", "AMC_DOOR_STATE": "Starea <PERSON>ii", "AMC_ENTITY_FIRST_NAME": "Prenume", "AMC_ENTITY_LAST_NAME": "Nume de familie", "AMC_ENTITY_MIN_AGE": "<PERSON><PERSON><PERSON><PERSON> mini<PERSON>", "AMC_ENTITY_MAX_AGE": "Vârsta maxim<PERSON>", "AMC_ENTITY_GENDER": "Gen", "AMC_ENTITY_USER_ID": "ID utilizator", "AMC_ENTITY_CONFIDENCE": "<PERSON><PERSON><PERSON><PERSON>", "AMC_DETECTION_TIME": "<PERSON><PERSON>", "AMC_SOURCE_DESCRIPTION": "Descrier<PERSON> sursei", "AMC_SHOCK": "Șoc", "PEOPLE_COUNT": "<PERSON><PERSON><PERSON><PERSON>", "videoWall": "Perete video", "AMC_SENSOR_CO": "Nivel CO", "saveLayout": "<PERSON><PERSON><PERSON>", "layoutSelected": "Macheta selectată", "playerAdded": "Player <PERSON><PERSON><PERSON><PERSON>", "makeFullScreen": "Folosiți tot ecranul", "showSideBar": "Afișați bara laterală", "PERSON_STATS": "Statistici persoane", "VEHICLE_STATS": "Statistici vehicule", "FACE_STATS": "Statistici față", "AMC_IO_ALARM_OUTPUT": "Alarme IO Output", "AMC_IO_ANALOG_VALUE": "Valoare analogă IO", "AMC_IO_VALUE_0_VOLT": "Valoare 0 Volt IO", "AMC_IO_VALUE_10_VOLT": "Valoare 10 Volt IO", "AMC_IO_COMMUNICATION_STATUS": "Stare comunicare IO", "AMC_IO_COMMUNICATION_MODE": "Mod comunicare IO", "AMC_IO_ALARM_TEXT": "Text alarme IO", "AMC_IO_ALARM_ID": "ID alarme IO", "AMC_IO_ALARM_SEVERITY": "Severitate alarme IO", "Line1ActiveWatts": "Putere R", "Line2ActiveWatts": "Putere S", "Line3ActiveWatts": "Putere T", "ActiveWattHours": "Energie totală activă", "Line1ApparentVoltsAmps": "Putere aparentă R", "Line2ApparentVoltsAmps": "Putere aparentă S", "Line3ApparentVoltsAmps": "Putere aparentă T", "ApparentVoltAmpHours": "Ore de amploare a volumului aparent", "CombinedActiveWatts123": "Combinate active active 123", "CombinedApparentWatts123": "Watts aparent combinate 123", "CombinedPowerFactor123": "Factor de putere combinată 123", "CombinedReactiveWatts123": "Watts Reactive Combinate 123", "Line1Current": "Curent R", "Line2Current": "Curent S", "Line3Current": "Curent T", "NeutralLineCurrent": "Line Neutră Current", "Line1PowerFactor": "PF R", "Line2PowerFactor": "PF S", "Line3PowerFactor": "PF T", "Line1VoltsAmpsReactive": "Putere reactivă R", "Line2VoltsAmpsReactive": "Putere reactivă S", "Line3VoltsAmpsReactive": "Putere reactivă T", "Line3ToLine1Voltage": "Tensiune încrucișată TR", "Line1ToLine2Voltage": "Tensiune încrucișată RS", "Line2ToLine3Voltage": "Tensiune încrucișată ST", "Line1Voltage": "Line Voltaj R", "Line2Voltage": "Line Voltaj S", "Line3Voltage": "Line Voltaj T", "NeutralCurrent": "Curent neutru", "p1": "P1", "p2": "P2", "p3": "P3", "p4": "P4", "p5": "P5", "p6": "P6", "p7": "P7", "p8": "P8", "p9": "P9", "p10": "P10", "p11": "P11", "p12": "P12", "p13": "P13", "p14": "P14", "p15": "P15", "p16": "P16", "p17": "P17", "p18": "P18", "p19": "P19", "p20": "P20", "p21": "P21", "p22": "P22", "p23": "P23", "p24": "P24", "p25": "P25", "p26": "P26", "p27": "P27", "p28": "P28", "p29": "P29", "p30": "P30", "lamptype": "<PERSON><PERSON>", "dimmingRequest": "Solicitare potentiometru", "refreshPowerMeterData": "Actualizează datelor contorului electric", "refreshPowerMeterDataSuccess": "Actualizarea contorului electric a fost facută cu success", "refreshPowerMeterDataError": "Actualizarea contorului electric nu a reușit", "refreshPowerMeterDataFailure": "Actualizarea contorului electric a eșuat", "scheduler": "Planificator", "addedScheduler": "Programarea a fost adăugată", "cannotAddSchedule": "Programarea nu a putut fi adăugată", "deletedScheduler": "Programarea a fost stearsă", "updatedScheduler": "Programarea a fost actualizată", "cannotUpdateSchedule": "Programarea nu a putut fi actualizată", "type": "Tip", "description": "Des<PERSON><PERSON><PERSON>", "add": "Adaugă", "edit": "Editează", "duplicate": "<PERSON><PERSON>", "action": "Acţiune", "priority": "Prioritate", "timeType": "Perioadă zi", "time": "<PERSON><PERSON>", "specificTime": "Perioadă specifică", "sunrise": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sunset": "Apus", "choose": "Selectează", "SpecificDay": "Zile specifice", "downloadEntitiesTemplate": "Descărcați șablonul de entități", "downloadLightsTemplate": "Descărcați șablonul de lumini", "yearly": "<PERSON><PERSON>", "monthly": "Lunar", "customDate": "Perioadă personalizată", "sun": "<PERSON><PERSON><PERSON><PERSON>", "mon": "<PERSON><PERSON>", "tue": "<PERSON><PERSON><PERSON>", "wed": "<PERSON><PERSON><PERSON><PERSON>", "thu": "<PERSON><PERSON>", "fri": "<PERSON><PERSON>", "sat": "Sâmbătă", "entities": "Entități", "settings": "<PERSON><PERSON><PERSON>", "LightName": "Numele lumină", "DimLevel": "Nivel de luminozitate", "LastUpdateTime": "Ultima actualizare", "onDeletedScheduler": "Ștergeți programarea?", "areYouSureYouWantToDeleteThisSchedule": "Esti sigur că vrei să ștergi acestă programare?", "FireTrigger": "Declanșează acţiunea", "IoCommand": "Setează status grup", "SetLightGroupStrength": "Setează intensitatea grupului de lumini", "resourceGroups": "Grupuri de resurse", "actionLevel": "Nivel intensitate", "dayOfMonth": "<PERSON><PERSON><PERSON> lunii", "Custom": "Personalizat", "RepeatYearly": "Repetați anual", "RepeatMonthly": "Repetați lunar", "RepeatWeekly": "Repetați săptămânal", "Every": "<PERSON><PERSON><PERSON>", "Years": "<PERSON><PERSON>", "Year": "An", "upload": "Încă<PERSON><PERSON><PERSON>", "lumenFactor": "Factor de lumen", "addedRtsp": "RTSP a fost adăugat", "cannotAddRtsp": "RTSP nu a putut fi adăugată", "selectTypeOfTheFile": "Selectați tipul de fișier", "CSVUpload": "Încărcare CSV", "rtspChannels": "Canale RTSP", "dropFileOrClickToUpload": "Încarcă fișierul sau faceți clic pentru a încărca", "PowerMeter_MinVoltage": "PM <PERSON>", "PowerMeter_MaxVoltage": "PM <PERSON>", "PowerMeter_MinTotalPower": "PM <PERSON>", "PowerMeter_MaxTotalPower": "PM <PERSON> TP", "PowerMeter_MinVoltageBetweenPhases": "PM Min V BP", "PowerMeter_MaxVoltageBetweenPhases": "PM Max V BP", "PowerMeter_MinPowerFactor": "PM <PERSON>", "PowerMeter_MaxPowerFactor": "PM <PERSON>", "PowerMeter_Line1ActiveWatts": "PM L1 AW", "PowerMeter_Line2ActiveWatts": "PM L2 AW", "PowerMeter_Line3ActiveWatts": "PM L3 Aw", "PowerMeter_ActiveWattHours": "PM Active WH", "PowerMeter_Line1ApparentVoltsAmps": "Putere R aparenta", "PowerMeter_Line2ApparentVoltsAmps": "Putere S aparenta", "PowerMeter_Line3ApparentVoltsAmps": "Putere T aparenta", "PowerMeter_ApparentVoltAmpHours": "PM AVAH", "PowerMeter_CombinedActiveWatts_123": "Wati activi combinati 123", "PowerMeter_CombinedApparentWatts_123": "Amperi activi combinati 123", "PowerMeter_CombinedPowerFactor_123": "Factor de putere combinata 123", "PowerMeter_CombinedReactiveWatts_123": "Wati reactivi combinati 123", "PowerMeter_Line1Current": "Curent R", "PowerMeter_Line2Current": "Curent S", "PowerMeter_Line3Current": "Curent T", "PowerMeter_NeutralLineCurrent": "PM NLC", "PowerMeter_Line1PowerFactor": "PF R", "PowerMeter_Line2PowerFactor": "PF S", "PowerMeter_Line3PowerFactor": "PF T", "PowerMeter_Line1VoltsAmpsReactive": "Putere reactiva R", "PowerMeter_Line2VoltsAmpsReactive": "Putere reactiva S", "PowerMeter_Line3VoltsAmpsReactive": "Putere reactiva T", "PowerMeter_Line3ToLine1Voltage": "Cross Voltage TR", "PowerMeter_Line1ToLine2Voltage": "Cross Voltage RS", "PowerMeter_Line1ToLine3Voltage": "Cross Voltage ST", "PowerMeter_Line1Voltage": "Voltaj R", "PowerMeter_Line2Voltage": "Voltaj S", "PowerMeter_Line3Voltage": "Voltaj T", "PowerMeter_NeutralCurrent": "<PERSON><PERSON>", "PowerMeter_Failure": "PM F", "showResourcesFromGroup": "Afișați resursele din Grupul de resurse", "acknowledgeClick": "<PERSON><PERSON><PERSON> clic pentru a confirma", "dismiss": "Respinge", "acknowledge": "Aprobă", "enableAutoAcknowledgeEvents": "Activați recunoașterea automată", "enableAutoClearEvents": "Activați ștergerea automată", "noElementsForSnapshotFound": "Nu s-au găsit elemente pentru snapshot", "deleteResource": "Resursa a fost ștearsă", "cannotDeleteResources": "Resursele nu pot fi șterse", "StatusChange": "<PERSON><PERSON><PERSON><PERSON> de stare", "resourceStatus": "Starea resursei", "LightColor": "Culoare<PERSON>", "LeditechStatusCode": "Cod de stare <PERSON>", "ackEventSuccess": "Aprobarea evenimentul a reușit", "ackEventError": "Aprobarea evenimentul a eșuat", "widgetOutOfBounds": "Widgetul a fost eliminat deoarece nu incape pe tabloul de bord", "icon-view-1": "1 ecran activ", "icon-view-4": "4 ecrane active", "icon-view-9": "9 ecrane active", "icon-view-10": "10 ecrane active", "showMore": "Afișați mai multe", "showLess": "<PERSON><PERSON> mai pu<PERSON>in", "default": "Mod implicit", "low": "<PERSON><PERSON><PERSON><PERSON>", "normal": "Normal", "important": "Important", "urgent": "<PERSON><PERSON>", "ipAddress": "Adresa IP", "userName": "Nume Utilizator", "enabled": "Activ", "remove": "Elimină", "selectCSVColumn": "Selectați o coloană CSV", "REPORT_COUNT": "<PERSON><PERSON><PERSON><PERSON>", "active_watt_hours": "Ore active watt-oră", "apparent_volt_amp_hours": "Ore aparente de volt amp", "combined_active_watts_123": "123 de wați activi combinați", "combined_apparent_watts_123": "Potențe aparente combinate 123", "combined_power_factor_123": "Factor de putere combinat 123", "combined_reactive_watts_123": "Wați reactivi combinați 123", "line1_active_watts": "Linie 1 wați activi", "line1_apparent_volt_amps": "Amplificatori de tensiune aparentă Line1", "line1_current": "Linia 1 curent", "line1_power_factor": "Factor de putere Line1", "line1_to_line2_voltage": "Tensiunea de la linia 1 la linia 2", "line1_voltage": "Tensiunea liniei 1", "line1_volts_amps_reactive": "Linia 1 volți amperi reactivi", "line2_active_watts": "Line2 wați activi", "line2_apparent_volt_amps": "Amplificatori de voltaj aparent Line2", "line2_current": "Linia 2 curent", "line2_power_factor": "Factor de putere Line2", "line2_to_line3_voltage": "Tensiunea de la linia 2 la linia 3", "line2_voltage": "Tensiunea liniei 2", "line2_volts_amps_reactive": "Linia 2 volți amperi reactivi", "line3_active_watts": "Line3 wați activi", "line3_apparent_volt_amps": "Amplificatori de voltaj aparent Line3", "line3_current": "Linia 3 curent", "line3_power_factor": "Factor de putere Line3", "line3_to_line1_voltage": "Tensiunea de la linia 3 la linia 1", "line3_voltage": "Tensiunea liniei 3", "line3_volts_amps_reactive": "Linia 3 volți amperi reactivi", "max_power_factor": "Factor de putere maxim", "max_total_power": "Puterea totală maximă", "max_voltage": "Tensiunea maximă", "max_voltage_between_phases": "Tensiunea maximă între faze", "min_power_factor": "Factor de putere minim", "min_total_power": "Putere totală minimă", "min_voltage": "Tensiune minimă", "min_voltage_between_phases": "Tensiunea minimă între faze", "neutral_current": "Curent neutru", "neutral_line_current": "Curent de linie neutră", "LEDITECH_CONSUMPTION": "Consum de energie", "number_items": "Număr de articole", "select_item": "Selectați elemente", "generateChart": "Generați diagramă", "data_values": "Valori", "PowerMeter_AverageVoltage": "PM Avg V", "PowerMeter_AveragePowerFactor": "PM Avg PF", "PowerMeter_TotalPower": "PM Total P", "PowerMeter_Line2ToLine3Voltage": "PM L2-L3-V", "Core_RES_Light_Plural": "<PERSON><PERSON>", "Core_RES_Input_Plural": "<PERSON><PERSON><PERSON><PERSON>", "Core_RES_Output_Plural": "<PERSON><PERSON><PERSON><PERSON>", "Core_RES_PowerMeter_Plural": "<PERSON><PERSON><PERSON> de <PERSON>ere", "Core_RES_InputChannel_Plural": "Camere", "link": "Legătură web", "invalidDomain": "Adresa URL trebuie să conțină domeniul local", "reportResults": "Rezultate Raport", "gridColumnSize": "<PERSON><PERSON><PERSON><PERSON><PERSON> colo<PERSON>i", "dashboardHasDifferentResolution": "Acest tablou de bord a fost creat pentru dimensiunea ecranului de {{width}} x {{height}}", "unableToSaveDashboardDueToScrollInfo": "Tabloul de bord nu va fi salvat dacă depășește dimensiunea verticală a ecranului", "unableToSaveDashboardDueToScrollError": "Tabloul de bord nu poate fi salvat deoarece depășește dimensiunea verticală a ecranului", "cameraAlreadyInUse": "Camera este deja utilizată", "dateIsRequired": "Trebuie să selectați o dată", "tableView": "Vizualizare în tabel", "mapView": "Vizualizare în hartă", "viewType": "Tipul de vizualizare", "dashboxResourcesView": "Vizualizare resurse Dashbox", "dismissEventSuccess": "Anularea evenimentului a reușit", "dismissEventError": "Anularea evenimentului a eșuat", "alreadyAckNotification": "Ați recunoscut deja acest eveniment", "AMC_LPR_DATE_TIME": "Data ora LPR", "AMC_LPR_PLATE_NO": "Numărul plăcii LPR", "AMC_LPR_PLATE_COUNTRY": "Țara plăcii LPR", "AMC_LPR_PLATE_CONFIDENCE": "Încrederea plăcii LPR", "AMC_LPR_MOVE_DIRECTION": "Direcția de mișcare a LPR", "AMC_LPR_MS_IMAGE_PROCESSING": "Procesarea imaginii LPR", "AMC_LPR_VEHICLE_BRAND": "Marca vehiculului LPR", "AMC_LPR_VEHICLE_MODEL": "Model de vehicul LPR", "AMC_LPR_VEHICLE_TYPE": "Tipul vehicul LPR", "AMC_LPR_VEHICLE_COLOR": "Culoarea vehiculului LPR", "AMC_LPR_VEHICLE_CONFIDENCE": "Încrederea vehiculului LPR", "AMC_LPR_SOURCE_CAMERA_NAME": "Numele camerei sursă LPR", "AMC_LPR_SOURCE_CAMERA_ADDRESS": "Adresa camerei LPR", "AMC_PERSON_STATS": "Statistici despre persoane", "AMC_VEHICLE_STATS": "Statistici vehicul", "AMC_PEOPLE_COUNTING": "Num<PERSON><PERSON><PERSON><PERSON><PERSON>ni", "detectionList": {"detectionList": "Detecții în Trafic", "camera": "<PERSON><PERSON><PERSON>", "brand": "Brand", "color": "<PERSON><PERSON><PERSON>", "country": "Țară", "licensePlate": "Placuță de Înmatriculare", "vehicleModel": "<PERSON>eh<PERSON>", "vehicleType": "<PERSON><PERSON>", "cameraTime": "<PERSON><PERSON>", "plateConfidence": "Confidență Placuță", "vehicleConfidence": "Confidență Vehicul", "platePhoto": "<PERSON><PERSON> Placuței", "downloadPhotos": "Des<PERSON><PERSON><PERSON> pozele", "downloadFailed": "Descarcarea pozelor a eșuat", "platePhotoDeleted": "Poza placuței a fost ștearsă", "platePhotoFailedToDownload": "Descarcarea pozei placuței a eșuat", "detectionPhotoDeleted": "Poza detecției a fost ștearsă", "detectionPhotoFailedToDownload": "Descarcarea pozei detecției a eșuat", "actions": "Acțiuni", "viewOnMap": "<PERSON>ez<PERSON> pe <PERSON>", "missingDetectionPhoto": "Fotografia detecției lipsește", "searchSpecific": "Cau<PERSON>i ceva anume?", "reset": "Resetați filtrele", "noTrafficDetectionsFound": "Nu s-au găsit detecții de trafic"}, "follow": {"follow": "Urmareș<PERSON>", "items": "<PERSON><PERSON><PERSON><PERSON>", "location": "Locație GPS", "missingPlateImage": "Fotografia placuței lipsește", "newDetectionHasOccurred": "O nouă detecție a avut loc"}, "vehicleTraffic": {"back": "Înapoi", "pollutionStandardBroken": "Acest vehicul a incalcat standardul minim de poluare admis în zonă", "accessFeeNotPayed": "Acest vehicul nu a achitat taxa de acces în zonă", "licenseExpiredOrSuspended": "Soferul titular al acestui autovehicul are permisul suspendat sau expirat", "vignetteNotPayed": "Acest autovehicul nu are vigneta activă", "technicalInspectionExpired": "ITP-ul acestui autovehicul a expirat", "vehicleTaxNotPayed": "Acest vehicul nu are taxele platite la zi"}, "detectionsChart": {"notEnoughData": "Date insuficiente pentru a afisa un grafic", "detectionsInInterval": "Detecții în intervalul"}, "parkingDetections": {"parkingDetections": "Detecții Parcare", "paidAccess": "Taxa de parcare a fost platită", "status": "Stare"}, "playerBackgroundImage": "Imaginea de fundal a player-elor", "resetBackgroundImage": "Resetează imaginea de fundal a player-elor", "vcaOptions": "Opțiuni de analiză video", "showVCAObjects": "Afișează obiecte VCA", "showVCAConfig": "Afișează configurația VCA", "showPTZControls": "Afișează controalele PTZ", "openToggleTimeline": "Calendarul înregistrărilor", "entitySavedSuccessfully": "Entitatea a fost salvată", "signalingServerDown": "Serverul de semnalizare este oprit !", "procedureStep": "Pa<PERSON> procedurii", "generalDetections": {"generalDetections": "Detecții generale", "detectionId": "ID detecție", "description": "Des<PERSON><PERSON><PERSON>", "timeStamp": "Data și ora", "inputChannel": "Canal", "startDate": "Data de început", "endDate": "Data de sfârșit", "entityId": "ID entitate", "plateId": "<PERSON><PERSON><PERSON><PERSON>", "downloadArchive": "Descărcați arhiva", "jumpToTrafficDetections": "Salt la detecțiile de trafic", "followOnTrafficDetections": "Urmăriți detecțiile de trafic", "jumpToDossier": "Salt la dosar", "generalDetection": "Detectie generală", "reset": "Resetați filtrele", "actions": "Acțiuni", "noGeneralDetectionsFound": "Nu s-au găsit detecții generale", "searchFirstField": "Căutare după primul criteriu", "searchSecondField": "Căutare după al doilea criteriu", "selectResourceGroup": "Selectați un grup de resurse", "selectStartDate": "Selectați data și ora de început", "selectEndDate": "Selectați data și ora de sfârșit"}, "welcomeBack": "Bine ai revenit!", "enterLoginDetails": "<PERSON>ă rugăm să introduceți detaliile de conectare mai jos", "enterSystemIP": "Introduceți IP-ul sistemului", "enterUsername": "Introduceți numele de utilizator", "enterPassword": "Introduceți parolă", "signIn": "Conectare", "version": "Versiune", "notificationsPage": {"totalRecordsFormat": "{totalRecords} Total Alerte", "value": "Valoare"}, "car": "Autoturism", "truck": "Camion", "suv": "Suv", "van": "Furgoneta", "lcv": "Dub<PERSON>ț<PERSON>", "lastOpenedCameras": "Ultimele deschise", "layouts": "Layout-uri", "systemStructure": "Structura de Sistem", "cameras": "Camere", "online": "Online", "offline": "Offline", "cameraStatusHistory": "Istoricul Stării Camerelor", "clearHistory": "Șterge Istoricul", "noCommunication": "Fără <PERSON>", "actualizationIn": "Actualizare automată în", "sec": "sec", "dispatch": "<PERSON><PERSON><PERSON><PERSON>", "alerts": "<PERSON><PERSON><PERSON>", "alarms": "Alarme", "warnings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "camera": "Camera", "detections": "Detecții", "loading": "Se încarcă...", "playback": "Redare", "downloadArchive": "Descarcă arhiva", "sumarAlarmeUltimele72Ore": "<PERSON><PERSON> alarme pentru ultimele 72 de ore", "unresolvedAlarms": "Alarme Nesoluționate", "nonconformingAlarms": "Alarme Neconforme", "alarmsVerified": "Alarme Verificate", "today": "<PERSON><PERSON><PERSON><PERSON>", "eventID": "ID eveniment", "pm": "PM", "hello": "<PERSON>un venit", "search": "<PERSON><PERSON><PERSON><PERSON>", "searchByCamera": "<PERSON><PERSON><PERSON><PERSON> dup<PERSON> cameră, locație...", "searchButton": "Caut<PERSON>", "dossiers": "<PERSON><PERSON><PERSON>", "dossierTable": {"title": "Dosar", "searchByName": "Cautare dupa nume", "readOnly": "Doar citire", "all": "Toate", "onlyDossiers": "<PERSON><PERSON>", "onlyArchives": "<PERSON><PERSON>hi<PERSON>", "startTime": "<PERSON>p start", "endTime": "<PERSON><PERSON>", "autoDelete": "Auto-Ștergere", "name": "Nume", "timestamp": "Data și ora", "actions": "Acțiuni", "viewDetections": "<PERSON>ezi Detecții", "toggleAutoDelete": "Comută Auto-Ștergere", "uploadDossier": "Încărcați Dosar", "viewDetails": "<PERSON><PERSON><PERSON>", "noDossiersFound": "Nu s-au gasit dosare", "showingEntries": "Se afiseaza de la {first} la {last} din {totalRecords} dosare", "autoDeleteTrue": "Ștergere automată activată", "autoDeleteFalse": "Ștergere automată dezactivată", "reset": "Resetați filtrele", "alreadyDeleted": "Dosarul a fost șters deja la: {{ timestamp }}", "deletionTime": "Or<PERSON>ii"}, "dossierHistory": {"title": "Istoric", "search": "Cautare...", "startTime": "<PERSON>p start", "endTime": "<PERSON><PERSON>", "details": "<PERSON><PERSON><PERSON>", "timestamp": "Data și ora", "actions": "Acțiuni", "download": "Des<PERSON><PERSON><PERSON>", "noHistoryFound": "Nu s-a gasit istoric", "showingEntries": "Se afisează de la {first} la {last} din {totalRecords} intr<PERSON>ri", "downloadError": "Descarcarea dosarului a eșuat", "loadError": "Incarcarea istoricului dosarului a eșuat", "noDossierId": "Nu a fost găsit niciun ID de dosar pentru ștergere", "dossierDeletedSuccessfully": "Dosarul a fost șters cu succes", "failedDeleteDossier": "Nu s-a putut șterge dosarul", "noEventIdNavigation": "Nu a fost găsit niciun ID de eveniment pentru navigare", "noEventIdDownload": "Nu a fost găsit niciun ID de eveniment pentru descărcare", "failedToLoadDossiers": "Nu s-au putut încărca dosarele", "failedToLoadDossierDetails": "Nu s-au putut încărca detaliile dosarului", "failedToggleReadOnly": "Nu s-a putut comuta doar în citire", "failedToggleAutoDelete": "Nu s-a putut activa ștergerea automată"}, "dossierDetails": {"title": "<PERSON><PERSON><PERSON>", "basicInformation": "Informații de bază", "id": "ID", "eventId": "ID Eveniment", "name": "Nume", "creationTime": "Data creării", "status": "Status", "autoDelete": "Ștergere automată", "enabled": "Activat", "disabled": "Dezactivat", "readOnly": "Doar citire", "yes": "Da", "no": "<PERSON>u", "liveDuration": "<PERSON><PERSON> live", "archiveDuration": "<PERSON><PERSON><PERSON> a<PERSON>", "hours": "ore", "storage": "Stocare", "path": "<PERSON>", "size": "Mărime", "close": "<PERSON><PERSON><PERSON>", "details": "<PERSON><PERSON><PERSON>", "timeStamp": "Data și ora", "fileList": "Listă fișiere"}, "dossierUpload": {"noFileSelected": "<PERSON><PERSON><PERSON> selectat", "fileUploadSuccessfully": "Incărcarea s-a efectuat cu succes", "invalidFileType": "Fisier invalid. V<PERSON> rugăm să selectați un fișier acceptat: {{fileTypes}}"}, "dossier": {"autoDelete": "Ștergere automată {{status}}", "readOnly": "Numai citire {{status}}", "statusOptions": {"enabled": "activat", "disabled": "dezactivat"}}, "audit": {"title": "Jurnalizare/Audit", "fromPage": "din pagina", "clicked": "a apăsat", "entered": "a introdus", "submitted": "a trimis", "value": "valoarea", "inField": "în c<PERSON>", "form": "formularul", "withValues": "cu valorile", "element": "elementul", "button": "but<PERSON><PERSON>", "inputField": "câmpul de intrare", "textField": "câmpul de text", "checkbox": "caseta de bifat", "radioButton": "butonul radio", "passwordField": "câmpul de parolă", "submitButton": "butonul de trimitere", "dropdown": "lista derulantă", "textArea": "zona de text", "menuItem": "elementul de meniu", "selector": "selectorul", "calendar": "calendarul", "withId": "cu ID-ul", "withName": "cu numele", "withValue": "cu valoarea", "withClass": "cu clasa", "primengComponent": "componenta", "currentPage": "pagina curent<PERSON>"}, "infoNote": "Notă de informare", "infoNoteDescription": "Adaugat in portal nota de informare cu caracter personal", "viewInfoPage": "Vizualizați pagina de informare", "accept": "Accept", "togglePTZControls": "Comutare controale PTZ", "showVCAConfigTooltip": "Afișează sau ascunde configurația VCA", "showVCAObjectsTooltip": "Afișează sau ascunde obiectele detectate VCA", "saveSettings": "Salvează setările", "cancelChanges": "Anulează modificările", "closeLayouts": "Închide panoul de layout-uri", "closeCameraSelection": "<PERSON>nch<PERSON> se<PERSON> camerei", "auditFilter": {"searchSpecific": "Cauți ceva specific?", "searchMessage": "Caut<PERSON> după mesaj", "searchUser": "Caută după utilizator", "searchIP": "Caută după adresa IP", "startDate": "Data de început", "endDate": "Data de sfârșit", "reset": "Resetați"}, "auditTable": {"name": "Utilizator", "action": "Acțiune", "message": "<PERSON><PERSON>", "timeStamp": "Data și ora", "ipAddress": "Adresă IP", "noAuditsFound": "Nu s-au găsit audituri"}, "generalDetectionsDetails": {"type": "Tip", "timestamp": "Data și ora", "actions": "Acțiuni", "ack": "Confirmare", "closeDetails": "<PERSON><PERSON><PERSON>ii", "noDetectionsDetailsFound": "Nu s-au găsit detalii ale detecțiilor"}, "reportSelection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "latestAlerts": "Ultimele alerte", "period": "Perioadă", "endDate": "Data sfârșit", "selectEndDate": "Selectează dată sfâ<PERSON>șit", "reportType": "Tipul raportului", "alert": "<PERSON><PERSON><PERSON>", "trigger": "<PERSON><PERSON><PERSON>", "selectAlerts": "Selectare alerte", "selectTriggers": "Selectare declanșatoare", "alertsSelected": "Alerte selectate", "triggersSelected": "Declanșatoare selectate", "reportAlreadyGenerating": "Un raport este deja în curs de generare", "reportGenerationStarted": "Generarea raportului a început", "templateNotFound": "Șablonul nu a fost găsit, poate a fost șters", "templateRequiresName": "Un șablon trebuie sa aibă un nume", "templateNameExists": "Un șablon de căutare cu acest nume există deja", "templateRemoved": "Șablonul a fost eliminat cu succes", "failedToRemoveTemplate": "Eliminarea sablonului a eșuat", "updateSuccess": "Actualizat cu succes", "saveSuccess": "Salvat cu succes", "saveFailed": "Salvarea a eșuat", "invalidDateRange": "Data de inceput trebuie sa fie inainte de data de sfârșit", "selectValidDateRange": "Vă rugăm selectați un interval de date valid", "invalidDateFilterType": "<PERSON><PERSON> de filtru de da<PERSON> invalid", "reportCancelled": "Generarea raportului a fost anulată", "noDataToExport": "Nu există date pentru export", "exportSuccess": "Export finalizat cu succes", "exportFailed": "Exportul a eșuat", "exportEndpointNotFound": "Endpoint-ul de export nu a fost găsit", "auditServiceIsNotRunning": "Serviciul de audit nu funcționează", "pdfExportNotAvailable": "Exportul PDF este temporar indisponibil. Vă rugăm să folosiți exportul CSV în schimb.", "AM": "AM", "PM": "PM", "Mon": "<PERSON>n", "Tue": "Mar", "Wed": "<PERSON><PERSON>", "Thu": "<PERSON><PERSON>", "Fri": "Vin", "Sat": "Sâm", "Sun": "<PERSON><PERSON>", "January": "<PERSON><PERSON><PERSON>", "February": "<PERSON><PERSON><PERSON><PERSON>", "March": "<PERSON><PERSON>", "April": "<PERSON><PERSON>", "May": "<PERSON>", "June": "<PERSON><PERSON><PERSON>", "July": "<PERSON><PERSON><PERSON>", "August": "August", "September": "Septembrie", "October": "<PERSON><PERSON><PERSON>", "November": "Noiembrie", "December": "Decembrie", "alertsAlarms": {"plateNumber": "<PERSON><PERSON><PERSON><PERSON>", "eventName": "Nume eveniment", "noData": "Fără date"}, "totalAlerts": "Total Alerte", "totalAlarms": "Total Alarme", "totalWarnings": "Total Avertismente", "switchPosition": "Poziția comutatorului", "watermark": {"user": "Utilizator", "date": "Dată"}, "ai": {"title": "Inteligență Artificială", "content": "Conținut AI", "loading": "Se încarcă conținutul AI...", "error": "Încărcarea conținutului AI a eșuat. Vă rugăm să încercați din nou mai târziu."}, "noValue": "Lipsă date", "selectFromResourceGroup": "Selectați din grupul de resurse", "selectChannelTour": "Selectați turul de camere", "tour": "<PERSON><PERSON>", "videoWallSettings": "<PERSON><PERSON><PERSON> perete video", "layout": "Aspect", "export": "Exportare", "exporting": "Se exportă", "exportToHTML": "Exportă în HTML", "weatherInfo": {"humidity": "Umiditate", "pressure": "Presiune", "title": "Informații meteo", "loadingWeatherData": "Se încarcă datele meteo.", "loadingWeatherDataError": "Nu s-au putut încărca datele meteo."}}