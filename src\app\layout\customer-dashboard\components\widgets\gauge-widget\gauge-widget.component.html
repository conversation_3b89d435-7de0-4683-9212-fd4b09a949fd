<div class="gauge-wrapper" #gaugeWrapper >
  <div class="measurment-unit" *ngIf="data.widgetData.measurmentUnit">{{data.widgetData.measurmentUnit}}</div>
  <div class="gauge-resizer" [ngClass]="gaugeAspectRatio < 1 ? 'portrait' : 'landscape'">
    <mwl-gauge *ngIf="isGaugeVisible" [min]="data.widgetData.minValue" [max]="data.widgetData.maxValue"
      [dialStartAngle]="180" [dialEndAngle]="0" [value]="value" [animated]="true" [animationDuration]="1"
      [label]="setLabel" [class]="gaugeClass" [valueClass]="'gauge-text'" #gaugeElement>
    </mwl-gauge>
    <div class="gauge-custom-label">

    </div>
  </div>
</div>