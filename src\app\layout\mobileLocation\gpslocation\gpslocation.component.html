<div class="mainContainer">
  <div class="buttonContainer">
<i class="fa fa-refresh" aria-hidden="true" (click)="refreshLocation()"></i>
  <p class="mainTitle" >{{'resources' | translate}}</p>
</div>
  <p-dropdown class=""
    [options]="items"
    class="mainDropdown"
    placeholder="Select Item"
    [virtualScroll]="true"
    [itemSize]="31"
    (onChange)="onItemSelect($event)"
    [(ngModel)]="selectedResource"
    [filter]="true"
  ></p-dropdown>
  <div class="map-wrapper" #mapWrapper>
    <app-generic-map 
       
        [markers]="markerDictionary"
       
        
        #map>
    </app-generic-map>
    <ng-container #customMarkersFactory ></ng-container>
  </div>  
  <!-- <div id="ol-map" class="map-container"></div> -->
  <button class="btn btn-primary mainButton" (click)="changeMarkerLocation()">
    <i class="pi pi-map-marker"></i>{{'saveLocation' | translate}}
  </button>
</div>