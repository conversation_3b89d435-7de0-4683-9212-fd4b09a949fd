::ng-deep .thumbnail {
    height: 100px;
    width: 100px;
    background-color: whitesmoke;
}

::ng-deep .ui-fileupload-choose{
    // width: 100%;

    background-color: #3b86ff;
    color: #fff;
}
::ng-deep .ui-calendar{
    border: 1px solid #aeaeaf;
    width:100%;
}
::ng-deep .ui-calendar .ui-calendar-button{
    background-color: #3b86ff !important;
    color: #fff !important;
}
input.ng-invalid.ng-touched {
    border: 1px solid red;
    background-color: mistyrose;
}

.panel {
    border:1px solid #a6acaf;
    border-radius:5px;
    padding: 7px 12px;
}

.panel-body {
    margin-bottom: 0rem;
}

.center-vertical-txt {
    line-height: 1;
    padding-top: 8px
}

.center-vertical-btn {
    line-height: 1;
    padding-top: 5px
}

.plus-button {
    padding-bottom: 1px;
    padding-top: 3px;
    padding-left: 4px;
    padding-right: 4px
}

.form-check-input {
    margin-left: 0;
    margin-top: 7px;
}

.help-block {
    font-size: 15px;
    color: red;
}

.invalid-dates {
    border: 1px solid red;
    background-color: mistyrose;
    border-radius: 5px;
    padding: 0 5px;
}

.form-group {
    margin-bottom: 5px;
}

.col-form-label{
    padding-bottom: 0;
}

.panel {
    margin-bottom: 10px;
}

.custom-select {
    height: auto;
    padding: 4px;
    width: inherit;
}

.form-control-sm {
    width: inherit;
}

.btn {
    cursor: pointer;
}

.dropdown-rtl {
    background: #fff url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23333' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat left 0.75rem center;
    background-size: 8px 10px;
}
.datetime-block{
    display: block;
}
:host ::ng-deep .ui-button-text-icon-left .ui-button-text{
    margin-left:10px !important;
}
.associatedDevicesAdd{
    background-color: #3b86ff;
    color: #fff;
}
.text-center{
    display: flex;
    justify-content: center;

    }
