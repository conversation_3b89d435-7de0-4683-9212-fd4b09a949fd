import { Component, OnInit, OnChanges, Input, SimpleChanges, ViewChild, TemplateRef, Output, EventEmitter } from '@angular/core';
import { AppNotification } from 'app/shared/modules/data-layer/models/app-notification';
import { TableColumnProperties, TableCell, ElementTableCell } from 'app/shared/components/ng-turbo-table/models/table.models';
import { eventsLiveActiveColumns } from './events-live-active-columns';
import * as _ from "lodash";
import { TimeService } from 'app/shared/services/time.service';
import * as moment from 'moment';
import { DisplayImageComponent } from 'app/shared/components/display-image/display-image.component';
import { ImageTableCell } from 'app/shared/components/ng-turbo-table/models/image-table-element';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { GalleriaImageSource } from 'app/shared/models/galleria-image-source.interface';
import { DefaultActionsComponent } from '../actions/default-actions-component/default-actions.component';
import { Entity } from 'app/shared/modules/data-layer/models/entity';
import { EventsActionData } from '../../models/events-action-data.interface';

@Component({
  selector: 'app-events-live',
  templateUrl: './events-live.component.html',
  styleUrls: ['./events-live.component.scss']
})
export class EventsLiveComponent implements OnInit, OnChanges {
  @Input('notificationsModelStore') notificationsModelStore: {[id: string]: AppNotification}
  @Input('newNotifications') newNotifications: AppNotification[];
  @ViewChild('imageCell', {static:true}) imageCell: TemplateRef<DisplayImageComponent>
  @ViewChild('actionsDefault', {static: false}) actionsDefault: TemplateRef<DefaultActionsComponent>;
  @Output('selectAction') selectAction: EventEmitter<EventsActionData> = new EventEmitter();

  data: {[columnField: string]: TableCell | string }[] = [];
  columns: TableColumnProperties[] = eventsLiveActiveColumns;
  liveEventsEnabled: boolean = true;

  constructor(
    private timeService: TimeService,
    private domSanitizer: DomSanitizer
  ) { }

  ngOnInit(): void {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if(changes && changes.notificationsModelStore && changes.notificationsModelStore.currentValue){
      this.data = this.getFlattenObject(_.values(changes.notificationsModelStore.currentValue));
    }
    if(changes && changes.newNotifications && changes.newNotifications.currentValue && this.liveEventsEnabled){
      this.getFlattenObject(changes.newNotifications.currentValue).forEach(item => this.data.unshift(item));
    }
  }

  getFlattenObject(data: AppNotification[]):{[columnField: string]: TableCell | string }[] {
    const results = [];
    data.forEach((notification: AppNotification) => {
      notification.eventData.forEach(event => {
        let temp: any = {};
        temp.description = notification.description;
        temp.types = notification.types.map(item => { return item }).join(', ');
        temp.source = event.resourceName;
        // TODO use the tiemstamp of the notification type or of the event ?
        temp.timeStamp = notification.timeStamp ? notification.timeStamp.toString() : '';

        // TODO don't know if this is the right data i'm guessing here
        temp.entityName = event.entityInfo ? event.entityInfo.firstName + " " + event.entityInfo.lastName : null;

        temp.identifiedValue = event.jsonData.description.entityValue;
        temp.confidence = event.jsonData.description.confidence;

        temp.actions = new ElementTableCell(this.actionsDefault);

        let images: GalleriaImageSource[] = [];
        for(let key in event.images){
          images.push({source: this.domSanitizer.bypassSecurityTrustResourceUrl('data:image/png;base64, ' + event.images[key])})
        }
        temp.image = new ImageTableCell(this.imageCell, images);

        temp.entityInfo = event.entityInfo;
        temp.deviceId = event.deviceId

        results.push(temp);
      })

    })

    return results

  }

  toggleLiveEvents(event: {checked: boolean, originalEvent: MouseEvent}): void {
    this.liveEventsEnabled = event.checked;
  }

  onSelectedItem(event): void {
    this.selectAction.next({action: event.action, entityInfo: event.data.entityInfo});
  }

}
