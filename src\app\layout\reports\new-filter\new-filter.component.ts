import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Host<PERSON><PERSON>ener, ElementRef, EventEmitter, Input, Output, ViewEncapsulation } from '@angular/core';
import * as moment from 'moment';
import { SelectItem, ConfirmationService, MessageService } from 'primeng/api';
import { TranslateService } from '@ngx-translate/core';
import * as _ from 'lodash';
import { takeWhile, take } from 'rxjs/operators';


import { ReportsQueryParams } from '../../customer-dashboard/models/reports-query-params.interface';
import { Guid } from '../../../shared/enum/guid';
import { ToastTypes } from '../../../shared/enum/toast-types';
import { TriggerTypes } from '../../../shared/enum/trigger-types';
import { ReportCollectOption } from '../../../shared/modules/data-layer/enum/reports/report-collect-options.enum';
import { ServerTypes } from '../../../shared/modules/data-layer/enum/resource/resource-server-types.enum';
import { ResourceGroup } from '../../../shared/modules/data-layer/models/resource-group';
import { RuleEvent } from '../../../shared/modules/data-layer/models/rule-event';
import { AuthService } from '../../../shared/services/auth.service';
import { DataService } from '../../../shared/services/data.service';
import { RuleEventService } from "../../../shared/modules/data-layer/services/rule-event/rule-event.service";
import { ResourceGroupService } from "../../../shared/services/resourceGroup.service";
import { ApiCommands, DateRangeSearchType, DateUnit, GuidUtils, SearchType } from "../../../shared/enum/enum";
import { ReportsService } from '../../../shared/services/reports.service';
import { ReportsTurboTableService } from '../../../shared/services/reports-turbo-table.service';
import { EventService } from '../../../shared/services/event.service';
import { EventType } from '../../../shared/services/event.service';

enum SelectionMode {
  Start,
  End
}

@Component({
  selector: 'app-new-filter',
  templateUrl: './new-filter.component.html',
  styleUrls: ['./new-filter.component.scss'],
  providers: [ConfirmationService],
  encapsulation: ViewEncapsulation.Emulated
})
export class NewFilterComponent implements OnInit, OnDestroy {

  isDropdownOpen: boolean = false;
  isPeriodDropdownOpen: boolean = false;
  isReportTypeDropdownOpen: boolean = false;
  periodValue: number = 3;
  periodUnit: string = 'days';

  startDate: Date | null = null;
  endDate: Date | null = null;
  isAm: boolean = true;
  timeValue: string = '09:41';

  currentMonth: moment.Moment = moment();
  calendarDays: any[] = [];
  weekDays: string[] = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];
  selectionMode: SelectionMode = SelectionMode.Start;
  selectedReportType: string = '';
  selectedAlert: any = null;
  selectedTrigger: any = null;
  selectedAlerts: any[] = [];
  selectedTriggers: any[] = [];

  alertOptions: SelectItem[] = [];
  triggerOptions: SelectItem[] = [];


  protected readonly SearchType = SearchType;
  protected readonly DateRangeSearchType = DateRangeSearchType;
  protected readonly DateUnit = DateUnit;

  dateDefaultVal: DateRangeSearchType = DateRangeSearchType.Date;
  lastDefaultVal: DateUnit = DateUnit.Seconds;
  searchByDefaultVal: SearchType = SearchType.Event;
  reportDefault: string = 'addNew';
  transformationDefault: string = '';

  selectedReport: string = this.reportDefault;
  dateFilterValue: DateRangeSearchType = this.dateDefaultVal;
  searchBySelectVal: SearchType = this.searchByDefaultVal;
  selectedResourceGroup: string = Guid.EMPTY;
  selectedResource: string = Guid.EMPTY;
  newTemplateName: string = '';
  selectedDateLastOption: DateUnit = this.lastDefaultVal;
  triggerType: SelectItem[] = [];
  triggerTypeSelectVal: number[] = [];
  transformBySelectVal: string = this.transformationDefault;
  textFilter: string = '';
  searchTemplates: any[] = [];
  dateLast: number = 10;
  resourceGroups: ResourceGroup[] = [];
  resourceGroupsSelectItems: SelectItem[] = [{ label: "allResources", value: Guid.EMPTY }];
  resourceSelectItems: SelectItem[] = [{ label: "allResourcesInGroup", value: Guid.EMPTY }];
  fromDate: Date = moment().startOf('day').toDate();
  toDate: Date = moment().endOf('day').toDate();
  currDate: Date = new Date();
  searchBy: SelectItem[] = [];
  transformBy: SelectItem[] = [];
  dateFilterOptions: SelectItem[] = [];
  dateFilterLastOptions: SelectItem[] = [];
  specialChars = /[-!@$%^&*()_+|~=`\\#{}\[\]:";'<>?,.\/]/;
  disableExceuteBtn: boolean = false;
  storeObservables: any[] = [];
  private alive: boolean = true;
  eventsNameData: SelectItem[] = [];
  reportsName: SelectItem[] = [];
  eventIdsSelectVal: string[] = [];
  reportGenerationCancelled: boolean = true;

  @Output('generateReportStart') generateReportStart: EventEmitter<{ reportQueryParams: ReportsQueryParams, filterQueryparams: string }> = new EventEmitter();
  @Output('cancelReportGeneration') cancelReportGeneration: EventEmitter<null> = new EventEmitter();
  @Input('reportQueryId') reportQueryId: number;

  constructor(
    private elementRef: ElementRef,
    private dataService: DataService,
    private confirmationService: ConfirmationService,
    private messageService: MessageService,
    private i18n: TranslateService,
    private ruleService: RuleEventService,
    private authService: AuthService,
    private resourceGroupService: ResourceGroupService,
    private reportsService: ReportsService,
    private reportsTurboTableService: ReportsTurboTableService,
    private eventService: EventService
  ) { }

  ngOnInit(): void {
    this.generateCalendarData();
    this.initVariables();
    this.getRuleEvents();
    this.getSearchTemplates();
    this.getResourceGroups();
    this.getTriggers();
    this.getTransformations();
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {



    const dateFilterButton = this.elementRef.nativeElement.querySelector('.select-wrapper .report-type-button:not(.report-type-wrapper .report-type-button)');
    const dateFilterDropdown = this.elementRef.nativeElement.querySelector('[data-dropdown-id="date-filter-dropdown"]');
    if (this.isDropdownOpen &&
        !(dateFilterButton && dateFilterButton.contains(event.target as Node)) &&
        !(dateFilterDropdown && dateFilterDropdown.contains(event.target as Node))) {
      this.isDropdownOpen = false;
    }


    const periodWrapper = this.elementRef.nativeElement.querySelector('.period-select-wrapper');
    if (this.isPeriodDropdownOpen &&
        periodWrapper &&
        !periodWrapper.contains(event.target as Node)) {
      this.isPeriodDropdownOpen = false;
    }


    const reportTypeWrapper = this.elementRef.nativeElement.querySelector('.report-type-wrapper');
    if (this.isReportTypeDropdownOpen &&
        reportTypeWrapper &&
        !reportTypeWrapper.contains(event.target as Node)) {
      this.isReportTypeDropdownOpen = false;
    }
  }

  toggleDropdown(event: Event): void {
    event.stopPropagation();
    this.isDropdownOpen = !this.isDropdownOpen;
    this.isPeriodDropdownOpen = false;
    this.isReportTypeDropdownOpen = false;
  }

  togglePeriodDropdown(event: Event): void {
    event.stopPropagation();
    this.isPeriodDropdownOpen = !this.isPeriodDropdownOpen;
    this.isReportTypeDropdownOpen = false;
  }

  toggleReportTypeDropdown(event: Event): void {
    event.stopPropagation();
    this.isReportTypeDropdownOpen = !this.isReportTypeDropdownOpen;
    this.isPeriodDropdownOpen = false;
  }

  initVariables(): void {
    this.newTemplateName = "";
    this.dateDefaultVal = DateRangeSearchType.Range;
    this.lastDefaultVal = DateUnit.Days;
    this.searchByDefaultVal = SearchType.Event;
    this.reportDefault = 'addNew';
    this.transformationDefault = '';

    this.selectedReport = this.reportDefault;
    this.dateFilterValue = this.dateDefaultVal;
    this.searchBySelectVal = this.searchByDefaultVal;
    this.selectedDateLastOption = this.lastDefaultVal;
    this.triggerTypeSelectVal = [];
    this.transformBySelectVal = this.transformationDefault;
    this.textFilter = '';
    this.dateLast = this.periodValue;
    this.eventIdsSelectVal = [];
    this.selectedAlert = null;
    this.selectedTrigger = null;
    this.selectedAlerts = [];
    this.selectedTriggers = [];
    this.currDate = new Date();
    this.fromDate = moment().startOf('day').toDate();
    this.toDate = moment().endOf('day').toDate();
    this.selectedResourceGroup = Guid.EMPTY;
    this.selectedResource = Guid.EMPTY;

    this.startDate = null;
    this.endDate = null;

    this.dateFilterOptions = [
      { value: DateRangeSearchType.Last, label: "last" },
      { value: DateRangeSearchType.Range, label: "dateRange" }
    ];

    this.searchBy = [
      { value: SearchType.Event, label: "events" },
      { value: SearchType.Trigger, label: "triggers" },
      { value: SearchType.Transformation, label: "transformation" },
    ];

    this.dateFilterLastOptions = [
      { value: DateUnit.Seconds, label: "seconds" },
      { value: DateUnit.Minutes, label: "minutes" },
      { value: DateUnit.Hours, label: "hours" },
      { value: DateUnit.Days, label: "days" },
      { value: DateUnit.Weeks, label: "weeks" },
      { value: DateUnit.Months, label: "months" }
    ];
  }

  getSearchTemplates(): void {
      const sub = this.dataService.api({
          type: ApiCommands.GetSearchTemplates,
      }).subscribe(res => {
          if (res) {
              this.searchTemplates = res;
              this.reportsName = res.map((item: any) => ({
                  label: item.Name,
                  value: item.Identity,
              }));
              this.reportsName.unshift({ label: this.i18n.instant('addNewSearchTemplate'), value: 'addNew' });
          } else {
              this.searchTemplates = [];
              this.reportsName = [{ label: this.i18n.instant('addNewSearchTemplate'), value: 'addNew' }];
          }
          if (!this.doesExistInSearchTemplates(this.selectedReport) && this.selectedReport !== 'addNew') {
              this.messageService.add({ severity: ToastTypes.info, detail: this.i18n.instant('selectedTemplateNotFound') });
              this.initVariables();
          } else if (this.doesExistInSearchTemplates(this.selectedReport)) {
              this.selectReportOnChange(this.selectedReport);
          }
      });
      this.storeObservables.push(sub);
  }

  getResourceGroups(): void {
      const sub = this.resourceGroupService.getAll().pipe(take(1)).subscribe(res => {
          this.resourceGroups = res;
          this.fillResources(res);
      });
      this.storeObservables.push(sub);
  }

  getTriggers(): void {
      this.triggerType = (Object.keys(TriggerTypes) as (keyof typeof TriggerTypes)[])
          .filter(key => Number.isNaN(parseInt(key as string)))
          .map(key => ({ label: this.i18n.instant(key as string), value: TriggerTypes[key] }));


      this.triggerOptions = this.triggerType;
  }

  getTransformations(): void {
      const sub = this.dataService.apiEx(ApiCommands.GetReportTransformation).pipe(take(1)).subscribe(res => {
          this.fillTransformations(res);
      });
      this.storeObservables.push(sub);
  }

  getRuleEvents(): void {
    const sub = this.ruleService.getAll().subscribe((response) => {
        if (response) {

            let eventsData: RuleEvent[] = Object["values"](response);
            this.eventsNameData = [];


            eventsData.forEach((item) => {
                this.eventsNameData.push({
                    label: item.name,
                    value: item.identity,
                    disabled: false
                });
            });


            this.alertOptions = [...this.eventsNameData];

        } else {
            this.eventsNameData = [];
            this.alertOptions = [];
        }
    }, (error) => {
        this.eventsNameData = [];
        this.alertOptions = [];
    });
    this.storeObservables.push(sub);
  }

  generateReport(): void {
    this.reportGenerationCancelled = false;

    let queryparams: ReportsQueryParams | null = this.returnReportQueryParams(GuidUtils.newGuid());
    if (queryparams) {
      const filterString = this.reportTemplateToString(queryparams);


      this.generateReportStart.next({
        reportQueryParams: queryparams,
        filterQueryparams: filterString
      });


      this.reportQueryId = queryparams.QueryId as number;


      this.messageService.add({
        severity: ToastTypes.success,
        summary: this.i18n.instant('success'),
        detail: this.i18n.instant('reportGenerationStarted')
      });
    } else {
      this.reportGenerationCancelled = true;
    }
  }

  handleReportGenerationError(): void {
      this.messageService.add({
          severity: ToastTypes.error,
          summary: this.i18n.instant('error'),
          detail: this.i18n.instant('reportGenerationFailed')
      });
      this.reportQueryId = null as any;

      this.reportGenerationCancelled = false;
  }

  selectReportOnChange(id: string): void {
      if (id === 'addNew') {
          this.initVariables();
          this.selectedReport = 'addNew';
      } else {
          const item = this.searchTemplates.find(t => t.Identity === id);
          if (item) {
              this.fillForm(item);
          } else {
              this.messageService.add({
                severity: ToastTypes.info,
                detail: this.i18n.instant('templateNotFoundMaybeDeleted')
              });
              this.initVariables();
              this.selectedReport = 'addNew';
          }
      }
  }

  onSaveTemplate(): void {
      if (!this.newTemplateName || this.newTemplateName.trim() === '') {
          this.messageService.add({
            severity: ToastTypes.error,
            summary: this.i18n.instant('error'),
            detail: this.i18n.instant('aTemplateMustHaveAName')
          });
          return;
      }

      if (this.searchTemplates.some(t => t.Name === this.newTemplateName.trim() && t.Identity !== this.selectedReport)) {
          this.messageService.add({
            severity: ToastTypes.error,
            summary: this.i18n.instant('error'),
            detail: this.i18n.instant('searchTemplateWithThisNameAlredyExist')
          });
          return;
      }

      this.saveUpdateTemplate(GuidUtils.newGuid(), false);
  }

  onUpdateTemplate(): void {
       if (!this.newTemplateName || this.newTemplateName.trim() === '') {
          this.messageService.add({
            severity: ToastTypes.error,
            summary: this.i18n.instant('error'),
            detail: this.i18n.instant('aTemplateMustHaveAName')
          });
          return;
      }
       if (this.searchTemplates.some(t => t.Name === this.newTemplateName.trim() && t.Identity !== this.selectedReport)) {
          this.messageService.add({
            severity: ToastTypes.error,
            summary: this.i18n.instant('error'),
            detail: this.i18n.instant('searchTemplateWithThisNameAlredyExist')
          });
          return;
      }
      this.saveUpdateTemplate(this.selectedReport, true);
  }

  onRemoveTemplate(): void {
      if (this.selectedReport === 'addNew') return;
      this.confirmationService.confirm({
          message: this.i18n.instant('areYouSureDeleteTemplate', { name: this.getSelectedTemplateName() }),
          header: this.i18n.instant('removeTemplate'),
          icon: 'pi pi-exclamation-triangle',
          acceptLabel: this.i18n.instant('yes'),
          rejectLabel: this.i18n.instant('no'),
          accept: () => {
              const sub = this.dataService.api({
                  type: ApiCommands.RemoveSearchTemplate,
                  urlParams: this.selectedReport
              }).subscribe(() => {
                  this.messageService.add({
                    severity: ToastTypes.success,
                    detail: this.i18n.instant('templateRemovedSuccessfully')
                  });
                  this.getSearchTemplates();
                  this.initVariables();
                  this.selectedReport = 'addNew';
              }, err => {
                  this.messageService.add({
                    severity: ToastTypes.error,
                    detail: this.i18n.instant('failedToRemoveTemplate')
                  });
              });
              this.storeObservables.push(sub);
          }
      });
  }

  getSelectedTemplateName(): string {
      const template = this.searchTemplates.find(t => t.Identity === this.selectedReport);
      return template ? template.Name : '';
  }

  saveUpdateTemplate(id: string, isUpdate: boolean): void {
    let template = this.returnReportQueryParams(id);
    if (template) {
      const sub = this.dataService.api({
        type: ApiCommands.SetSearchTemplate,
        data: template
      }).subscribe(
        () => {
          const message = isUpdate ? this.i18n.instant('updatedSuccessfully') : this.i18n.instant('savedSuccessfully');
          this.messageService.add({ severity: ToastTypes.success, summary: this.i18n.instant('success'), detail: message });
          this.getSearchTemplates();
          if (!isUpdate) {
            this.selectedReport = template.Identity;
          }
        },
        (err) => {
          this.messageService.add({ severity: ToastTypes.error, summary: this.i18n.instant('error'), detail: this.i18n.instant('failedToSave') });
        }
      );
      this.storeObservables.push(sub);
    }
  }

  fillForm(item: any): void {
    this.selectedReport = item.Identity;
    this.newTemplateName = item.Name;
    this.textFilter = item.TextFilter || '';
    this.searchBySelectVal = item.Type || SearchType.Event;
    this.eventIdsSelectVal = item.EventIds || [];
    this.triggerTypeSelectVal = item.TriggerTypes && item.TriggerTypes[0] !== 4294967295 ? item.TriggerTypes : [];
    this.transformBySelectVal = item.Transformation || this.transformationDefault;


    if (this.searchBySelectVal.toString() === SearchType.Event.toString() && this.eventIdsSelectVal.length > 0) {
        this.selectedAlert = this.eventIdsSelectVal[0];
        this.selectedAlerts = [...this.eventIdsSelectVal];
    } else {
        this.selectedAlert = null;
        this.selectedAlerts = [];
    }


    if (this.searchBySelectVal.toString() === SearchType.Trigger.toString() && this.triggerTypeSelectVal.length > 0) {
        this.selectedTrigger = this.triggerTypeSelectVal[0];
        this.selectedTriggers = [...this.triggerTypeSelectVal];
    } else {
        this.selectedTrigger = null;
        this.selectedTriggers = [];
    }

    this.dateFilterValue = +item.DateRangeType || DateRangeSearchType.Range;
    this.dateLast = item.Value || 10;
    this.selectedDateLastOption = item.DateLastUnit || DateUnit.Days;
    this.fromDate = item.From ? moment.utc(item.From).toDate() : moment().startOf('day').toDate();
    this.toDate = item.To ? moment.utc(item.To).toDate() : moment().endOf('day').toDate();
    this.currDate = item.From ? moment.utc(item.From).toDate() : new Date();

    this.selectedResourceGroup = item.ResourceGroup || Guid.EMPTY;
    this.selectResourceGroupOnChange(this.selectedResourceGroup);
    this.selectedResource = item.Resource || Guid.EMPTY;

    this.checkDateRangeValid();

    if (this.dateFilterValue === DateRangeSearchType.Range) {
        this.startDate = this.fromDate;
        this.endDate = this.toDate;
        this.periodValue = 3;
        this.periodUnit = 'days';
    } else if (this.dateFilterValue === DateRangeSearchType.Last) {
        this.startDate = null;
        this.endDate = null;
        this.periodValue = this.dateLast;
        this.periodUnit = this.mapDateUnitToString(this.selectedDateLastOption);
    } else {
        this.startDate = null;
        this.endDate = null;
    }
    this.updateReportTypeDropdownState(this.searchBySelectVal);
  }

  mapDateUnitToString(unit: DateUnit): string {
      switch(unit) {
          case DateUnit.Seconds: return 'seconds';
          case DateUnit.Minutes: return 'minutes';
          case DateUnit.Hours: return 'hours';
          case DateUnit.Days: return 'days';
          case DateUnit.Weeks: return 'weeks';
          case DateUnit.Months: return 'months';
          default: return 'days';
      }
  }

  mapStringToDateUnit(unit: string): DateUnit {
      switch(unit.toLowerCase()) {
          case 'seconds': return DateUnit.Seconds;
          case 'minutes': return DateUnit.Minutes;
          case 'hours': return DateUnit.Hours;
          case 'days': return DateUnit.Days;
          case 'weeks': return DateUnit.Weeks;
          case 'months': return DateUnit.Months;
          default: return DateUnit.Days;
      }
  }

  selectResourceGroupOnChange(id: string): void {
      this.selectedResource = Guid.EMPTY;
      this.resourceSelectItems = [{ label: this.i18n.instant('allResourcesInGroup'), value: Guid.EMPTY }];
      if (id !== Guid.EMPTY) {
          const selectedGroup = this.resourceGroups.find(g => g.identity === id);
          if (selectedGroup && selectedGroup.resources) {
              selectedGroup.resources.forEach(resource => {
                  if (this.includeTypes(resource.resourceType)) {
                      this.resourceSelectItems.push({
                          label: resource.name,
                          value: resource.identity
                      });
                  }
              });
          }
      }
  }

  includeTypes(type: any): boolean {
      return (
          type === ServerTypes.Core_RES_ALPRLane ||
          type === ServerTypes.Core_RES_Cabinet ||
          type === ServerTypes.Core_RES_Device ||
          type === ServerTypes.Core_RES_InputChannel ||
          type === ServerTypes.Core_RES_Light ||
          type === ServerTypes.Core_RES_Node ||
          type === ServerTypes.Core_RES_Output ||
          type === ServerTypes.Core_RES_Input ||
          type === ServerTypes.Core_RES_PowerMeter
      );
  }

  fillResources(resourceGroups: ResourceGroup[]): void {
      this.resourceGroupsSelectItems = [{ label: this.i18n.instant("allResources"), value: Guid.EMPTY }];
      resourceGroups.forEach(group => {
          this.resourceGroupsSelectItems.push({ label: group.name, value: group.identity });
      });
      this.selectResourceGroupOnChange(this.selectedResourceGroup);
  }

  fillTransformations(transformations: any): void {
      if (transformations instanceof Array && transformations.length > 0) {
          this.transformBy = transformations.map(e => ({ value: e, label: e }));
      } else {
          this.transformBy = [];
      }
  }

  doesExistInSearchTemplates(id: string): boolean {
    return this.searchTemplates.some(t => t.Identity === id);
  }

  validateNewTemplateInput(event: KeyboardEvent): boolean {
    return !this.specialChars.test(event.key);
  }

  generateCalendarData(): void {
    const startOfMonth = this.currentMonth.clone().startOf('month');
    const endOfMonth = this.currentMonth.clone().endOf('month');
    const firstDay = startOfMonth.day();
    this.calendarDays = [];
    for (let i = 0; i < firstDay; i++) {
        this.calendarDays.push({ day: null, isCurrentMonth: false });
    }
    for (let i = 1; i <= endOfMonth.date(); i++) {
        this.calendarDays.push({
            day: i,
            isCurrentMonth: true,
            date: moment(this.currentMonth).date(i)
        });
    }
  }

  previousMonth(): void {
    this.currentMonth = this.currentMonth.clone().subtract(1, 'month');
    this.generateCalendarData();
  }

  nextMonth(): void {
    this.currentMonth = this.currentMonth.clone().add(1, 'month');
    this.generateCalendarData();
  }

  get formattedCurrentMonth(): string {
    return this.currentMonth.format('MMMM YYYY');
  }

  selectDate(day: any): void {
      if (!day || !day.day) return;
      const selectedDate = day.date.toDate();

      if (this.selectionMode === SelectionMode.Start) {
          this.startDate = selectedDate;
          this.endDate = null;
          this.fromDate = selectedDate;
          this.toDate = null as any;
          this.selectionMode = SelectionMode.End;
          this.dateFilterValue = DateRangeSearchType.Range;
      } else {
          if (this.startDate && selectedDate < this.startDate) {
              this.endDate = this.startDate;
              this.startDate = selectedDate;
          } else {
              this.endDate = selectedDate;
          }
          this.fromDate = this.startDate!;
          this.toDate = this.endDate!;
          this.selectionMode = SelectionMode.Start;
          this.checkDateRangeValid();
      }
  }

  useLatestAlerts(): void {
    this.dateFilterValue = DateRangeSearchType.Last;
    this.startDate = null;
    this.endDate = null;
    this.dateLast = this.periodValue;
    this.selectedDateLastOption = this.mapStringToDateUnit(this.periodUnit);
    this.isDropdownOpen = false;
  }

  checkDateRangeValid(): void {
       this.disableExceuteBtn = this.fromDate && this.toDate && moment(this.fromDate).isAfter(moment(this.toDate));
       if (this.disableExceuteBtn) {
           this.messageService.add({severity: ToastTypes.error, detail: this.i18n.instant('fromDateShouldBeBeforeToDate')});
       }
  }

  isDateInRange(day: any): boolean {
    if (!day || !day.day || !this.startDate || !this.endDate) return false;
    const date = day.date.toDate();

    return date >= this.startDate! && date <= this.endDate!;
  }

  isDateSelected(day: any): boolean {
    if (!day || !day.day) return false;
    const date = day.date.toDate();
    const startMatch = this.startDate ? date.getTime() === this.startDate.getTime() : false;
    const endMatch = this.endDate ? date.getTime() === this.endDate.getTime() : false;
    return startMatch || endMatch;
  }

  onPeriodValueChange(): void {
      this.dateLast = this.periodValue;
      if(this.dateFilterValue === DateRangeSearchType.Last) {
         this.selectedDateLastOption = this.mapStringToDateUnit(this.periodUnit);
      }
  }

  selectPeriodUnit(unit: string, event: Event): void {
      event.stopPropagation();
      this.periodUnit = unit;
      this.selectedDateLastOption = this.mapStringToDateUnit(unit);
      this.isPeriodDropdownOpen = false;
      if(this.dateFilterValue === DateRangeSearchType.Last) {
         this.dateLast = this.periodValue;
      }
  }

  setAmPm(isAm: boolean): void {
    this.isAm = isAm;
  }

  onSearchByValChange(value: SearchType): void {
     this.searchBySelectVal = value;
     this.triggerTypeSelectVal = [];
     this.transformBySelectVal = this.transformationDefault;
     this.eventIdsSelectVal = [];
     this.updateReportTypeDropdownState(value);
  }

  updateReportTypeDropdownState(searchType: SearchType): void {
      const selectedSearchOption = this.searchBy.find(opt => opt.value === searchType);
      this.selectedReportType = selectedSearchOption ? this.i18n.instant(selectedSearchOption.label ?? '') : 'Tipul raportului';


      if (searchType !== SearchType.Event) {
          this.selectedAlert = null;

      }

      if (searchType !== SearchType.Trigger) {
          this.selectedTrigger = null;

      }
  }

  onTriggerChange(trigger: any): void {
    if (trigger) {

      this.triggerTypeSelectVal = [trigger];

      if (this.searchBySelectVal === SearchType.Event) {
        this.searchBySelectVal = SearchType.Trigger;
        this.updateReportTypeDropdownState(SearchType.Trigger);
      }
    } else {

      this.triggerTypeSelectVal = [];
    }
  }

  onAlertChange(alert: any): void {
    if (alert) {

      this.eventIdsSelectVal = [alert];

      if (this.searchBySelectVal === SearchType.Trigger) {
        this.searchBySelectVal = SearchType.Event;
        this.updateReportTypeDropdownState(SearchType.Event);
      }
    } else {

      this.eventIdsSelectVal = [];
    }
  }

  onTriggersChange(triggers: any[]): void {
    if (triggers && triggers.length > 0) {

      this.triggerTypeSelectVal = [...triggers];


      this.selectedAlerts = [];
      this.eventIdsSelectVal = [];


      if (this.searchBySelectVal !== SearchType.Trigger) {
        this.searchBySelectVal = SearchType.Trigger;
        this.updateReportTypeDropdownState(SearchType.Trigger);
      }
    } else {

      this.triggerTypeSelectVal = [];
    }
  }

  onAlertsChange(alerts: any[]): void {
    if (alerts && alerts.length > 0) {

      this.eventIdsSelectVal = [...alerts];
      this.selectedAlerts = [...alerts];
      this.selectedTriggers = [];
      this.triggerTypeSelectVal = [];
      if (this.searchBySelectVal !== SearchType.Event) {
        this.searchBySelectVal = SearchType.Event;
        this.updateReportTypeDropdownState(SearchType.Event);
      }
    } else {

      this.eventIdsSelectVal = [];
      this.selectedAlerts = [];
    }
  }

  returnReportQueryParams(id: string): ReportsQueryParams | null {
    let dateFromMoment = moment();
    let dateToMoment = moment();

    if (this.dateFilterValue === DateRangeSearchType.Range) {
        if (!this.fromDate || !this.toDate) {
             this.messageService.add({ severity: ToastTypes.error, detail: this.i18n.instant('pleaseSelectValidDateRange') });
             return null;
        }
        dateFromMoment = moment(this.fromDate);
        dateToMoment = moment(this.toDate);
        if (dateToMoment.isBefore(dateFromMoment)) {
            this.messageService.add({ severity: ToastTypes.error, detail: this.i18n.instant('fromDateShouldBeBeforeToDate') });
            return null;
        }
    } else if (this.dateFilterValue === DateRangeSearchType.Last) {
        dateFromMoment = moment();
        dateToMoment = moment();
    } else {
        this.messageService.add({ severity: ToastTypes.error, detail: this.i18n.instant('invalidDateFilterType') });
        return null;
    }

    const queryId = Math.floor(Math.random() * 9999);


    let finalEventIds: string[] = [];
    if (this.searchBySelectVal === SearchType.Event) {
        if (this.selectedAlerts && this.selectedAlerts.length > 0) {
            finalEventIds = [...this.selectedAlerts];
        } else if (this.eventIdsSelectVal && this.eventIdsSelectVal.length > 0) {
            finalEventIds = [...this.eventIdsSelectVal];
        }
    }


    let finalTriggerTypes: number[] = [];
    if (this.searchBySelectVal === SearchType.Trigger) {
        if (this.selectedTrigger) {
            finalTriggerTypes = [this.selectedTrigger];
        } else if (this.triggerTypeSelectVal && this.triggerTypeSelectVal.length > 0) {
            finalTriggerTypes = [...this.triggerTypeSelectVal];
        }
    } else if (this.searchBySelectVal === SearchType.Event) {
        if (this.triggerTypeSelectVal && this.triggerTypeSelectVal.length > 0) {
            finalTriggerTypes = [...this.triggerTypeSelectVal];
        }
    }


    let finalTransformation = '';
    if (this.searchBySelectVal === SearchType.Transformation) {
        finalTransformation = this.transformBySelectVal || '';
    }

    return {
      selectedReport: this.selectedReport,
      Name: this.newTemplateName.trim(),
      Type: this.searchBySelectVal.toString(),
      TriggerTypes: finalTriggerTypes,
      EventIds: finalEventIds,
      TextFilter: this.textFilter,
      DateRangeType: this.dateFilterValue.toString(),
      Value: this.dateLast,
      DateLastUnit: this.selectedDateLastOption,
      Transformation: finalTransformation,
      From: dateFromMoment.toISOString(),
      To: dateToMoment.toISOString(),
      ResourceGroup: this.selectedResourceGroup,
      Resource: this.selectedResource || '',
      Identity: id,
      UserSessionId: this.authService.user && this.authService.user.Identity ? this.authService.user.Identity : '',
      CollectOption: ReportCollectOption.Notify,
      QueryId: queryId
    };
  }

  reportTemplateToString(template: ReportsQueryParams): string {
    let retVal = this.i18n.instant("searchTemplateParams") + "_";
    if (template.Name) retVal += this.i18n.instant("Name") + "_" + template.Name + "_";

    if (template.DateRangeType === DateRangeSearchType.Range.toString()) {
        retVal += this.i18n.instant("From") + "_" + moment(template.From).format("DD-MM-YYYY_HH-mm") + "_" + this.i18n.instant("To") + "_" + moment(template.To).format("DD-MM-YYYY_HH-mm");
    } else if (template.DateRangeType === DateRangeSearchType.Last.toString()) {
        let units = this.dateFilterLastOptions.find(x => x.value === template.DateLastUnit);
        if (units) retVal += this.i18n.instant("last") + "_" + template.Value + "_" + this.i18n.instant(units.label ?? '');
    }
    let searchByOption = this.searchBy.find(x => x.value.toString() === template.Type);
    if (searchByOption) retVal += "_" + this.i18n.instant("searchBy") + "_" + this.i18n.instant(searchByOption.label ?? '');
    if (template.Type === SearchType.Transformation.toString() && template.Transformation) retVal += "_" + this.i18n.instant("reportTransformation") + "_" + template.Transformation;
    return retVal.split(' ').join('_');
  }

  decrementPeriod(): void {
    if (this.periodValue > 1) {
      this.periodValue--;
      this.onPeriodValueChange();
    }
  }

  incrementPeriod(): void {
    if (this.periodValue < 9999) {
      this.periodValue++;
      this.onPeriodValueChange();
    }
  }

  ngOnDestroy(): void {
    this.storeObservables.forEach(sub => sub.unsubscribe());
    this.alive = false;
  }


  onCancelReport(): void {

    this.cancelReportGeneration.next();
    this.reportQueryId = null as any;
    this.reportGenerationCancelled = true;
    this.messageService.add({
      severity: ToastTypes.info,
      detail: this.i18n.instant('reportGenerationCancelled')
    });
  }


  retryReport(): void {
    this.reportQueryId = null as any;
    this.reportGenerationCancelled = false;
    this.generateReport();
  }


  exportToPdf(): void {

    const reportResultsComponent = document.querySelector('app-report-results');
    const reportTableComponent = document.querySelector('app-report-table');
    const hasTableData = reportResultsComponent && reportTableComponent;

    if (hasTableData) {

      const tableData = this.getTableDataFromReportResults();

      this.reportsTurboTableService.exportToPDF(tableData);
    } else {
      this.messageService.add({
        severity: ToastTypes.info,
        detail: this.i18n.instant('noDataToExport')
      });
    }
  }

  exportToCsv(): void {
    const reportResultsComponent = document.querySelector('app-report-results');
    const reportTableComponent = document.querySelector('app-report-table');
    const hasTableData = reportResultsComponent && reportTableComponent;

    if (hasTableData) {
      // Get the table data from the report results component
      const tableData = this.getTableDataFromReportResults();
      if (tableData && tableData.length > 0) {
        // Use the direct method from the service
        this.reportsTurboTableService.exportToCSV(tableData, 'reportExport');
      } else {
        this.messageService.add({
          severity: ToastTypes.info,
          detail: this.i18n.instant('noDataToExport')
        });
      }
    } else {
      this.messageService.add({
        severity: ToastTypes.info,
        detail: this.i18n.instant('noDataToExport')
      });
    }
  }


  private getTableDataFromReportResults(): any[] {
    try {

      const reportResultsComponent = document.querySelector('app-report-results');
      if (reportResultsComponent) {

        const componentInstance = reportResultsComponent['__ngContext__']?.find(item => item && item.tableData);
        if (componentInstance && componentInstance.tableData) {
          return componentInstance.tableData;
        }
      }
    } catch (error) {
    }
    return [];
  }
}
