import { ToastTypes } from './../../../shared/enum/toast-types';
import { Guid } from './../../../shared/enum/guid';
import { Component, Input, Output, EventEmitter, ViewChild } from '@angular/core';
import { MessageService } from 'primeng/api';
import { DossierHistoryComponent } from '../dossier-history/dossier-history.component';
import { Router } from '@angular/router';
import * as moment from 'moment';
import { TranslateService } from '@ngx-translate/core';
import { DossierService } from '../../../services/dossier/dossier.service';
import { Dossier } from '../models/dosier.model';

@Component({
  selector: 'app-dossier-actions',
  templateUrl: './dossier-actions.component.html',
  styleUrls: ['./dossier-actions.component.scss']
})
export class DossierActionsComponent {
  @Input() dossier: Dossier;
  @Output() refresh = new EventEmitter<void>();
  @ViewChild('historyDialog') historyDialog!: DossierHistoryComponent;
  loading = false;

  constructor(
    private messageService: MessageService,
    private translateService: TranslateService,
    private dossierService: DossierService,
    private router: Router
  ) {}

  navigateToDetections(): void {
    if (!this.dossier?.eventId) {
      this.messageService.add({
        severity: 'error',
        summary: this.translateService.instant(ToastTypes.error),
        detail: this.translateService.instant('dossierHistory.noEventIdNavigation')
      });
      return;
    }

    // Calculate timestamp range (1 hour before and after the dossier timestamp)
    const dossierTime = moment(this.dossier.timeStamp);
    const startTime = dossierTime.clone().subtract(1, 'hour').toISOString();
    const endTime = dossierTime.clone().add(1, 'hour').toISOString();


    // Navigate using the Router with the correct URL structure
    const baseUrl = '/new-dashboard/generalDetections';
    const pageIndex = 0;
    const pageSize = 30;
    const resourceGroupId = Guid.EMPTY; // Default empty GUID
    const searchValue2 = '-'; // Default value when no secondary search

    this.router.navigate([baseUrl], {
      queryParams: {
        pageIndex: pageIndex,
        pageSize: pageSize,
        startTimeStamp: startTime,
        endTimeStamp: endTime,
        resourceGroupId: resourceGroupId,
        searchValue1: this.dossier.eventId,
        searchValue2: searchValue2
      }
    });
  }

  downloadDossier(): void {
    if (!this.dossier?.eventId) {
      this.messageService.add({
        severity: 'error',
        summary: this.translateService.instant(ToastTypes.error),
        detail: this.translateService.instant('dossierHistory.noEventIdDownload')
      });
      return;
    }

    this.loading = true;
    this.dossierService.dossierDetections(this.dossier.eventId)
      .subscribe({
        next: (blob: Blob) => {
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `${this.dossier.name || 'dossier'}.zip`;
          link.click();
          window.URL.revokeObjectURL(url);
          this.loading = false;
        },
        error: () => {
          this.messageService.add({
            severity: 'error',
            summary: this.translateService.instant(ToastTypes.error),
            detail: this.translateService.instant('dossierHistory.downloadError')
          });
          this.loading = false;
        }
      });
  }

  viewHistory(): void {
    if (this.historyDialog) {
      this.historyDialog.show();
    }
  }

  deleteDossier(): void {
    if (!this.dossier?.id) {
      this.messageService.add({
        severity: 'error',
        summary: this.translateService.instant(ToastTypes.error),
        detail: this.translateService.instant('dossierHistory.noDossierId')
      });
      return;
    }

    this.loading = true;
    this.dossierService.deleteDossier(this.dossier.id)
      .subscribe({
        next: () => {
          this.messageService.add({
            severity: 'success',
            summary: this.translateService.instant(ToastTypes.success),
            detail: this.translateService.instant('dossierHistory.dossierDeletedSuccessfully')
          });
          this.refresh.emit();
          this.loading = false;
        },
        error: () => {
          this.messageService.add({
            severity: 'error',
            summary: this.translateService.instant(ToastTypes.error),
            detail: this.translateService.instant('dossierHistory.failedDeleteDossier')
          });
          this.loading = false;
        }
      });
  }
}