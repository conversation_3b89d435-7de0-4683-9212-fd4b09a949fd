    <p-table
    [value]="data"
    [loading]="loading"
    [paginator]="true"
    [rows]="pageSize"
    [totalRecords]="totalRecords"
    [lazy]="true"
    selectionMode="single"
    [(selection)]="selectedRow"
    dataKey="id"
    (onRowSelect)="onRowSelect($event.data)"
    [first]="pageIndex * pageSize"
    [showCurrentPageReport]="true"
    (onPage)="onPageChange($event)"
    styleClass="p-datatable-striped">
    <ng-template pTemplate="header">
        <tr>
            <th>{{ 'generalDetections.plateId' | translate }}</th>
            <th>{{ 'generalDetections.inputChannel' | translate }}</th>
            <th>{{ 'generalDetections.description' | translate }}</th>
            <th>{{ 'generalDetections.timeStamp' | translate }}</th>
            <th *ngIf="showActions" style="text-align: right">{{ 'generalDetections.actions' | translate }}</th>
        </tr>
    </ng-template>

    <ng-template pTemplate="loading">
        <div class="loading-container">
            <div class="loading-content">
                <p-progressSpinner styleClass="custom-spinner" strokeWidth="4" animationDuration=".5s"></p-progressSpinner>
            </div>
        </div>
    </ng-template>

    <ng-template pTemplate="body" let-item>
        <tr [pSelectableRow]="item">
            <td>
                <p [ngClass]="{ 'entity-id': !!item.entityId }">
                    {{ item.entityId | valueOrDefault }}
                </p>
            </td>
            <td>{{item.inputChannel | resourceName | valueOrDefault }} </td>
            <td>{{item.description | valueOrDefault  }}</td>
            <td>{{item.timeStamp | iotTimestamp | valueOrDefault  }} </td>
            <td *ngIf="showActions">
                <app-general-detection-actions [row]="item"></app-general-detection-actions>
            </td>
        </tr>
    </ng-template>

    <ng-template pTemplate="emptymessage">
        <tr>
            <td colspan="6" class="text-center">{{ 'generalDetections.noGeneralDetectionsFound' | translate }}</td>
        </tr>
    </ng-template>
</p-table>
