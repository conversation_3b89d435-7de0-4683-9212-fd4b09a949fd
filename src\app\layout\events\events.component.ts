import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Component<PERSON>ef, ComponentFactoryResolver, ViewChild, ViewContainerRef, Renderer2, Type } from '@angular/core';
import { CymsidebarService } from 'app/shared/components/cym-sidebar/cym-sidebar.service';
import { ConfirmationService, MessageService } from 'primeng/api';
import { EventsTabOptions } from './models/events-tab-options.interface';
import { EventsTabs } from './enums/events-tabs.enum';
import { GlobalAction } from 'app/shared/models/global-action.interface';
import { EventsActionType } from './enums/events-action-type.enum';
import { Subscription } from 'rxjs';

import { AppNotification } from 'app/shared/modules/data-layer/models/app-notification';
import { DefaultEventsEditorComponent } from './components/default-events-editor/default-events-editor.component';
import { EventsEditorComponents } from './models/events-editor-components';
import { EventsActionData } from './models/events-action-data.interface';
import { Entity } from 'app/shared/modules/data-layer/models/entity';

import { TranslateService } from '@ngx-translate/core';
import { ToastTypes } from 'app/shared/enum/toast-types';
import { EntityService } from 'app/services/entity/entity.service';
import {AppNotificationService} from "app/services/app-notification.service";

@Component({
  selector: 'app-events',
  templateUrl: './events.component.html',
  styleUrls: ['./events.component.scss'],
  providers: [CymsidebarService, ConfirmationService]
})
export class EventsComponent implements OnInit, OnDestroy {

  public opened: boolean = false;
  public viewOptions: EventsTabOptions[] = [
    {id: EventsTabs.live, name: "eventsPage.liveView", isActive: true},
    {id: EventsTabs.identification, name: "eventsPage.identifications", isActive: false}
  ];
  public dropDownActions: GlobalAction[] = [
    {name: 'addEntity', type: EventsActionType.addEntity}
  ];
  public subscriptions: Subscription[] = [];

  public notificationsModelStore: {[id: string]: AppNotification};
  public newNotifications: AppNotification[];
  componentRef: ComponentRef<DefaultEventsEditorComponent>;
  @ViewChild('editComponentFactory', {read: ViewContainerRef, static: false}) editComponent: ViewContainerRef;

  constructor(
    private cymSidebarService: CymsidebarService,
    private appNotificationService: AppNotificationService,
    private componentFactoryResolver: ComponentFactoryResolver,
    private renderer2: Renderer2,
    private entityService: EntityService,
    private messageService: MessageService,
    private translateService: TranslateService
  ) { }

  ngOnInit(): void {
    let notificationServiceSubscription = this.appNotificationService.getAll().subscribe(res => {
      this.notificationsModelStore = res;
    });
    this.subscriptions.push(notificationServiceSubscription);

    let newNotificationsSubscription = this.appNotificationService.resourceChanged.subscribe(res => {
      this.newNotifications = res.models;
    })
    this.subscriptions.push(newNotificationsSubscription);
  }

  onSelectView(event: EventsTabs): void {
    this.viewOptions.map(item => item.isActive = item.id === event ? true : false);
  }

  performEventsActionOnData(data: EventsActionData): void {
    switch(data.action){
      case EventsActionType.addEntity:
      case EventsActionType.editEntity:
        this.createEditComponent(EventsEditorComponents[data.action], data.entityInfo, 'onEditEntityComponentAction');
        this.cymSidebarService.openSidebar();
        break;
      case EventsActionType.saveEntity:
        this.addUpdateEntity(data.entityInfo);
        this.cymSidebarService.closeSidebar();
        break;
      case EventsActionType.cancel:
        if(this.componentRef) this.componentRef.destroy();
        this.cymSidebarService.closeSidebar();
        break;
      default:
        console.error("There is no component action for: ", data.action)
        break;
    }
  }

  addUpdateEntity(entity: Entity): void {
    this.messageService.add({severity: 'info', key: ToastTypes.requestPending, summary: this.translateService.instant(ToastTypes.info), detail: this.translateService.instant('uploadingEntities')});
    this.entityService.create(entity).subscribe(res => {
      this.messageService.clear(ToastTypes.requestPending);
      this.messageService.add({severity: 'success', summary: this.translateService.instant(ToastTypes.success), detail: this.translateService.instant('eventsPage.entitySaved')});
    }, error => {
      this.messageService.clear(ToastTypes.requestPending);
      this.messageService.add({severity: 'error', summary: this.translateService.instant(ToastTypes.error), detail: this.translateService.instant('eventsPage.entitySavedError')});
    });
  }

  createEditComponent(editComponent: Type<DefaultEventsEditorComponent>, data: Entity, output?: string){
    if(!editComponent){
        return
    }

    if(this.componentRef){
        this.componentRef.destroy();
    }
    const factory = this.componentFactoryResolver.resolveComponentFactory(editComponent as Type<DefaultEventsEditorComponent>);
    this.componentRef = this.editComponent.createComponent(factory);
    this.renderer2.addClass(this.componentRef.location.nativeElement, 'dynamic-edit-component');
    this.componentRef.instance.data = data;
    if(output){
      this.componentRef.instance[output].subscribe((event:EventsActionData) => {
        this.performEventsActionOnData(event);
      });
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(item => item.unsubscribe());
  }

}
