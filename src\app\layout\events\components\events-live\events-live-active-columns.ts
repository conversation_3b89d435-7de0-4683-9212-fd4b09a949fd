import { TableColumnProperties } from 'app/shared/components/ng-turbo-table/models/table-column';

export const eventsLiveActiveColumns: TableColumnProperties[] = [
    {
        field: "description",
        header: "description",
        frozen: false,
        visibile: true,
        sortable: true
    },
    {
        field: "types",
        header: "eventsPage.deviceType",
        frozen: false,
        visibile: true,
        sortable: true
    },
    {
        field: "source",
        header: "eventsPage.source",
        frozen: false,
        visibile: true,
        sortable: true
    },
    {
        field: "timeStamp",
        header: "time_stamp",
        frozen: false,
        visibile: true,
        sortable: true
    },
    {
        field: "entityName",
        header: "eventsPage.entityName",
        frozen: false,
        visibile: true,
        sortable: true
    },
    {
        field: "identifiedValue",
        header: "eventsPage.identifiedValue",
        frozen: false,
        visibile: true,
        sortable: true
    },
    {
        field: "confidence",
        header: "confidence",
        frozen: false,
        visibile: true,
        sortable: true
    },
    {
        field: "actions",
        header: "actions",
        frozen: false,
        visibile: true,
        sortable: true
    },
    {
        field: "image",
        header: "eventsPage.snapshot",
        frozen: false,
        visibile: true,
        sortable: true
    }
]