<div class="dashboardSettings">

    <app-edit-widget [selectedWidget]="data"></app-edit-widget>

    <div class="form-item">
        <h2>{{ 'customerDashboard.selectGroup' | translate }}</h2>
        <p-dropdown [options]="groupList" [filter]="true" filterBy="label" [(ngModel)]="data.selectedGroupId"
            [styleClass]="'input-element'" (onChange)="onWidgetDataChange()">
            <ng-template let-item pTemplate="selectedItem">
                <span>{{item.label| translate}}</span>
            </ng-template>
            <ng-template let-item pTemplate="item">
                <div class="ui-helper-clearfix">
                    <div>{{item.label | translate}}</div>
                </div>
            </ng-template>
        </p-dropdown>
    </div>
    <div class="form-item">
        <h2>{{ 'customerDashboard.selectResourceState' | translate }}</h2>
        <p-multiSelect [options]="statusList" [(ngModel)]="data.resourceStates" [styleClass]="'input-element'"
            (onChange)="onWidgetDataChange()" selectedItemsLabel="{{ 'nrItemsSelected' | translate }}"
            defaultLabel="{{ 'customerDashboard.selectResourceState' | translate }}" [maxSelectedLabels]="0">
            <ng-template let-item pTemplate="item">
                {{item.label | translate}}
            </ng-template>
        </p-multiSelect>
    </div>
</div>