<p-dialog [(visible)]="visible" [style]="{width: '70vw'}" [modal]="true" [draggable]="false" [resizable]="false">
    <ng-template  pTemplate="header">
        <div class="iot-modal-header">
            <h3 [pTooltip]="dossierName" [tooltipOptions]="tooltipOptions">
                <span class="dossier-name">{{ dossierName }}</span>
                <span class="translated-text">{{ 'dossierDetails.details' | translate }}</span>
              </h3>
        </div>
    </ng-template>

    <p-table *ngIf="dossiers" 
        [value]="dossiers"
        [loading]="loading"
        [paginator]="true"
        [rows]="10"
        currentPageReportTemplate="{{ 'dossierDetails.showingEntries' | translate }}" styleClass="p-datatable-striped">

        <ng-template pTemplate="header">
            <tr>
                <th>{{ 'dossierDetails.fileList' | translate }}</th>
                <th>{{ 'dossierDetails.size' | translate }}</th>
                <th>{{ 'dossierDetails.timeStamp' | translate }}</th>
            </tr>
        </ng-template>

        <ng-template pTemplate="body" let-item>
            <tr>
                <td>
                    <div class="flex">
                        <i class="fa fa-file-o"></i>
                        <span class="bold">{{item.name}}</span>
                    </div>
                </td>
                <td>{{item.compressedSize | bytesToMB }} </td>
                <td>{{item.creationTime | date:'MMM d, yyyy hh:mm:ss a'}}</td>
            </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage">
            <tr>
                <td colspan="4" class="text-center">{{ 'dossierHistory.noHistoryFound' | translate }}</td>
            </tr>
        </ng-template>
    </p-table>
</p-dialog>