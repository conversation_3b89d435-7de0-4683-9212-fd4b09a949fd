import {Component,OnInit,Output,<PERSON><PERSON><PERSON>ter,<PERSON><PERSON><PERSON><PERSON>,Inject} from '@angular/core';
import {DefaultMapEditorComponent} from '../default-map-editor/default-map-editor.component';
import {MapState} from '../../models/map-state.interface';
import {MapObject} from '../../models/map-object.interface';
import {Subscription,Subject,EMPTY} from 'rxjs';
import {FormGroup,FormControl,Validators,FormBuilder} from '@angular/forms';
import {Map} from 'app/shared/modules/data-layer/models/map';
import {SelectItem} from 'primeng/api';
import {debounceTime,distinctUntilChanged,switchMap} from 'rxjs/operators';
import {MapUtilsService} from '../../services/map-utils.service';
import {MapForm} from '../../enums/map-form.enum';

@Component({
    selector:'app-map-edit',
    templateUrl:'./map-edit.component.html',
    styleUrls:['./map-edit.component.scss']
})
export class MapEditComponent extends DefaultMapEditorComponent implements OnInit,OnDestroy {

    public data: {
        state: MapState,
        selectedMap: MapObject
    };

    private subscriptions: Subscription[] = [];
    public name$ = new Subject<string>();
    editMapForm: FormGroup;
    public tileSourceList: SelectItem[] = [];


    constructor(
        private formBuilder: FormBuilder,
        private mapUtilsService: MapUtilsService,
        @Inject('mapTileSourceList') tileSourceList: SelectItem[]
    ) {
        super();
        this.tileSourceList = tileSourceList;
    }

    ngOnInit() {

        this.generateForm(this.data.selectedMap.item);

        let mapNameSubscription = this.name$.pipe(
            debounceTime(500),
            distinctUntilChanged(),
            switchMap(name=>{
                this.data.selectedMap.item.name = name;
                this.mapUtilsService.setMapDataChange(this.data.selectedMap.item);
                return EMPTY;
            })
        ).subscribe();
        this.subscriptions.push(mapNameSubscription);

        let formStatusChangedSubscription = this.editMapForm.statusChanges.subscribe(status=>{
            this.mapUtilsService.setMapValidatorChange({[MapForm.EDITMAP]:status === 'VALID' ? true : false});
        });
        this.subscriptions.push(formStatusChangedSubscription);
    }

    ngOnDestroy() {
        this.subscriptions.forEach(subscription=>subscription.unsubscribe());
    }

    private generateForm(data: Map): void {
        this.editMapForm = this.formBuilder.group({
            mapName:new FormControl(data ? data.name : '',Validators.required),
            tileLayer:new FormControl(data ? data.type : 3,Validators.required)
        });
        this.mapUtilsService.setMapValidatorChange({[MapForm.EDITMAP]:this.editMapForm.status === 'VALID' ? true : false});
    }

    public onTileLayerDataChange(event) {
        this.data.selectedMap.item.type = event.value;
        this.mapUtilsService.setMapDataChange(this.data.selectedMap.item);
    }

}
