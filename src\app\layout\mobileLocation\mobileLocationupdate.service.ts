import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Resource } from "app/shared/modules/data-layer/models/resource";
import { apiMap } from "app/shared/services/api.map";
import { environment } from "environments/environment";
import { Observable } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class MobileLocationUpdateService {
  constructor(private httpClient: HttpClient) {}

  updateLocation(resourceIdentity: Resource, latitude: number, longitude: number): Observable<[]> {
    let data = {
      Identity: resourceIdentity.identity,
      Latitude: latitude.toString(),
      Longitude: longitude.toString(),
      Heading: 0.0,
      ResourceURI: null,
    };
    return this.httpClient.post<[]>(environment.apiUrl + apiMap.addUpdateResourcesLocation.url, [data]);
  }
}
