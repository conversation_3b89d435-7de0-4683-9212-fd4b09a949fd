import { FilterOptions } from "app/shared/models/filterOptions.interface";

export const dataTabel: Array<{}> = [
    {
        id: "bca3520c-5a14-11ea-be5f-509a4c01f3c7",
        name: "3_5, Channel  1",
        resource: "resource 1",
        type: "Dashboard",
        manufacturerId: 1,
        deviceTypeId: 1, 
        action: '',
        status: "Online"
      },
      {
        id: "8871c2d7-2ae7-4f85-9753-b9310e21ad88",
        name: "Test2",
        resource: "resource 2",
        type: "Device",
        manufacturerId: 2,
        deviceTypeId: 2, 
        action: '',
        status: "Offline"
      },
      {
        id: "8871c2d7-2ae7-4f85-9753-b9310e21ad88",
        name: "Test2",
        resource: "resource 3",
        type: "Device",
        manufacturerId: 3,
        deviceTypeId: 3, 
        action: '',
        status: "Off"
      },
      {
        id: "8871c2d7-2ae7-4f85-9753-b9310e21ad88",
        name: "Test2",
        resource: "unassigned",
        type: "Device",
        manufacturerId: 2,
        deviceTypeId: 2, 
        action: '',
        status: "Unknown"
      },

   
]

export const nameFilterOptions: FilterOptions[] = [
  { label: "Test2", value: "Test2" },
  { label: "3_5, Channel 1", value: "3_5, Channel 1" },
];

export const typeFilterOptions: FilterOptions[] = [
  { label: "Dashboard", value: "Dashboard"},
  { label: "Device", value: "Device" },
  { label: "Camera", value: "Camera" },
];

export const statusFilterOptions: FilterOptions[] = [
  { label: "Online", value: "Online" },
  { label: "Offline", value: "Offline" },
  { label: "Off", value: "Off" },
  { label: "Unknown", value: "Unknown" },
];


export const columnsTabel: Array<{}> = [
    { field: 'name', header: 'name', visibile: true, frozen: false, filterOptions: nameFilterOptions,  filterable: true, sortable: true, filterMatchMode: 'in'},
    { field: 'groups', header: 'resourceGroup',  visibile: true,  frozen: false, filterable: true,  sortable: true },
    { field: 'resourceType', header: 'type',  visibile: true,  frozen: false, filterOptions: typeFilterOptions, filterable: true, sortable: true, filterMatchMode: 'in'},
    { field: 'action', header: 'actions',   visibile: true,  frozen: false, filterable: false,  sortable: false},
    { field: 'status', header: 'status',  visibile: true,  frozen: false, filterOptions: statusFilterOptions, filterable: true,  sortable: true, filterMatchMode: 'in'},
    { field: 'location', header: 'location',  visibile: true,  frozen: false,  sortable: true, },
    { field: 'Identity', header: 'id',  visibile: true,  frozen: false,  sortable: true, },
]


export const manufacturersData:  Array<{}> = [
  { label: 'Advanced', value: 1 },
  { label: 'AgentVI', value: 2 },
  { label: 'AnyVision System', value: 3 }
]

export const manufacturersFilterListData:  Array<{}> = [
  { label: 'ADAM 6050', value: '1', manufacturedId: 1 },
  { label: 'VI Agent 16 Channles', value: '2', manufacturedId: 2 },
  { label: 'AnyVision System', value: '3', manufacturedId: 3 },
  { label: 'Test AnyVision System', value: '4', manufacturedId: 3 },
]

export const resourceGroupsData:  Array<{}> = [
  { label: 'Group 1', value: 1 },
  { label: 'Group 2', value: 2 },
  { label: 'Group 3', value: 3 },
  { label: 'Group 4', value: 4 },
  { label: 'Group 5', value: 5 }
]