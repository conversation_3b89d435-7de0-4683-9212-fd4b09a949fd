$red: #FF0000;
$green: #00D600;
$textColor: #808381;

.green  {
    color: $green;
}

.red  {
    color: $red;
}

.bold {
    font-weight: bold;
}

.filters-container {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.filters-wrapper {
    display: grid;
    grid-template-columns: auto 1fr; 
    gap: 20px;
    align-items: center;
}

.title {
    font-size: 20px;
    color: #3a3a3a;
    font-weight: 600;
    white-space: nowrap;
}

.filters-content {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
}

.filter-element-container {
    width: 100%;
    text-align: center;
}

.filter-element {
    display: flex;
    align-items: center;
    background: #F2F6F3;
    padding: 5px;
    border-radius: 8px;
    gap: 5px;
    flex: 1; 
}

.reset-button {
    width: min-content;
}

.p-inputtext {
    &:focus {
        box-shadow: 0 0 0 0.2rem rgba($green, 0.5);
        border: 0px;
    }
    border: 1px solid #E4ECE6;
}

.p-inputtext,
    p-dropdown,
    p-calendar {
    flex: 1;
    font-weight: 500;
    background: #F2F6F3;
}

::ng-deep .p-dropdown {
    background: #F2F6F3;
    border: 1px solid #E4ECE6;
    font-weight: 500;
    .p-dropdown-label,
    .p-dropdown-trigger{
        color: $textColor;
    }
}

.filter-element i, 
.filter-element img {
    font-size: 1.1rem;
    padding: 0px 5px 0px 5px;
}


:host ::ng-deep {
    .p-calendar {
       width: 100%;
       border: 1px solid #E4ECE6;
       .p-inputtext {
           background: transparent;
           border: 0px;
       }
       &:focus {
           box-shadow: 0 0 0 0.2rem $green
       }
   }
}


@media (max-width: 1200px) {
    .filters-content, 
    .filters-wrapper {
        grid-template-columns: 1fr;
    }
}
