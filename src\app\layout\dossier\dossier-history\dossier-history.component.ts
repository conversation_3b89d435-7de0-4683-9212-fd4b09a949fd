import { Component, Input, OnD<PERSON>roy } from '@angular/core';
import { MessageService } from 'primeng/api';
import { Subject, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { DossierHistoryRequest } from '../models/dosierHistoryRequest.model';
import { DossierHistory } from '../models/dosierHistory.mode';
import { TranslateService } from '@ngx-translate/core';
import { ToastTypes } from './../../../shared/enum/toast-types';
import { DossierService } from '../../../services/dossier/dossier.service';
import { Dossier } from '../models/dosier.model';

@Component({
  selector: 'app-dossier-history',
  templateUrl: './dossier-history.component.html',
  styleUrls: ['./dossier-history.component.scss']
})
export class DossierHistoryComponent implements OnDestroy{
  @Input() dossier: Dossier;
  visible = false;
  loading = false;
  historyItems: DossierHistory[] = [];
  pageSize = 12;
  totalRecords = 0;
  searchQuery: string = '';
  startDate: Date | null = null;
  endDate: Date | null = null;
  subscriptions: Subscription[] = [];
  
  tooltipOptions = {
    showDelay: 150,
    autoHide: false,
    tooltipEvent: 'hover',
    tooltipPosition: 'top'
  };

  private searchSubject = new Subject<string>();

  constructor(
    private messageService: MessageService,
    private dossierService: DossierService,
    private translateService: TranslateService) {
    this.subscriptions.push(
      this.searchSubject.pipe(debounceTime(1000), distinctUntilChanged()).subscribe(() => {
        if (this.dossier) {
          this.updateFilter({ PageIndex: 0 });
        }
      }));
  }

  get isResetDisabled(): boolean {
    return !this.searchQuery && !this.startDate && !this.endDate;
  }

  show(): void {
    this.visible = true;
    this.resetData();
    this.updateFilter({ PageIndex: 0 });
  }

  hide(): void {
    this.visible = false;
    this.resetData();
  }

  resetData(): void {
    this.searchQuery = '';
    this.startDate = null;
    this.endDate = null;
  }

  onSearch(): void {
    this.searchSubject.next(this.searchQuery);
  }

  onDateSelect(): void {
    this.updateFilter({ PageIndex: 0 });
  }

  onPageChange(event: any): void {
    let pageIndex = event.first / event.rows;
    this.updateFilter({ PageIndex: pageIndex });
  }

  resetFilterTable(): void {
    this.resetData();
    this.updateFilter({ PageIndex: 0 });
  }

  onDownload(event: Event, item: DossierHistory): void {
    event.stopPropagation();
    if (!item?.dossierId) {
      this.messageService.add({
        severity: 'error',
        summary: this.translateService.instant(ToastTypes.error),
        detail: this.translateService.instant('dossierHistory.downloadError')
      });
      return;
    }
    this.downloadDossierArchive(item.id);
  }

  downloadDossierArchive(dossierHistoryId: string): void {
    this.loading = true;
    this.subscriptions.push(
      this.dossierService.downloadDossierHistoryArchive(dossierHistoryId)
      .subscribe({
        next: (blob: Blob) => {
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `dossier_${dossierHistoryId}.zip`;
          link.click();
          window.URL.revokeObjectURL(url);
          this.loading = false;
        },
        error: () => {
          this.messageService.add({
            severity: 'error',
            summary: this.translateService.instant(ToastTypes.error),
            detail: this.translateService.instant('dossierHistory.downloadError')
          });
          this.loading = false;
        }
      }));
  }

  loadHistory(request: DossierHistoryRequest): void {
    if (!this.dossier) {
      return;
    }
    this.loading = true;
    this.subscriptions.push(
      this.dossierService.dossierHistory(request)
        .subscribe({
          next: (response) => {
            if (response.status?.isSuccess === false) {
              this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant(ToastTypes.error),
                detail: response.status.message || this.translateService.instant('dossierHistory.loadError')
              });
              return;
            }

            this.historyItems = response.items || [];
            this.totalRecords = response.totalCount || 0;
            this.loading = false;
          },
          error: () => {
            this.loading = false;
            this.messageService.add({
              severity: 'error',
              summary: this.translateService.instant(ToastTypes.error),
              detail: this.translateService.instant('dossierHistory.loadError')
            });
          }
        }));
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((i) => i.unsubscribe());
  }

  private updateFilter(overrides: Partial<DossierHistoryRequest> = {}): void {
    const request: DossierHistoryRequest = {
      PageIndex: 0,
      PageSize: this.pageSize,
      Details: this.searchQuery || null,
      DossierId: this.dossier.id,
      StartTimestamp: this.startDate ? this.startDate.toISOString() : undefined,
      EndTimestamp: this.endDate ? this.endDate.toISOString() : undefined,
      ...overrides
    };
    this.loadHistory(request);
  }
}
