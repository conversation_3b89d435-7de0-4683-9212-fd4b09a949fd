import { NgModule } from '@angular/core';
import { SharedModule } from 'app/shared/modules/shared.module';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DefaultActionsComponent } from './components/actions/default-actions-component/default-actions.component';
import { EditEntityComponent } from './components/edit-entity/edit-entity.component';
import { EventsActionsComponent } from './components/events-actions/events-actions.component';
import { EventsLiveComponent } from './components/events-live/events-live.component';
import { IdentificationsComponent } from './components/identifications/identifications.component';
import { EventsRoutingModule } from './events-routing.module';
import { EventsComponent } from './events.component';




@NgModule({
  declarations: [
    EventsComponent, 
    EventsLiveComponent, 
    IdentificationsComponent, 
    EventsActionsComponent, 
    EditEntityComponent,
    DefaultActionsComponent
  ],
  imports: [
    SharedModule,
    EventsRoutingModule,
    ConfirmDialogModule
  ],
  entryComponents: [
    EditEntityComponent
  ]
})
export class EventsModule { }
