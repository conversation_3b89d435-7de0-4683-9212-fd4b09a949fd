<!-- Main Content -->
<div class="reports-container" [class.sidebar-collapsed]="isSidebarCollapsed">
    <div class="reports-content h-100">
        <app-page-title title="reports"></app-page-title>
        <div class="header-actions">
            <div class="d-flex justify-content-between align-items-center">
                <app-new-filter class="responsive-filter"
                    [reportQueryId]="reportQueryId"
                    (generateReportStart)="onGenerateReportStart($event)"
                    (cancelReportGeneration)="onCancelReportGeneration()">
                </app-new-filter>

            </div>
        </div>

        <div class="content-area">
            <app-report-results
                [reportQueryId]="reportQueryId"
                [reportDataChunkPayload]="reportDataChunkPayload"
                [filterQueryparams]="filterQueryparams"
                [resultsLength]="resultsLength">
            </app-report-results>
        </div>
    </div>
</div>



<!-- Settings Modal -->
<app-modal #settingsModal [title]="'settings'" styleClass="modal-content settings-modal" [position]="position">
    <ng-container ngProjectAs="contentModal">
        <div class="settings-wrapper">
            <h2 class="settings-title">{{ "reportSettings" | translate }}</h2>
            <div class="settings-options">
                <div class="setting-item">
                    <p-checkbox label="{{ 'autoRefresh' | translate }}" binary="true" [(ngModel)]="autoRefresh">
                    </p-checkbox>
                </div>
                <div class="setting-item">
                    <p-checkbox label="{{ 'showFilters' | translate }}" binary="true" [(ngModel)]="showFilters">
                    </p-checkbox>
                </div>
            </div>
        </div>
    </ng-container>
    <ng-container ngProjectAs="footerModal">
        <div class="modal-footer">
            <button class="btn btn-secondary" (click)="onSettingsCancel()">
                {{ 'cancel' | translate }}
            </button>
            <button class="btn btn-primary" (click)="onSettingsConfirm()">
                {{ 'ok' | translate }}
            </button>
        </div>
    </ng-container>
</app-modal>


