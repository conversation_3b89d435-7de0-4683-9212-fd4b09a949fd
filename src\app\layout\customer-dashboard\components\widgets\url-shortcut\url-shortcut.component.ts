import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UrlShortcutWidget } from 'app/layout/customer-dashboard/models/url-shortcut-widget';
import { NavigationService } from '../../../../../shared/services/navigation.service';
@Component({
  selector: 'app-url-shortcut',
  templateUrl: './url-shortcut.component.html',
  styleUrls: ['./url-shortcut.component.scss']
})
export class UrlShortcutComponent implements OnInit {
  data: { index: number, widgetData: UrlShortcutWidget };

  constructor(private router: Router, private navigationService: NavigationService) {
  }

  ngOnInit(): void {
    this.data.widgetData = new UrlShortcutWidget(this.data.widgetData);
  }

  redirect(url: string): void {
    let urlFormat =  url.replace(/^.*\/\/[^\/]+/, '');
    this.router.navigateByUrl(urlFormat);
  }

}
