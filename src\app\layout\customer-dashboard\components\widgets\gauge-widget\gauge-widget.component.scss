@import '~styles/colors';
::ng-deep {
    mwl-gauge {
        width: 100%;
        height: 100%;
        .gauge > .gauge-text {
            display: none;
            font-size: 0.8em;
        }
        .value {
            stroke-width: 20;
            transition: stroke 1s ease-in-out;
        }
        .dial {
            stroke-width: 20;
            stroke: var(--secondary-highlight-5);
        }
        
        .value-text {
            fill: var(--primary-1);
        }

        &.default {
            .value {
                stroke: var(--primary-1);    
            }
        }
        &.blue {
            .value {
                stroke: var(--info-2);    
            }
        }
        &.green {
            .value {
                stroke: var(--success);    
            }
        }
        &.yellow {
            .value {
                stroke: var(--info);    
            }
        }
        &.red {
            .value {
                stroke: var(--error);    
            }
        }

        .gauge {
            position: absolute;
            left: 0;
            right: 0;
            margin: auto;
            max-width: 100%;
            text {
                transform: translateY(-3px);
            }
        }
    }
}
:host {
    .gauge-custom-label {
        text-align: center;
        position: absolute;
        margin: 0 auto;
        right: 0;
        left: 0;
        bottom: -20px;
        p {
            font-size: 1.8em;
            color: var(--dark)
        }
    }
    .gauge-wrapper {
        display: flex;
        justify-content: center;
        position: relative;
    }
    .gauge-resizer {
        width: 90%;
        height: 90%;
        position: relative;
        &.portrait {
            ::ng-deep{
                .gauge {
                    width: 100%;
                    height: auto;
                    top: 100%;
                    transform: translateY(-50%);
                }
            }
        }
        &.landscape {
            ::ng-deep{
                .gauge {
                    width: 100%;
                    height: 170%;
                    top: 100%;
                    transform: translateY(-50%);
                }
            }    
        }
        
    }
    .no-data {
        position: absolute;
        top: 15px;
        left: 0;
        text-align: left;
        width: 100%;
        z-index: 1;
        padding-left: 10px;

    }
    .measurment-unit {
        position: absolute;
        bottom: 45px;
        width: 100%;
        color: var(--primary-1);
        text-align: center;
        z-index: 1;
    }
}
:host-context(.rtl) {
    .no-data {
        right: 0;
        left: unset;
        text-align: right;
        padding-right: 10px;
    }
}
:host-context(.widget.medium) { 
    .measurment-unit {
        font-size: 1rem;
        bottom: 35px;
    }    
}

:host-context(.widget.big) { 
    .measurment-unit {
        font-size: 1.5rem;
        bottom: 60px;
    }    
}
:host-context(.edit) { 
    .measurment-unit {
        display: none;
    }    
}