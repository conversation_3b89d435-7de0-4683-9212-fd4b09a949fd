import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from "@angular/core";
import { Activated<PERSON>out<PERSON>, Router } from "@angular/router";
import { GeneralDetectionService } from "../../services/generalDetections/general-detection.service";
import { GeneralDetection } from "../../shared/modules/data-layer/models/general-detections/general-detection";
import { ResourceGroup } from "../../shared/modules/data-layer/models/resource-group";
import { Subscription } from "rxjs";
import { Guid } from "../../shared/enum/guid";
import { GeneralDetectionExport, GeneralDetectionsFilter } from "./models/general-detections-filter.model";
import { GeneralDetectionsTableComponent } from './general-detections-table.component';
import { GeneralDetectionFilterComponent } from './general-detection-filter/general-detection-filter.component';
import { MessageService } from 'primeng/api';
import { TranslateService } from '@ngx-translate/core';
import { ToastTypes } from '../../shared/enum/toast-types';
import { NotificationsService } from '../../shared/components/header/notifications/notifications.service';
import { INotificationData } from "../../shared/components/header/notifications/INotificationData";
import * as _ from 'lodash';
import { ExportTypes } from "../../shared/enum/export-types.enum";
import { HttpResponse } from "@angular/common/http";

@Component({
    selector: "general-detections",
    templateUrl: "./general-detections.component.html",
    styleUrls: ["./general-detections.component.scss"],
})
export class GeneralDetectionsComponent implements OnInit, OnDestroy {
    @ViewChild(GeneralDetectionsTableComponent) tableComponent!: GeneralDetectionsTableComponent;
    @ViewChild('filterComponent') filterComponent!: GeneralDetectionFilterComponent;

    data: GeneralDetection[] = [];
    loading: boolean = false;
    loadingDetails: boolean = false;
    pageSize: number = 12;
    detailsPageSize: number = 11;
    totalRecords: number = 0;
    totalDetailsRecords: number = 0;
    currentPageIndex: number = 0;
    detailsCurrentPageIndex: number = 0;
    resourceGroups: ResourceGroup[] = [];
    selectedResourceGroup: ResourceGroup;
    routeEventId: string = '';
    detectionDetailsTitle: string = '';
    filterValues: GeneralDetectionsFilter;
    detectionDetails: INotificationData[] = [];

    private subscriptions: Subscription[] = [];

    constructor(
        private generalDetectionService: GeneralDetectionService,
        private notificationsService: NotificationsService,
        private router: Router,
        private activatedRoute: ActivatedRoute,
        private messageService: MessageService,
        private translateService: TranslateService) { }

    ngOnInit(): void {
        this.activatedRoute.queryParams.subscribe((params) => {
            this.routeEventId = params['eventId'] || '';
        });
    }

    onPageChange(newPageIndex: number): void {
        this.currentPageIndex = newPageIndex;
        this.resetDetailsData();
        this.getDetections();
    }

    onDetectionsFilter(event: GeneralDetectionsFilter): void {
        this.filterValues = event;
        if(!this.filterValues.eventId) {
            this._removeEventId();
        }
        this.resetDetailsData();
        this.getDetections();
    }

    getDetections(): void {
        this.loading = true;
        const startDate = this.filterValues?.startDate ?? new Date(1970, 1);
        const endDate = this.filterValues?.endDate ?? new Date(2100, 1);
        const searchValue1 = this.filterValues?.searchValue1?.trim() || "-";
        const searchValue2 = this.filterValues?.searchValue2?.trim() || "-";
        const resourceGroupId = this.filterValues?.resourceGroupId || Guid.EMPTY;
        const eventId = this.routeEventId || "-";

        this.generalDetectionService.getPage(this.currentPageIndex, this.pageSize, startDate, endDate, resourceGroupId, searchValue1, searchValue2, eventId).subscribe((detectionsResponse) => {
            this.data = detectionsResponse.Items;
            this.totalRecords = detectionsResponse.TotalCount;
            this.loading = false;
        }, () => {
            this.loading = false;
        });
    }

    private _removeEventId(): void {
        this.router.navigate([], {
          relativeTo: this.activatedRoute,
          queryParams: { eventId: null },
          queryParamsHandling: 'merge'
        });
    }

    onSelectedRowDetection(item: GeneralDetection): void {
        this.loadingDetails = true;
        this.detectionDetailsTitle = item.entityId;
        this.subscriptions.push(
            this.notificationsService.getDetectionNotificationByEventId(item.id).subscribe((response) => {
                this.detectionDetails = response;
                this.loadingDetails = false;
        }));
    }

    onCloseDetails(): void {
        this.resetDetailsData();
        this.filterComponent.resetExportState();
    }


    resetDetailsData(): void {
        this.detectionDetails = [];
        this.detectionDetailsTitle = '';
        this.detailsPageSize = 11;
        this.detailsCurrentPageIndex = 0;
        if (this.tableComponent) {
            this.tableComponent.selectedRow = null;
        }
    }

    onExportRequest(event: {type: ExportTypes, filters: GeneralDetectionsFilter}): void {
        this.getAllDetectionsForExport(event)
    }

    private getAllDetectionsForExport(event: { type: ExportTypes, filters: GeneralDetectionsFilter }): void {
        if (!event || !event?.type) {
            return;
        }
        const request: GeneralDetectionExport = {} as GeneralDetectionExport;
        if (event.type) {
            request.FileType = event.type.toUpperCase();
        }

        if (event.filters?.startDate) {
            request.StartTimestamp = event.filters.startDate
        }
        if (event.filters?.endDate) {
            request.EndTimestamp = event.filters.endDate
        }
        if (event.filters?.searchValue1) {
            request.FilterValue = event.filters?.searchValue1.trim()
        }
        if (event.filters?.searchValue2) {
            request.FilterValue = event.filters?.searchValue2.trim()
        }
        if (event.filters?.resourceGroupId && event.filters.resourceGroupId !== Guid.EMPTY) {
            request.ResourceGroupId = event.filters?.resourceGroupId
        }
        if (event.filters?.eventId) {
            request.EventId = event.filters?.eventId
        }
        this.subscriptions.push(
            this.generalDetectionService.exportGeneralDetections(request).subscribe({
                next: (response) => {
                    this.handleExportSuccess(response)
                },
                error: () => {
                    this.handleExportError();
                }
            })
        );
    }

      private handleExportSuccess(response: HttpResponse<Blob>): void {
        if (!response.body) {
            this.messageService.add({
            severity: ToastTypes.error,
            detail: this.translateService.instant('noDataToExport')
            });
            this.filterComponent.resetExportState();
            return;
        }

        const blob = response.body;    
        const mimeType = blob.type;
        const extension = mimeType ? `.${mimeType.split('/')[1]}` : '';

        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const fileName = `General-detections-export-${timestamp}${extension}`;

        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        this.messageService.add({
          severity: ToastTypes.success,
          detail: this.translateService.instant('exportSuccess')
        });
        this.filterComponent.resetExportState();
      }


    private handleExportError(): void {
        let errorMessage = this.translateService.instant('exportFailed');
        this.messageService.add({
            severity: ToastTypes.error,
            detail: errorMessage
        });
        this.filterComponent.resetExportState();
    } 

    ngOnDestroy(): void {
        this.subscriptions.forEach((sub) => sub.unsubscribe());
        this.routeEventId = '';
        this.resetDetailsData();
    }
}
