import {
    <PERSON><PERSON><PERSON>w<PERSON><PERSON><PERSON>,
    ChangeDetector<PERSON>ef,
    Component,
    ComponentFactoryResolver,
    ComponentRef,
    Inject,
    OnInit,
    ViewChild,
    ViewContainerRef
} from "@angular/core";
import { Router } from "@angular/router";
import { TranslateService } from "@ngx-translate/core";
import { ToastTypes } from "app/shared/enum/toast-types";
import { CymbiotMarker } from "app/shared/modules/cymbiot-map/components/cymbiot-map/cymbiot-marker";
import { GpsMarker } from "app/shared/modules/data-layer/models/gpsLocationMarker";

import { ResourceService } from "app/services/resource/resource.service";
import { Resource } from "app/shared/modules/data-layer/models/resource";
import { ResourceCacheService } from "app/shared/modules/data-layer/services/resource/resource.cache.service";
import { IMapProjectionTransformService } from "app/shared/modules/generic-map/components/base-map-transform.service";
import { GenericMapComponent } from "app/shared/modules/generic-map/components/generic-map.component";
import { Mark<PERSON> } from "app/shared/modules/generic-map/models/map.models";
import { MessageService, SelectItem } from "primeng/api";
import { GpsMarkerComponent } from "../gps-marker/gps-marker.component";
import { MobileLocationUpdateService } from "../mobileLocationupdate.service";

@Component({
    selector: "app-gpslocation",
    templateUrl: "./gpslocation.component.html",
    styleUrls: ["./gpslocation.component.scss"],
})
export class GpslocationComponent implements OnInit, AfterViewInit {
    selectedResource;
    markerDictionary: Map<string, CymbiotMarker> = new Map<string, CymbiotMarker>();
    public lat: number;
    public lng: number;
    scadaMarkerId = "00000";
    markers: CymbiotMarker[] = [];

    items: SelectItem[];

    public selectedItem: Resource;
    @ViewChild("customMarkersFactory", { read: ViewContainerRef, static: false }) customMarkersFactory: ViewContainerRef;
    @ViewChild("map", { static: false }) map: GenericMapComponent;

    constructor(
        private resourceCacheService: ResourceCacheService,
        private resourceService: ResourceService,
        @Inject("IMapProjectionTransformService") private projectionTransform: IMapProjectionTransformService,
        private componentFactoryResolver: ComponentFactoryResolver,
        private changeDetectorRef: ChangeDetectorRef,
        private scadaUpdateService: MobileLocationUpdateService,
        private messageService: MessageService,
        private i18n: TranslateService,
        private router: Router
    ) {
        this.items = [];

        let resourcesSubscription = this.resourceCacheService.storageCompleted.subscribe(() => {
            let resourceCache = this.resourceCacheService.getAll();
            const data: Resource[] = Array.from(resourceCache.values());
            data.map((item) => {
                if (item.name && item.name != "" && item.identity) {
                    this.items.push({ label: item.name, value: item.identity });
                }
            });
        });
    }

    ngAfterViewInit(): void {
        this.getLocation();
    }

    ngOnInit(): void {}

    getLocation(): void {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    if (position) {
                        this.lat = position.coords.latitude;
                        this.lng = position.coords.longitude;
                        let center = this.projectionTransform.fromLatLng(this.lat, this.lng);
                        let marker = {
                            id: this.scadaMarkerId,
                            Latitude: this.lat,
                            Longitude: this.lng,
                        };
                        let markerCreated = this.createMarker(marker, 1);
                        this.markerDictionary.set(this.scadaMarkerId, markerCreated);

                        this.changeDetectorRef.detectChanges();
                        this.map.setCenter(center, 14, true);
                    }
                },
                (error) => console.log(error)
            );
        } else {
            throw new Error("Geolocation is not supported by this browser.");
        }
    }

    onItemSelect(item): void {
        this.selectedItem = this.resourceCacheService.get(item.value);
    }

    changeMarkerLocation(): void {
        this.scadaUpdateService.updateLocation(this.selectedItem, this.lat, this.lng).subscribe(
            (response) => {
                this.messageService.add({
                    severity: "success",
                    summary: this.i18n.instant(ToastTypes.success),
                    detail: this.i18n.instant("locationUpdated")
                });
            },
            (error) => {
                this.messageService.add({
                    severity: "error",
                    summary: this.i18n.instant(ToastTypes.error),
                    detail: this.i18n.instant("locationUpdateError")
                });
            }
        );
    }

    createMarker(sensor: GpsMarker, index: number): CymbiotMarker {
        let marker = new CymbiotMarker({
            x: sensor.Latitude,
            y: sensor.Longitude,
        });
        marker.id = sensor.id;
        marker.generateTemplateRef = (markers: Marker[]) => {
            if (!marker.component) {
                let factory = this.componentFactoryResolver.resolveComponentFactory(GpsMarkerComponent);
                let componentRef: ComponentRef<GpsMarkerComponent> = this.customMarkersFactory.createComponent(factory);
                componentRef.changeDetectorRef.detectChanges();
                marker.component = componentRef.instance;
            }
            return marker.component.getElementRef();
        };
        marker.generateTemplateRef([]);
        return marker;
    }

    refreshLocation(): void {
        this.markerDictionary.clear(); // Clears the Map instead of replacing it with an object

        this.getLocation();
    }
}
