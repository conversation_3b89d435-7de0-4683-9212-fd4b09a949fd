import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy, ViewChild, ElementRef } from '@angular/core';
import { GaugeWidget } from 'app/layout/customer-dashboard/models/gauge-widget';
import { Subscription } from 'rxjs';
import { DataService } from 'app/shared/services/data.service';
import * as moment from 'moment';
import { ApiCfg } from 'app/interfaces/api-cfg.interface';
import { take } from 'rxjs/operators';
import { ReportsQueryParams } from 'app/layout/customer-dashboard/models/reports-query-params.interface';
import { EventService, EventType } from 'app/shared/services/event.service';
import { GaugeDisplayResults } from 'app/layout/customer-dashboard/enums/gauge-widget-display-results.enum';
import { PartialReportTriggerData } from 'app/shared/modules/data-layer/models/partial-report-trigger-data';
import { ReportCollectOption } from 'app/shared/modules/data-layer/enum/reports/report-collect-options.enum';
import {ApiCommands,GuidUtils} from "app/shared/enum/enum";


@Component({
  selector: 'app-gauge-widget',
  templateUrl: './gauge-widget.component.html',
  styleUrls: ['./gauge-widget.component.scss']
})
export class GaugeWidgetComponent implements OnInit, OnDestroy {
  data: {index: number, widgetData: GaugeWidget};
  subscriptions: Subscription[] = [];
  value: number = null;
  gaugeClass: string;
  setLabel: (value:number) => string;
  isGaugeVisible: boolean = true;
  currentDate: Date = new Date();
  queryParams: ReportsQueryParams;
  gaugeAspectRatio: number;
  @ViewChild('gaugeWrapper', {static: true}) gaugeWrapper: ElementRef;
  @ViewChild('gaugeElement', {static: true}) gaugeElement: ElementRef;
  requestTimeout: ReturnType<typeof setTimeout> = null;
  getReportLatestOnFirstRequest: boolean = false;
  constructor(private dataService: DataService, private eventService: EventService) {
    this.setLabel = function(value:number): string {
       return `${Math.round(value)} / ${this['max']}`;
    };
  }

  ngOnInit(): void {
    this.data.widgetData = new GaugeWidget(this.data.widgetData);
    let sidebarResizeSubscription = this.eventService.observe(EventType.SidebarStateChanged).subscribe(() => { return this.calculateAspectRatio();});
    this.subscriptions.push(sidebarResizeSubscription);
    this.calculateAspectRatio();
    this.startDataInterval();
  }

  ngOnDestroy(): void{
    this.subscriptions.forEach(subscription => { return subscription.unsubscribe();});
    clearTimeout(this.requestTimeout);
  }

  startDataInterval(): void{
    if(this.data.widgetData.selectedResourceId && this.data.widgetData.selectedTrigger && this.data.widgetData.minValue !== null && this.data.widgetData.maxValue !== null && this.data.widgetData.displayResultsMethod !== null){
      this.getReportsData();
    }
  }

  getThresholdActions(value: number,  callback: (data: { class: string, event: string }) => void ): void {
    let action: {class: string, event: string} = {class: 'default', event: null};
    this.data.widgetData.thresholdPoints.forEach(point => {
      if(value >= point.start && value <= point.end){
        action.class =  point.class;
        action.event = point.eventName;
      }
    });
    callback(action);
  }

  sendThresholdEvent(): void {
    //TODO
    // call api method when ready
    console.log("triggerType: ", this.data.widgetData.selectedTrigger, "reportQuery: ", this.queryParams);
  }

  getReportsData(): void{
    let dateFrom = moment(this.currentDate).startOf('day');
    let dateTo = moment(this.currentDate).endOf('day');
    this.queryParams = {
      DateLastUnit: 0,
      DateRangeType: this.getReportLatestOnFirstRequest ? "1" : "3",
      From: dateFrom.toISOString(),
      To: dateTo.toISOString(),
      Identity: GuidUtils.newGuid(),
      Name: "",
      Resource: this.data.widgetData.selectedResourceId,
      ResourceGroup: this.data.widgetData.selectedGroupId,
      TextFilter: "",
      Transformation: "",
      TriggerTypes: [this.data.widgetData.selectedTrigger],
      Type: "1",
      Value: 10, //last 10 seconds
      selectedReport: "addNew",
      CollectOption: ReportCollectOption.Collect
    };

    let queryId = Math.floor(Math.random() * 9999);
    let cfg: ApiCfg = {
      type: ApiCommands.GenerateReport,
      data: this.queryParams,
      disableBI: true,
      disableErrorHandler: true,
      urlParams: queryId
    };

    this.dataService.api(cfg).pipe(take(1)).subscribe(res => {
        //TODO
        // map triggers by type and show specific data now only counters work
      this.getReportLatestOnFirstRequest = true;
      this.requestTimeout = setTimeout(() => {
        this.getReportsData();
      }, 10000);
      this.value = this.returnGaugeValue(res.Data.map((el) => { return new PartialReportTriggerData(JSON.parse(el));}));
      this.getThresholdActions(this.value, (data: { class: string, event: string }) => {
        this.gaugeClass = data.class;
        if (data.event) {
          this.sendThresholdEvent();
        }
      });
    });
  }

  private calculateAspectRatio(){
    this.gaugeAspectRatio = parseFloat((this.gaugeWrapper.nativeElement.offsetWidth / this.gaugeWrapper.nativeElement.offsetHeight).toFixed(2));
  }

  returnGaugeValue(data: PartialReportTriggerData[]): number {
    let value: number;
    switch(this.data.widgetData.displayResultsMethod){
      case GaugeDisplayResults.countResults:
        value = data.length;
        break;
      case GaugeDisplayResults.resultValue:
        value = this.returnValueByTimestamp(data) || this.value;
        break;
      default:
        console.error("No values for gauge display type: ", this.data.widgetData.displayResultsMethod);
        break;
    }
    return value;
  }

  returnValueByTimestamp(data: PartialReportTriggerData[]): number {
    if(data.length === 0) {
      return null;
    }
    let mostRecentTimeStamp = this.returnMostRecentTimeStamp(data);
    if(!mostRecentTimeStamp) {
      return null;
    }
    return data.find(el =>
      {return new Date(el.time_stamp_formatted).getTime() === mostRecentTimeStamp.getTime() && el.resource_id === this.data.widgetData.selectedResourceId;}).field_value;
  }

  returnMostRecentTimeStamp(data: PartialReportTriggerData[]) : Date {
    return new Date(Math.max.apply(null, data.map(el => {
      return new Date(el.time_stamp_formatted);
    })));
  }

}
