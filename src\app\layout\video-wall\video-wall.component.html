<div class="row main-videowall">
    <sub-header class="videowall-container" content [PrimarySelectOptions]="layouts" [Type]="type" [pageTitle]="'cameras'"
        [ngClass]="isSidearOpen ? 'content-body-cameras' : 'w-100'">
        <div header class="menu-items-wrapper">

            <div class="top-half">
                <ul class="menu-options">
                    <li class="item vms-layout" (click)="openLayouts($event)" pTooltip="{{ 'layouts' | translate }}"  tooltipPosition="bottom"></li>            
                    <li class="dots vms-options" (click)="openVCAoptions($event)"  pTooltip="{{ 'settings' | translate }}" tooltipPosition="bottom"></li>
                </ul>
            </div>

            <div class="bottom-half">
                <ul class="menu-options">
                    <li class="fullscreen vms-fullscreen" pTooltip="{{ 'fullScreen' | translate }}" tooltipPosition="bottom" (click)="toggleFullscreen()">
                        <app-fullscreen [element]="playersWrapper" #fullScreen></app-fullscreen>
                    </li>
                    <li class="vms-close" (click)="closeAllPlayers()" pTooltip="{{ 'closeAll' | translate }}" tooltipPosition="bottom"></li>
                </ul>
            </div>
        </div>

        <ng-container body [ngSwitch]="playerCountNew">
            <div #playersWrapper class="players-wrapper"
                [ngClass]="{ 'timeline-open': isTimelineOpen, 'no-ptz': !showPTZControls }">
                <ng-container *ngSwitchCase="1">
                    <div class="row">
                        <div class="w-100 p-1" #container></div>
                    </div>
                </ng-container>
                <ng-container *ngSwitchCase="4">
                    <div class="row">
                        <div class="w-50 p-1" #container></div>
                        <div class="w-50 p-1" #container></div>
                    </div>
                    <div class="row">
                        <div class="w-50 p-1" #container></div>
                        <div class="w-50 p-1" #container></div>
                    </div>
                </ng-container>

                <ng-container *ngSwitchCase="6">
                    <div class="row">
                        <div class="w-33 p-1" #container></div>
                        <div class="w-33 p-1" #container></div>
                        <div class="w-33 p-1" #container></div>
                    </div>
                    <div class="row">
                        <div class="w-33 p-1" #container></div>
                        <div class="w-33 p-1" #container></div>
                        <div class="w-33 p-1" #container></div>
                    </div>
                </ng-container>
                <ng-container *ngSwitchCase="9">
                    <div class="row">
                        <div class="w-33 p-1" #container></div>
                        <div class="w-33 p-1" #container></div>
                        <div class="w-33 p-1" #container></div>
                    </div>
                    <div class="row">
                        <div class="w-33 p-1" #container></div>
                        <div class="w-33 p-1" #container></div>
                        <div class="w-33 p-1" #container></div>
                    </div>
                    <div class="row ">
                        <div class="w-33 p-1" #container></div>
                        <div class="w-33 p-1" #container></div>
                        <div class="w-33 p-1" #container></div>
                    </div>
                </ng-container>
                <ng-container *ngSwitchCase="10">
                    <div class="row">
                        <div class="w-50 p-1" #container></div>
                        <div class="w-50 p-1" #container></div>
                    </div>
                    <div class="row">
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                    </div>
                    <div class="row">
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                    </div>
                </ng-container>

                <ng-container *ngSwitchCase="12">
                    <div class="row">
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>

                    </div>
                    <div class="row">
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>

                    </div>
                    <div class="row">
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>

                    </div>

                </ng-container>

                <ng-container *ngSwitchCase="16">
                    <div class="row">
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                    </div>
                    <div class="row">
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                    </div>
                    <div class="row">
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                    </div>
                    <div class="row">
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                    </div>
                </ng-container>



                <ng-container *ngSwitchCase="8">
                    <div class="row">
                        <div class="w-80 p-1" #container></div>
                        <div class="d-flex flex-column w-20">
                            <div #container class="p-1"></div>
                            <div #container class="p-1"></div>
                            <div #container class="p-1"></div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                        <div class="w-25 p-1" #container></div>
                    </div>
                </ng-container>





                <ng-container *ngSwitchCase="11">
                    <div class="row">
                        <div class="w-50 p-1" #container></div>
                        <div class="d-flex flex-column w-25">
                            <div #container class="p-1"></div>
                            <div #container class="p-1"></div>
                            <div #container class="p-1"></div>
                        </div>
                        <div class="d-flex flex-column w-25">
                            <div #container class="p-1"></div>
                            <div #container class="p-1"></div>
                        </div>
                    </div>
                </ng-container>


            </div>
        </ng-container>

        <ng-container appTimeline>
            <div #timelineWrapper [hidden]="!isTimelineOpen" class="timeline-wrapper">
                <app-timeline *ngIf="isTimelineOpen" class="timeline" #timeline></app-timeline>
            </div>
        </ng-container>
    </sub-header>
    <p-confirmDialog appendTo="body" acceptButtonStyleClass="btn-primary"
        rejectButtonStyleClass="btn-secondary"></p-confirmDialog>


    <p-overlayPanel #settingOverlayPanel [appendTo]="'body'" [dismissable]="true">
        <div class="custom-overlay">
            <div class="custom-overlay-header">
            <h3>{{ "settings" | translate }}</h3>
                <button type="button" (click)="settingOverlayPanel.hide()" class="custom-close-button">
                &#x2715;
            </button>
            </div>

            <div class="custom-overlay-content">
                <div class="settings-content">
                <div class="d-flex justify-content-left mb-3">
                    <span>{{ "vcaOptions" | translate }}</span>
                </div>
                <p-checkbox label="{{ 'showPTZControls' | translate }}" binary="true" [(ngModel)]="showPTZControls">
                </p-checkbox>
            </div> 
            </div>
        
            <div class="custom-overlay-footer">
                <button type="button" (click)="onConfirm()" class="btn btn-primary">{{ 'ok' | translate }}</button>
                <button type="button" (click)="settingOverlayPanel.hide()" class="btn btn-secondary">{{ 'cancel' | translate }}</button>
            </div>
        </div>
    </p-overlayPanel>


    <p-overlayPanel #layoutOverlayPanel [appendTo]="'body'" [dismissable]="true">
        <div class="custom-overlay">
            <div class="custom-overlay-header">
            <h3>{{ "layouts" | translate }}</h3>
                <button type="button" (click)="layoutOverlayPanel.hide()" class="custom-close-button">
                &#x2715;
            </button>
            </div>
            <div class="custom-overlay-content">
                <ul class="layouts">
                    <li *ngFor="let option of layouts" class="layout-item">
                        <span class="menu-icon {{ option.label }}" pTooltip="{{ option.label  | translate }}"
                        tooltipPosition="bottom" [ngClass]="{
                                active: option.value == playerCountNew}" (click)="onLayoutChanged(option)"></span>
                    </li>
                </ul>
            </div>
        </div>
    </p-overlayPanel>

    <app-modal #camerasModal [title]="'cameras'" styleClass="modal-content settings-modal" [position]="position">
        <ng-container ngProjectAs="contentModal">
            <div class="settings-wrapper">
                <app-add-video-channel 
                    class="" 
                    [playerId]="selectedPlayerId"
                    [videoWallGuid]="videoWallGuid"
                    [videoWalllayouts]="videoWalllayouts" 
                    [showActions]="true"
                    [sidebarAutoClose]="false"
                    [monitorCount]="monitorCount"
                    side
                    (playerIndexChanged)="getMonitorIndex($event)" 
                    (viewLayout)="viewLayout($event)"
                    (onChannelSelect)="onChannelSelect($event)">
                </app-add-video-channel>
            </div>
        </ng-container>
    </app-modal>
</div>