<div class="responsive-layout-container">
    <!-- Sidebar -->
    <div class="sidebar-container" [ngClass]="{'expanded': sidebarExpanded}">
        <div class="sidebar-header">
            <div class="header-content">
                <div class="logo-container">
                    <img
                        src="assets/public/assets/iot-total-logo.png"
                        loading="lazy"
                        class="dashboard-iot-total-logo"
                        alt="IoT Total Logo"
                        title="IoT Total"
                    />
                </div>
            </div>
        </div>

        <!-- Sidebar content with scroll -->
        <div class="sidebar-content">
            <app-sidebar></app-sidebar>
        </div>
    </div>

    <!-- Main content -->
    <div class="content-container">
        <!-- User menu without background -->
        <div class="user-menu-container">
            <div class="user-menu">
                <div class="user-info">
                    <span class="user-greeting">{{ userName }}</span>
                </div>
                <button
                    type="button"
                    class="logout-button-container"
                    (click)="logout()"
                    title="Logout"
                    aria-label="Logout">
                    <img
                        src="assets/public/assets/LogOut.svg"
                        class="logout-button"
                        alt="LogOut"
                    />
                    <span class="sr-only">Logout</span>
                </button>
            </div>
        </div>

        <!-- Content wrapper with scroll -->
        <div class="content-wrapper">
            <router-outlet></router-outlet>
        </div>

        <!-- Footer container for equal spacing -->
        <div class="footer-container"></div>
    </div>
</div>
