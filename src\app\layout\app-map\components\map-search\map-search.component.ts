import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { DefaultMapEditorComponent } from '../default-map-editor/default-map-editor.component';
import { FormGroup, FormBuilder, FormControl, Validators, AbstractControl } from '@angular/forms';
import { MapSearchProperties } from '../../models/map-search-properties.interface';
import { MapState } from '../../models/map-state.interface';
import { MapObject } from '../../models/map-object.interface';
import { SelectItem } from 'primeng/api';
import { MapSearchOperand } from '../../enums/map-search-operand.enum';

@Component({
  selector: 'app-map-search',
  templateUrl: './map-search.component.html',
  styleUrls: ['./map-search.component.scss']
})
export class MapSearchComponent extends DefaultMapEditorComponent implements OnInit {
  public searchForm: FormGroup = null;
  data: {
    state: MapState,
    selectedMap: MapObject,
    mapSearchProperties: MapSearchProperties
  }
  @Output('onSearch') onSearch: EventEmitter<MapSearchProperties> = new EventEmitter();
  formFilters: {fieldName: SelectItem[], operand: SelectItem[]} = {
    fieldName: [
      {label: 'name', value: 'name'},
      {label: 'status', value: 'status'},
      {label: 'resourceGroup', value: 'resourceGroup'},
      {label: 'extraData', value: 'extraData'}
    ],
    operand: [
      {label: 'operand.equals', value: MapSearchOperand.equals},
      {label: 'operand.notEquals', value: MapSearchOperand.notEquals},
      {label: 'operand.contains', value: MapSearchOperand.contains},
      {label: 'operand.notContains', value: MapSearchOperand.notContains}
    ]
  }
  constructor(
    private formBuilder: FormBuilder
  ) {
    super();
  }

  ngOnInit(): void {
    this.generateForm(this.data.mapSearchProperties);
  }

  private generateForm(data: MapSearchProperties):void {
    this.searchForm = this.formBuilder.group({
      fieldName: new FormControl(data ? data.fieldName : null, Validators.required),
      operand: new FormControl(data ? data.operand : null, Validators.required),
      freeText: new FormControl(data ? data.freeText : null, Validators.required)
    });
  }

  onSubmit(): void{
    this.onSearch.next(this.searchForm.value);
  }

  onReset(): void {
    this.searchForm.reset();
    this.onSearch.next(this.searchForm.value);
  }

  get fieldName(): AbstractControl { return this.searchForm.get('fieldName'); }
  get operand(): AbstractControl { return this.searchForm.get('operand'); }
  get freeText(): AbstractControl { return this.searchForm.get('freeText'); }

}
