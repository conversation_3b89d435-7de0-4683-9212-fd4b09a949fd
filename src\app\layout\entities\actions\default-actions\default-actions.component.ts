import { Component, Input, Output, EventEmitter } from '@angular/core';
import { TableCell, TableActionsComponent } from 'app/shared/components/ng-turbo-table/models/table.models';
import { EntitiesActionType } from '../../enums/entitiesActionType.enum';

@Component({
  selector: 'app-default-actions',
  templateUrl: './default-actions.component.html',
  styleUrls: ['./default-actions.component.scss']
})
export class DefaultActionsComponent extends TableActionsComponent {

  @Input('row') row: { [columnField: string]: TableCell | string };
  @Output('selectedItem') selectedItem: EventEmitter<string> = new EventEmitter();
  enititesActionType = EntitiesActionType;

  constructor() {
    super();
  }

  rowItem(type: EntitiesActionType, item: { [columnField: string]: TableCell | string }) :void{
    if( !item || !item.eventId){
      console.error("Event id is not defined");
      return;
    }
    switch (type) {
      case EntitiesActionType.addEntity:
        this.selectedItem.next(item.eventId.toString());
        break;
      default:
        console.error('Type not found!');
        break;
    }
  }

}
