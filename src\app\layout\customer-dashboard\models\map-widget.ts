import { Widget } from "./widget";
import { DashboardWidget } from "./dashboard-widget.interface";
import { Guid } from "app/shared/enum/guid";
import { WidgetSize } from "../enums/widget-size.enum";
import { MapLayer } from 'app/shared/modules/data-layer/models/map-layer';
import {DefaultResources} from "app/shared/enum/enum";


export class MapWidget extends Widget {
    selectedMapId: string;
    mapLayers: MapLayer[];

    constructor(obj?: DashboardWidget){
        super(obj);
        if(!this.selectedMapId){
            this.selectedMapId = DefaultResources.DefaultMap;
        }
    }

    getWidgetSize(): WidgetSize[]{
        return [WidgetSize.big, WidgetSize.xBig, WidgetSize.xXBig]
    }

}
