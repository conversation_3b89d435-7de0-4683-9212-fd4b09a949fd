<div class="dashboard-rectangle-16">
  <p-tabView class="notification-tab" >

    <p-tabPanel header="{{ 'alarms' | translate }}">
      <div class="list-header" *ngIf="alarms && alarms.length > 0">
        <div class="header-item">{{ 'alertsAlarms.plateNumber' | translate }}</div>
        <div class="header-item">{{ 'alertsAlarms.eventName' | translate }}</div>
        <div class="header-item">{{ 'TimeStamp' | translate }}</div>
      </div>

      <div class="scrollable-container" (scroll)="onScroll($event, 'alarms')">
        <ul class="notification-list" *ngIf="alarms && alarms.length > 0">
          <li *ngFor="let item of alarms" class="notification-item" (click)="onRowClick(item)">
            <div class="item-content">
              <div class="item-cell">
                <div class="image-inline">
                  <img src="assets/public/assets/alarma-camera-car.png" alt="car alarm"/>
                  <p *ngIf="item.entityId" class="entity-id">{{ item.entityId }}</p>
                </div>
              </div>
              <div class="item-cell">{{ item.description }}</div>
              <div class="item-cell">{{ item.timeStamp | iotTimestamp }}</div>
            </div>
          </li>
        </ul>

        <div *ngIf="!alarms || alarms.length === 0" class="no-data">
          {{ 'alertsAlarms.noData' | translate }}
        </div>

        <!-- Loading indicator for initial load -->
        <div *ngIf="loading && alarms.length === 0" class="loading-indicator">
          <div class="spinner"></div>
        </div>

        <!-- Bottom loading indicator for infinite scroll -->
        <div *ngIf="loading && alarms.length > 0" class="bottom-loading-indicator">
          <div class="spinner"></div>
        </div>
      </div>
    </p-tabPanel>

    <p-tabPanel header="{{ 'alert' | translate }}">
      <div class="list-header" *ngIf="alerts && alerts.length > 0">
        <div class="header-item">{{ 'alertsAlarms.plateNumber' | translate }}</div>
        <div class="header-item">{{ 'alertsAlarms.eventName' | translate }}</div>
        <div class="header-item">{{ 'TimeStamp' | translate }}</div>
      </div>

      <div class="scrollable-container" (scroll)="onScroll($event, 'alerts')">
        <ul class="notification-list" *ngIf="alerts && alerts.length > 0">
          <li *ngFor="let item of alerts" class="notification-item" (click)="onRowClick(item)">
            <div class="item-content">
              <div class="item-cell">
                <div class="image-inline">
                  <img src="assets/public/assets/alarma-camera-car.png" alt="car alarm"/>
                  <p *ngIf="item.entityId" class="entity-id">{{ item.entityId }}</p>
                </div>
              </div>
              <div class="item-cell">{{ item.description }}</div>
              <div class="item-cell">{{ item.timeStamp | iotTimestamp }}</div>
            </div>
          </li>
        </ul>

        <div *ngIf="!alerts || alerts.length === 0" class="no-data">
          {{ 'alertsAlarms.noData' | translate }}
        </div>

        <!-- Loading indicator for initial load -->
        <div *ngIf="alertLoading && alerts.length === 0" class="loading-indicator">
          <div class="spinner"></div>
        </div>

        <!-- Bottom loading indicator for infinite scroll -->
        <div *ngIf="alertLoading && alerts.length > 0" class="bottom-loading-indicator">
          <div class="spinner"></div>
        </div>
      </div>
    </p-tabPanel>
  </p-tabView>
</div>
