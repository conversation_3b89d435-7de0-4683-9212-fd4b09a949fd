import { Component, OnInit } from '@angular/core';
import { WeatherInfo } from '../../../shared/modules/data-layer/models/weather-info';
import { WeatherInfoService } from '../../../services/weather-info/weather-info.service';

@Component({
  selector: 'app-weather-info',
  templateUrl: './weather-info.component.html',
  styleUrls: ['./weather-info.component.scss']
})
export class WeatherInfoComponent implements OnInit {
  weatherInfo?: WeatherInfo;
  loading = false;
  hasError = false;
  constructor(private weatherService: WeatherInfoService) {}

  ngOnInit(): void {
    this.loading = true;
    this.weatherService.getWeatherInfo().subscribe({
      next: (data) => {
        this.weatherInfo = data;
        this.loading = false;
      },
      error: (err) => {
        console.log('error', err);
        this.hasError = true;
        this.loading = false;
      } 
    });
  }
}
