<a href="javascript:void(0)" title="{{ selectedMap?.item ? selectedMap?.item?.name : ('map.pleaseSelectMap' | translate) }}" (click)="startNavigationPanel($event)">
    <span class="selected">{{ selectedMap?.item?.name === 'defaultMap' ? ('customerDashboard.defaultMap' | translate) : selectedMap?.item?.name }}</span>
    <span class="fa fa-chevron-down" app-padding="5px" direction="left" *ngIf="mapsModelStore && mapsModelStore.size > 1"></span>
</a>
<p-overlayPanel appendTo="body" #mapsOp>
    <ul class="maps-dropdown">
        <li *ngFor="let map of mapsModelStore | keyvalue">
            <a href="javascript:void(0)" title="{{ map.value.name }}" *ngIf="selectedMap.id !== map.key" (click)="selectItem(map.value)">
                <span>{{map.value.name === 'defaultMap' ? ('customerDashboard.defaultMap' | translate) : map.value.name }}</span>
            </a>
        </li>
    </ul>
</p-overlayPanel>
