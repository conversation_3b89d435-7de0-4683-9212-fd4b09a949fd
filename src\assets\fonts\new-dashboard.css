@import url("https://fonts.googleapis.com/css2?family=Rounded Mplus 1c Bold:wght@400;700");
@import url("https://api.fontshare.com/css?f[]=rounded mplus 1c bold@400,700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Rounded Mplus 1c:wght@400");
@import url("https://api.fontshare.com/css?f[]=rounded mplus 1c@400&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;700");
@import url("https://api.fontshare.com/css?f[]=inter@400,700&display=swap");

/*
  1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
  2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
  */

*,
::before,
::after {
    box-sizing: border-box; /* 1 */
    border-width: 0; /* 2 */
    border-style: solid; /* 2 */
    border-color: theme("borderColor.DEFAULT", currentColor); /* 2 */
}

::before,
::after {
    --tw-content: "";
}

/*
  1. Use a consistent sensible line-height in all browsers.
  2. Prevent adjustments of font size after orientation changes in iOS.
  3. Use a more readable tab size.
  4. Use the user's configured sans font-family by default.
  5. Use the user's configured sans font-feature-settings by default.
  6. Use the user's configured sans font-variation-settings by default.
  7. Disable tap highlights on iOS
  */

html,
:host {
    line-height: 1.5; /* 1 */
    -webkit-text-size-adjust: 100%; /* 2 */
    -moz-tab-size: 4; /* 3 */
    tab-size: 4; /* 3 */
    font-family: theme(
        "fontFamily.sans",
        ui-sans-serif,
        system-ui,
        sans-serif,
        "Apple Color Emoji",
        "Segoe UI Emoji",
        "Segoe UI Symbol",
        "Noto Color Emoji"
    ); /* 4 */
    font-feature-settings: theme(
        "fontFamily.sans[1].fontFeatureSettings",
        normal
    ); /* 5 */
    font-variation-settings: theme(
        "fontFamily.sans[1].fontVariationSettings",
        normal
    ); /* 6 */
    -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
  1. Remove the margin in all browsers.
  2. Inherit line-height from html so users can set them as a class directly on the html element.
  */

body {
    margin: 0; /* 1 */
    line-height: inherit; /* 2 */
    overflow: scroll;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden] {
    display: none;
}

p,
ol,
ul {
    margin: 0px;
}

button {
    padding: 0px;
}

ol,
ul {
    padding-inline-start: 1.5em;
    list-style-type: none;
}

input {
    background-image: none;
    background-color: transparent;
}


h2, .h2 {
    font-size: 24px;
}

.common-39,
.dashboard-rectangle-9,
.dashboard-rectangle-15 {
    position: absolute;
    top: 0px;
}

.common-38,
.dashboard-rectangle,
.dashboard-maps,
.dashboard-t-bg-camera-1mingcute-alert-fill,
.dashboard-t-bg-camera-3mingcute-alert-fill,
.dashboard-t-bg-camera-22mingcute-alert-fill {
    background-position: center;
    background-size: cover;
    width: 100%;
}
.dashboard-maps{
    height: 100%;
    padding: 0px !important;
}
.common-37,
.dashboard-rectangle-3,
.dashboard-rectangle-4,
.dashboard-rectangle-5,
.dashboard-rectangle-6,
.dashboard-rectangle-11,
.dashboard-rectangle-12,
.dashboard-rectangle-13 {
    border-style: solid;
    border-color: gray;
}

.common-17,
.dashboard-t-video-wall,
.dashboard-t-inventory,
.dashboard-t-inventory-1,
.dashboard-t-reports,
.dashboard-t-lpr,
.dashboard-t-procedures,
.dashboard-t-general-detection,
.dashboard-t-file-manager,
.dashboard-t-background-notifications,
.dashboard-rectangle,
.dashboard-t-rectangle-1 {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
}

.common-18,
.dashboard-t-rectangle,
.dashboard-tttt-bg-camera-3mingcute-alert-fill-camera {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.common-19,
.dashboard-user-navigation-control,
.dashboard-operations-center,
.dashboard-ttt-camera-4camera-27ttttbg-camera-3mingcute-alert-fill-camera-28tttcamera-5ttcamera-2camera-6tcamera-10ttttcamera-25ttbg-camera-22mingcute-alert-fill-tcamera-20tcamera-17tcamera,
.dashboard-ttt-camera-25ttbg-camera-22mingcute-alert-fill-tcamera-20tcamera-17tcamera {
    display: flex;
    align-items: flex-start;
}

.common-20,
.dashboard-t-ooui-next-rtl-tooui-next-ltr-rectangle,
.dashboard-health-status-monitor,
.dashboard-t-card-title-tedit {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    flex-wrap: wrap;
}

.common-21,
.dashboard-t-camere-tedit-ented-to-view,
.dashboard-connection-status,
.alarm-summary {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.common-22,
.dashboard-t-iot-total-logo,
.dashboard-t-majesticons-chevron-up,
.dashboard-t-dispatch,
.dashboard-t-majesticons-chevron-up-1,
.dashboard-management-panel,
.dashboard-t-ooui-next-ltr-rectangle,
.dashboard-rectangle-1,
.dashboard-t-material-symbols-speed-camera,
.dashboard-t-pajamas-status-health,
.dashboard-t-pajamas-status-health-1,
.dashboard-t-pajamas-status-health-2,
.dashboard-t-pajamas-status-health-3,
.dashboard-t-vector,
.dashboard-t-group,
.dashboard-tttt-rectangle-6alarme-neconforme-trectangle-6alarme-nesolutionate-trectangle-6alarme-verificate,
.dashboard-t-bxs-bell-off,
.dashboard-t-t281ttoday-13112024150922pm,
.dashboard-maps,
.dashboard-ttt-camera-5ttcamera-2camera-6tcamera-10ttttcamera-25ttbg-camera-22mingcute-alert-fill-tcamera-20tcamera-17tcamera,
.dashboard-t-camera-1,
.dashboard-t-camera-3,
.dashboard-tt-bg-camera-22mingcute-alert-fill,
.dashboard-t-camera-6,
.dashboard-t-edit {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.common-23,
.dashboard-health-status-tracker,
.dashboard-operational-status,
.dashboard-map-layout {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.common-24,
.dashboard-t-bg-camera-3mingcute-alert-fill,
.dashboard-tt-camera-5ttcamera-2camera-6tcamera-10ttttcamera-25ttbg-camera-22mingcute-alert-fill-tcamera-20tcamera-17tcamera,
.dashboard-t-bg-camera-22mingcute-alert-fill {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.common-25,
.dashboard-header-navigation,
.dashboard-user-menu,
.dashboard-tt-ellipse-7ellipse-6ellipse-5ellipse-1ellipse-2ellipse-3ellipse,
.dashboard-tt-group-12749ttttrectangle-6alarme-neconforme-trectangle-6alarme-nesolutionate-trectangle-6alarme-verificate-ttbxs-bell-off-tt281ttoday-13112024150922pm {
    display: flex;
    justify-content: center;
    align-items: flex-end;
}

.common-26,
.dashboard-user-profile-menu,
.dashboard-t-ellipse-7ellipse-6ellipse-5ellipse-1ellipse-2ellipse-3ellipse,
.dashboard-t-edit-ented-to-view,
.dashboard-status-health-indicator,
.dashboard-tt-pajamas-status-health-offline,
.dashboard-tt-pajamas-status-health-error-1,
.dashboard-rectangle-3,
.dashboard-rectangle-4,
.dashboard-rectangle-5,
.dashboard-rectangle-6,
.dashboard-tt-vector-actualizare-automata-in,
.dashboard-section-layout,
.dashboard-t-uil-edit-ant-design-export-outlined,
.dashboard-unresolved-alarm-edit,
.dashboard-ttt-rectangle-6alarme-neconforme,
.dashboard-t-rectangle-6alarme-neconforme,
.dashboard-rectangle-11,
.dashboard-t-rectangle-6alarme-nesolutionate,
.dashboard-t-rectangle-6alarme-verificate,
.dashboard-rectangle-13,
.dashboard-tt-camera-4camera-27ttttbg-camera-3mingcute-alert-fill-camera {
    display: flex;
    justify-content: center;
    align-items: center;
}

.common-27,
.dashboard-t-camera-2,
.dashboard-tttt-camera-25ttbg-camera-22mingcute-alert-fill-tcamera-20tcamera-17tcamera {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
}

.common-28,
.dashboard-t-camera-4,
.dashboard-t-camera-5 {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.common-29,
.dashboard-system-overview,
.dashboard-system-structure-map {
    display: flex;
    justify-content: center;
}

.common-30,
.dashboard-background-selected,
.dashboard-background-selected-1,
.dashboard-background-notifications {
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.common-31,
.dashboard-rectangle-8,
.alarm-summary-72h-actions {
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.common-32,
.dashboard-dashboard,
.dashboard-t-camera-4camera,
.dashboard-ttt-bg-camera-3mingcute-alert-fill-camera {
    display: flex;
    flex-direction: column;
}

.common-33,
.dashboard-user-control-panel,
.dashboard-tt-pajamas-status-health-online,
.dashboard-tt-pajamas-status-health-error,
.dashboard-ttt-group-12749ttttrectangle-6alarme-neconforme-trectangle-6alarme-nesolutionate-trectangle-6alarme-verificate-ttbxs-bell-off-tt281ttoday-13112024150922pm,
.dashboard-tt-rectangle-6alarme-neconforme,
.dashboard-rectangle-12,
.dashboard-t-rectangle-2,
.dashboard-t-camera {
    display: flex;
    align-items: center;
}

.common-34,
.dashboard-tt-bxs-bell-off-tt281ttoday-13112024150922pm,
.dashboard-tt-camera-25ttbg-camera-22mingcute-alert-fill {
    display: flex;
    align-items: flex-start;
}

.common-35,
.dashboard-rectangle-2,
.dashboard-layout-grid {
    flex-direction: column;
    align-items: center;
}

.common-36,
.dashboard-rectangle-10,
.dashboard-tt-bg-camera-3mingcute-alert-fill,
.dashboard-rectangle-16 {
    flex-direction: column;
    align-items: flex-start;
}

.common-16,
.dashboard-dashboard-1 {
    font-size: 24px;
}

.common-1,
.dashboard-layout-grid,
.dashboard-t-rectangle-7rectangle {
    display: flex;
    width: 33px;
}

.common-2,
.dashboard-t-bg-camera-1mingcute-alert-fill,
.dashboard-tt-bg-camera-3mingcute-alert-fill {
    display: flex;
    width: 25px;
}

.common-3,
.dashboard-background-selected,
.dashboard-background-selected-1,
.dashboard-background-notifications {
    display: flex;
}

.common-4,
.dashboard-rectangle-2,
.dashboard-rectangle-8,
.dashboard-rectangle-10,
.dashboard-rectangle-16 {
    display: flex;
    background-color: #f2f6f3;
}

.common-5,
.alarm-summary-72h-actions,
.dashboard-component-4,
.dashboard-component-8 {
    display: flex;
    color: darkslategray;
}

.common-6,
.dashboard-majesticons-chevron-up,
.dashboard-majesticons-chevron-up-1,
.dashboard-lucide-expand,
.dashboard-cbi-camera-car {
    width: 24px;
    height: 24px;
}

.common-7,
.dashboard-tabler-settings-background,
.dashboard-settings,
.dashboard-log-out,
.dashboard-home,
.dashboard-cameras,
.dashboard-video-wall,
.dashboard-inventory,
.dashboard-inventory-1,
.dashboard-reports,
.dashboard-lpr,
.dashboard-procedures,
.dashboard-general-detection,
.dashboard-file-manager,
.dashboard-notifications,
.dashboard-material-symbols-speed-camera,
.dashboard-bxs-bell-off {
    width: 48px;
    height: 48px;
}

.common-8,
.dashboard-ooui-next-rtl,
.dashboard-ooui-next-ltr {
    width: 20px;
    height: 46px;
}

.common-9,
.dashboard-ellipse,
.dashboard-ellipse-1,
.dashboard-ellipse-2,
.dashboard-ellipse-3,
.dashboard-ellipse-4,
.dashboard-ellipse-5,
.dashboard-ellipse-6 {
    width: 6px;
    height: 6px;
}

.common-10,
.dashboard-edit,
.dashboard-ented-to-view,
.dashboard-uil-edit,
.dashboard-ant-design-export-outlined,
.dashboard-uil-edit-1,
.dashboard-ant-design-export-outlined-1,
.dashboard-edit-1 {
    width: 18px;
    height: 18px;
}

.common-11,
.dashboard-pajamas-status-health,
.dashboard-pajamas-status-health-1,
.dashboard-pajamas-status-health-2,
.dashboard-pajamas-status-health-3,
.dashboard-mingcute-alert-fill-3 {
    width: 12px;
    height: 12px;
}

.common-12,
.dashboard-t-vector,
.dashboard-vector {
    width: 13px;
    height: 13px;
}

.common-13,
.dashboard-mingcute-alert-fill,
.dashboard-mingcute-alert-fill-1,
.dashboard-mingcute-alert-fill-2 {
    width: 10px;
    height: 8px;
}

.common-14,
.dashboard-camera,
.dashboard-camera-1,
.dashboard-camera-2,
.dashboard-camera-3,
.dashboard-camera-4,
.dashboard-camera-5,
.dashboard-camera-6,
.dashboard-camera-7,
.dashboard-camera-8,
.dashboard-camera-9,
.dashboard-camera-10 {
    width: 25px;
    height: 30px;
}

.common-15,
.dashboard-rectangle-7,
.dashboard-rectangle-9,
.dashboard-rectangle-15 {
    height: 1px;
    background-color: #dcdcdc7f;
}

.dashboard-dashboard {
    background-color: #E4ECE6;
    row-gap: 27px;
    padding-bottom: 32px;
    padding-left: 24px;
    padding-right: 40px;
    padding-top: 38px;
    width: 100%;
    font-family: "Rounded Mplus 1c Bold", ui-sans-serif, system-ui, sans-serif;
    font-size: 12px;
    color: #3a3a3a;
    line-height: normal;
    font-weight: 700;
    letter-spacing: 0em;
    height: 100%;
}

.dashboard-user-control-panel {
    row-gap: 5px;
    column-gap: 5px;
    justify-content: space-between;
    flex-wrap: wrap;
}

.dashboard-user-navigation-control {
    row-gap: 14px;
    column-gap: 43px;
    padding-bottom: 4px;
}

.dashboard-t-iot-total-logo {
    padding-bottom: 7px;
}

.dashboard-iot-total-logo {
    object-fit: cover;
    object-position: center;
    width: 112px;
    height: 20px;
    flex-shrink: 0;
}

.dashboard-user-profile-menu {
    column-gap: 8px;
}

.dashboard-background-selected {
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;
    width: 60px;
    height: 60px;
    padding-bottom: 6px;
    padding-left: 6px;
    padding-right: 6px;
    padding-top: 6px;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    flex-shrink: 0;
}

.dashboard-tabler-settings-background {
    flex-shrink: 0;
}

.dashboard-user-menu {
    column-gap: 20px;
}

.dashboard-user-greeting {
    flex-grow: 1;
    align-self: stretch;
    max-width: 67px;
    padding-top: 6px;
    font-family: "Rounded Mplus 1c", ui-sans-serif, system-ui, sans-serif;
}

.dashboard-text {
    color: white;
}

.dashboard-para-1 {
    color: limegreen;
}

.dashboard-settings {
    flex-shrink: 0;
}

.dashboard-log-out {
    flex-shrink: 0;
    cursor: pointer;
}

.dashboard-system-overview {
    row-gap: 9px;
    column-gap: 9px;
    padding-left: 10px;
    flex-grow: 1;
}

.dashboard-operations-center {
    row-gap: 39px;
    column-gap: 14px;
    flex-grow: 1;
}

.dashboard-management-panel {
    row-gap: 9px;
}

.dashboard-background-selected-1 {
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;
    padding-bottom: 7px;
    padding-left: 6px;
    padding-right: 6px;
    padding-top: 8px;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
}

.dashboard-home {
    flex-shrink: 0;
}

.dashboard-cameras {
    flex-shrink: 0;
}


.dashboard-video-wall {
    flex-shrink: 0;
}

.dashboard-inventory {
    flex-shrink: 0;
}


.dashboard-inventory-1 {
    flex-shrink: 0;
}

.dashboard-reports {
    flex-shrink: 0;
}

.dashboard-lpr {
    flex-shrink: 0;
}


.dashboard-procedures {
    flex-shrink: 0;
}



.dashboard-general-detection {
    flex-shrink: 0;
}


.dashboard-file-manager {
    flex-shrink: 0;
}

.dashboard-background-notifications {
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;
    padding-bottom: 8px;
    padding-left: 6px;
    padding-right: 6px;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
}

.dashboard-notifications {
    flex-shrink: 0;
}

.dashboard-system-structure-map {
    row-gap: 52px;
    column-gap: 12px;
    flex-grow: 1;
    align-self: stretch;
}

.dashboard-rectangle {
    border-bottom-left-radius: 24px;
    border-bottom-right-radius: 24px;
    padding-top: 114px;
    border-top-left-radius: 24px;
    border-top-right-radius: 24px;
}

.dashboard-rectangle-16 {
    border-radius: 24px;
    row-gap: 5px;
    padding: 12px;
    justify-content: flex-start;
    height: 100%;
}

.dashboard-t-card-title-tedit {
    row-gap: 5px;
    column-gap: 180px;
    padding-top: 8px;
}

.dashboard-card-title {
    font-family: Inter, ui-sans-serif, system-ui, sans-serif;
    font-size: 22px;
    color: midnightblue;
    line-height: 28px;
}

.dashboard-t-edit {
    height: 10px;
}

.dashboard-edit-1 {
    margin-top: -8px;
    flex-shrink: 0;
}

.dashboard-actualizare-automata-in-3 {
    padding-left: 13px;
}

  .dashboard-management-panel {
     display: flex;
     flex-direction: column;
     height: 100%;
    &::-webkit-scrollbar {
          width: 5px;
    }
    &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 10px;
    }
    &::-webkit-scrollbar-track {
        background-color: transparent;
    }
  }

/* Menu Item Active and Hover Styles */
.dashboard-management-panel a,
.dashboard-management-panel a.active  {
    background-color: #f2f6f3;
    transition: background-color 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    border-radius: 10px;
    position: relative;
    width: 52px;
    height: 52px;
    background-size: 25px 25px;
    background-repeat: no-repeat;
    background-position: center;
    transition: background 0.3s ease;
    align-items: center;
}

.dashboard-management-panel a:hover {
    background-color: #00d603;
    transition: background-color 0.3s ease;
    transform: translateY(-2px);
    box-shadow: 0 0 15px rgba(50, 205, 50, 0.3);
}

.dashboard-management-panel a.active {
    background-color: #00d603;
    box-shadow: 0 0 10px rgba(50, 205, 50, 0.3);
}

/* Menu Item Icon Styles */
.dashboard-management-panel a i {
    font-size: 24px;
    color: #8c8c8c;
    transition: color 0.3s ease;
    position: relative;
    z-index: 1;
}

.dashboard-management-panel a:hover i,
.dashboard-management-panel a.active i {
    color: white;
}

/* Animation assignments */
.dashboard-management-panel a:hover i {
    transition: transform 0.3s ease-in-out;
    transform: scale(1.2);
}

.dashboard-management-panel a.active i {
    transition: transform 0.3s ease-in-out;
    transform: translateY(-2px);
}

.dashboard-management-panel > div {
    transition: opacity 0.5s ease, transform 0.5s ease;
    opacity: 1;
    transform: translateX(0);
    margin-bottom: 10px;
    &:first-child {
        padding-top: 0px;
    }
    &:last-child    {
        padding-bottom: 0px;
    }
}


:host ::ng-deep #results-div {
    background-color: #f5fffa;
}
