/* eslint-disable arrow-body-style */
// Disabled arrow body function for this page because we don't this for loadChildren function
// More info: https://angular.io/guide/lazy-loading-ngmodules#routes-at-the-app-level
import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { NotificationsPageComponent } from "app/pages/notifications-page/notifications-page.component";
import { AuthGuard } from "app/shared";
import { MobiledetectionGuard } from "app/shared/guard/mobiledetection.guard";
import { LayoutComponent } from "./layout.component";

let routes: Routes = [
  {
    path: "",
    component: LayoutComponent,
    children: [
      { path: "", loadChildren: () => import("./customer-dashboard/customer-dashboard.module").then((m) => m.CustomerDashboardModule) },
      {
        path: "dashboard",
        canLoad: [AuthGuard],
        canActivate: [AuthGuard, MobiledetectionGuard],
        loadChildren: () => import("./customer-dashboard/customer-dashboard.module").then((m) => m.CustomerDashboardModule),
      },

      { path: "map-new", canLoad: [AuthGuard], canActivate: [AuthGuard, MobiledetectionGuard], loadChildren: () => import("./app-map/app-map.module").then((m) => m.AppMapModule) },
      { path: "reports", canLoad: [AuthGuard], canActivate: [AuthGuard, MobiledetectionGuard], loadChildren: () => import("./reports/reports.module").then((m) => m.ReportsModule) },
      { path: "vms", canLoad: [AuthGuard], canActivate: [AuthGuard, MobiledetectionGuard], loadChildren: () => import("./vms/vms.module").then((m) => m.VMSModule) },
      { path: "entities", canLoad: [AuthGuard], canActivate: [AuthGuard, MobiledetectionGuard], loadChildren: () => import("./entities/entities.module").then((m) => m.EntitiesModule) },
      { path: "inventory", canLoad: [AuthGuard], canActivate: [AuthGuard, MobiledetectionGuard], loadChildren: () => import("./inventory-new/inventory-new.module").then((m) => m.InventoryNewModule) },
      { path: "scheduler", canLoad: [AuthGuard], canActivate: [AuthGuard, MobiledetectionGuard], loadChildren: () => import("./scheduler-new/scheduler-new.module").then((m) => m.SchedulerNewModule) },
      {
        path: "assetsManagement",
        canLoad: [AuthGuard],
        canActivate: [AuthGuard, MobiledetectionGuard],
        loadChildren: () => import("./assets-management/assets-management.module").then((m) => m.AssetsManagementModule),
      },
      { path: "procedures", canLoad: [AuthGuard], canActivate: [AuthGuard, MobiledetectionGuard], loadChildren: () => import("./procedures/procedures.module").then((m) => m.ProceduresModule) },
      { path: "events", canLoad: [AuthGuard], canActivate: [AuthGuard, MobiledetectionGuard], loadChildren: () => import("./events/events.module").then((m) => m.EventsModule) },
      { path: "swarmLPR", canLoad: [AuthGuard], canActivate: [AuthGuard, MobiledetectionGuard], loadChildren: () => import("./swarm-lpr/swarm-lpr.module").then((m) => m.SwarmLPRModule) },
      { path: "parkingLPR", canLoad: [AuthGuard], canActivate: [AuthGuard, MobiledetectionGuard], loadChildren: () => import("./parking-lpr/parking-lpr.module").then((m) => m.ParkingLPRModule) },
      { path: "generalDetections", canLoad: [AuthGuard], canActivate: [AuthGuard, MobiledetectionGuard],
            loadChildren: () => import("./general-detections/general-detections.module")
                .then((m) => m.GeneralDetectionsModule) },
      { path: "vehicleTraffic",canLoad: [AuthGuard], canActivate: [AuthGuard, MobiledetectionGuard], 
      loadChildren: () => import("./vehicle-traffic/vehicle-traffic.module").then((m) => m.VehicleTrafficModule)
        },
      {
            path: 'notifications',//TODO add guarding and load lazily
            component: NotificationsPageComponent
        },
        {
            path: "ai",
            canLoad: [AuthGuard],
            canActivate: [AuthGuard, MobiledetectionGuard],
            loadChildren: () => import("./ai/ai.module").then((m) => m.AiModule)
        },
    ],
  },
  { path: "video-wall", canLoad: [AuthGuard], canActivate: [AuthGuard, MobiledetectionGuard], loadChildren: () => import("./video-wall/video-wall.module").then((m) => m.VideoWallModule) },
  { path: "mobile-location", canLoad: [AuthGuard], canActivate: [AuthGuard], loadChildren: () => import("./mobileLocation/mobileLocation.module").then((m) => m.MobileLocationModule) },
    { path: "new-dashboard", loadChildren: () => import("../iot-dashboard/iot-dashboard.module").then((m) => m.IotDashboardModule) },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class LayoutRoutingModule {}
