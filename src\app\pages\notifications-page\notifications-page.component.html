  <app-page-title title="notifications"></app-page-title>

  <div class="iot-table">
    <p-table [value]="notifications" styleClass="p-datatable-striped" [paginator]="true" [rows]="pageSize"
      [first]="first" [totalRecords]="totalRecords" [lazy]="true" [loading]="loading"
      (onLazyLoad)="pageIndexChanged($event)" [showCurrentPageReport]="true">

      <ng-template pTemplate="header">
        <tr>

          <th>{{ 'name' | translate }}</th>
          <th>{{ 'type' | translate }}</th>
          <th>{{ 'notificationsPage.value' | translate }}</th>
          <th>{{ 'description' | translate }}</th>
          <th>{{ 'date' | translate }}</th>

          <th>{{ 'actions' | translate }}</th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-notification>
        <tr [class.fade-out]="animateNotification[notification.NotificationId]">
          <td>

            <app-icon [isFont]=true type="icon-car" *ngIf="notification.IsVehicle"></app-icon>

            <div class="notification-name">
              <span class="color-dot" [style.background-color]="notification.RuleColor"></span>
              {{notification.Name}}
            </div>
          </td>
          <td>
            <span class="type-badge" *ngFor="let label of notification.Labels">{{label}}</span>
          </td>
          <td>{{notification.ValueAsString}}</td>
          <td>{{notification.Description}}</td>
          <td>{{formatMoment(notification.TimeStamp)}}</td>

          <td>
            <div class="action-buttons">
              <button pButton type="button" icon="pi pi-check"
                class="p-button-rounded p-button-sm p-button-success p-button-text"
                (click)="acknowledgeNotification(notification)" *ngIf="!notification.isAcknowledged"
                title="{{ 'acknowledge' | translate }}">
              </button>
              <button pButton type="button" icon="pi pi-map-marker"
                class="p-button-rounded p-button-sm p-button-danger p-button-text" (click)="jumpToMap(notification)"
                title="{{ 'jumpToMap' | translate }}">
              </button>
            </div>
          </td>
        </tr>
      </ng-template>

      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="6" class="text-center"> {{ 'noRecordsFound' | translate }} </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
