import { Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Pipe({ name: 'valueOrDefault' })

  
export class IOTValueOrDefaultPipe implements PipeTransform {

  constructor(private translate: TranslateService) {}

  transform(value: any, fallback: string = 'noValue'): any {
    const isEmpty = value === null || value === undefined || value === '' || (Array.isArray(value) && value.length === 0);
    if (isEmpty) {
      return this.translate.instant(fallback);
    }
    return value;
  }
}
