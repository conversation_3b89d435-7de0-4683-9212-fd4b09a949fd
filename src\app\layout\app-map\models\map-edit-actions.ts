import { GlobalAction } from "app/shared/models/global-action.interface";
import { MapActionType } from "../enums/map-action-type.enum";

export const mapEditActions: GlobalAction[] = [
    {name: 'appMap.resetSearch', type: MapActionType.resetSearch, isVisible: false, isActive: true, iconClass: 'fa fa-repeat'},
    {name: 'appMap.search', type: MapActionType.search, isVisible: true, isActive: true, iconClass: 'icon-search'},
    {name: 'appMap.groupActions', type: MapActionType.groupAction, isVisible: true, isActive: true, iconClass: 'icon-group-resources'},
    {name: 'appMap.editLayers', type: MapActionType.editLayers, isVisible: true, isActive: true, iconClass: 'icon-layer-group'},
    // {name: 'appMap.filter', type: MapActionType.filter, isVisible: true, iconClass: 'icon-filter'}
];