import { Component, ComponentFactoryResolver, ComponentRef, Input, OnDestroy, OnInit, ViewChild, ViewContainerRef } from '@angular/core';
import { OverlayPanel } from 'primeng/overlaypanel';
import { DashboardActionType } from '../../enums/dashboard-action-type.enum';
import { WidgetComponents } from '../../models/WidgetComponents';
import { DashboardAction } from '../../models/dashboard-action.interface';
import { Widget } from '../../models/widget';
import { DashboardUtilsService } from '../../services/dashboard-utils.service';
import { WidgetSettingsService } from '../../services/widget-settings.service';
import { DefaultWidgetComponent } from '../widgets/default-widget/default-widget.component';

@Component({
  selector: 'app-generic-widget',
  templateUrl: './generic-widget.component.html',
  styleUrls: ['./generic-widget.component.scss']
})
export class GenericWidgetComponent implements OnInit, OnDestroy {
  @Input() widgetData: Widget;
  @Input() index: number;
  @Input() dashboardEditMode: boolean;
  @ViewChild('widgetFactory', {read: ViewContainerRef, static: false}) widget: ViewContainerRef;
  @ViewChild('overlayActions', {static: false}) overlayActions: OverlayPanel;

  componentRef: ComponentRef<DefaultWidgetComponent>;
  editMode: boolean = false;
  widgetSettings: {[propertyName: string]: string } = {};
  public dropDownActions: DashboardAction[] = [
    {name: 'customerDashboard.editWidget', type: DashboardActionType.editWidget, isVisible: true},
    {name: 'customerDashboard.deleteWidget', type: DashboardActionType.deleteWidget, isVisible: true}
  ]

  constructor(
    private componentFactoryResolver: ComponentFactoryResolver,
    private dashboardUtilsService: DashboardUtilsService,
    private widgetSettingsService: WidgetSettingsService
  ) {}

  ngOnInit(): void {
    this.widgetData = this.dashboardUtilsService.returnWidgetClass(this.widgetData);

    let extraDropDownActions = this.widgetData.getWidgetExtraActions();
    if(extraDropDownActions.length > 0){
      this.dropDownActions = [...extraDropDownActions, ...this.dropDownActions];
    }

    let widgetSettings = this.widgetSettingsService.getWidgetSettings(this.widgetData.id);
    Object.keys(widgetSettings).forEach(key => {
      this.widgetSettings[key] = widgetSettings[key];
    });


    //Fix for drag & drop rearange of widgets. Somehow this gets called again after moving the widgets around.
    setTimeout(() => {
      this.createComponent();
    }, 10);
  }

  ngOnDestroy(): void{
    this.componentRef?.destroy();
  }

  createComponent(): void{
    const widgetComponent = WidgetComponents[this.widgetData.type];
    const factory = this.componentFactoryResolver.resolveComponentFactory(widgetComponent);
    this.componentRef = this.widget.createComponent(factory);
    this.componentRef.instance.data = {widgetData: this.widgetData, index: this.index, widgetSettings: this.widgetSettings};
  }

  emitWidgetAction(actionType: DashboardActionType): void{
    this.dashboardUtilsService.setWidgetAction(this.widgetData, actionType);
    this.overlayActions.hide();
  }
}
