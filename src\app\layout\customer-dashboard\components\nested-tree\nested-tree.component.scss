:host {
    border-bottom: 1px solid var(--secondary-highlight-2);
    display: block;

    &:last-of-type {
        border: 0;
    }
    .item-title {
        display: inline-block;
    }
    
    ul {
        list-style-type: none;
        margin: 0;
        padding: 0;
    }
    
    .btn {
        margin-left: auto;
        line-height: 1rem;
        &:disabled {
            color: var(--secondary-2);
        }
    }

    .item-wrapper{
        padding: 0 0 0 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-flow: row wrap;
        a {
            color: var(--primary-1);
            cursor: pointer;
            width: calc(100% - 50px);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 8px 16px 8px 0;
            .fa {
                margin-right: 8px; 
            }
        }
        .item-children {
            width: 100%;
        }
        .btn {
            width: 50px;
        }

        .btn, a {
            &:hover {
                text-decoration: none;
                color: var(--secondary-3);
            }
        }
    }
    .item-children {
        .item-wrapper {
            padding: 0 0 0 8px;
        }
    }
}
