<form [formGroup]="layerForm" (ngSubmit)="onSubmit()">
  <div class="form-item">
      <input 
          type="text" 
          name="name"
          class="form-control name-control"
          formControlName="name"
          placeholder="{{'appMap.layerName' | translate}}" />
  </div>
  <div class="form-item">
    <p-multiSelect
      formControlName="selectedResourceGroups"  
      [options]="formFilters.groupList"
      [filter]="true"
      filterBy="label" 
      [styleClass]="'input-element'" 
      selectedItemsLabel="{{ 'nrItemsSelected' | translate }}"
      defaultLabel="{{ 'appMap.selectResourceGroup' | translate }}"
      [maxSelectedLabels]="0"
      [appendTo]="'body'">
      <ng-template let-item pTemplate="item">
          {{item.label | translate}}
      </ng-template>
    </p-multiSelect>
  </div>
  <div class="form-item">
    <p-multiSelect
      formControlName="selectedResources"  
      [options]="formFilters.resourceList"
      [filter]="true"
      filterBy="label" 
      [styleClass]="'input-element'" 
      selectedItemsLabel="{{ 'nrItemsSelected' | translate }}"
      defaultLabel="{{ 'appMap.selectResource' | translate }}"
      [maxSelectedLabels]="0"
      [appendTo]="'body'"
      [virtualScroll]="true"
      itemSize="30">
      <ng-template let-item pTemplate="item">
          {{item.label | translate}}
      </ng-template>
    </p-multiSelect>
  </div>
  <div class="form-item">
    <p-multiSelect
      formControlName="selectedStates"  
      [options]="formFilters.resourceStateList"
      [filter]="true"
      filterBy="label" 
      [styleClass]="'input-element'" 
      selectedItemsLabel="{{ 'nrItemsSelected' | translate }}"
      defaultLabel="{{ 'appMap.selectResourceState' | translate }}"
      [maxSelectedLabels]="0"
      [appendTo]="'body'">
      <ng-template let-item pTemplate="item">
          {{item.label | translate}}
      </ng-template>
    </p-multiSelect>
  </div>
  <div class="form-item">
    <p-multiSelect
      formControlName="selectedTypes"  
      [options]="formFilters.resourceTypeList"
      [filter]="true"
      filterBy="label" 
      [styleClass]="'input-element'" 
      selectedItemsLabel="{{ 'nrItemsSelected' | translate }}"
      defaultLabel="{{ 'appMap.selectResourceType' | translate }}"
      [maxSelectedLabels]="0"
      [appendTo]="'body'">
      <ng-template let-item pTemplate="item">
          {{item.label | translate}}
      </ng-template>
    </p-multiSelect>
  </div>
  <div class="sidebar-actions">
    <button class="btn btn-secondary" type="submit" (click)="onCancel()">{{ 'cancel' | translate}}</button>
    <button class="btn btn-primary" type="submit" [disabled]="!layerForm.valid">{{(data.isNew ? 'create':'update') | translate}}</button>
  </div>
</form> 
