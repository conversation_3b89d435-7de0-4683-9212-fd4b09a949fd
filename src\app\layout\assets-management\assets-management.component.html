
    <div class="page-format dashboard-content" content>
        <app-page-title title="assetsManagement"></app-page-title>
        <div class="container-fluid">
            <div class="row border-bottom">
                <div class="col inline-headers align-self-center">
                    <ul class="list-inline heading-list list-format">
                        <li class="list-inline-item li-heading">
                            <a class="active " href="javascript:void(0)" >
                                {{ 'assetsManagement' | translate }}
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="col structure-floating align-self-center">
                    <ul class="list-inline icon-list list-format">

                       <li class="list-inline-item">
                            <button class="btn btn-link" (click)="editEntityForm()" pTooltip="{{ 'inventory.addDevice' | translate }}"
                                tooltipPosition="bottom">
                                <i class="fa fa-plus"></i>
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="row content-router">
                <ng-turbo-table #turboTable [data]="data"  [columns]="columns" [showResultsCountTop]="true" [exportFileName]="'inventoryExport'"
                [showToggleColumns]="true" [numOfRows]="10" [enableRowClick]="true" [scrollHeight]="'600px'" [showTableCaption]="showTableCaption"
                (selectedElements)="onSelectedElements($event)"  >
                </ng-turbo-table>

                <ng-template #actionsChannel let-rowData>
                    <button type="button" #button class="btn btn-secondary btn-action" (click)="editEntityForm(rowData)">
                        <i class="fa fa-pencil" aria-hidden="true" ></i>
                      </button>

                      <button type="button" #button class="btn btn-secondary btn-action" (click)="deleteEntity(rowData)">
                        <i class="fa fa-trash" aria-hidden="true" ></i>
                      </button>
                </ng-template>

            </div>
        </div>
    </div>
    <app-modal [title]="'addEditEntity'" #entityFormEdit>
        <ng-container ngProjectAs="contentModal">
            <app-entity-form #form (displayForm)='closeForm()'  (saveEntity)="saveEntity($event)" (deleteEntity)="deleteEntity($event)" [newEntity]="newEntity"></app-entity-form>
        </ng-container>
    </app-modal>


