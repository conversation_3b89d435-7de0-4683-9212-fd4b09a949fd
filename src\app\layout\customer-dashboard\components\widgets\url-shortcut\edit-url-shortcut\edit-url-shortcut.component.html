<div class="dashboardSettings">
    <app-edit-widget [selectedWidget]="data"></app-edit-widget>
    <form role="form" class="form-format" [formGroup]="urlShortcutForm">
        <div class="form-item">
            <h2>{{ 'customerDashboard.widgetType.urlShortcut' | translate }}</h2>
            <input type="text"  name="url" formControlName="url">
            <div class="all-pattern-error"
            *ngIf="!urlShortcutForm.controls.url.valid && urlShortcutForm.controls.url.touched">
            <div class="input-error">
              <small *ngIf="urlShortcutForm.controls['url'].hasError('required')" class="text-danger">
                {{ 'formValidation.field'  | translate}}
              </small>
              <small *ngIf="urlShortcutForm.get('url').errors.invalidDomain" class="text-danger">
                 {{ 'invalidDomain' | translate}} {{ localDomain }}.
              </small>
            </div>
          </div>
        </div>
    </form>
</div>