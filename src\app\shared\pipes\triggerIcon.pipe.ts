import { Pipe, PipeTransform } from '@angular/core';
import { INotificationData } from '../components/header/notifications/INotificationData';

@Pipe({
  name: 'triggerIcon'
})
export class TriggerIconPipe implements PipeTransform {
  transform(notification: INotificationData): string {
    if (!notification || !notification.Types?.length) {
      return '';
    }
    return `assets/public/assets/${notification.Types[0]}.png`;
  }
}
