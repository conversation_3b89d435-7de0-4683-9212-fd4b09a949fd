<div class="notification-wrapper" >
  <div class="scroll-wrapper">
    <div class="data-type-wrapper" *ngIf="notifications.length === 0">
      {{ 'noDataWaitingForEvents' | translate}}
    </div>

    <div class="notification-container">
        <div (click)="dismissAll()" class="dismissAll" *ngIf="notifications.length > 0">{{'dismissAll' | translate}}</div>
      <div class="notification-item" *ngFor="let item of notifications" (click)="performAction(item)"
        pTooltip="{{ 'acknowledgeClick' | translate }}" tooltipPosition="top">
        <div class="event-link">
          <button class="btn {{item.ActionType.toString().toLowerCase()}}" [ngClass]="{'notification-acknowledged': item.IsAcknowledged}" [disabled]="item.IsActionDisabled"
            >
            <i class="fa fa-check" *ngIf="item.IsAcknowledged" aria-hidden="true"></i>
            <span class="icon" *ngIf="!item.IsAcknowledged"></span>
          </button>
        </div>
        <div class="item-description" [ngStyle]="{'color': item.RuleColor}">
          <div class="highlight">{{item.Name}}</div>
          <div><span>{{item.Types[0]}} {{item.Value}}</span></div>

          <div>{{item.Description}}</div>
          <div>
             {{item.Date}}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
