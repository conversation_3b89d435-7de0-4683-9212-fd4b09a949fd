@import '~styles/colors';  
    .dashbox-container {
        border-radius: 4px;
        border: 1px solid var(--secondary-highlight-5);
        height: 100%;
        cursor: pointer;
        .dashbox-no-data {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
        }
        .dashbox-data {
            overflow: hidden;
            display: flex;
            padding: 5px 10px;
            height: 100%;
            &.device-type-25 {
            .data-icon {
                &:before {
                        content: "\e900";
                    }
            }
            }
            &.device-type-4 {
                .data-icon {
                    &:before {
                        content: "\e902";
                    }
                }
            }
            &.device-type-38 {
                .data-icon {
                    font-family: 'FontAwesome';
                    &:before {
                        content: "\f0e7";
                    }
                }
            }
        }
        .dashbox-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 10px;
            :host-context(.rtl) & {
                margin-left: 10px;
                margin-right: 0px;
            }
        .data-icon {
                width: 55px;
                height: 55px;
                border-radius: 50%;
                font-family: 'icomoon';
                font-size: 1.7rem;
                display: flex;
                justify-content: center;
                align-items: center;
                background-color: var(--primary-1);
                color: var(--light);
                &:before {
                    display: block;
                    content: "\e912";
                }
            
            }
        }
        .no-resource-state {
            display: flex;
            align-items: center;
        }
        .dashbox-info {
            padding: 10px 0px;
            width: 100%;
            white-space: nowrap;
            display:flex;
            flex-direction: column;
            align-items:stretch;
            overflow: hidden;
            text-overflow: ellipsis;
            .dashbox-title {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                font-size: 1.3rem;
                margin-bottom: 0px;
            }
            ul {
                columns: 3;
                padding: 0px;
                margin-bottom: 0px;
                li {
                    list-style: none;
                    font-size: 0.75rem;
                    text-transform: lowercase;
                    span:first-child {
                        font-weight: 700;
                    }
                }
            }
        }
    }
::ng-deep {
    p-table {
        .ui-table-scrollable-body, .ui-paginator-bottom, .ui-table-scrollable-header-box {
            border: 0 !important;
        }
        .ui-table-scrollable-body {
            border-top: 1px solid var(--secondary-highlight-5) !important;
        } 
    }
    .rtl {
        .view-switch-container {
            right: auto !important;
            left: 25px;
        }
        .view-title {
            text-align: right !important;
            padding-right: 5px;        
        }
        
    }    
}    
::ng-deep .ui-dialog {
    
    .ui-dialog-footer {
        padding: 0 !important;
    }

    .component-view-container {
        display: block;
        width: 80vw;
        height: 70vh;
        /* This margin has to have negative values as to the app modal padding */
        margin: -1.8rem -1.8rem -1.8rem -1.2rem;
    }

    .turbo-table {
        position: relative !important;
    }
    
    app-cymbiot-map {
        display: block;
        width: 100%;
        height: 100%;
    }

    .view-switch-container {
        position: absolute;
        right: 25px;
        top: -33px;
        display: flex;
        .view-title {
            padding-left: 5px;
            font-size: 0.8125rem;
            display: block;
            text-align: left;
            min-width: 100px;
        }
    }
} 