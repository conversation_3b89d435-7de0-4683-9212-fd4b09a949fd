import { INotificationData } from './../../../shared/components/header/notifications/INotificationData';
import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { Subject, Subscription } from 'rxjs';
import { debounceTime, filter, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'warning-list',
  templateUrl: './warning-list.component.html',
  styleUrls: ['./warning-list.component.scss']
})
export class WarningListComponent implements OnInit {

  @Input() notifications: INotificationData[] = [];
  @Input() totalNotificationRecords = 0;

  @Input() notificationLoading = false;
  @Input() pageSize = 10;
  @Input() notificationPageIndex = 0;

  @Output() notificationLazyLoad = new EventEmitter<number>();
  @Output() rowSelect = new EventEmitter<INotificationData>();
  private notificationsScroll$ = new Subject<Event>();
  private destroy$ = new Subject<void>();
  private subscriptions: Subscription[] = [];

  constructor( ) {}
  
  ngOnInit(): void {
    this.setupScrollHandlers();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private setupScrollHandlers(): void {
    const alarmsSubscription = this.notificationsScroll$
      .pipe(
        debounceTime(200),
        filter(event => this.isScrolledToBottom(event.target as HTMLElement)),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        if (this.notifications.length < this.totalNotificationRecords) {
          this.notificationPageIndex++;
          this.notificationLazyLoad.emit(this.notificationPageIndex);
        }
      });

    this.subscriptions.push(alarmsSubscription);
  }


  onRowClick (notification: INotificationData): void {
    this.rowSelect.emit(notification);
  }

  onScroll(event: Event): void {
      this.notificationsScroll$.next(event);
  }

   private isScrolledToBottom(element: HTMLElement): boolean {
    return element.scrollHeight - element.scrollTop - element.clientHeight < 50;
  }


}