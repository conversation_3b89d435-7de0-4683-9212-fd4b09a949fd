import { INotificationData } from './../../../shared/components/header/notifications/INotificationData';
import { Component, EventEmitter, Input, Output } from "@angular/core";
import { LazyLoadEvent } from 'primeng/api';

@Component({
  selector: 'warning-list',
  templateUrl: './warning-list.component.html',
  styleUrls: ['./warning-list.component.scss']
})
export class WarningListComponent {

  @Input() notifications: INotificationData[] = [];
  @Input() totalNotificationRecords = 0;

  @Input() notificationLoading = false;
  @Input() pageSize = 10;
  @Input() notificationPageIndex = 0;

  @Output() notificationLazyLoad = new EventEmitter<number>();
  @Output() rowSelect = new EventEmitter<INotificationData>();
  
  constructor( ) {}

  onNotificationLazyLoad (e: LazyLoadEvent): void {
    this.notificationPageIndex++;
    this.notificationLazyLoad.emit(this.notificationPageIndex++);
  }

  onRowClick (notification: INotificationData): void {
    this.rowSelect.emit(notification);
  }

}