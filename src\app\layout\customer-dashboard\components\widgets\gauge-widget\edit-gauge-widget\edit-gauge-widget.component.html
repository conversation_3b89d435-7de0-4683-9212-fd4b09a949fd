<div class="dashboardSettings">
    
    <app-edit-widget [selectedWidget]="data"></app-edit-widget>

    <div class="form-item">
        <h2>{{ 'eventName' | translate }}</h2>
        <p-multiSelect
            [options]="eventsNameData"
            [(ngModel)]="data.eventIdsSelectVal"
            (onChange)="eventNameChange()"
            defaultLabel='{{ "allEvents" | translate }}'
            selectedItemsLabel="{{ 'nrItemsSelected' | translate }}"
            [maxSelectedLabels]="0">
            <ng-template let-event pTemplate="item">
                {{ event.label }}
            </ng-template>
        </p-multiSelect>    
    </div>

    <div class="form-item">
        <h2>{{ 'resourceGroup' | translate }}</h2>  
        <p-dropdown 
            [options]="groupList"
            [filter]="true"
            filterBy="label" 
            [(ngModel)]="data.selectedGroupId" 
            (onChange)="selectGroup($event)"
            placeholder="{{'customerDashboard.selectGroup' | translate}}">
            <ng-template let-item pTemplate="selectedItem">
                <span>{{item.label| translate}}</span>
            </ng-template>
            <ng-template let-item pTemplate="item">
                <div class="ui-helper-clearfix">
                    <div>{{item.label | translate}}</div>
                </div>
            </ng-template>
        </p-dropdown>
    </div>
    <div class="form-item">
        <h2>{{ 'resource' | translate }}</h2>  
        <p-dropdown 
            [options]="resourceList" 
            [filter]="true"
            filterBy="label"
            [(ngModel)]="data.selectedResourceId" 
            (onChange)="selectResource($event)"
            placeholder="{{'customerDashboard.selectResource' | translate}}" 
            required="true"
            #selectedResourceId="ngModel"
            [virtualScroll]="true"
            itemSize="30">
            <ng-template let-item pTemplate="selectedItem">
                <span>{{item.label| translate}}</span>
            </ng-template>
            <ng-template let-item pTemplate="item">
                <div class="ui-helper-clearfix">
                    <div>{{item.label | translate}}</div>
                </div>
            </ng-template>
        </p-dropdown>
        <div *ngIf="selectedResourceId.errors" class="input-error">
            <span *ngIf="selectedResourceId.errors.required && selectedResourceId.touched">{{'formErrors.required' | translate}}</span>
        </div>
    </div>
    <div class="form-item">
        <h2>{{ 'triggers' | translate }}</h2>  
        <p-dropdown 
            [options]="triggerList"
            [(ngModel)]="data.selectedTrigger" 
            [filter]="true"
            filterBy="label" 
            (onChange)="selectResourceTrigger($event)"
            placeholder="{{'customerDashboard.selectTrigger' | translate}}" 
            required="true"
            #selectedTrigger="ngModel">
            <ng-template let-item pTemplate="selectedItem">
                <span>{{item.label| translate}}</span>
            </ng-template>
            <ng-template let-item pTemplate="item">
                <div class="ui-helper-clearfix">
                    <div>{{item.label | translate}}</div>
                </div>
            </ng-template>
        </p-dropdown>
        <div *ngIf="selectedTrigger.errors" class="input-error">
            <span *ngIf="selectedTrigger.errors.required && selectedTrigger.touched">{{'formErrors.required' | translate}}</span>
        </div>
    </div>

    <div class="form-item">
        <h2>{{ 'customerDashboard.displayMethod' | translate }}</h2>  
        <p-dropdown 
            [options]="displayResultList"
            [(ngModel)]="data.displayResultsMethod" 
            (onChange)="selectDisplayResult($event)" 
            placeholder="{{'customerDashboard.displayMethod' | translate}}" 
            required="true"
            #displayResultsMethod="ngModel">
            <ng-template let-item pTemplate="selectedItem">
                <span>{{item.label| translate}}</span>
            </ng-template>
            <ng-template let-item pTemplate="item">
                <div class="ui-helper-clearfix">
                    <div>{{item.label | translate}}</div>
                </div>
            </ng-template>
        </p-dropdown>
        <div *ngIf="displayResultsMethod.errors" class="input-error">
            <span *ngIf="displayResultsMethod.errors.required && displayResultsMethod.touched">{{'formErrors.required' | translate}}</span>
        </div>
    </div>

    <div class="form-item multiple">
        <h2>{{ 'customerDashboard.minMaxMeasurmentValues' | translate }}</h2>  
        <div class="group">
            <input type="number" name="minValue" [(ngModel)]="data.minValue" #minValue="ngModel"  (ngModelChange)="onMinMaxValue$.next({value: $event, option: 'minValue'})" placeholder="{{'min' | translate}}" required />
            <div *ngIf="minValue.errors" class="input-error">
                <span *ngIf="minValue.errors.required && minValue.touched">{{'formErrors.required' | translate}}</span>
            </div>
        </div>
        <div class="group">
            <input type="number" name="maxValue" [(ngModel)]="data.maxValue" #maxValue="ngModel" (ngModelChange)="onMinMaxValue$.next({value: $event, option: 'maxValue'})" placeholder="{{'max' | translate}}"  required />
            <div *ngIf="maxValue.errors" class="input-error">
                <span *ngIf="maxValue.errors.required && maxValue.touched">{{'formErrors.required' | translate}}</span>
            </div>
        </div>
        <div class="group">
            <input type="text" name="measurmentUnit" [(ngModel)]="data.measurmentUnit" #measurmentUnit="ngModel" (ngModelChange)="onMinMaxValue$.next({value: $event, option: 'measurmentUnit'})" placeholder="{{'measurementUnit' | translate}}" required />
            <div *ngIf="measurmentUnit.errors" class="input-error">
                <span *ngIf="measurmentUnit.errors.required && measurmentUnit.touched">{{'formErrors.required' | translate}}</span>
            </div>
        </div>
    </div>
    <div class="form-item">
        <h2>{{ 'customerDashboard.thresholdPoints' | translate }}</h2>
        <form [formGroup]="thresholdPointsForm">
            <div formArrayName="items"
                *ngFor="let item of thresholdPointsForm.get('items')['controls']; let i = index">
                <div [formGroupName]="i" class="form-group">
                    
                    <input type="number" formControlName="start" placeholder="{{'customerDashboard.thresholdStart' | translate}}" class="threshold number" required />
                    
                    <input type="number" formControlName="end" placeholder="{{'customerDashboard.thresholdEnd' | translate}}" class="threshold number" required />
                    
                    <input type="text" formControlName="eventName" placeholder="{{'customerDashboard.thresholdEventName' | translate}}" class="threshold text" />
                    
                    <div class="thrColor {{item.controls.class.value}}" (click)="showOp(i)"></div>
                    
                    <button class="btn btn-secondary" (click)="removeThresholdItem(i)">
                        <i class="fa fa-trash-o" aria-hidden="true"></i>
                    </button>

                    <div *ngIf="item.invalid && item.touched" class="input-error">
                        <span>{{'formErrors.startEndRequired' | translate}}</span>
                    </div>    
                </div>
            </div>
        </form>
        
        <button class="btn btn-secondary add-button" (click)="addThresholdItem()" [disabled]="minValue?.errors?.required || maxValue?.errors?.required">
            <i class="fa fa-plus" aria-hidden="true"></i>
        </button>
        
        <p-overlayPanel appendTo="body" styleClass="thr-option" #colorsOp>
            <ng-container *ngFor="let item of thresholdClasses">
                <div class="{{item}}" (click)="selectClass(item)"></div>
            </ng-container>
        </p-overlayPanel>
    </div>

</div>
