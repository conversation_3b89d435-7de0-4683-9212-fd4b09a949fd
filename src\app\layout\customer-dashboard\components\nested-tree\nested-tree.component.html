<div class="item-wrapper">
	<a href="javascript:void(0)" class="item-title" title="{{ item.name }}" (click)="selectTreeItem(item)">
		<i class="fa fa-caret-right" aria-hidden="true" *ngIf="hasParent"></i>
		<span>{{ item.name }}</span>
	</a>
	<button class="btn btn-link" title="{{ 'showChildren' | translate}}" (click)="showChildren()" [disabled]="!item.children.length">
		<i class="fa {{isChildVisible ? 'fa-minus' : 'fa-plus'}}" aria-hidden="true"></i>
	</button> 
	<ul class="item-children" *ngIf="isChildVisible">
		<li *ngFor="let child of item.children">
			<app-nested-tree [item]="child" [hasParent]="true" class="app-nested-tree"></app-nested-tree>
		</li>
	</ul>
</div>