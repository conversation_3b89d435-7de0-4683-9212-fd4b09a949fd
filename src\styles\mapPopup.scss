@import '~styles/colors';

.h1 {
    font-size: 0.875rem;
}

.popup-wrapper {
    position: absolute;
    bottom: 10px;
    left: -263px;
    z-index: 999;
    background: var(--light);
    max-width: 557px;
    min-width: 557px;
    padding: 15px;
    color: #4D565C;
    font-size: 0.813rem;
    box-shadow: 1px 1px 14px 2px var(--shadow);

    h4 {
        font-size: 0.875rem;
        font-weight: 500;
    }

    .arrow-down {
        position: absolute;
        bottom: -10px;
        left: calc(50% - 10px);
        width: 0;
        height: 0;
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
        border-top: 10px solid var(--light);
        box-shadow: 0px 2px 2px -2px rgba(0,0,0,0.3);
        display: block; /* Ensure the arrow is visible */
    }
}

:host ::ng-deep {
    .dim-light-wrapper {
        .form-item {
            margin: 5px 0px 0px 0px;

            .form-control {
                padding: 5px 0px !important;
            }
        }

    }
}

.close-popup {
    position: absolute;
    top: 10px;
    right: 20px;
    color: #EBEBEB;
    font-size: 1.375rem;
    cursor: pointer;

    :host-context(.rtl) & {
        right: auto;
        left: 20px;
    }
}

.actions,
.navigation {

    .edit,
    .navi {
        background-color: #EBEBEB;
        color: #646262;
        font-size: 24px;
        padding: 0 3px;
        font-family: 'FontAwesome';
        margin-right: 10px;

        &:hover,
        &.active {
            background-color: #0275D8;
            color: #FFFFFF;
        }
    }
}

.custom-popup {
    padding: 10px 0;
    border-width: 1px 0;
    border-color: #EBEBEB;
    border-style: solid;
    margin-bottom: 10px;
    height: 321px;

    .visibility-toggle {
        text-align: center;
        cursor: pointer;
    }
}

.property-list {
    list-style: none;
    padding: 0px;
    margin: 0px;
    max-height: 272px;
    overflow-x: hidden;
    overflow-y: hidden;

    &.full-view {
        overflow-y: scroll;
    }

    .item {
        margin: 0;
        padding: 0;
    }

    li {
        padding: 5px;
        display: flex;
        justify-content: flex-start;
        white-space: normal;
        align-items: center;

        &:nth-child(odd) {
            background: #EBEBEB;
        }

        &.li-group {
            min-height: 31px;
        }
    }

}

span.name {
    min-width: 110px;
    max-width: 50%;
    white-space: break-spaces;
    margin-right: 10px;
}

.group {
    text-decoration: underline;
    cursor: pointer;

    &:hover {
        text-decoration: underline;
        color: #0275D8;
    }

}

.group-list {
    list-style: none;
    margin: 0px;
    padding: 0px;
    white-space: normal;

    li {
        display: inline-flex;
        justify-content: flex-end;
        align-items: center;
        padding: 0px;
        margin-right: 5px;

        &:nth-child(odd) {
            background: transparent;
        }
    }

    .group-active {
        color: #fff;
        text-decoration: none;
        border: 1px solid #0275D8;
        padding: 0px 5px;
        margin-left: 3px;
        border-radius: 5px;
        background: #0275D8;

        &:hover {
            color: #fff;
            text-decoration: none;
        }
    }
}

.group-name {
    color: #fff;
    border: 1px solid #0275D8;
    padding: 0px 5px;
    margin-left: 3px;
    border-radius: 5px;
    background: #0275D8;

    :host-context(.rtl) & {
        margin-left: 0px;
        margin-right: 3px;
    }
}

.status-icon-online,
.status-icon-on,
.status-icon-checkLight,
.status-icon-CheckLight,
.status-icon-checklight,
.status-icon-check.light,
.status-icon-ON-PARAMS-FLUCTUATION,
.status-icon-onParamsFluctuation {
    color: $online;
}

.status-icon-off,
.status-icon-OFF,
.status-icon-OFF.MAINTENANCE.CHECK,
.status-icon-offMaintenanceCheck,
.status-icon-offmaintenancecheck,
.status-icon-off.maintenance.check,
.status-icon-OFF-PARAMS-FLUCTUATION,
.status-icon-offParamsFluctuation {
    color: $off;
}

.status-icon-offline {
    color: $offline;
}

.status-icon-unknown,
.status-icon-checkLight,
.status-icon-Checklight,
.status-icon-checklight,
.status-icon-check.light,
.status-icon-CHECK.LIGHT {
    color: $unknown;
}

.status-icon-dim,
.status-icon-MAINTENANCE.CHECK,
.status-icon-maintenanceCheck,
.status-icon-maintenancecheck,
.status-icon-maintenance.check {
    color: $dim;
}

.status-icon-offbypower {
    color: $offByPower;
}

.status-icon-temperror {
    color: $tempError;
}

.status-icon-error,
.status-icon-simIssue,
.status-icon-simissue,
.status-icon-SIM.ISSUE,
.status-icon-sim.issue {
    color: $error;
}

.status-icon-notspecified {
    color: $notSpecified;
}

.status-icon-withoutcommunication,
.status-icon-NO.COMM {
    color: $withoutCommunication;
}

.navigation {
    display: flex;
    align-items: center;
    margin-left: auto;

    :host-context(.rtl) & {
        margin-left: 0;
        margin-right: auto;
        flex-direction: row-reverse;

        span {
            direction: initial;
        }
    }

    .navi {
        padding: 0 7px;
        margin: 0;
        background: none;
    }

    span {
        margin: 0 5px;
    }
}

.device-wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    height: 463px;
}

h4,
app-data-properties {
    flex-basis: 100%;
}

h4 {
    margin-bottom: 10px;
}
