import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { NotificationWidget } from 'app/layout/customer-dashboard/models/notification-widget';
import { Subscription } from 'rxjs';
import { AuthService } from 'app/shared/services/auth.service';
import { TranslateService } from '@ngx-translate/core';
import { MessageService } from 'primeng/api';
import { ToastTypes } from 'app/shared/enum/toast-types';
import { INotificationData } from 'app/shared/components/header/notifications/INotificationData';
import { NotificationsService } from 'app/shared/components/header/notifications/notifications.service';
import { AppNotificationService } from 'app/services/app-notification.service';

@Component({
  selector: 'app-notification-widget',
  templateUrl: './notification-widget.component.html',
  styleUrls: ['./notification-widget.component.scss']
})
export class NotificationWidgetComponent implements OnInit, OnDestroy {
  data: {index: number, widgetData: NotificationWidget};
  public notifications: INotificationData[] = [];
  private subscriptions: Subscription[] = [];
  notificationSubscription;
    private colors={
        'white': '#FFFFFF',
        'grey': '#808080',
    }
  constructor(
    private appNotificationService: AppNotificationService,
    private authService: AuthService,
    private messageService: MessageService,
    private translateService: TranslateService,
    private notificationService:NotificationsService  ) {

    this.notificationSubscription=this.notificationService.newNotification.subscribe((event) => {
      this.insertNotification([event]);
    })
    this.subscriptions.push(this.notificationSubscription);
  }

  ngOnInit(): void {
    this.data.widgetData = new NotificationWidget(this.data.widgetData);

    this.getNotifications();
  }

  getNotifications(): void{
    let notificationSubscription = this.notificationService.getNotifications().subscribe(notifications => {
      this.notifications = notifications;
        for (let i=0; i< this.notifications.length; i++)
        {
            let notification = this.notifications[i];
            if(notification.RuleColor == this.colors.white){
                notification.RuleColor = this.colors.grey;
            }
        }
    });
    this.subscriptions.push(notificationSubscription);
  }

  insertNotification(notifications: INotificationData[]): void {
    notifications.forEach((item) => {
      if(this.isMaxNotificationNumber(this.notifications)){
        this.notifications.pop();
      }
      this.notifications.unshift(item);
    });
  }

  isMaxNotificationNumber(notifications: INotificationData[]): boolean{
    if(this.data.widgetData.selectedNotificationNumber <= notifications.length){
      return true;
    }
    return false;
  }

  performAction(notification: INotificationData): void {
    let acknowledgeSubscription = this.notificationService.Acknowledge(notification.NotificationId)
        .subscribe(notifications => {
        this.notifications = notifications;
    });
    this.subscriptions.push(acknowledgeSubscription);

    this.notificationService.DismissEvent(notification).subscribe(success => {
        if (success) {
            this.messageService.add({severity: 'success', summary: this.translateService.instant(ToastTypes.success), detail: this.translateService.instant('dismissEventSuccess')});
        } else {
            this.messageService.add({severity: 'error', summary: this.translateService.instant(ToastTypes.error), detail: this.translateService.instant('dismissEventError')});
        }
    });

    let dimissNotificationSubscription = this.notificationService.DismissNotification(notification).subscribe((response: boolean) => {

      if (response) {
        let index = this.notifications.findIndex(n => n.NotificationId == notification.NotificationId);
        this.notifications.splice(index, 1);
      }

      this.performNotificationPostActionLogic(response, notification, true);
    });
    this.subscriptions.push(dimissNotificationSubscription);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(subscription => {subscription.unsubscribe();});
    this.notifications = [];
    this.notificationSubscription.unsubscribe();
  }

  dismissAll()
  {
    this.notificationService.DismissAllEvents().subscribe(success => {
        if (success) {
            this.messageService.add({severity: 'success', summary: this.translateService.instant(ToastTypes.success), detail: this.translateService.instant('dismissEventSuccess')});
        } else {
            this.messageService.add({severity: 'error', summary: this.translateService.instant(ToastTypes.error), detail: this.translateService.instant('dismissEventError')});
        }
    });

    let dimissNotificationSubscription = this.notificationService.DismissAllNotifications().subscribe((response: boolean) => {

      this.performNotificationPostActionLogic(response, null, false);
    });
    this.subscriptions.push(dimissNotificationSubscription);

    this.getNotifications();
  }

  performNotificationPostActionLogic(response: boolean, notification: INotificationData, isAcknowledge: boolean): void {
    if (isAcknowledge) {
        if (response) {
            if(notification.IsAcknowledged) {
                this.messageService.add({severity: 'info', summary: this.translateService.instant(ToastTypes.info), detail: this.translateService.instant('alreadyAckNotification')});
                return;
              }
            let ackSubscription = this.appNotificationService.ackNotification(this.authService.user.Identity, notification).subscribe(() => {
                this.messageService.add({severity: 'success', summary: this.translateService.instant(ToastTypes.success), detail: this.translateService.instant('ackEventSuccess')});
                notification.IsAcknowledged = true;
              }, () => {
                this.messageService.add({severity: 'error', summary: this.translateService.instant(ToastTypes.error), detail: this.translateService.instant('ackEventError')});
              });
            this.subscriptions.push(ackSubscription);
        }
        else {
          this.messageService.add({severity: 'error', summary: this.translateService.instant(ToastTypes.error), detail: this.translateService.instant('ackEventError')});
        }
    }
    else {
      if (response) {
        this.messageService.add({severity: 'success', summary: this.translateService.instant(ToastTypes.success), detail: this.translateService.instant('dismissEventSuccess')});
      }
      else {
        this.messageService.add({severity: 'error', summary: this.translateService.instant(ToastTypes.error), detail: this.translateService.instant('dismissEventError')});
      }
    }
  }
}
