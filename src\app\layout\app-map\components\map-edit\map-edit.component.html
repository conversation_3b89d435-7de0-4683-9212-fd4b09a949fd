<div class="map-edit-wrapper">
    <form [formGroup]="editMapForm">
        <div class="form-item">
            <h1>{{ (data.state.addNew ? 'appMap.addMap' : 'appMap.editMap') | translate }}</h1>
            <input type="text" name="mapName" class="form-control" (keyup)="name$.next($event.target.value)"
                formControlName="mapName" placeholder="{{'appMap.mapName' | translate}}" />
        </div>
        <div class="form-item">
            <p-dropdown [options]="tileSourceList" formControlName="tileLayer" [styleClass]="'input-element'"
                (onChange)="onTileLayerDataChange($event)" placeholder="{{'appMap.selectTileSource' | translate}}"
                [appendTo]="'body'">
                <ng-template let-item pTemplate="selectedItem">
                    <span>{{item.label| translate}}</span>
                </ng-template>
                <ng-template let-item pTemplate="item">
                    <div class="ui-helper-clearfix">
                        <div>{{item.label | translate}}</div>
                    </div>
                </ng-template>
            </p-dropdown>
        </div>
    </form>
</div>