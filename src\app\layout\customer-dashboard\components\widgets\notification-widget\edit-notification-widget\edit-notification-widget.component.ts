import { Component, OnInit } from '@angular/core';
import { DefaultWidgetEditorComponent } from '../../default-widget/default-widget-editor.component';
import { NotificationWidget } from 'app/layout/customer-dashboard/models/notification-widget';
import { SelectItem } from 'primeng/api';
import { DashboardUtilsService } from 'app/layout/customer-dashboard/services/dashboard-utils.service';

@Component({
  selector: 'app-edit-notification-widget',
  templateUrl: './edit-notification-widget.component.html',
  styleUrls: ['./edit-notification-widget.component.scss']
})
export class EditNotificationWidgetComponent extends DefaultWidgetEditorComponent implements OnInit {
  data: NotificationWidget;
  notificationNumberList: SelectItem[] = [
    {label: "5", value: 5},
    {label: "10", value: 10},
    {label: "20", value: 20}
  ];
  
  constructor(private dashboardUtilsService: DashboardUtilsService) { 
    super();
  }

  ngOnInit() {
    this.data = new NotificationWidget(this.data);
  }

  selectNumber(event):void{
    this.data.selectedNotificationNumber = event.value;
    this.dashboardUtilsService.setWidgetDataChange(this.data);
  }

}
